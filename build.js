import inquirer from 'inquirer'
import { execSync } from 'child_process'
import chalk from 'chalk'
import archiver from 'archiver'
import fs from 'fs'
import path from 'path'
import dayjs from 'dayjs'

const compress = (output, resolve, reject) => {
  console.log('开始压缩...')
  const buildOutputDir = 'dist';

  const archive = archiver('zip', {
    zlib: { level: 9 } // 设置压缩级别
  })
  output.on('close', () => {
    console.log(archive.pointer() + ' total bytes');
  });
  archive.on('warning', err => {
    if (err?.code !== 'ENOENT') {
      reject(err);
    }
  });
  archive.on('error', (err) => {
    console.log(chalk.red('× 压缩失败'))
    reject(err);
  });
  archive.directory(buildOutputDir, true);
  archive.pipe(output);
  archive.finalize().then(() => {
    console.log('√ 压缩成功')
    resolve();
  })
}

// 封装execSync命令+打包为返回Promise的函数
const execPromise = (command, output) => {
  return new Promise((resolve, reject) => {
    execSync(command, (error, stdout, stderr) => {
      if (error) {
        reject(error);
      } else if (stderr) {
        console.error(`stderr: ${stderr}`);
        reject(stdout); // 或者你可以选择reject，取决于你想要的错误处理策略
      }
    });
    compress(output, resolve, reject)
  })
}

const buildAndCompress = async () => {
  try {
    const answer = await inquirer.prompt([
      {
        type: 'input', // input, number, password, list, confirm, rawlist, expand, checkbox, editor
        name: 'version', // 问题的名称，用于在答案对象中引用该问题的答案
        message: '请输入版本号：',  // 问题的提示信息
      },
    ])
    // 处理忘记写v
    if (answer.version[0] !== 'v') {
      answer.version = 'v' + answer.version
    }
    // 是否需要打tag
    const answerTag = await inquirer.prompt([
      {
        type: 'confirm', // input, number, password, list, confirm, rawlist, expand, checkbox, editor
        name: 'needTag', // 问题的名称，用于在答案对象中引用该问题的答案
        message: `是否需要将版本号${answer.version}作为标签并推送到远程仓库？`,  // 问题的提示信息
      },
      {
        type: 'list',
        message: '请选择需要打包的环境？',
        name: 'envs',
        choices: ['虚机', '外网v1', '外网v2', '虚机+外网v1', '虚机+外网v2', '虚机+外网v1+外网v2'],
        default: '虚机+外网v1',
        validate: (answer) => {
          if (answer.length < 1) {
            return '你至少需要选择一个需要打包的环境';
          }
          return true;
        }
      }
    ])

    console.log(chalk.bgWhite.bold(`\n>>>>>> 开始执行打包任务：${answerTag.envs}${answerTag.needTag ? '，并打标签' : ''} <<<<<<`));
    console.log(chalk.bgWhite.bold(`版本号：${answer.version}\n`))

    // 虚机
    if (answerTag.envs.includes('虚机')) {
      console.log('\n>> 开始执行【虚机】打包命令...\n')
      const name1 = `ai外呼生产发布（虚机）${answer.version}.zip`
      const filePath = path.resolve(`../docs/项目包/${dayjs().format('YYYY-MM-DD')}`);
      await fs.promises.mkdir(filePath, { recursive: true });
      const output1 = fs.createWriteStream(path.join(filePath, name1));
      await execPromise('yarn build.internal', output1)
      console.log(chalk.green('\n>> 完成【虚机】打包命令\n'))
    }

    // 外网人工外呼第一套
    if (answerTag.envs.includes('外网v1')) {
      console.log('\n>> 开始执行【外网v1】打包命令...\n')
      const name2 = `ai外呼生产发布（外网）${answer.version}.zip`
      const filePath = path.resolve(`../docs/项目包/${dayjs().format('YYYY-MM-DD')}`);
      await fs.promises.mkdir(filePath, { recursive: true });
      const output2 = fs.createWriteStream(path.join(filePath, name2));
      await execPromise('yarn build', output2)
      console.log(chalk.green('\n>> 完成【外网v1】打包命令\n'))
    }

    // 外网人工外呼第二套
    if (answerTag.envs.includes('外网v2')) {
      console.log('\n>> 开始执行【外网v2】打包命令...\n')
      const name3 = `ai外呼生产发布（外网第二套）${answer.version}.zip`
      const filePath = path.resolve(`../docs/项目包/${dayjs().format('YYYY-MM-DD')}`);
      await fs.promises.mkdir(filePath, { recursive: true });
      const output3 = fs.createWriteStream(path.join(filePath, name3));
      await execPromise('yarn build.manual-call.2', output3)
      console.log(chalk.green('\n>> 完成【外网v2】打包命令\n'))
    }

    if (answerTag.needTag) {
      console.log(`\n>> 开始打标签${answer.version}\n`)
      execSync(`git tag ${answer.version}`)
      execSync(`git push origin ${answer.version}`)
      console.log(chalk.green(`\n>> 完成打标签${answer.version}\n`))
    }

    console.log(chalk.bgWhite.bold(`\n版本号：${answer.version}`))
    console.log(chalk.bgWhite.bold(`>>>>>> √ 执行完成：${answerTag.envs}${answerTag.needTag ? '，并打标签' : ''} <<<<<<\n`));
  } catch (err) {
    console.log(err)
  } finally {
  }
}
buildAndCompress()
