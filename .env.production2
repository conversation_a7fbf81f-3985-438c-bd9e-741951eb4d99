NODE_ENV='production'
VUE_APP_TITLE='prod'

# 生产环境

# HTTP API
# VITE_API_ORIGIN_HTTP = 'http://prod.bountech.com/aiprod/'
VITE_API_ORIGIN_HTTP='http://zy.bountech.com/aiprod/ptest/'
# HTTPS API
# VITE_API_ORIGIN_HTTPS = 'https://prod.bountech.com/aiprod/'
VITE_API_ORIGIN_HTTPS='https://zy.bountech.com/aiprod/ptest/'

# Janus HTTP 服务器
VITE_JANUS_HTTP='http://**************:8888/janus'
# Janus HTTPS 服务器
VITE_JANUS_HTTPS='https://janus.system.bountech.com:8889/janus'
# Janus WebSocket 服务器
VITE_JANUS_WEBSOCKET='ws://**************:8890/janus'
# Janus WebSockets 服务器
VITE_JANUS_WEBSOCKETS='wss://**************:8891/janus'

# STUN 服务器
VITE_STUN_SERVER='stun:turn.system.bountech.com:3478'
# TURN 服务器 UDP 模式
VITE_TURN_SERVER_UDP='turn:turn.system.bountech.com:3478?transport=udp'
# TURN 服务器 TCP 普通明文模式
VITE_TURN_SERVER_TCP_PLAIN='turn:turn.system.bountech.com:3478?transport=tcp'
# TURN 服务器 TCP TLS加密模式
VITE_TURN_SERVER_TCP_TLS='turns:turn.system.bountech.com:5349?transport=tcp'
# TURN 用户名
VITE_TURN_USERNAME='username'
# TURN 密码
VITE_TURN_PASSWORD='password'

# 人工外呼 WebSocket 服务器地址
VITE_WEBSOCKET_URL='wss://ws.system.bountech.com:8868/ws'

# 监控平台 HTTP API
VITE_MONITOR_HTTP='http://ai.api.bountech.com/aiprod/Monitor'
# 监控平台 HTTPS API
VITE_MONITOR_HTTPS='https://ai.api.bountech.com/aiprod/Monitor'
