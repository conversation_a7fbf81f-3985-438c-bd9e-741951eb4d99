import { defineStore } from 'pinia'
import { MerchantAccountInfo, MerchantInfo, MerchantLineInfo, SmsTemplateItem } from '@/type/merchant'

// 当前选中的商户信息
const currentMerchant: MerchantInfo = {}
// 当前选中的账号信息
const currentAccount: MerchantAccountInfo = {}
// 当前账号下的商户线路列表
const merchantLineList: MerchantLineInfo[] = []
// 当前正在编辑的商户线路信息
const editingMerchantLine: MerchantLineInfo = {}
// 是否只读
const readonly: boolean = true
// 正在编辑的短信模板
const editingSmsTemplate: SmsTemplateItem = {}

export const useMerchantStore = defineStore({
  id: 'merchant',
  state() {
    return {
      currentMerchant,
      currentAccount,
      merchantLineList,
      editingMerchantLine,
      readonly,
      editingSmsTemplate,
    }
  },
  actions: {
    clear() {
      this.$state.currentMerchant = {}
      this.$state.currentAccount = {}
      this.$state.merchantLineList = []
      this.$state.readonly = false
      this.$state.editingSmsTemplate = {}
    },
  },
  persist: [
    {
      storage: sessionStorage,
    }
  ]
})
