import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { smsUrlModel } from '@/api/sms'
import to from 'await-to-js';
import { SmsSendParam } from '@/type/sms'

export const useSmsStore = defineStore('smsStore', () => {

  const domainList = ref<string[]>([]) // 普通白泽短信域名
  
  const getDomainList = async (updateNew: boolean = false) => {
    if (!!updateNew || !domainList.value || domainList.value.length < 1) {
      const [err, res] = await to(smsUrlModel.findNormalDomainList())
      domainList.value = res || []
    }
    return domainList.value || []
  }

  const volcanoDomainList = ref<string[]>([]) // 火山短信域名
  const getVolcanoDomainList = async (updateNew: boolean = false) => {
    if (!!updateNew || !volcanoDomainList.value || volcanoDomainList.value.length < 1) {
      const [err, res] = await to(smsUrlModel.findVolcanoDomainList())
      volcanoDomainList.value = res || []
    }
    return volcanoDomainList.value || []
  }

  const domainAllList = computed(() => {
    return [...new Set([...(domainList.value || []), ...volcanoDomainList.value])]
  })

  // 发送短信弹窗是否显示
  const sendSmsDialogVisible = ref<boolean>(false)
  // 发送短信弹窗表单
  const sendSmsDialogForm = ref<SmsSendParam>({})
  /**
   * 重置发送短信弹窗表单
   */
  const resetSendSmsDialogForm = () => {
    sendSmsDialogForm.value = {}
  }
  /**
   * 显示发送短信弹窗
   */
  const showSendSmsDialog = () => {
    sendSmsDialogVisible.value = true
  }
  /**
   * 隐藏发送短信弹窗
   */
  const hideSendSmsDialog = () => {
    sendSmsDialogVisible.value = false
    resetSendSmsDialogForm()
  }

  // 短信全文长度
  const smsLength = ref<number>(2)
  /**
   * 计算短信全文长度
   * @param sign 短信签名，包含前后两个括号【】，所以长度最小为2
   * @param content 短信内容，普通文本算1，变量算3，短链算14
   */
  const computeSmsTotalLength = (sign: string = '', content: string = '') => {
    // console.log('computeSmsTotalLength', sign, content)
    // 短信签名
    const signLength: number = (sign?.length ?? 0) + 2

    // 短信内容
    let contentLength: number = 0
    // 将短信内容文本按变量的花括号进行分割，得到子字符串列表
    const contentList: string[] = content?.split(/{{|}}/)
    // console.log('contentList', contentList)
    // 无论什么情况，变量或短链都是在数组的奇数索引里
    contentList.forEach((item: string, index: number) => {
      if (index % 2 === 1) {
        // 奇数索引，变量或短链
        if (item.includes('shortOrdinaryId:')) {
          // 普通短链
          contentLength += 14
        } else if (item.includes('shortThousandId:')) {
          // 千人千链
          contentLength += 14
        } else {
          // 变量
          contentLength += 3
        }
      } else {
        // 偶数索引，普通文本
        contentLength += item.length
      }
    })

    // 短信全文长度
    smsLength.value = (signLength + contentLength) || 2
    // console.log('signLength', signLength, 'contentLength', contentLength, 'smsLength.value', smsLength.value)
  }

  return {
    domainList,
    getDomainList,
    volcanoDomainList,
    getVolcanoDomainList,
    domainAllList,

    sendSmsDialogVisible,
    sendSmsDialogForm,
    showSendSmsDialog,
    hideSendSmsDialog,
    resetSendSmsDialogForm,

    smsLength,
    computeSmsTotalLength,
  }
}, {
  persist: [
    {
      paths: [
        'swiperEnabled'
      ],
      storage: localStorage,
    },
  ]
})
