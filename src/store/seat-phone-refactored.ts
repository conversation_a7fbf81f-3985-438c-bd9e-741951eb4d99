import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { SeatManager } from './seat/seat-manager'
import type { 
  SeatLogItem,
  SeatLogTypeEnum,
  SeatLogActionEnum,
  SeatStatusEnum,
  SeatPageEnum,
  SeatCallTypeEnum
} from '@/type/seat'
import type { ClueItem } from '@/type/clue'
import type { IdleAccount } from '@/type/speech-craft'

/**
 * 重构后的坐席电话Store
 * 基于模块化架构，职责清晰，易于维护
 */
export const useSeatPhoneStore = defineStore('seatPhone', () => {
  // ================================ 核心管理器 ================================
  
  const seatManager = new SeatManager((log: SeatLogItem) => {
    // 日志上报回调
    console.log('坐席日志:', log)
    // 这里可以调用实际的日志上报服务
    // await logService.report(log)
  })

  // ================================ 响应式状态 ================================
  
  // 从管理器中获取响应式状态
  const currentMode = computed(() => seatManager.currentMode.value)
  const seatStatus = computed(() => seatManager.seatStatus.value)
  const seatPage = computed(() => seatManager.seatPage.value)
  const seatOnline = computed(() => seatManager.seatOnline.value)
  const isInCall = computed(() => seatManager.isInCall.value)
  const canGoOnline = computed(() => seatManager.canGoOnline.value)
  const canGoOffline = computed(() => seatManager.canGoOffline.value)

  // 当前坐席和账号信息
  const currentSeat = computed(() => seatManager.currentSeat.value)
  const fsAccount = computed(() => seatManager.fsAccount.value)

  // 各模式的详细状态
  const manualDirectState = computed(() => seatManager.getManualDirectState())
  const humanMachineMonitorState = computed(() => seatManager.getHumanMachineMonitorState())
  const humanMachineTakeoverState = computed(() => seatManager.getHumanMachineTakeoverState())

  // ================================ 坐席生命周期管理 ================================

  /**
   * 坐席上线
   */
  const goOnline = async (): Promise<boolean> => {
    return await seatManager.goOnline()
  }

  /**
   * 坐席下线
   */
  const goOffline = async (): Promise<boolean> => {
    return await seatManager.goOffline()
  }

  /**
   * 设置坐席信息
   */
  const setSeatInfo = (seat: any, account: IdleAccount): void => {
    seatManager.setSeatInfo(seat, account)
  }

  /**
   * 设置坐席页面
   */
  const setSeatPage = (page: SeatPageEnum): void => {
    seatManager.setSeatPage(page)
  }

  // ================================ 人工直呼模式 ================================

  /**
   * 发起人工直呼
   */
  const launchManualCall = async (clue: ClueItem): Promise<boolean> => {
    return await seatManager.launchManualCall(clue)
  }

  // ================================ 人机协同监听模式 ================================

  /**
   * 开始人机协同监听
   */
  const startHumanMachineMonitor = async (callRecord: any): Promise<boolean> => {
    return await seatManager.startHumanMachineMonitor(callRecord)
  }

  /**
   * 执行人机协同介入
   */
  const executeHumanMachineIntervene = async (): Promise<boolean> => {
    return await seatManager.executeHumanMachineIntervene()
  }

  /**
   * 退出人机协同监听
   */
  const exitHumanMachineMonitor = async (reason?: string): Promise<boolean> => {
    return await seatManager.exitHumanMachineMonitor(reason)
  }

  // ================================ 人机协同直接接管模式 ================================

  /**
   * 执行人机协同直接接管
   */
  const executeHumanMachineTakeover = async (callRecord: any): Promise<boolean> => {
    return await seatManager.executeHumanMachineTakeover(callRecord)
  }

  // ================================ 通用操作 ================================

  /**
   * 挂断当前通话
   */
  const hangupCurrent = (): void => {
    seatManager.hangupCurrent()
  }

  /**
   * 挂断所有通话
   */
  const hangupAll = (): void => {
    seatManager.hangupAll()
  }

  /**
   * 切换静音状态
   */
  const toggleMute = (muted: boolean): boolean => {
    return seatManager.toggleMute(muted)
  }

  // ================================ 状态查询 ================================

  /**
   * 获取当前模式的详细状态
   */
  const getCurrentModeState = () => {
    switch (currentMode.value) {
      case 'DIRECT':
        return manualDirectState.value
      case 'LISTEN':
        return humanMachineMonitorState.value
      case 'ANSWER':
        return humanMachineTakeoverState.value
      default:
        return null
    }
  }

  /**
   * 获取全局状态快照
   */
  const getGlobalState = () => {
    return seatManager.getGlobalState()
  }

  /**
   * 检查是否可以执行特定操作
   */
  const canExecuteAction = (action: string): boolean => {
    switch (action) {
      case 'launch-call':
        return seatOnline.value && !isInCall.value && currentMode.value !== 'LISTEN'
      case 'start-monitor':
        return seatOnline.value && !isInCall.value
      case 'intervene':
        return currentMode.value === 'LISTEN' && humanMachineMonitorState.value.canExecuteIntervene
      case 'takeover':
        return seatOnline.value && !isInCall.value
      case 'hangup':
        return isInCall.value
      default:
        return false
    }
  }

  // ================================ 调试和监控 ================================

  /**
   * 获取当前服务实例（用于调试）
   */
  const getCurrentService = () => {
    return seatManager.getCurrentService()
  }

  /**
   * 获取所有服务的健康状态
   */
  const getServicesHealth = () => {
    return {
      manualDirect: {
        available: seatManager['manualDirectService'].isAvailable(),
        registered: seatManager['manualDirectService'].registered.value,
        connected: seatManager['manualDirectService'].isConnected.value
      },
      humanMachineMonitor: {
        available: seatManager['humanMachineMonitorService'].isAvailable(),
        registered: seatManager['humanMachineMonitorService'].registered.value,
        connected: seatManager['humanMachineMonitorService'].isConnected.value
      },
      humanMachineTakeover: {
        available: seatManager['humanMachineTakeoverService'].isAvailable(),
        registered: seatManager['humanMachineTakeoverService'].registered.value,
        connected: seatManager['humanMachineTakeoverService'].isConnected.value
      }
    }
  }

  // ================================ 兼容性方法 ================================
  
  /**
   * 为了保持向后兼容，提供一些原有的方法名
   */
  const launchCall = launchManualCall
  const intervene = executeHumanMachineIntervene
  const hangup = hangupCurrent

  // ================================ 返回接口 ================================

  return {
    // 状态
    currentMode,
    seatStatus,
    seatPage,
    seatOnline,
    isInCall,
    canGoOnline,
    canGoOffline,
    currentSeat,
    fsAccount,
    
    // 各模式状态
    manualDirectState,
    humanMachineMonitorState,
    humanMachineTakeoverState,

    // 坐席生命周期
    goOnline,
    goOffline,
    setSeatInfo,
    setSeatPage,

    // 人工直呼
    launchManualCall,

    // 人机协同监听
    startHumanMachineMonitor,
    executeHumanMachineIntervene,
    exitHumanMachineMonitor,

    // 人机协同接管
    executeHumanMachineTakeover,

    // 通用操作
    hangupCurrent,
    hangupAll,
    toggleMute,

    // 状态查询
    getCurrentModeState,
    getGlobalState,
    canExecuteAction,

    // 调试和监控
    getCurrentService,
    getServicesHealth,

    // 兼容性方法
    launchCall,
    intervene,
    hangup,
  }
})
