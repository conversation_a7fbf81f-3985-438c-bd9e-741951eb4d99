import { defineStore } from "pinia";
import { AppRouteRecordRawT } from "@/type/route";
const asyncRouts: AppRouteRecordRawT[] = [];
const constantRoutes: AppRouteRecordRawT[] = [];
const menuRoutes: AppRouteRecordRawT[] = [];
export const useRouteStore = defineStore({
  id: "routes",
  state() {
    return {
      asyncRouts,
      constantRoutes,
      menuRoutes,
      initStatus: false,
    };
  },
  actions: {
    setAsyncRoutes(hasRoles: AppRouteRecordRawT[]) {
      this.asyncRouts = hasRoles;
    },
    updateInitStatus(val: boolean) {
      this.initStatus = val
    }
  },
  persist: [
    {
      paths: ['asyncRouts',],
      storage: sessionStorage,
    },
    // {
    //   paths: ['initStatus',],
    //   storage: sessionStorage,
    // },
  ]
});
