import { defineStore } from 'pinia'
import { ref } from 'vue'
import { CallSetting } from '@/type/seat'
import { callSettingModel } from '@/api/seat'
import to from 'await-to-js'
import { changeAudioUrlOrigin } from '@/utils/utils'

export const useCallSettingStore = defineStore('callSetting', () => {
  // 转人工坐席处理时长（人机协同来电响铃时长），默认值，单位秒
  const DEFAULT_TRANSFER_SECOND = 60
  // 转人工坐席处理时长（人机协同来电响铃时长），总时长，单位秒
  const totalTransferSecond = ref<number>(DEFAULT_TRANSFER_SECOND)
  // 转人工坐席处理时长（人机协同来电响铃时长），剩余时长，单位秒
  const leftTransferSecond = ref<number>(DEFAULT_TRANSFER_SECOND)

  // 转人工弹窗铃声 URL
  const ringtoneUrl = ref<string>('')
  // 转人工弹窗铃声 DOM
  const ringtoneDom = ref<HTMLAudioElement | null>(null)

  // 人工话后处理时长，默认值，单位秒
  const DEFAULT_POST_SECOND = 60
  // 人工话后处理时长，总时长，单位秒
  const totalPostSecond = ref<number>(DEFAULT_POST_SECOND)

  /**
   * 更新通话设置
   */
  const updateCallSetting = async () => {
    // 请求接口
    const [err, res] = <[any, CallSetting]>await to(callSettingModel.findCallSetting())

    // 返回失败结果
    if (err) {
      // 使用默认值
      totalTransferSecond.value = DEFAULT_TRANSFER_SECOND
      leftTransferSecond.value = totalTransferSecond.value
      ringtoneUrl.value = ''
      ringtoneDom.value = null
      totalPostSecond.value = DEFAULT_POST_SECOND
      // console.log('默认值', '转人工坐席处理时长', totalTransferSecond.value, '\n转人工弹窗铃声', ringtoneUrl.value, '\n人工话后处理时长', totalPostSecond.value)
      return
    }

    // 返回成功结果

    // 转人工坐席处理时长
    totalTransferSecond.value = Math.max(0, res.transferManualSeatProcessTime ?? DEFAULT_TRANSFER_SECOND)
    leftTransferSecond.value = totalTransferSecond.value

    // 转人工弹窗铃声
    let audioUrl: string = res.waitingRingtoneUrl || ''
    // 生产环境调整替换音频地址的域名
    ringtoneUrl.value = changeAudioUrlOrigin(audioUrl)
    // 更新到DOM
    ringtoneDom.value = new Audio()
    ringtoneDom.value.loop = true
    ringtoneDom.value.src = ringtoneUrl.value
    ringtoneDom.value.load()

    // 人工话后处理时长
    totalPostSecond.value = Math.max(0, res.postCallProcessTime ?? DEFAULT_POST_SECOND)
    // console.log('转人工坐席处理时长', totalTransferSecond.value, '\n转人工弹窗铃声', ringtoneUrl.value, '\n人工话后处理时长', totalPostSecond.value)
  }

  return {
    totalTransferSecond,
    leftTransferSecond,
    ringtoneDom,
    totalPostSecond,
    updateCallSetting,
  }
})
