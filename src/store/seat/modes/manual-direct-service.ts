import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { BaseCallService } from '../core/base-call-service'
import { seatWorkbenchCallModel } from '@/api/seat'
import type { 
  SipEventMessage, 
  JSEP,
  PluginCreateAnswerParam 
} from '@/type/janus'
import type { 
  SeatCallManualRes,
  SeatCallInfo,
  SeatFsCallParam
} from '@/type/seat'
import type { ClueItem } from '@/type/clue'

/**
 * 人工直呼模式服务
 * 处理坐席主动发起的直接呼叫
 */
export class ManualDirectService extends BaseCallService {
  // 人工直呼特有状态
  public readonly currentClue = ref<ClueItem>({})
  public readonly callRecord = ref<SeatCallManualRes>({})
  public readonly callInfo = ref<SeatCallInfo>({})
  public readonly isDialing = ref(false)
  public readonly dialingDuration = ref(0)

  // 定时器管理
  private dialingTimer: number | null = null

  // 计算属性
  public readonly canLaunchCall = computed(() => 
    this.canMakeCall.value && !this.isDialing.value && !!this.currentClue.value.id
  )

  /**
   * 发起人工直呼
   */
  async launchCall(clue: ClueItem): Promise<boolean> {
    if (!this.canLaunchCall.value) {
      ElMessage.warning('当前无法发起呼叫')
      return false
    }

    try {
      this.currentClue.value = { ...clue }
      this.isDialing.value = true
      this.dialingDuration.value = 0

      this.report('info', '人工直呼', '开始发起呼叫', { clueId: clue.id })

      // 调用呼叫接口
      const result = await this.callDirectAPI(clue)
      
      // 更新通话信息
      this.callRecord.value = result
      this.callInfo.value = {
        callId: result.callId,
        aiCallIp: result.aiCallIp,
        aiCallPort: result.aiCallPort
      }

      // 开始拨号计时
      this.startDialingTimer()

      ElMessage.success('呼叫发起成功')
      return true

    } catch (error) {
      this.isDialing.value = false
      this.report('error', '人工直呼', '发起呼叫失败', error)
      ElMessage.error('发起呼叫失败')
      return false
    }
  }

  /**
   * 调用人工直呼接口
   */
  private async callDirectAPI(clue: ClueItem): Promise<SeatCallManualRes> {
    const params: SeatFsCallParam = {
      clueId: clue.id!,
      phone: clue.phone!,
      // 其他必要参数...
    }

    const response = await seatWorkbenchCallModel.startCallManual(params)
    
    this.report('success', '人工直呼', '接口调用成功', response)
    
    return response
  }

  /**
   * 开始拨号计时
   */
  private startDialingTimer(): void {
    this.stopDialingTimer()
    
    this.dialingTimer = window.setInterval(() => {
      this.dialingDuration.value++
      
      // 拨号超时检查（3分钟）
      if (this.dialingDuration.value >= 180) {
        this.handleDialingTimeout()
      }
    }, 1000)
  }

  /**
   * 停止拨号计时
   */
  private stopDialingTimer(): void {
    if (this.dialingTimer) {
      clearInterval(this.dialingTimer)
      this.dialingTimer = null
    }
  }

  /**
   * 处理拨号超时
   */
  private handleDialingTimeout(): void {
    this.stopDialingTimer()
    this.isDialing.value = false
    
    ElMessage.warning('呼叫超时')
    this.report('warning', '人工直呼', '呼叫超时')
    
    // 自动挂断
    this.hangup()
  }

  /**
   * 处理SIP消息
   */
  protected async handleSipMessage(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    const event = msg?.result?.event ?? ''
    
    console.log('人工直呼收到SIP事件:', event)

    switch (event) {
      case 'registered':
        await this.handleRegistered(msg, jsep)
        break
      case 'unregistered':
        this.handleUnregistered(msg, jsep)
        break
      case 'incomingcall':
        await this.handleIncomingCall(msg, jsep)
        break
      case 'accepted':
        await this.handleCallAccepted(msg, jsep)
        break
      case 'hangup':
        await this.handleCallHangup(msg, jsep)
        break
      case 'progress':
        this.handleCallProgress(msg, jsep)
        break
      default:
        console.warn('未处理的SIP事件:', event)
    }
  }

  /**
   * 处理注册成功
   */
  private async handleRegistered(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    this.registered.value = true
    ElMessage.success('账号注册成功')
    this.report('success', '人工直呼', 'SIP注册成功')
  }

  /**
   * 处理注销
   */
  private handleUnregistered(msg: SipEventMessage, jsep: JSEP): void {
    this.registered.value = false
    ElMessage.warning('账号已注销')
    this.report('warning', '人工直呼', 'SIP已注销')
  }

  /**
   * 处理来电（人工直呼模式下的回呼）
   */
  private async handleIncomingCall(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    console.log('人工直呼收到来电')
    
    // 人工直呼模式下自动接听
    await this.acceptCall(jsep)
  }

  /**
   * 接听电话
   */
  private async acceptCall(jsep: JSEP): Promise<void> {
    const sipHandle = this.getSipHandle()
    if (!sipHandle) {
      throw new Error('SIP句柄不可用')
    }

    return new Promise((resolve, reject) => {
      sipHandle.createAnswer({
        jsep,
        media: { audioSend: true, videoSend: false },
        success: (jsepAnswer: JSEP) => {
          sipHandle.send({
            message: { request: 'accept' },
            jsep: jsepAnswer
          })
          resolve()
        },
        error: (error: string) => {
          console.error('创建应答失败:', error)
          reject(new Error(error))
        }
      } as PluginCreateAnswerParam)
    })
  }

  /**
   * 处理电话接通
   */
  private async handleCallAccepted(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    this.stopDialingTimer()
    this.isDialing.value = false
    this.busy.value = true

    ElMessage.success('电话已接通')
    this.report('success', '人工直呼', '电话接通')

    // 开始通话计时等后续处理...
  }

  /**
   * 处理电话挂断
   */
  private async handleCallHangup(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    this.stopDialingTimer()
    this.isDialing.value = false
    this.busy.value = false

    ElMessage.warning('电话已挂断')
    this.report('warning', '人工直呼', '电话挂断')

    // 清理状态
    this.clearCallState()
  }

  /**
   * 处理通话进展
   */
  private handleCallProgress(msg: SipEventMessage, jsep: JSEP): void {
    console.log('通话进展:', msg?.result)
    // 处理早期媒体等
  }

  /**
   * 清理通话状态
   */
  private clearCallState(): void {
    this.currentClue.value = {}
    this.callRecord.value = {}
    this.callInfo.value = {}
    this.dialingDuration.value = 0
  }

  /**
   * 重写挂断方法
   */
  hangup(): void {
    super.hangup()
    this.stopDialingTimer()
    this.isDialing.value = false
    this.busy.value = false
    this.clearCallState()
  }

  /**
   * 重写清理方法
   */
  protected cleanup(): void {
    super.cleanup()
    this.stopDialingTimer()
    this.clearCallState()
  }

  /**
   * 获取当前状态
   */
  getState() {
    return {
      currentClue: this.currentClue.value,
      callRecord: this.callRecord.value,
      callInfo: this.callInfo.value,
      isDialing: this.isDialing.value,
      dialingDuration: this.dialingDuration.value,
      canLaunchCall: this.canLaunchCall.value,
      // 基础状态
      iceState: this.iceState.value,
      busy: this.busy.value,
      registered: this.registered.value,
      isConnected: this.isConnected.value
    }
  }
}
