import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { BaseCallService } from '../core/base-call-service'
import { seatWorkbenchCallModel } from '@/api/seat'
import type { 
  SipEventMessage, 
  JSEP,
  PluginCreateAnswerParam 
} from '@/type/janus'
import type { 
  SeatCallRecord,
  SeatCallMixInterveneRes,
  SeatCallParam
} from '@/type/seat'

/**
 * 人机协同监听模式服务
 * 处理坐席监听AI通话并可选择介入
 */
export class HumanMachineMonitorService extends BaseCallService {
  // 监听模式特有状态
  public readonly callRecord = ref<SeatCallRecord>({})
  public readonly isMonitoring = ref(false)
  public readonly monitorDuration = ref(0)
  public readonly canIntervene = ref(false)
  public readonly isIntervening = ref(false)
  public readonly interveneButtonEnabled = ref(true)

  // 定时器管理
  private monitorTimer: number | null = null
  private interveneDelayTimer: number | null = null

  // 介入控制
  private interveneRetryCount = 0
  private readonly maxInterveneRetries = 3

  // 计算属性
  public readonly canStartMonitor = computed(() => 
    this.registered.value && !this.isMonitoring.value && !!this.callRecord.value.recordId
  )

  public readonly canExecuteIntervene = computed(() => 
    this.isMonitoring.value && 
    this.canIntervene.value && 
    this.interveneButtonEnabled.value &&
    !this.isIntervening.value &&
    this.isConnected.value
  )

  /**
   * 开始监听
   */
  async startMonitor(callRecord: SeatCallRecord): Promise<boolean> {
    if (!this.canStartMonitor.value) {
      ElMessage.warning('当前无法开始监听')
      return false
    }

    try {
      this.callRecord.value = { ...callRecord }
      this.isMonitoring.value = true
      this.monitorDuration.value = 0
      this.interveneRetryCount = 0

      this.report('info', '人机协同监听', '开始监听', { recordId: callRecord.recordId })

      // 调用监听接口
      await this.callMonitorAPI(callRecord)

      // 开始监听计时
      this.startMonitorTimer()

      // 启动介入延迟定时器（防止立即介入）
      this.startInterveneDelayTimer()

      ElMessage.success('开始监听')
      return true

    } catch (error) {
      this.isMonitoring.value = false
      this.report('error', '人机协同监听', '开始监听失败', error)
      ElMessage.error('开始监听失败')
      return false
    }
  }

  /**
   * 调用监听接口
   */
  private async callMonitorAPI(callRecord: SeatCallRecord): Promise<void> {
    // 这里调用实际的监听接口
    // await seatWorkbenchCallModel.startMonitorHumanMachine({
    //   recordId: callRecord.recordId
    // })
    
    this.report('success', '人机协同监听', '监听接口调用成功')
  }

  /**
   * 执行介入
   */
  async executeIntervene(): Promise<boolean> {
    if (!this.canExecuteIntervene.value) {
      ElMessage.warning('当前无法介入')
      return false
    }

    this.isIntervening.value = true

    try {
      this.report('info', '人机协同介入', '开始介入流程')

      // 发送DTMF介入信号
      await this.sendDTMF('**', 3000) // 3秒延迟

      // 调用介入接口
      const result = await this.callInterveneAPI()

      // 处理介入成功
      await this.handleInterveneSuccess(result)

      ElMessage.success('介入成功')
      return true

    } catch (error) {
      await this.handleInterveneFailure(error as Error)
      return false
    } finally {
      this.isIntervening.value = false
    }
  }

  /**
   * 调用介入接口
   */
  private async callInterveneAPI(): Promise<SeatCallMixInterveneRes> {
    const params: SeatCallParam = {
      recordId: this.callRecord.value.recordId,
      isTransToHuman: false
    }

    // 重试机制
    for (let i = 0; i <= this.maxInterveneRetries; i++) {
      try {
        const result = await seatWorkbenchCallModel.startSpeakHumanMachine(params)
        this.report('success', '人机协同介入', '介入接口调用成功', result)
        return result
      } catch (error) {
        if (i === this.maxInterveneRetries) {
          throw error
        }
        
        console.warn(`介入接口调用失败，重试 ${i + 1}/${this.maxInterveneRetries}:`, error)
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    throw new Error('介入接口调用失败')
  }

  /**
   * 处理介入成功
   */
  private async handleInterveneSuccess(result: SeatCallMixInterveneRes): Promise<void> {
    // 更新状态
    this.busy.value = true
    this.isMonitoring.value = false
    
    // 停止监听计时
    this.stopMonitorTimer()

    this.report('success', '人机协同介入', '介入成功', result)
  }

  /**
   * 处理介入失败
   */
  private async handleInterveneFailure(error: Error): Promise<void> {
    const errorMsg = error.message

    this.report('error', '人机协同介入', '介入失败', { error: errorMsg })

    // 根据错误类型处理
    if (errorMsg.includes('挂断') || errorMsg.includes('已结束')) {
      ElMessage.warning('通话已结束，无法介入')
      await this.exitMonitor('通话已结束')
    } else if (errorMsg.includes('权限')) {
      ElMessage.error('没有介入权限')
    } else if (errorMsg.includes('频率')) {
      ElMessage.warning('操作过于频繁，请稍后重试')
    } else {
      ElMessage.error('介入失败，请重试')
    }
  }

  /**
   * 退出监听
   */
  async exitMonitor(reason: string = '手动退出'): Promise<boolean> {
    try {
      // 调用退出监听接口
      // await seatWorkbenchCallModel.exitMonitorHumanMachine({
      //   recordId: this.callRecord.value.recordId,
      //   exitReason: reason
      // })

      this.stopMonitorTimer()
      this.stopInterveneDelayTimer()
      this.isMonitoring.value = false
      this.canIntervene.value = false

      this.report('info', '人机协同监听', '退出监听', { reason })
      ElMessage.success('已退出监听')
      
      return true
    } catch (error) {
      this.report('error', '人机协同监听', '退出监听失败', error)
      ElMessage.error('退出监听失败')
      return false
    }
  }

  /**
   * 开始监听计时
   */
  private startMonitorTimer(): void {
    this.stopMonitorTimer()
    
    this.monitorTimer = window.setInterval(() => {
      this.monitorDuration.value++
    }, 1000)
  }

  /**
   * 停止监听计时
   */
  private stopMonitorTimer(): void {
    if (this.monitorTimer) {
      clearInterval(this.monitorTimer)
      this.monitorTimer = null
    }
  }

  /**
   * 启动介入延迟定时器
   */
  private startInterveneDelayTimer(): void {
    this.stopInterveneDelayTimer()
    
    this.interveneButtonEnabled.value = false
    this.canIntervene.value = false

    // 3秒后启用介入功能
    this.interveneDelayTimer = window.setTimeout(() => {
      this.interveneButtonEnabled.value = true
      this.canIntervene.value = true
      this.interveneDelayTimer = null
      console.log('介入功能已启用')
    }, 3000)
  }

  /**
   * 停止介入延迟定时器
   */
  private stopInterveneDelayTimer(): void {
    if (this.interveneDelayTimer) {
      clearTimeout(this.interveneDelayTimer)
      this.interveneDelayTimer = null
    }
    this.interveneButtonEnabled.value = true
  }

  /**
   * 处理SIP消息
   */
  protected async handleSipMessage(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    const event = msg?.result?.event ?? ''
    
    console.log('人机协同监听收到SIP事件:', event)

    switch (event) {
      case 'registered':
        await this.handleRegistered(msg, jsep)
        break
      case 'unregistered':
        this.handleUnregistered(msg, jsep)
        break
      case 'incomingcall':
        await this.handleIncomingCall(msg, jsep)
        break
      case 'accepted':
        await this.handleCallAccepted(msg, jsep)
        break
      case 'hangup':
        await this.handleCallHangup(msg, jsep)
        break
      default:
        console.warn('未处理的SIP事件:', event)
    }
  }

  /**
   * 处理注册成功
   */
  private async handleRegistered(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    this.registered.value = true
    ElMessage.success('账号注册成功')
    this.report('success', '人机协同监听', 'SIP注册成功')
  }

  /**
   * 处理注销
   */
  private handleUnregistered(msg: SipEventMessage, jsep: JSEP): void {
    this.registered.value = false
    ElMessage.warning('账号已注销')
    this.report('warning', '人机协同监听', 'SIP已注销')
  }

  /**
   * 处理来电
   */
  private async handleIncomingCall(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    console.log('人机协同监听收到来电')
    
    // 监听模式下自动接听
    await this.acceptCall(jsep)
  }

  /**
   * 接听电话
   */
  private async acceptCall(jsep: JSEP): Promise<void> {
    const sipHandle = this.getSipHandle()
    if (!sipHandle) {
      throw new Error('SIP句柄不可用')
    }

    return new Promise((resolve, reject) => {
      sipHandle.createAnswer({
        jsep,
        media: { audioSend: true, videoSend: false },
        success: (jsepAnswer: JSEP) => {
          sipHandle.send({
            message: { request: 'accept' },
            jsep: jsepAnswer
          })
          resolve()
        },
        error: (error: string) => {
          console.error('创建应答失败:', error)
          reject(new Error(error))
        }
      } as PluginCreateAnswerParam)
    })
  }

  /**
   * 处理电话接通
   */
  private async handleCallAccepted(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    this.busy.value = true
    ElMessage.success('监听已建立')
    this.report('success', '人机协同监听', '监听连接建立')
  }

  /**
   * 处理电话挂断
   */
  private async handleCallHangup(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    this.busy.value = false
    this.isMonitoring.value = false
    this.canIntervene.value = false

    this.stopMonitorTimer()
    this.stopInterveneDelayTimer()

    ElMessage.warning('监听已断开')
    this.report('warning', '人机协同监听', '监听连接断开')
  }

  /**
   * 重写清理方法
   */
  protected cleanup(): void {
    super.cleanup()
    this.stopMonitorTimer()
    this.stopInterveneDelayTimer()
    this.isMonitoring.value = false
    this.canIntervene.value = false
    this.isIntervening.value = false
  }

  /**
   * 获取当前状态
   */
  getState() {
    return {
      callRecord: this.callRecord.value,
      isMonitoring: this.isMonitoring.value,
      monitorDuration: this.monitorDuration.value,
      canIntervene: this.canIntervene.value,
      isIntervening: this.isIntervening.value,
      interveneButtonEnabled: this.interveneButtonEnabled.value,
      canStartMonitor: this.canStartMonitor.value,
      canExecuteIntervene: this.canExecuteIntervene.value,
      // 基础状态
      iceState: this.iceState.value,
      busy: this.busy.value,
      registered: this.registered.value,
      isConnected: this.isConnected.value
    }
  }
}
