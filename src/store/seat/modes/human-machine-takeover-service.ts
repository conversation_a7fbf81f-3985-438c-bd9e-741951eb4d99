import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { BaseCallService } from '../core/base-call-service'
import { seatWorkbenchCallModel } from '@/api/seat'
import type { 
  SipEventMessage, 
  JSEP,
  PluginCreateAnswerParam 
} from '@/type/janus'
import type { 
  SeatCallRecord,
  SeatCallMixInterveneRes,
  SeatCallParam
} from '@/type/seat'

/**
 * 人机协同直接接管模式服务
 * 处理坐席直接接管AI通话（无监听过程）
 */
export class HumanMachineTakeoverService extends BaseCallService {
  // 直接接管特有状态
  public readonly callRecord = ref<SeatCallRecord>({})
  public readonly isTakingOver = ref(false)
  public readonly isWaitingForIce = ref(false)
  public readonly takeoverDuration = ref(0)

  // 定时器管理
  private takeoverTimer: number | null = null
  private iceWaitTimer: number | null = null
  private readonly maxIceWaitTime = 15000 // 15秒ICE等待超时
  private readonly maxDtmfRetries = 3
  private dtmfRetryCount = 0

  // 计算属性
  public readonly canTakeover = computed(() => 
    this.registered.value && !this.isTakingOver.value && !!this.callRecord.value.recordId
  )

  /**
   * 执行直接接管
   */
  async executeTakeover(callRecord: SeatCallRecord): Promise<boolean> {
    if (!this.canTakeover.value) {
      ElMessage.warning('当前无法执行接管')
      return false
    }

    this.callRecord.value = { ...callRecord }
    this.isTakingOver.value = true
    this.dtmfRetryCount = 0

    try {
      this.report('info', '人机协同接管', '开始直接接管流程', { recordId: callRecord.recordId })

      // 等待ICE连接就绪
      await this.waitForIceReady()

      // 发送DTMF接管信号
      await this.sendDTMF('**', 3000) // 3秒延迟确保媒体流就绪

      // 调用接管接口
      const result = await this.callTakeoverAPI()

      // 处理接管成功
      await this.handleTakeoverSuccess(result)

      ElMessage.success('直接接管成功')
      return true

    } catch (error) {
      await this.handleTakeoverFailure(error as Error)
      return false
    } finally {
      this.isTakingOver.value = false
      this.isWaitingForIce.value = false
      this.clearTimers()
    }
  }

  /**
   * 等待ICE连接就绪
   */
  private async waitForIceReady(): Promise<void> {
    // 如果ICE已经连接，直接返回
    if (this.isConnected.value) {
      console.log('ICE已连接，直接进行接管')
      return
    }

    console.log('等待ICE连接，当前状态:', this.iceState.value)
    this.isWaitingForIce.value = true

    return new Promise((resolve, reject) => {
      ElMessage({
        message: '正在建立音频连接，请稍候...',
        type: 'info',
        duration: 2000
      })

      // 监听ICE状态变化
      const stopWatcher = watch(this.iceState, (newState) => {
        console.log('ICE状态变化:', newState)
        
        if (newState === 'connected' || newState === 'completed') {
          // ICE连接成功
          console.log('ICE连接成功')
          stopWatcher()
          this.clearIceWaitTimer()
          this.isWaitingForIce.value = false
          resolve()
        } else if (newState === 'failed' || newState === 'disconnected') {
          // ICE连接失败
          console.error('ICE连接失败，状态:', newState)
          stopWatcher()
          this.clearIceWaitTimer()
          this.isWaitingForIce.value = false
          reject(new Error('音频连接失败'))
        }
      })

      // 设置ICE等待超时
      this.iceWaitTimer = window.setTimeout(() => {
        console.error('等待ICE连接超时')
        stopWatcher()
        this.isWaitingForIce.value = false
        reject(new Error('音频连接超时'))
      }, this.maxIceWaitTime)
    })
  }

  /**
   * 调用接管接口
   */
  private async callTakeoverAPI(): Promise<SeatCallMixInterveneRes> {
    const params: SeatCallParam = {
      recordId: this.callRecord.value.recordId,
      isTransToHuman: true // 直接接管标记
    }

    const result = await seatWorkbenchCallModel.startSpeakHumanMachine(params)
    this.report('success', '人机协同接管', '接管接口调用成功', result)
    
    return result
  }

  /**
   * 处理接管成功
   */
  private async handleTakeoverSuccess(result: SeatCallMixInterveneRes): Promise<void> {
    // 更新状态
    this.busy.value = true
    
    // 开始接管计时
    this.startTakeoverTimer()

    this.report('success', '人机协同接管', '直接接管成功', result)
  }

  /**
   * 处理接管失败
   */
  private async handleTakeoverFailure(error: Error): Promise<void> {
    const errorMsg = error.message

    this.report('error', '人机协同接管', '直接接管失败', { error: errorMsg })

    // 挂断电话
    this.hangup()

    // 显示失败原因弹窗
    this.showTakeoverFailureDialog(errorMsg)
  }

  /**
   * 显示接管失败弹窗
   */
  private showTakeoverFailureDialog(reason: string): void {
    ElMessageBox.alert(
      `接管失败原因：${reason}\n\n请检查网络连接或联系技术支持。`,
      '人机协同接管失败',
      {
        confirmButtonText: '确定',
        type: 'error',
        callback: () => {
          // 可以在这里添加返回到线索页面等逻辑
        }
      }
    )
  }

  /**
   * 开始接管计时
   */
  private startTakeoverTimer(): void {
    this.stopTakeoverTimer()
    
    this.takeoverTimer = window.setInterval(() => {
      this.takeoverDuration.value++
    }, 1000)
  }

  /**
   * 停止接管计时
   */
  private stopTakeoverTimer(): void {
    if (this.takeoverTimer) {
      clearInterval(this.takeoverTimer)
      this.takeoverTimer = null
    }
  }

  /**
   * 清除ICE等待定时器
   */
  private clearIceWaitTimer(): void {
    if (this.iceWaitTimer) {
      clearTimeout(this.iceWaitTimer)
      this.iceWaitTimer = null
    }
  }

  /**
   * 清除所有定时器
   */
  private clearTimers(): void {
    this.stopTakeoverTimer()
    this.clearIceWaitTimer()
  }

  /**
   * 处理SIP消息
   */
  protected async handleSipMessage(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    const event = msg?.result?.event ?? ''
    
    console.log('人机协同接管收到SIP事件:', event)

    switch (event) {
      case 'registered':
        await this.handleRegistered(msg, jsep)
        break
      case 'unregistered':
        this.handleUnregistered(msg, jsep)
        break
      case 'incomingcall':
        await this.handleIncomingCall(msg, jsep)
        break
      case 'accepted':
        await this.handleCallAccepted(msg, jsep)
        break
      case 'hangup':
        await this.handleCallHangup(msg, jsep)
        break
      default:
        console.warn('未处理的SIP事件:', event)
    }
  }

  /**
   * 处理注册成功
   */
  private async handleRegistered(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    this.registered.value = true
    ElMessage.success('账号注册成功')
    this.report('success', '人机协同接管', 'SIP注册成功')
  }

  /**
   * 处理注销
   */
  private handleUnregistered(msg: SipEventMessage, jsep: JSEP): void {
    this.registered.value = false
    ElMessage.warning('账号已注销')
    this.report('warning', '人机协同接管', 'SIP已注销')
  }

  /**
   * 处理来电
   */
  private async handleIncomingCall(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    console.log('人机协同接管收到来电')
    
    // 直接接管模式下自动接听
    await this.acceptCall(jsep)
  }

  /**
   * 接听电话
   */
  private async acceptCall(jsep: JSEP): Promise<void> {
    const sipHandle = this.getSipHandle()
    if (!sipHandle) {
      throw new Error('SIP句柄不可用')
    }

    return new Promise((resolve, reject) => {
      sipHandle.createAnswer({
        jsep,
        media: { audioSend: true, videoSend: false },
        success: (jsepAnswer: JSEP) => {
          sipHandle.send({
            message: { request: 'accept' },
            jsep: jsepAnswer
          })
          resolve()
        },
        error: (error: string) => {
          console.error('创建应答失败:', error)
          reject(new Error(error))
        }
      } as PluginCreateAnswerParam)
    })
  }

  /**
   * 处理电话接通
   */
  private async handleCallAccepted(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    this.busy.value = true
    ElMessage.success('接管连接已建立')
    this.report('success', '人机协同接管', '接管连接建立')
  }

  /**
   * 处理电话挂断
   */
  private async handleCallHangup(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    this.busy.value = false
    this.isTakingOver.value = false
    this.stopTakeoverTimer()

    ElMessage.warning('接管连接已断开')
    this.report('warning', '人机协同接管', '接管连接断开')
  }

  /**
   * 重写挂断方法
   */
  hangup(): void {
    super.hangup()
    this.clearTimers()
    this.isTakingOver.value = false
    this.isWaitingForIce.value = false
  }

  /**
   * 重写清理方法
   */
  protected cleanup(): void {
    super.cleanup()
    this.clearTimers()
    this.isTakingOver.value = false
    this.isWaitingForIce.value = false
  }

  /**
   * 获取当前状态
   */
  getState() {
    return {
      callRecord: this.callRecord.value,
      isTakingOver: this.isTakingOver.value,
      isWaitingForIce: this.isWaitingForIce.value,
      takeoverDuration: this.takeoverDuration.value,
      canTakeover: this.canTakeover.value,
      // 基础状态
      iceState: this.iceState.value,
      busy: this.busy.value,
      registered: this.registered.value,
      isConnected: this.isConnected.value
    }
  }
}
