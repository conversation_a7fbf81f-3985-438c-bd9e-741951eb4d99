import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { ManualDirectService } from './modes/manual-direct-service'
import { HumanMachineMonitorService } from './modes/human-machine-monitor-service'
import { HumanMachineTakeoverService } from './modes/human-machine-takeover-service'
import type { IdleAccount } from '@/type/speech-craft'
import type { 
  SeatStatusEnum, 
  SeatCallTypeEnum,
  SeatPageEnum 
} from '@/type/seat'
import type { ClueItem } from '@/type/clue'

/**
 * 坐席管理器
 * 统一管理三种通话模式，提供统一的API接口
 */
export class SeatManager {
  // 服务实例
  private manualDirectService: ManualDirectService
  private humanMachineMonitorService: HumanMachineMonitorService
  private humanMachineTakeoverService: HumanMachineTakeoverService

  // 全局状态
  public readonly currentMode = ref<SeatCallTypeEnum | null>(null)
  public readonly seatStatus = ref<SeatStatusEnum>('IDLE')
  public readonly seatPage = ref<SeatPageEnum>('CLUE')
  public readonly seatOnline = ref(false)
  public readonly currentSeat = ref<any>({})
  public readonly fsAccount = ref<IdleAccount>({})

  // 计算属性
  public readonly isInCall = computed(() => {
    switch (this.currentMode.value) {
      case 'DIRECT':
        return this.manualDirectService.busy.value
      case 'LISTEN':
        return this.humanMachineMonitorService.busy.value
      case 'ANSWER':
        return this.humanMachineTakeoverService.busy.value
      default:
        return false
    }
  })

  public readonly canGoOnline = computed(() => 
    !this.seatOnline.value && !!this.currentSeat.value.id && !!this.fsAccount.value.account
  )

  public readonly canGoOffline = computed(() => 
    this.seatOnline.value && !this.isInCall.value
  )

  constructor(reportCallback?: (log: any) => void) {
    // 初始化服务实例
    this.manualDirectService = new ManualDirectService('workbench', reportCallback)
    this.humanMachineMonitorService = new HumanMachineMonitorService('workbench', reportCallback)
    this.humanMachineTakeoverService = new HumanMachineTakeoverService('workbench', reportCallback)

    // 监听模式变化
    this.setupModeWatchers()
  }

  /**
   * 设置模式监听器
   */
  private setupModeWatchers(): void {
    // 监听各服务的状态变化，同步到全局状态
    watch([
      this.manualDirectService.busy,
      this.humanMachineMonitorService.busy,
      this.humanMachineTakeoverService.busy
    ], () => {
      this.updateSeatStatus()
    })
  }

  /**
   * 更新坐席状态
   */
  private updateSeatStatus(): void {
    if (this.manualDirectService.busy.value) {
      this.seatStatus.value = 'MANUAL_DIRECT_DIALING'
    } else if (this.humanMachineMonitorService.isMonitoring.value) {
      this.seatStatus.value = 'HUMAN_MACHINE_LISTEN'
    } else if (this.humanMachineTakeoverService.busy.value) {
      this.seatStatus.value = 'HUMAN_MACHINE_DIALING'
    } else if (this.seatOnline.value) {
      this.seatStatus.value = 'IDLE'
    } else {
      this.seatStatus.value = 'OFFLINE'
    }
  }

  // ================================ 坐席生命周期管理 ================================

  /**
   * 坐席上线
   */
  async goOnline(): Promise<boolean> {
    if (!this.canGoOnline.value) {
      ElMessage.warning('当前无法上线')
      return false
    }

    try {
      // 初始化所有服务的Janus连接
      await Promise.all([
        this.manualDirectService.initializeJanus(),
        this.humanMachineMonitorService.initializeJanus(),
        this.humanMachineTakeoverService.initializeJanus()
      ])

      // 注册SIP账号（所有服务使用同一个账号）
      this.manualDirectService.register(this.fsAccount.value)
      this.humanMachineMonitorService.register(this.fsAccount.value)
      this.humanMachineTakeoverService.register(this.fsAccount.value)

      this.seatOnline.value = true
      this.updateSeatStatus()

      ElMessage.success('坐席上线成功')
      return true

    } catch (error) {
      ElMessage.error('坐席上线失败')
      console.error('坐席上线失败:', error)
      return false
    }
  }

  /**
   * 坐席下线
   */
  async goOffline(): Promise<boolean> {
    if (!this.canGoOffline.value) {
      ElMessage.warning('当前无法下线')
      return false
    }

    try {
      // 挂断所有通话
      this.hangupAll()

      // 注销所有服务
      this.manualDirectService.unregister()
      this.humanMachineMonitorService.unregister()
      this.humanMachineTakeoverService.unregister()

      // 销毁所有服务
      this.manualDirectService.destroy()
      this.humanMachineMonitorService.destroy()
      this.humanMachineTakeoverService.destroy()

      this.seatOnline.value = false
      this.currentMode.value = null
      this.updateSeatStatus()

      ElMessage.success('坐席下线成功')
      return true

    } catch (error) {
      ElMessage.error('坐席下线失败')
      console.error('坐席下线失败:', error)
      return false
    }
  }

  // ================================ 人工直呼模式 ================================

  /**
   * 发起人工直呼
   */
  async launchManualCall(clue: ClueItem): Promise<boolean> {
    if (this.currentMode.value && this.currentMode.value !== 'DIRECT') {
      ElMessage.warning('请先结束当前通话')
      return false
    }

    this.currentMode.value = 'DIRECT'
    const success = await this.manualDirectService.launchCall(clue)
    
    if (!success) {
      this.currentMode.value = null
    }

    return success
  }

  /**
   * 获取人工直呼状态
   */
  getManualDirectState() {
    return this.manualDirectService.getState()
  }

  // ================================ 人机协同监听模式 ================================

  /**
   * 开始人机协同监听
   */
  async startHumanMachineMonitor(callRecord: any): Promise<boolean> {
    if (this.currentMode.value && this.currentMode.value !== 'LISTEN') {
      ElMessage.warning('请先结束当前通话')
      return false
    }

    this.currentMode.value = 'LISTEN'
    const success = await this.humanMachineMonitorService.startMonitor(callRecord)
    
    if (!success) {
      this.currentMode.value = null
    }

    return success
  }

  /**
   * 执行人机协同介入
   */
  async executeHumanMachineIntervene(): Promise<boolean> {
    if (this.currentMode.value !== 'LISTEN') {
      ElMessage.warning('当前不在监听模式')
      return false
    }

    return await this.humanMachineMonitorService.executeIntervene()
  }

  /**
   * 退出人机协同监听
   */
  async exitHumanMachineMonitor(reason?: string): Promise<boolean> {
    if (this.currentMode.value !== 'LISTEN') {
      return true
    }

    const success = await this.humanMachineMonitorService.exitMonitor(reason)
    
    if (success) {
      this.currentMode.value = null
    }

    return success
  }

  /**
   * 获取人机协同监听状态
   */
  getHumanMachineMonitorState() {
    return this.humanMachineMonitorService.getState()
  }

  // ================================ 人机协同直接接管模式 ================================

  /**
   * 执行人机协同直接接管
   */
  async executeHumanMachineTakeover(callRecord: any): Promise<boolean> {
    if (this.currentMode.value && this.currentMode.value !== 'ANSWER') {
      ElMessage.warning('请先结束当前通话')
      return false
    }

    this.currentMode.value = 'ANSWER'
    const success = await this.humanMachineTakeoverService.executeTakeover(callRecord)
    
    if (!success) {
      this.currentMode.value = null
    }

    return success
  }

  /**
   * 获取人机协同接管状态
   */
  getHumanMachineTakeoverState() {
    return this.humanMachineTakeoverService.getState()
  }

  // ================================ 通用操作 ================================

  /**
   * 挂断当前通话
   */
  hangupCurrent(): void {
    switch (this.currentMode.value) {
      case 'DIRECT':
        this.manualDirectService.hangup()
        break
      case 'LISTEN':
        this.humanMachineMonitorService.hangup()
        break
      case 'ANSWER':
        this.humanMachineTakeoverService.hangup()
        break
    }
    
    this.currentMode.value = null
  }

  /**
   * 挂断所有通话
   */
  hangupAll(): void {
    this.manualDirectService.hangup()
    this.humanMachineMonitorService.hangup()
    this.humanMachineTakeoverService.hangup()
    this.currentMode.value = null
  }

  /**
   * 切换静音状态
   */
  toggleMute(muted: boolean): boolean {
    switch (this.currentMode.value) {
      case 'DIRECT':
        return this.manualDirectService.toggleMute(muted)
      case 'LISTEN':
        return this.humanMachineMonitorService.toggleMute(muted)
      case 'ANSWER':
        return this.humanMachineTakeoverService.toggleMute(muted)
      default:
        return false
    }
  }

  /**
   * 获取当前服务实例
   */
  getCurrentService() {
    switch (this.currentMode.value) {
      case 'DIRECT':
        return this.manualDirectService
      case 'LISTEN':
        return this.humanMachineMonitorService
      case 'ANSWER':
        return this.humanMachineTakeoverService
      default:
        return null
    }
  }

  /**
   * 获取全局状态
   */
  getGlobalState() {
    return {
      currentMode: this.currentMode.value,
      seatStatus: this.seatStatus.value,
      seatPage: this.seatPage.value,
      seatOnline: this.seatOnline.value,
      isInCall: this.isInCall.value,
      canGoOnline: this.canGoOnline.value,
      canGoOffline: this.canGoOffline.value,
      currentSeat: this.currentSeat.value,
      fsAccount: this.fsAccount.value
    }
  }

  /**
   * 设置坐席信息
   */
  setSeatInfo(seat: any, fsAccount: IdleAccount): void {
    this.currentSeat.value = seat
    this.fsAccount.value = fsAccount
  }

  /**
   * 设置坐席页面
   */
  setSeatPage(page: SeatPageEnum): void {
    this.seatPage.value = page
  }
}
