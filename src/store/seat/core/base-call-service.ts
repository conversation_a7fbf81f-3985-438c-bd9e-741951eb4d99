import { ref, computed, type Ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import janusUtil, { janusList, sipcallList } from '@/utils/janus'
import type { 
  PluginHandle, 
  SipEventMessage, 
  JSEP, 
  PluginDtmfParam,
  ConstructorOptions,
  PluginOptions
} from '@/type/janus'
import type { 
  SeatLogTypeEnum, 
  SeatLogActionEnum,
  SeatStatusEnum 
} from '@/type/seat'
import { iceServers, janusServer } from '@/assets/js/constant'
import type { IdleAccount } from '@/type/speech-craft'

/**
 * 基础通话服务类
 * 提供所有通话模式的公共功能
 */
export class BaseCallService {
  protected instanceId: string
  protected reportCallback?: (log: any) => void
  
  // 基础状态
  public readonly iceState = ref<RTCIceConnectionState>('closed')
  public readonly busy = ref(false)
  public readonly registered = ref(false)
  
  // 计算属性
  public readonly isConnected = computed(() => 
    this.iceState.value === 'connected' || this.iceState.value === 'completed'
  )
  
  public readonly canMakeCall = computed(() => 
    this.registered.value && !this.busy.value && this.isConnected.value
  )

  constructor(instanceId: string = 'workbench', reportCallback?: (log: any) => void) {
    this.instanceId = instanceId
    this.reportCallback = reportCallback
  }

  /**
   * 初始化Janus连接
   */
  async initializeJanus(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (janusList[this.instanceId] && sipcallList[this.instanceId]) {
        resolve(true)
        return
      }

      console.log('初始化Janus服务')
      
      janusUtil.init(this.instanceId, {
        server: janusServer,
        iceServers,
        success: () => {
          this.attachSipPlugin()
            .then(() => resolve(true))
            .catch(reject)
        },
        error: () => {
          ElMessage.error('Janus 连接失败')
          reject(new Error('Janus连接失败'))
        },
      } as ConstructorOptions)
    })
  }

  /**
   * 附加SIP插件
   */
  protected async attachSipPlugin(): Promise<void> {
    return new Promise((resolve, reject) => {
      janusUtil.attach(this.instanceId, {
        plugin: 'janus.plugin.sip',
        opaqueId: 'sip-' + janusUtil.randomString(12),
        success: (handle: PluginHandle) => {
          sipcallList[this.instanceId] = handle
          console.log('SIP插件加载成功')
          resolve()
        },
        error: (error: string) => {
          console.error('SIP插件加载失败:', error)
          reject(new Error(`SIP插件加载失败: ${error}`))
        },
        iceState: (state: RTCIceConnectionState) => {
          this.handleIceStateChange(state)
        },
        slowLink: (uplink: boolean, lost: number, mid: string) => {
          console.warn('网络波动:', { uplink, lost, mid })
          ElMessage.warning('网络波动，请注意网络环境')
        },
        onmessage: async (msg: SipEventMessage, jsepData: JSEP) => {
          await this.handleSipMessage(msg, jsepData)
        },
        onlocaltrack: (track: MediaStreamTrack, on: boolean) => {
          console.log('本地媒体流:', { active: on, kind: track.kind })
        },
        onremotetrack: (track: MediaStreamTrack, mid: string, on: boolean) => {
          this.handleRemoteTrack(track, mid, on)
        },
        oncleanup: () => {
          this.cleanup()
        },
      } as PluginOptions)
    })
  }

  /**
   * 注册SIP账号
   */
  register(fsAccount: IdleAccount): void {
    const sipHandle = sipcallList[this.instanceId]
    if (!sipHandle) {
      throw new Error('SIP插件未初始化')
    }

    sipHandle.send({
      message: {
        request: 'register',
        force_tcp: true,
        proxy: `sip:${fsAccount.address}`,
        username: `sip:${fsAccount.account}@${fsAccount.address}`,
        authuser: fsAccount.account,
        secret: fsAccount.password,
        display_name: fsAccount.account,
      }
    })

    this.report('info', 'SIP注册', '发送注册请求', { account: fsAccount.account })
  }

  /**
   * 注销SIP账号
   */
  unregister(): void {
    const sipHandle = sipcallList[this.instanceId]
    if (!sipHandle) return

    sipHandle.send({
      message: { request: 'unregister' }
    })

    this.report('info', 'SIP注销', '发送注销请求')
  }

  /**
   * 挂断电话
   */
  hangup(): void {
    const sipHandle = sipcallList[this.instanceId]
    if (!sipHandle) return

    sipHandle.send({
      message: {
        request: 'hangup',
        autoaccept_reinvites: false
      }
    })

    this.report('info', '挂断电话', '发送挂断请求')
  }

  /**
   * 拒接电话
   */
  decline(): void {
    const sipHandle = sipcallList[this.instanceId]
    if (!sipHandle) return

    sipHandle.send({
      message: { request: 'decline' }
    })

    this.report('info', '拒接电话', '发送拒接请求')
  }

  /**
   * 切换静音状态
   */
  toggleMute(muted: boolean): boolean {
    const sipHandle = sipcallList[this.instanceId]
    if (!sipHandle?.muteAudio || !sipHandle?.unmuteAudio) {
      ElMessage.warning('无法切换静音状态')
      return false
    }

    if (muted) {
      sipHandle.muteAudio()
    } else {
      sipHandle.unmuteAudio()
    }

    const result = sipHandle.isAudioMuted() ?? true
    ElMessage({
      message: '麦克风已' + (result ? '关闭' : '打开'),
      type: result ? 'warning' : 'success',
      duration: 1000,
    })

    return result
  }

  /**
   * 发送DTMF信号
   */
  async sendDTMF(tones: string, delay: number = 3000): Promise<void> {
    return new Promise((resolve, reject) => {
      const sipHandle = sipcallList[this.instanceId]
      
      if (!sipHandle) {
        reject(new Error('SIP句柄不可用'))
        return
      }

      // 检查前置条件
      if (!this.isConnected.value) {
        reject(new Error('ICE连接未就绪'))
        return
      }

      if (!this.busy.value) {
        reject(new Error('当前不在通话中'))
        return
      }

      console.log(`准备发送DTMF: ${tones}，延迟: ${delay}ms`)

      // 延迟发送确保媒体流就绪
      setTimeout(() => {
        try {
          const timeout = setTimeout(() => {
            reject(new Error('DTMF发送超时'))
          }, 10000)

          sipHandle.dtmf({
            dtmf: { tones },
            success: (data: any) => {
              clearTimeout(timeout)
              console.log('DTMF发送成功:', data)
              this.report('success', 'DTMF发送', `成功发送: ${tones}`, data)
              resolve(data)
            },
            error: (error: string) => {
              clearTimeout(timeout)
              console.error('DTMF发送失败:', error)
              this.report('error', 'DTMF发送', `发送失败: ${error}`)
              reject(new Error(error))
            }
          } as PluginDtmfParam)
        } catch (error) {
          console.error('DTMF发送异常:', error)
          reject(new Error('DTMF发送异常: ' + (error as Error).message))
        }
      }, delay)
    })
  }

  /**
   * 处理ICE状态变化
   */
  protected handleIceStateChange(state: RTCIceConnectionState): void {
    console.log('ICE状态变化:', state)
    this.iceState.value = state
    this.report('info', 'ICE状态', `状态变更为: ${state}`)
  }

  /**
   * 处理SIP消息 - 子类需要实现
   */
  protected async handleSipMessage(msg: SipEventMessage, jsep: JSEP): Promise<void> {
    // 基类提供默认实现，子类可以重写
    console.log('收到SIP消息:', msg?.result?.event)
  }

  /**
   * 处理远程音频轨道
   */
  protected handleRemoteTrack(track: MediaStreamTrack, mid: string, on: boolean): void {
    if (track.kind === 'audio' && !track.muted && on) {
      const audio = new Audio()
      audio.srcObject = new MediaStream([track])
      audio.load()
      audio.play()
        .then(() => console.log('开始播放远程音频'))
        .catch(err => console.error('播放远程音频失败:', err))
    }
  }

  /**
   * 清理资源
   */
  protected cleanup(): void {
    console.log('清理通话资源')
    this.busy.value = false
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    janusUtil.destroy(this.instanceId)
    this.cleanup()
  }

  /**
   * 获取SIP句柄
   */
  getSipHandle(): PluginHandle | undefined {
    return sipcallList[this.instanceId]
  }

  /**
   * 检查服务是否可用
   */
  isAvailable(): boolean {
    return !!(janusList[this.instanceId] && sipcallList[this.instanceId])
  }

  /**
   * 日志上报
   */
  protected report(
    type: 'info' | 'success' | 'warning' | 'error',
    action: string,
    desc: string,
    data?: any
  ): void {
    if (this.reportCallback) {
      this.reportCallback({
        type,
        action,
        desc,
        data,
        timestamp: new Date().toISOString()
      })
    }
  }
}
