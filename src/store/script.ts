import { defineStore } from "pinia";
import { Node, Cell  } from '@antv/x6'
import { CorpusTypeEnum, QueryTypeEnum, ScriptInfo } from '@/type/speech-craft'
import { ElMessage, } from 'element-plus'
import { scriptCanvasModel, scriptCorpusModel, scriptIntentionModel, scriptEventModel, scriptInfoModel, scriptCoreSemanticModel, scriptTableModel } from '@/api/speech-craft'
import { LabelItem, IntentionType, } from '@/type/IntentionType'
import { QaAndPriorCorpusItem, SpeechCraftStatusEnum, SpeechCraftInfoItem, ScriptCorpusItem, EventItem, InfoQueryItem, } from '@/type/speech-craft'
import { EdgeItem } from "@/type/common";
import { AiLabelSemantics } from '@/type/core-semantic'
import { findValueInEnum, pickAttrFromObj } from "@/utils/utils";
import to from 'await-to-js'
import { KnowledgeGroupItem } from '../type/speech-craft';
import { CascaderOption } from 'element-plus'
import { trace } from '@/utils/trace'

const isChecked: boolean = true
const multiContentVersion: boolean = false
const id: number = 0
const scriptStringId: string = '0'
const name: string = ''
const nodeList: Node.Metadata[] = []
const edgeList: EdgeItem[] = []
const intentionTagOptions: LabelItem[] = []
const intentionLevelOptions: IntentionType[] = []
const knowledgeGroupOptions: {
  id: number,
  name: string,
}[] = []
const masterProcessOptions: {
  id: number,
  name: string,
  headCorpusId: number,
}[] = []
const processCorpusOptions: CascaderOption[] = []
const processCorpusBranchOptions: CascaderOption[] = []
const semanticOptions: {
  id: number,
  name: string,
  suffix?: string
}[] = []
const semanticLabelOptions: {
  id: number,
  name: string,
}[] = []
const primaryIndustry = ''
const secondIndustryId = -1
const eventOptions: EventItem[] = []
const infoQueryList: InfoQueryItem[] = []
let currentCanvas: ScriptInfo | undefined
const leave: boolean = false
const x6History: {
  type: string,
  data: any,
}[] = []
const searchData: {
  name: string,
  status?: SpeechCraftStatusEnum
} = {
  name: '',
  status: undefined
}
const qaAndPriorOptions: {

}[] = []
const allDeepCorpusOption: CascaderOption[] = []

export const useScriptStore = defineStore({
  id: "script",
  state() {
    return {
      // 当前话术信息
      id,
      scriptStringId,
      name,
      isChecked,
      multiContentVersion,
      status: SpeechCraftStatusEnum['编辑中'],
      primaryIndustry,
      secondIndustryId,
      // 话术管理，进入详情保存的搜索数据，便于返回keep-alive
      searchData,
      // 画布
      nodeList,
      edgeList,
      currentCanvas,
      leave,
      x6History,
      // 选项
      eventOptions,
      qaAndPriorOptions,
      allDeepCorpusOption,
      infoQueryList,
      intentionLevelOptions,
      masterProcessOptions,
      processCorpusOptions,
      processCorpusBranchOptions,
      intentionTagOptions,
      semanticOptions,
      semanticLabelOptions,
      knowledgeGroupOptions,
    };
  },
  getters: {
    getEventValuesOptions (state) {
      const res:{
        id: number,
        name: string,
      }[] = []
      state.eventOptions?.forEach(item => {
        if (item.eventValuemap && Object.values(item.eventValuemap).length > 0) {
          Object.entries(item.eventValuemap).map((v) => {
            res.push({
              id: +v[0],
              name: `${item.eventName || ''}-${v[1].name || ''}`,
            })
          })
        }
      })
      return res
    },
  },
  actions: {
    /** 用于高级规则，基本问答选项（基本问答+深层沟通+最高优先，兼容原数据） */
    async getQaAndPriorOptions () {
      const res = await to(scriptCorpusModel.findKnowledgeAndPriorByScriptId({
        scriptId: this.id
      }))
      this.qaAndPriorOptions =  (res[1] || [])?.map(item => {
        let str = ''
        if (item.queryType && findValueInEnum(item.queryType, QueryTypeEnum)) {
          str = '-' + findValueInEnum(item.queryType, QueryTypeEnum)
        }
        return {
          name: item.name,
          id: item.id,
          suffix: `${findValueInEnum(item.corpusType, CorpusTypeEnum)}${str}`,
        }
      })
      return this.qaAndPriorOptions
    },
    /** 获取全部深层沟通 */
    async getAllDeepCorpusOptions () {
      const res = await to(scriptCorpusModel.findAllDeepCorpusByScriptId({
        scriptId: this.id
      }));
      const data: CascaderOption[] = [];
      (res[1] || []).map(item => {
        const index = data.findIndex(v => v.value === item.knowledgeGroupId)
        if (index >= 0 && data[index]?.children) {
          data[index]?.children?.push({ label: item.name, value: item.id, })
        } else {
          data.push({
            label: item.groupName,
            value: item.knowledgeGroupId,
            children: [{label: item.name, value: item.id}]
          })
        }
      })
      this.allDeepCorpusOption = data || []
      return this.allDeepCorpusOption
    },
    /** 意向等级/分类 */
    async getIntentionLevelOptions (needUpdate: boolean = false):Promise<IntentionType[]> {
      if (needUpdate || !this.intentionLevelOptions || this.intentionLevelOptions.length < 1) {
         const res = await scriptIntentionModel.findIntentionList(this.id) as IntentionType[]
         this.intentionLevelOptions = (res ? [...res] : []).sort((a,b) => a.intentionType.charCodeAt(0) - b.intentionType.charCodeAt(0)) || []
         return res || []
      } else {
        return this.intentionLevelOptions
      }
    },
    /** 意向标签 */
    async getIntentionTagOptions (needUpdate: boolean = false):Promise<LabelItem[]> {
      if (needUpdate || !this.intentionTagOptions || this.intentionTagOptions.length < 1) {
        const [_ ,res]  = await to(scriptIntentionModel.findLabelList(this.id))
         this.intentionTagOptions = (res || []).map(item => {
           return {
             id: item.id,
             labelName: item.labelName,
             sequence: item.sequence,
             scriptId: item.scriptId
           }
         })
        return this.intentionTagOptions || []
      } else {
        return this.intentionTagOptions || []
      }
    },
    // 主动流程列表，只返回部分数据用于连接到选项选项
    async getProcessOptions(needUpdate: boolean = false) {
      if (needUpdate || !this.masterProcessOptions || this.masterProcessOptions.length < 1) {
        const res = await to(scriptCanvasModel.findAllMasterCanvas({
          scriptId: this.id
        }))
        this.processCorpusBranchOptions = []
        this.processCorpusOptions = []
        this.masterProcessOptions = res[1]?.flatMap(item => {
          if (!!item.headCorpusId) {
            this.processCorpusBranchOptions.push({
              label: item.name!,
              value: item.headCorpusId,
              children: item.canvasCorpusDataMap ? Object.values(item.canvasCorpusDataMap)?.flatMap(v => {
                const branches = v.branches?.map(b => ({label: b.name!, value: b.id!})) || []
                return branches.length > 0 ? [{
                  label: v.name!,
                  value: v.corpusId!,
                  children: branches
                }] : []
              }) || [] : []
            });
            this.processCorpusOptions.push({
              label: item.name!,
              value: item.headCorpusId,
              children: item.canvasCorpusDataMap ? Object.values(item.canvasCorpusDataMap)?.map(v => {
                return {
                  label: v.name!,
                  value: v.corpusId!,
                }
              }) || [] : []
            })
            return  [{
              id: item.id!,
              name: item.name!,
              headCorpusId: item.headCorpusId
            }]
          } else {
            return []
          }
        }) || []
      }
      return this.masterProcessOptions
    },
    // 获取事件触发选项，用于语料编辑-其他设置
    async getEventOptions(needUpdate: boolean = false) {
      if (needUpdate || !this.eventOptions || this.eventOptions.length < 1) {
        this.eventOptions = await scriptEventModel.findEventList({scriptId: this.id}) as EventItem[] || []
        return this.eventOptions || []
      } else {
        return this.eventOptions || []
      }
    },
    // 获取信息查询选项，用于分支设置-查询分支
    async getInfoQueryOptions(needUpdate: boolean = false) {
      if (needUpdate || !this.infoQueryList || this.infoQueryList.length < 1) {
        this.infoQueryList = await scriptInfoModel.findInfoQueryList({id: this.id}) as InfoQueryItem[] || []
        return this.infoQueryList || []
      } else {
        return this.infoQueryList || []
      }
    },
    // async getMasterCanvasOptions() {
    //   const res = await to(scriptCanvasModel.findAllMasterCanvas({
    //     scriptId: this.id
    //   }))
    //   return res[1] || []
    // },
    // async getDeepCanvasOptions() {
    //   const res = await to(scriptCanvasModel.findAllDeepCanvas({
    //     scriptId: this.id
    //   }))
    //   return res[1] || []
    // },
    /** 语义选项 */
    async getSemanticOptions(needUpdate: boolean = false) {
      if (needUpdate || !this.semanticOptions || this.semanticOptions.length < 1) {
        const data = await scriptCoreSemanticModel.findLabelSemanticList({secondIndustryId: this.secondIndustryId}) as AiLabelSemantics[]
        this.semanticOptions = (data || [])?.map(item => {
          return {
            id: item.semanticId!,
            name: item.semantic!,
            suffix: item.semanticLabelList?.join(',') || ''
          }
        }) || []
        return this.semanticOptions || []
      } else {
        return this.semanticOptions || []
      }
    },
    /** 语义标签选项 */
    async getSemanticLabelOptions(needUpdate: boolean = false) {
      if (needUpdate || !this.semanticLabelOptions || this.semanticLabelOptions.length < 1) {
        const data = await scriptCoreSemanticModel.findAllSemanticLabelList({secondIndustryId: this.secondIndustryId}) as {
          id: number,
          semanticLabel: string
        }[]
        this.semanticLabelOptions = (data || [])?.map(item => {
          return {
            id: item.id!,
            name: item.semanticLabel!,
          }
        }) || []
        return this.semanticLabelOptions || []
      } else {
        return this.semanticLabelOptions || []
      }
    },
    
    /** 知识库列表 */
    async getKnowledgeGroupOptions(needUpdate: boolean = false):Promise<{
        id: number,
        name: string
      }[]> {
      if (needUpdate || !this.knowledgeGroupOptions || this.knowledgeGroupOptions.length < 1) {
        const [_, res] = await to(scriptCorpusModel.findKnowledgeGroupList({scriptId: this.id})) as [any, KnowledgeGroupItem[]]
        this.knowledgeGroupOptions = (res || [])?.sort((a,b) => a.priority! - b.priority!)?.map(item => {
          return {
            id: item.id!,
            name: item.groupName!
          }
        }) || []
        return this.knowledgeGroupOptions || []
      } else {
        return this.knowledgeGroupOptions || []
      }
    },

    initScriptOption () {
      this.getIntentionLevelOptions(true)
      this.getIntentionTagOptions(true)
      this.getSemanticOptions(true)
      this.getEventOptions(true)
      this.getInfoQueryOptions(true)
      this.getKnowledgeGroupOptions(true)
    },
    async setSpeechCraft(data: SpeechCraftInfoItem, isChecked: boolean, scriptStringId?: string) {
      if (!isChecked) {
        const [err, _] = await to(scriptTableModel.lockScript({scriptId: data.id as number}))
        this.isChecked = !err ? false : true;
        await trace({ page:'话术编辑-锁定话术', params:{
            scriptId: data.id,
            scriptName: data.scriptName,
            err: err
          }
        })
        if (err) {
          return false
        }
      } else {
        this.isChecked = true;
      }
      this.id = data.id as number;
      this.name = data.scriptName;
      this.primaryIndustry = data.primaryIndustry || '';
      this.secondIndustryId = data.secondaryIndustryId || -1;
      this.status = data.status || SpeechCraftStatusEnum['编辑中'];
      this.scriptStringId = scriptStringId ?? data?.scriptStringId ?? '-1';
      this.multiContentVersion = data.multiContentVersion ?? false;
      return true
    },
    async unLockSpeechCraft(scriptId: number) {
      if (scriptId) {
        const [err] = await to(scriptTableModel.unLockScript({scriptId: scriptId}))
        await trace({ page:'话术编辑-解锁话术', params:{
            scriptId: scriptId,
            err: err,
          }
        })
        this.isChecked = true
      }
    },
    updateScript(nodes: Node.Metadata[], edges: EdgeItem[], curCanvas?: ScriptInfo) {
      this.nodeList = nodes;
      this.edgeList = edges;
      if (curCanvas) {
        const newCanvas = pickAttrFromObj(curCanvas,
          ['preCanvasId', 'nextCanvasId', 'id', 'name', 'scriptId', 'isMasterCanvas', 'headCorpusId', 'weight', 'knowledgeGroupId']
        )
        this.currentCanvas ? Object.assign(this.currentCanvas, newCanvas) : (this.currentCanvas = newCanvas)
      }
    },
    async saveGraph(force: boolean = false, traceHistory: boolean = false, needUpdateMaster: boolean = false) {
      if (!force && (this.isChecked || !this.nodeList || this.nodeList.length < 1 || !this.currentCanvas || !this.currentCanvas.id || !this.x6History || this.x6History.length === 0)) return
      const { id, name, scriptId, headCorpusId } = this.currentCanvas as ScriptInfo
        const params: ScriptInfo = {
          id,
          scriptId,
          headCorpusId,
          canvasCorpusDataMap: {},
          canvasBranchDataMap: {},
        }
        if (!force && (!this.nodeList || this.nodeList.length < 1)) {
          return
        }
        this.nodeList.map(item => {
          const {  x, y, data, } = item
          params.canvasCorpusDataMap[data.corpusId] = {
            corpusId: data.corpusId,
            corX: x as number,
            corY: y as number,
            name: data.name,
            aiIntentionType: data.aiIntentionType,
            content: data.content,
            branches: data.branches,
            corpusType: data.corpusType,
            connectCorpusId: data.connectCorpusId || null,
            connectType: data.connectType,
            listenInOrTakeOver: data.listenInOrTakeOver || false,
            smsTriggerName: data.smsTriggerName,
            eventTriggerValueIds: data.eventTriggerValueIds || null,
          }
        })
        this.edgeList.map(item => {
          const {  source, target,} = item
          const id = item.id?.split('--')[1] as string
          params.canvasBranchDataMap[id] = {
            branchId: parseInt(id),
            // @ts-ignore
            preCorpusId: parseInt(source.cell.split('--')[1]),
            // @ts-ignore
            nextCorpusId: parseInt(target.cell.split('--')[1]),
            name: '',
          }
        })
        await scriptCanvasModel.saveOneMasterCanvas(params) as ScriptInfo
        ElMessage({
          message: `已为您自动保存流程【${name}】画布内容`,
          type: 'success',
        })
        traceHistory && this.traceX6History()
        needUpdateMaster && await this.getProcessOptions(true)
    },
    async traceX6History() {
      if (this.x6History && this.x6History.length > 0) {
        await trace({
          page: `话术编辑-画布操作记录(${this.id}-${this.currentCanvas?.id})`,
          params: this.x6History,
        })
        this.x6History = []
      }
    },
    /** 清空话术的缓存数据，并保留外部话术列表的搜索信息 */
    clearScriptData() {
      const data = this.searchData
      this.$reset();
      Object.assign(this.searchData, data)
    },
  },
  persist: [
    {
      storage: sessionStorage,
    },
    // {
    //   paths: ['initStatus',],
    //   storage: sessionStorage,
    // },
  ]
});
