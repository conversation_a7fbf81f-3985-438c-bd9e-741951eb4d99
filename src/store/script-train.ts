import { defineStore } from 'pinia'
import { ref, watch } from 'vue'
import {
  InfoQueryItem,
  InfoQueryValueItem,
  ScriptTrainStatusEnum,
  ScriptTrainTypeEnum,
} from '@/type/speech-craft'
import { scriptInfoModel, scriptTrainCommonModel } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import dayjs from 'dayjs'
import { ElMessageBox } from 'element-plus'
import { RecordDialogueData } from '@/type/task'
import { logWatchColorfully } from '@/utils/utils'

export const useScriptTrainStore = defineStore('scriptTrain', () => {

  // ---------------------------------------- 通用 开始 ----------------------------------------

  const scriptStore = useScriptStore()

  // 训练状态
  const status = ref<ScriptTrainStatusEnum>(ScriptTrainStatusEnum.IDLE)
  // 训练类型
  const trainType = ref<ScriptTrainTypeEnum>(ScriptTrainTypeEnum.NULL)
  // 需要更新列表
  const needUpdate = ref({
    // 训练历史
    history: false,
    // 对话详情
    dialog: false,
    // 对话详情，定时查询
    dialogRepeat: false,
    // 通话记录
    record: false,
  })
  // 需要清空列表
  const needClear = ref({
    // 训练历史
    history: false,
    // 对话详情
    dialog: false,
    // 通话记录
    record: false,
  })

  watch(status, (val, oldVal) => {
    logWatchColorfully(ScriptTrainStatusEnum[val], oldVal)('status', '训练状态', '#13BF77')
  }, { immediate: true })
  watch(trainType, (val, oldVal) => {
    logWatchColorfully(ScriptTrainTypeEnum[val], oldVal)('trainType', '训练类型', '#E54B17')
  }, { immediate: true })

  /**
   * 重置
   */
  const resetStatus = () => {
    status.value = ScriptTrainStatusEnum.IDLE
    trainType.value = ScriptTrainTypeEnum.NULL
    trainSecond.value = 0
    eventSelectedList.value = []
    leftCheckingTimes.value = maxCheckingTimes.value
    checkSuccessCallback = () => {
    }
    checkFailCallback = () => {
    }
  }

  // ---------------------------------------- 通用 结束 ----------------------------------------

  // ---------------------------------------- 事件 开始 ----------------------------------------

  // 全部事件列表
  const eventAllList = ref<InfoQueryValueItem[]>([])
  // 已选中事件列表
  const eventSelectedList = ref<InfoQueryValueItem[]>([])

  /**
   * 更新全部事件列表
   */
  const updateEventAllList = async () => {
    try {
      // 请求接口
      const res = <InfoQueryItem[]>await scriptInfoModel.findInfoQueryList({
        id: scriptStore.id
      })
      // 解析结果
      let list: InfoQueryValueItem[] = []
      res.forEach((arr: InfoQueryItem) => {
        if (arr?.infoQueryValues?.length) {
          arr.infoQueryValues.forEach((item: InfoQueryValueItem) => {
            list.push(item)
          })
        }
      })
      // 更新列表
      eventAllList.value = list
      eventSelectedList.value = []
    } catch (e) {
    }
  }

  // ---------------------------------------- 事件 结束 ----------------------------------------

  // ---------------------------------------- 检查话术 开始 ----------------------------------------

  // 检查话术定时器ID
  let checkingTimer: number | null = null
  // 检查话术计时，秒
  const checkingSecond = ref<number>(0)
  // 检查间隔时长，秒
  const checkingDuration = ref(5)
  // 最大检查次数，超过次数判定为检查未通过
  const maxCheckingTimes = ref(12)
  // 剩余检查次数
  const leftCheckingTimes = ref(12)
  // 话术检查通过的回调函数
  let checkSuccessCallback = () => {
  }
  // 话术检查未通过的回调函数
  let checkFailCallback = () => {
  }

  /**
   * 重置启检查话术定时器
   */
  const resetCheckingTimer = () => {
    checkingSecond.value = 0
    leftCheckingTimes.value = maxCheckingTimes.value
  }
  /**
   * 处理检查话术定时器
   */
  const handleCheckingTimer = () => {
    // 秒数+1
    checkingSecond.value++
    if (checkingSecond.value % checkingDuration.value === 0) {
      // 隔一段时间检查一次话术
      updateCheckingResult().then(() => {
      }).catch(() => {
      })
    }
  }
  /**
   * 关闭检查话术定时器
   */
  const closeCheckingTimer = () => {
    if (typeof checkingTimer === 'number') {
      clearInterval(checkingTimer)
    }
    checkingTimer = null
  }
  /**
   * 开启检查话术定时器
   */
  const openCheckingTimer = () => {
    closeCheckingTimer()
    resetCheckingTimer()
    checkingTimer = <number><unknown>setInterval(handleCheckingTimer, 1000)
  }

  /**
   * 获取检查话术结果
   */
  const updateCheckingResult = async () => {
    console.log('获取检查话术结果')
    status.value = ScriptTrainStatusEnum.CHECK_SCRIPT

    try {
      // 模拟异常
      // throw new Error('无法获取话术检查结果')
      // 请求接口
      const res = await scriptTrainCommonModel.checkScript({
        scriptStringId: scriptStore.scriptStringId
      })
      // 更新剩余检查次数
      leftCheckingTimes.value--

      if (!res) {
        // 如果接口返回false或其他逻辑否
        if (leftCheckingTimes.value <= 0) {
          console.warn('话术检查未通过，次数已经最大')
          // 关闭定时器
          closeCheckingTimer()
          resetCheckingTimer()
          throw new Error('话术检查未通过，次数已经最大')
        } else {
          console.warn('话术检查未通过，等待下次检查结果')
          console.log('剩余检查话术次数', leftCheckingTimes.value)
        }
      } else {
        // 如果接口返回true或其他逻辑是
        console.log('话术检查通过')
        // 关闭定时器
        closeCheckingTimer()
        resetCheckingTimer()
        // 训练状态改为等待来电
        status.value = ScriptTrainStatusEnum.WAIT_CALL
        // 执行检查通过的回调
        if (checkSuccessCallback) {
          checkSuccessCallback()
        }
      }
    } catch (e: any) {
      console.warn(e?.message ?? '话术检查未通过')
      // 重置状态
      resetStatus()
      // 关闭定时器
      closeCheckingTimer()
      resetCheckingTimer()
      ElMessageBox.alert('话术检查未通过或者话术同步失败', '无法开始话术训练', {
        confirmButtonText: '确定',
      }).then(() => {
      }).catch(() => {
      })
      // 执行检查未通过的回调
      if (checkFailCallback) {
        checkFailCallback()
      }
    }
  }
  /**
   * 启动检查话术
   * 先发布话术，接口返回成功后，循环定时检查话术发布结果
   * @param successCallback 成功回调
   * @param failCallback 失败回调
   */
  const launchCheckScript = async (successCallback: () => any, failCallback: () => any) => {
    checkSuccessCallback = () => {
    }
    checkFailCallback = () => {
    }

    checkSuccessCallback = successCallback
    checkFailCallback = failCallback

    try {
      // 发布话术接口
      await scriptTrainCommonModel.publishScript({
        scriptStringId: scriptStore.scriptStringId,
        scriptLongId: scriptStore.id
      })
      console.log('发布话术', 'scriptStringId', scriptStore.scriptStringId, 'id', scriptStore.id)
      // 更新训练状态
      status.value = ScriptTrainStatusEnum.CHECK_SCRIPT
      // 启动循环检查话术定时器
      leftCheckingTimes.value = maxCheckingTimes.value
      openCheckingTimer()
    } catch (e) {
    }
  }

  // ---------------------------------------- 检查话术 结束 ----------------------------------------

  // ---------------------------------------- 训练时长 开始 ----------------------------------------

  // 训练时长，定时器ID，停止是null，运行是正整数
  let trainTimer: number | null = null
  // 训练时长，秒数
  const trainSecond = ref<number>(0)
  // 训练开始时间，dayjs对象
  let trainBeginTime: dayjs.Dayjs = dayjs()
  // 训练现在时间，dayjs对象
  let trainCurrentTime: dayjs.Dayjs = dayjs()

  /**
   * 处理训练时长定时器
   */
  const handleTrainTimer = () => {
    // 更新当前时间
    trainCurrentTime = dayjs()
    // 计算差值秒数
    trainSecond.value = dayjs(trainCurrentTime.diff(trainBeginTime)).unix()
  }
  /**
   * 关闭训练时长定时器
   */
  const closeTrainTimer = () => {
    if (!trainTimer) {
      return
    }
    clearInterval(trainTimer)
    trainTimer = null
  }
  /**
   * 开启训练时长定时器
   */
  const openTrainTimer = () => {
    // 清除旧定时器
    closeTrainTimer()

    // 更新时间
    const now = dayjs()
    trainBeginTime = now
    trainCurrentTime = now
    trainSecond.value = 0

    // 启动新定时器
    trainTimer = <number><unknown>setInterval(handleTrainTimer, 1000)
  }

  // ---------------------------------------- 训练时长 结束 ----------------------------------------

  // ---------------------------------------- 对话详情 开始 ----------------------------------------

  // 对话详情，接口数据
  const dialogData = ref<RecordDialogueData[]>([])

  // ---------------------------------------- 对话详情 结束 ----------------------------------------

  // ---------------------------------------- 音频播放 结束 ----------------------------------------

  // 对话详情组件，停止正在播放的音频
  const clearAudio = ref(false)

  // ---------------------------------------- 音频播放 结束 ----------------------------------------

  // ---------------------------------------- 消息框 开始 ----------------------------------------

  // AI可打断开关
  const aiInterruptEnabled = ref(true)

  // ---------------------------------------- 消息框 结束 ----------------------------------------

  // ---------------------------------------- 通话记录 开始 ----------------------------------------

  // 通话记录ID
  const recordId = ref<string>('')

  // ---------------------------------------- 通话记录 结束 ----------------------------------------

  return {
    status,
    trainType,
    needUpdate,
    needClear,
    resetStatus,

    eventAllList,
    eventSelectedList,
    updateEventAllList,

    checkingSecond,
    closeCheckingTimer,
    launchCheckScript,

    trainSecond,
    closeTrainTimer,
    openTrainTimer,

    dialogData,

    clearAudio,

    aiInterruptEnabled,

    recordId,
  }
}, {
  persist: [
    {
      paths: [
        'aiInterruptEnabled',
      ],
      storage: localStorage,
    },
  ]
})
