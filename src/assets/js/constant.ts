export const CALLSTATUSLIST = ['全部', '未完成', '已停止', '待执行',]
export const DATETYPELIST = ['昨日', '今日', '自定义']
export const OPERATORLIST = ['中国移动', '中国联通', '中国电信', '其他']
export const ONSTATUSLIST = ['已接通', '未接通']
export const WEEKMAP = new Map([
  ['Sun', '星期日'],
  ['Mon', '星期一'],
  ['Tue', '星期二'],
  ['Wed', '星期三'],
  ['Thu', '星期四'],
  ['Fri', '星期五'],
  ['Sat', '星期六']
])

export const audioRateOption= [
  { name: '0.5x', value: 0.5,},
  { name: '1.0x', value: 1.0,}, 
  { name: '1.25x', value: 1.25,},
  { name: '1.5x', value: 1.5,}, 
]

// export const IMPORT_TEMPLATE = 'http://192.168.23.85/marketfront/file/ai-speech/ai/audio_record/模板.xlsx'
export const tableHeaderStyle = {
  background: '#f7f8fa',
  color: '#626366',
  fontWeight: 600,
}
export const cascaderOptions = [
  {
    value: 1,
    label: 'Asia',
    children: [
      {
        value: 2,
        label: 'China',
        children: [
          { value: 3, label: 'Beijing' },
          { value: 4, label: 'Shanghai' },
          { value: 5, label: 'Hangzhou' },
        ],
      },
      {
        value: 6,
        label: 'Japan',
        children: [
          { value: 7, label: 'Tokyo' },
          { value: 8, label: 'Osaka' },
          { value: 9, label: 'Kyoto' },
        ],
      },
      {
        value: 10,
        label: 'Korea',
        children: [
          { value: 11, label: 'Seoul' },
          { value: 12, label: 'Busan' },
          { value: 13, label: 'Taegu' },
        ],
      },
    ],
  },
  {
    value: 14,
    label: 'Europe',
    children: [
      {
        value: 15,
        label: 'France',
        children: [
          { value: 16, label: 'Paris' },
          { value: 17, label: 'Marseille' },
          { value: 18, label: 'Lyon' },
        ],
      },
      {
        value: 19,
        label: 'UK',
        children: [
          { value: 20, label: 'London' },
          { value: 21, label: 'Birmingham' },
          { value: 22, label: 'Manchester' },
        ],
      },
    ],
  },
  {
    value: 23,
    label: 'North America',
    children: [
      {
        value: 24,
        label: 'US',
        children: [
          { value: 25, label: 'New York' },
          { value: 26, label: 'Los Angeles' },
          { value: 27, label: 'Washington' },
        ],
      },
      {
        value: 28,
        label: 'Canada',
        children: [
          { value: 29, label: 'Toronto' },
          { value: 30, label: 'Montreal' },
          { value: 31, label: 'Ottawa' },
        ],
      },
    ],
  },
]
// ANTD组件的国际化配置
export const antdLocaleConfig = {
  "lang": {
    "locale": "zh_CN",
    "placeholder": "选择日期",
    "rangePlaceholder": ["开始日期", "结束日期"],
    "today": "今天",
    "now": "现在",
    "backToToday": "回到今天",
    "ok": "确定",
    "clear": "清除",
    "month": "月",
    "year": "年",
    "timeSelect": "选择时间",
    "dateSelect": "选择日期",
    "monthSelect": "选择月份",
    "yearSelect": "选择年份",
    "decadeSelect": "选择年代",
    "yearFormat": "YYYY年",
    "dateFormat": "YYYY-MM-DD",
    "dayFormat": "D日",
    "dateTimeFormat": "YYYY-MM-DD HH:mm:ss",
    "monthFormat": "M月",
    "monthBeforeYear": false,
    "previousMonth": "上个月 (PageUp)",
    "nextMonth": "下个月 (PageDown)",
    "previousYear": "去年 (Ctrl + ←)",
    "nextYear": "明年 (Ctrl + →)",
    "previousDecade": "上个年代",
    "nextDecade": "下个年代",
    "previousCentury": "上个世纪",
    "nextCentury": "下个世纪",
    "shortWeekDays": ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
    "shortMonths": [
      "1月",
      "2月",
      "3月",
      "4月",
      "5月",
      "6月",
      "7月",
      "8月",
      "9月",
      "10月",
      "11月",
      "12月"
    ]
  },
  "timePickerLocale": {
    "placeholder": "选择时间",
  },
  "dateFormat": "YYYY-MM-DD",
  "dateTimeFormat": "YYYY-MM-DD HH:mm:ss",
  "weekFormat": "YYYY-wo",
  "monthFormat": "YYYY-MM"
}

// Janus服务器，主要作用是SIP网关，收发SIP信令消息
export const janusServer: string = location.protocol === 'https:'
  ? import.meta.env.VITE_JANUS_WEBSOCKETS
  : import.meta.env.VITE_JANUS_WEBSOCKET

// ICE服务器（NAT穿透），用于Janus服务器和客户端（浏览器用户侧）的音频流传输
export const iceServers: any[] = [
  // STUN
  // {
  //   urls: import.meta.env.VITE_STUN_SERVER
  // },
  // TURN UDP
  // {
  //   urls: import.meta.env.VITE_TURN_SERVER_UDP,
  //   username: import.meta.env.VITE_TURN_USERNAME,
  //   credential: import.meta.env.VITE_TURN_PASSWORD
  // },
  // TURN TCP
  {
    urls: location.protocol === 'https:'
      ? import.meta.env.VITE_TURN_SERVER_TCP_TLS
      : import.meta.env.VITE_TURN_SERVER_TCP_PLAIN,
    username: import.meta.env.VITE_TURN_USERNAME,
    credential: import.meta.env.VITE_TURN_PASSWORD
  },
]

// 运营时间全量
export const timePickerOptions: Record<number, { begin: string; end: string; val: number }> = {
  1: { begin: '00:00', end: '00:30', val: 1 },
  2: { begin: '00:30', end: '01:00', val: 2 },
  3: { begin: '01:00', end: '01:30', val: 3 },
  4: { begin: '01:30', end: '02:00', val: 4 },
  5: { begin: '02:00', end: '02:30', val: 5 },
  6: { begin: '02:30', end: '03:00', val: 6 },
  7: { begin: '03:00', end: '03:30', val: 7 },
  8: { begin: '03:30', end: '04:00', val: 8 },
  9: { begin: '04:00', end: '04:30', val: 9 },
  10: { begin: '04:30', end: '05:00', val: 10 },
  11: { begin: '05:00', end: '05:30', val: 11 },
  12: { begin: '05:30', end: '06:00', val: 12 },
  13: { begin: '06:00', end: '06:30', val: 13 },
  14: { begin: '06:30', end: '07:00', val: 14 },
  15: { begin: '07:00', end: '07:30', val: 15 },
  16: { begin: '07:30', end: '08:00', val: 16 },
  17: { begin: '08:00', end: '08:30', val: 17 },
  18: { begin: '08:30', end: '09:00', val: 18 },
  19: { begin: '09:00', end: '09:30', val: 19 },
  20: { begin: '09:30', end: '10:00', val: 20 },
  21: { begin: '10:00', end: '10:30', val: 21 },
  22: { begin: '10:30', end: '11:00', val: 22 },
  23: { begin: '11:00', end: '11:30', val: 23 },
  24: { begin: '11:30', end: '12:00', val: 24 },
  25: { begin: '12:00', end: '12:30', val: 25 },
  26: { begin: '12:30', end: '13:00', val: 26 },
  27: { begin: '13:00', end: '13:30', val: 27 },
  28: { begin: '13:30', end: '14:00', val: 28 },
  29: { begin: '14:00', end: '14:30', val: 29 },
  30: { begin: '14:30', end: '15:00', val: 30 },
  31: { begin: '15:00', end: '15:30', val: 31 },
  32: { begin: '15:30', end: '16:00', val: 32 },
  33: { begin: '16:00', end: '16:30', val: 33 },
  34: { begin: '16:30', end: '17:00', val: 34 },
  35: { begin: '17:00', end: '17:30', val: 35 },
  36: { begin: '17:30', end: '18:00', val: 36 },
  37: { begin: '18:00', end: '18:30', val: 37 },
  38: { begin: '18:30', end: '19:00', val: 38 },
  39: { begin: '19:00', end: '19:30', val: 39 },
  40: { begin: '19:30', end: '20:00', val: 40 },
  41: { begin: '20:00', end: '20:30', val: 41 },
  42: { begin: '20:30', end: '21:00', val: 42 },
  43: { begin: '21:00', end: '21:30', val: 43 },
  44: { begin: '21:30', end: '22:00', val: 44 },
  45: { begin: '22:00', end: '22:30', val: 45 },
  46: { begin: '22:30', end: '23:00', val: 46 },
  47: { begin: '23:00', end: '23:30', val: 47 },
  48: { begin: '23:30', end: '24:00', val: 48 },
}
