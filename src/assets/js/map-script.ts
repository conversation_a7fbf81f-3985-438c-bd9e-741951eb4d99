// ---------- 话术 审核记录 提交类型 开始 ----------

import { color } from "echarts"

// 枚举
export enum scriptCommitTypeEnum {
  CREATE = '新建',
  EDIT = '修改',
}

// 配置表
export const scriptCommitTypeList: { [propName: string]: any } = {
  create: { name: 'create', text: '新建', val: scriptCommitTypeEnum.CREATE, color: 'green-status' },
  edit: { name: 'edit', text: '修改', val: scriptCommitTypeEnum.EDIT, color: 'blue-status' },
}

// 键值映射表
export const scriptCommitTypeMap = new Map<string, any>([
  [scriptCommitTypeEnum.CREATE, scriptCommitTypeList.create],
  [scriptCommitTypeEnum.EDIT, scriptCommitTypeList.edit],
])

// ---------- 话术 审核记录 提交类型 结束 ----------

// ---------- 话术 审核记录 审核结果 开始 ----------

// 枚举
export enum scriptCheckResEnum {
  PENDING = '审核中',
  PASS = '通过',
  REJECT = '驳回',
}

// 配置表
export const scriptCheckResList: { [propName: string]: any } = {
  pending: { name: 'pending', text: '审核中', val: scriptCheckResEnum.PENDING, color: 'blue-status' },
  pass: { name: 'pass', text: '通过', val: scriptCheckResEnum.PASS, color: 'green-status' },
  reject: { name: 'reject', text: '驳回', val: scriptCheckResEnum.REJECT, color: 'red-status' },
}

// 键值映射表
export const scriptCheckResMap = new Map<string, any>([
  [scriptCheckResEnum.PENDING, scriptCheckResList.pending],
  [scriptCheckResEnum.PASS, scriptCheckResList.pass],
  [scriptCheckResEnum.REJECT, scriptCheckResList.reject],
])

// ---------- 话术 审核记录 审核结果 结束 ----------
