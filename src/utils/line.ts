import router from '@/router'
import {
  MerchantInfo,
  MerchantLineInfo,
  MerchantAccountInfo,
  MerchantLineTypeEnum
} from '@/type/merchant'
import { merchantModel } from '@/api/merchant'
import { supplierModel } from '@/api/supplier'
import { useMerchantStore } from '@/store/merchant'
import { SupplierInfo, supplierLineTypeList } from '@/type/supplier'
import { ElMessage } from 'element-plus'
import { useSupplierStore } from '@/store/supplier'
import { SupplierLineInfoItem } from '@/type/line'
import { GatewayItem } from '@/type/gateway'
import { merchantUserModel } from '@/api/user'
import { findValueInEnum } from '@/utils/utils';

/**
 * 点击编辑进入商户线路编辑状态
 * @param row 当前商户线路信息，需要商户tenantId，商户线路tenantLineNumber
 * @param isEdit 是否是编辑、查看
 */
export const goMerchantLineDetail = async (row: {
  tenantLineNumber?: string,
  tenantNumber?: string,
  account?: string,
  groupId?: string,
}, isEdit: boolean = true) => {
  const merchantStore = useMerchantStore()
  const lineInfo = await merchantModel.getLineByNumber({ tenantLineNumber: row.tenantLineNumber! }) as MerchantLineInfo
  const merchants = await merchantModel.getMerchantList() as MerchantInfo[] || []
  const merchantInfo = merchants?.find(item => item.tenantNo === row.tenantNumber)
  if (!merchantInfo || !lineInfo) return ElMessage.warning('获取商户或商户线路信息失败！')
  const accounts = <MerchantAccountInfo[]>await merchantUserModel.getAccountList({tenantId: merchantInfo?.id!})
  // 为了能够在线路运营线路列表(只有groupId)和运行监控(只有account)都能找到主账号
  const accountInfo = accounts?.find(item => item.account === row.account || item.groupId === row.groupId)
  merchantStore.readonly = !isEdit
  if (accountInfo) {
    merchantStore.editingMerchantLine = lineInfo
    merchantStore.currentMerchant = merchantInfo
    merchantStore.currentAccount = accountInfo
    router.push({ name: 'MerchantLineDetail' })
  } else {
    ElMessage.warning('获取商户主账号信息失败！')
  }
}

/**
 * 点击编辑进入供应线路编辑状态
 * @param row 当前供应线路信息，需要供应商tenantId，商户线路tenantLineNumber
 * @param isEdit 编辑、查看
 */
export const goSupplyLinDetail = async (row: {
  supplyLineNumber?: string,
  supplierNumber?: string
}, isEdit: boolean = true) => {
  const lineInfo = await supplierModel.getLineByNumber({
    supplyLineNumber: row.supplyLineNumber!
  }) as SupplierLineInfoItem
  const suppliers = await supplierModel.getSupplierList({ status: 'ENABLE' }) as SupplierInfo[] || []
  const supplierInfo = suppliers?.find(item => item.supplierNumber === row.supplierNumber)
  // 将当前供应商信息放到本地缓存
  if (lineInfo && supplierInfo) {
    const supplierStore = useSupplierStore()
    supplierStore.editingSupplierLine = lineInfo
    supplierStore.currentSupplier = supplierInfo
    supplierStore.readonly = !isEdit
    // 切换路由到编辑线路子页面
    isEdit && router.push({ name: 'SupplierLineDetail' })
  } else {
    ElMessage({
      type: 'warning',
      message: '获取供应商或供应线路信息失败！'
    })
  }
}

/**
 * 获取商户线路类型的页面展示文本
 * @param lineType 线路类型
 */
export const getMerchantLineTypeText = (lineType: string) => {
  return findValueInEnum(lineType, MerchantLineTypeEnum) || '-'
}

/**
 * 获取供应线路类型的页面展示文本
 * @param lineType 线路类型
 */
export const getSupplierLineTypeText = (lineType: string) => {
  if (supplierLineTypeList.hasOwnProperty(lineType)) {
    return supplierLineTypeList[<keyof typeof supplierLineTypeList>lineType].name
  }
  return '-'
}
/**
 * 获取供应线路类型的页面展示文本
 * @param {GatewayItem[]} list 当前供应线路的网关信息列表
 * @return {string} 网关名称
 */
export const getSupplierLineGatewayText = (list: GatewayItem[]): string => {
  if (!list?.length) {
    return '-'
  }
  const nameList = list.map((item: GatewayItem) => {
    return item.name
  })
  return nameList.join(', ')
}
/**
 * 获取供应线路二级行业的页面展示文本
 * @param list 供应线路列表
 */
export const getSupplyLineSecondIndustriesText = (list: string[]) => {
  if (!list?.length) {
    return '-'
  }
  return list.join(',')
}
