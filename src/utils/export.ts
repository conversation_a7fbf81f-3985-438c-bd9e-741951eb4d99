import * as _XLSX from 'xlsx'
// import * as XLSXStyle from 'xlsx-style-vite';

export const exportExcel = (
  data: any[],
  fileName: string = "json2Excel.xlsx",
  sheetName: string = "sheet1",
) => {
  const jsonWorkSheet = _XLSX.utils.json_to_sheet(data);

  const workBook = {
    SheetNames: [sheetName], // 指定有序 sheet 的 name
    Sheets: {
      [sheetName]: jsonWorkSheet, // 表格数据内容
    },
  };

  _XLSX.writeFile(workBook, fileName); // 向文件系统写出文件
  workBook.SheetNames.length = 0
  workBook.Sheets = {}
  data.length = 0
};

export const exportTable2Excel = (
  table: any,
  fileName: string = "导出文档.xlsx",
  sheetName: string = "sheet1",
) => {
  const ws = _XLSX.utils.table_to_sheet(table, {raw: true});

  const workBook = {
    SheetNames: [sheetName], // 指定有序 sheet 的 name
    Sheets: {
      [sheetName]: ws, // 表格数据内容
    },
  };
  _XLSX.writeFile(workBook, fileName); // 向文件系统写出文件
  workBook.SheetNames.length = 0
  workBook.Sheets = {}
  table.length = 0
};


export const exportExcelMarked = async (
  data: any[],
  fileName: string = "json2Excel.xlsx",
) => {
  // @ts-ignore
  const module2 = await import('xlsx-style-vite')
  const XLSXStyle = module2.default

  const workbook  = _XLSX.utils.book_new();
  const ws  = _XLSX.utils.json_to_sheet(data);
  _XLSX.utils.book_append_sheet(workbook, ws);
  const wbout = XLSXStyle.write(workbook, { bookType: 'xlsx', bookSST: false, type: 'binary' });
  const blob = new Blob([s2ab(wbout)], { type: 'application/octet-stream' });
  const url = URL.createObjectURL(blob);
  // 创建下载链接并模拟点击下载
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  link.click();
  URL.revokeObjectURL(url);
  link.remove()
  workbook.SheetNames.length = 0
  workbook.Sheets = {}
  data.length = 0
};

// 将字符串转为ArrayBuffer对象
const s2ab = (s: string) => {
  const buf = new ArrayBuffer(s.length);
  const view = new Uint8Array(buf);
  for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
  return buf;
}

/**
 * 将Blob对象转换成文件并下载
 * @param {BlobPart} data Blob对象数据
 * @param {string} name 文件名
 * @param {string} type MIME 文件类型
 * {@link https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types 常见 MIME 类型列表}
 */
export const exportFileByBlob = (data: BlobPart, name: string = '文件', type: string = 'bin') => {
  const typeObj: Record<string, string> = {
    'zip': 'application/zip',
    'xls': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'wav': 'audio/wav',
    'bin': 'application/octet-stream'
  }
  const blob = new Blob([data], { type: typeObj[type] });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = name;
  link.style.display = 'none';
  document.body.appendChild(link);
  link.click();
  URL.revokeObjectURL(link.href)
  document.body.removeChild(link);
}
const csvToObject = (csvString: string) => {
  const csvArray = csvString.split(/\r\n|\n/);
  const dataArr = [];
  const headers = csvArray[0].split(",");
  for(var i = 1;i < csvArray.length;i++){
    const data: {[propName: string]: any} = {};
    if (csvArray[i].trim()) {
      const temp = csvArray[i].split(",");
      for(var j = 0;j<temp.length;j++){
        data[headers[j]] = temp[j];
      }
      if (Object.values(data).findIndex(item => !item.trim()) > 0) {
        dataArr.push(data);
      }
    }
  }
   return dataArr;
}
// @ts-ignore
export const readCSV = e => {
  const file = e && e.target!.files[0]
  return new Promise((resolve, reject) => {
    let res = []
    const reader = new FileReader()
    reader.onload = () => {
      res = csvToObject(reader.result as string)
      resolve(res);
    }
    reader.readAsText(file, 'utf-8')
  })
}
/**
 * 使用aoa的方式导出excel或者生产file
 * @param data string[][]，包括表头
 * @param fileName 如有则导出xls，无则生产file（用于单个号码上次）
 * @returns 
 */
export const generateExcelByAoa = (data: string[][], fileName?: string): File | undefined => {
  const ws = _XLSX.utils.aoa_to_sheet(data);
  const wb = _XLSX.utils.book_new();
  _XLSX.utils.book_append_sheet(wb, ws, 'sheet1');
  
  if (fileName) {
    _XLSX.writeFile(wb, fileName); 
  } else {
    const fileData = _XLSX.write(wb, {type: 'array', compression: true});  // compression 能有效减少文件体积，便于上传
    const newFile = new File([fileData], fileName || 'text.xlsx');
    return newFile;
  }
 
}

export const generateExcelBook = (data: {[key: string]: any;}[]): any => {
  const workbook = _XLSX.utils.book_new();
  const sheet = _XLSX.utils.json_to_sheet(data);
  _XLSX.utils.book_append_sheet(workbook, sheet, 'Sheet1');
  return _XLSX.write(workbook, { type: 'array', bookType: 'xls' });
}

// @ts-ignore
export const readXlsx = e => {
  const file = e && e.target!.files[0]
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (ev) => {
      const data = ev.target!.result;
      const workbook = _XLSX.read(data, { type: 'array' }); // 以二进制流方式读取得到整份excel表格对象
      const sheetName = workbook.Sheets[workbook.SheetNames[0]];
      const jsonData = _XLSX.utils.sheet_to_json(sheetName);
      const aoa = _XLSX.utils.sheet_to_json(sheetName, { header: 1 });
      resolve({
        data: jsonData,
        aoa: aoa
      });
    }
    reader.readAsArrayBuffer(file)
  })
}
