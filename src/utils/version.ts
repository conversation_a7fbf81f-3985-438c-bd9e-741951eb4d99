import Confirm from '@/components/message-box'
import { useUserStore } from '@/store/user'
import { useSeatInfoStore } from '@/store/seat/seat-info'
import { storeToRefs } from "pinia"

/**
 * 用于检测版本更新
 * 每1分钟检测一次
 */
export const versionCheck = async (immediate?: boolean) => {
  const basePath = import.meta.env.VITE_PUBLIC_PATH || '/'
  const response = await fetch(`${window.location.protocol}//${window.location.host}${basePath}versionData.json`,{
    headers: {
      'Cache-Control': 'no-cache'
    }
  }).then(res => res.json())
  const userInfo = useUserStore()
  // 坐席若在线直接不做更新提醒
  const seatInfoStore = useSeatInfoStore()
  const { seatOnline } = storeToRefs(seatInfoStore)
  if (userInfo.accountType !== 0 && seatOnline.value) return

  const currentTimeTag  = response.publishTime
  const timeTag = userInfo.versionTimeTag

  if (!timeTag && !!currentTimeTag) {
    userInfo.versionTimeTag = currentTimeTag
  } else if (timeTag !== currentTimeTag && !!currentTimeTag) {
    if (immediate) {
      userInfo.versionTimeTag = currentTimeTag
      window.location.reload()
    } else {
      Confirm({
        text: `新版本已上线，请您及时更新！`,
        type: 'warning',
        title: '立即更新'
      }).then(() => {
        userInfo.versionTimeTag = currentTimeTag
        window.location.reload()
      }).catch(() => {
        userInfo.versionTimeTag = timeTag
      })
    }
  }
}
