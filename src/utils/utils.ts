import { useUserStore } from '@/store/user'
import { TokenInfo } from '@/type/user'
import ClipboardJS from 'clipboard'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import { Ref, toRaw } from 'vue'

export const getToken = () => {
  const tokenObj: TokenInfo = useUserStore().token
  if (tokenObj.expire && tokenObj.expire > new Date().getTime() && tokenObj.token) {
    return tokenObj.token
  } else {
    return ''
  }
}
export const pickAttrFromObj = <T extends Object>(obj: T, arr: (keyof T)[]): T => {
  let res = {} as T
  arr.map((item) => {
    res[item] = obj[item]
  })
  return res
}
export const formatterEmptyData = (row: any, column: any, cellValue: any) => {
  const val = JSON.stringify(cellValue)
  if (!val || val === 'null' || val === '""') {
    return '-'
  }
  return cellValue
}
/**
 * 格式化日期
 */
export const formatTime = (row: any, column: any, cellValue: any) => {
  return cellValue ? (dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') || cellValue) : '-'
}
/**
 * 格式化数字
 * @param data 需要格式化的内容
 * @param num 保留的小数位数，默认不保留，只显示整数
 */
export const formatNumber = (data: number | string | null | undefined, num: number = 0) => {
  if (data) {
    // 内容不为空且不为0
    // 数字转字符串
    data = String(data);
    // left 整数部分 right 小数部分
    let left = data.split('.')[0], right = data.split('.')[1];
    // 小数部分，截断高位，保留低位
    right = right ? (right.length >= num ? '.' + right.slice(0, num) : '.' + right) : '';
    // 整数部分，确定是否需要划分数级（西方数字三位一级）
    const temp = left.split('').reverse().join('').match(/(\d{1,3})/g);
    // 负数添加负号
    // 整数部分超过3位，按数级添加半角逗号
    // 整数小数字符串拼接
    return (Number(data) < 0 ? "-" : "") + (temp ? temp.join(',').split('').reverse().join('') : '0') + right;
  } else if (data === 0 || data === '0') {
    // 内容为0
    return '0';
  } else {
    // 内容不正确、为空、不存在
    // 比如 字母字符串'abc' 空字符串'' null undefined 等
    return '-';
  }
}
export const formatNumber1 = (data: number | string | null | undefined) => {
  if (typeof data === 'number') {
    if (data > Math.pow(10, 4)) {
      return Math.round(data / Math.pow(10, 4) * 10) / 10 + 'w'
    } else {
      return data >= Math.pow(10, 3) ? Math.round(data) : data
    }
  } else {
    return data || '-';
  }
}
/**
 * 格式化数字并以百分比形式返回
 * @param data 需要格式化的内容
 * @param num 保留的小数位数，默认不保留，只显示整数
 * @param keepOrigin 是否保留原始数据，根据产品要求，针对商户端非主账号的部分数据需要特殊处理，防止出现超过0-100%的数值；
 */
export const formatNumberPercent = (data: number | string | null | undefined, num: number = 0, keepOrigin: boolean = true) => {
  const str = formatNumber(data, num)
  try {
    if (str === '-') {
      return str
    } else if (+str > 100) {
      return keepOrigin ? str + '%' : '100%'
    } else if (+str < 0) {
      return keepOrigin ? str + '%' : '0%'
    } else {
      return str + '%'
    }
  } catch (e) {
    return str + '%'
  }
  
}
// 时间转换为分钟
export const time2Minute = (time?: string) => {
  if (time) {
    const arr = time.split(':');
    return +arr[0] * 60 + +arr[1];
  }
  return 0;
}
// 时间转换为分钟
export const minutes2Time = (minutes: number[], reverse: boolean = false) => {
  if (!minutes || minutes.length < 1) return reverse ? {
    start: ['00:00'], end: ['24:00']
  } : {
    start: [], end: []
  }
  const startArr: string[] = []
  const endArr: string[] = []
  let minutes2 = []
  if (reverse) {
    for (let t = 0; t < 1440; t = t + 30) {
      !minutes.includes(t) && minutes2.push(t)
    }
  } else {
    minutes2 = minutes
  }
  minutes2 = minutes2.sort((a, b) => a - b)
  for (let i = 0; i < minutes2.length; i++) {
    const m1 = Math.floor(minutes2[i] / 60) + ''
    const s1 = minutes2[i] % 60 + ''
    startArr.push(`${m1.padStart(2, '0')}:${s1.padStart(2, '0')}`)
    while (minutes2[i + 1] && minutes2[i + 1] - minutes2[i] <= 30 && minutes2[i + 1] < 1440) {
      i++
    }
    const minutesEnd = minutes2[i] + 30
    const m2 = Math.floor(minutesEnd / 60) + ''
    const s2 = minutesEnd % 60 + ''
    endArr.push(`${m2.padStart(2, '0')}:${s2.padStart(2, '0')}`)
  }
  return {
    start: startArr, end: endArr
  };
}

// 将对象转换为列表
type ChartItem = {
  name: string;
  xName?: string
  value1: number;
  value2: number;
  value3?: number;
}
export const obj2List = (data: null | Record<string, Record<string, number>>, keyNames: {
  num: string; rate: string; total?: string
}, type?: 'timeRange') : ChartItem[] => {
  const res:ChartItem[] = []
  if (data && Object.keys(data) && Object.keys(data).length > 0) {
    Object.keys(data).map(item => {
      let str = (data[item][keyNames.rate] || 0) as string | number
      if (typeof str === 'string' && str?.includes('%')) {
        str = str.replace(/%/g, ''); // 替换%
        str = str.replace(/\s+/g, ''); // 替换空字符
        str = +str / 100
      } else {
        str = +str || 0
      }
      res?.push({
        name: item.trim(),
        xName: type === 'timeRange' ? item.split('-')[0] : item.trim(),
        value1: data[item][keyNames.num] || 0,
        value2: +str || 0,
        value3: keyNames.total ? data[item][keyNames.total] || 0 : undefined,
      })
    })
    type === 'timeRange' && res.sort((a, b) => {
      const bb = time2Minute(b.name.split('-')[0])
      const aa = time2Minute(a.name.split('-')[0])
      return aa - bb
    })
  }
  return res
}

/**
 * formatDuration 时间长，不含毫秒，毫秒自动进一
 * @param data
 * @returns
 */
export const formatDuration = (data: number | string | undefined | null) => {
  dayjs.extend(duration);
  if ((data ?? undefined) === undefined) {
    return '-'
  }
  // @ts-ignore
  if (+data === 0) {
    return '0'
  }
  // @ts-ignore
  const range = dayjs.duration(+data, 'seconds')
  if (range.asHours() >= 1) {
    const m = range.asMinutes()
    const hh = Math.floor(m / 60)
    const mm = Math.floor(m % 60)
    return (hh > 0 ? `${Math.round(hh)}时` : '') + (mm > 0 ? `${Math.round(mm)}分` : '')
  } else {
    const h = range.hours()
    const m = range.minutes()
    const ms = range.milliseconds()
    const s = range.seconds() + (ms ? 1 : 0)
    return (h > 0 ? `${Math.round(h)}时` : '') + (m > 0 ? `${Math.round(m)}分` : '') + (s > 0 ? `${Math.ceil(s)}秒` : '')
  }
}

/**
 * formatMsDuration 时间长，包含毫秒
 * @param data
 * @returns
 */
export const formatMsDuration = (data: number | string | undefined | null) => {
  dayjs.extend(duration);
  if ((data ?? undefined) === undefined) {
    return '-'
  }
  // @ts-ignore
  if (+data === 0) {
    return '0'
  }
  // @ts-ignore
  const range = dayjs.duration(+data, 'seconds')
  if (range.asHours() >= 1) {
    const m = range.asMinutes()
    const hh = Math.floor(m / 60)
    const mm = Math.floor(m % 60)
    return (hh > 0 ? `${Math.round(hh)}时` : '') + (mm > 0 ? `${Math.round(mm)}分` : '')
  } else if (range.asMinutes() >= 1) {
    const h = range.hours()
    const m = range.minutes()
    const s = range.seconds()
    return (h > 0 ? `${Math.round(h)}时` : '') + (m > 0 ? `${Math.round(m)}分` : '') + (s > 0 ? `${Math.round(s)}秒` : '')
  } else {
    const ms = range.asMilliseconds()
    const mm = Math.floor(ms / 1000)
    const mm2ss = Math.floor(ms % 1000)
    return (mm > 0 ? `${mm}秒` : '') + (mm2ss > 0 ? `${mm2ss}毫秒` : '')
  }
}

/**
 * 计算两个时间的时间差，无时间前后区分
 * @param p 时间1
 * @param n 时间2
 * @returns 时间差，示例：xx时xx分xx秒xx毫秒
 */
export const formatDiffDuration = (p?: string, n?: string) => {
  if (!p || !n) return ''
  try {
    const dur = dayjs(p).diff(dayjs(n), 's')
    return formatDuration(Math.abs(dur))
  } catch (err) {
    return ''
  }
}

export const filterEmptyParams = <T extends Object>(data: T): T => {
  return Object.keys(data).reduce((acc, key) => {
    if (!isEmpty(data[<keyof T>key])) {
      acc[key as keyof T] = data[key as keyof T];
    }
    return acc;
  }, {} as T)
}

export const filterPhone = (phone: string, begin: number = 6, end: number = 4) => {
  if (!phone) {
    return ''
  }
  if (phone.length < 7) {
    return phone
  }
  return phone.slice(0, begin) + '****' + phone.slice(-end)
}

export const isEmpty = (v: any) => {
  switch (typeof v) {
    case 'undefined':
      return true
    case 'string':
      if (v.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g, '').length == 0) return true
      break
    // case 'boolean':
    //   if (!v) return true
    //   break
    case 'number':
      if (isNaN(v)) return true
      break
    case 'object':
      if (null === v || v?.length === 0 || Object.keys(v)?.length === 0) return true
      for (var i in v) {
        return false
      }
      return true
  }
  return false
}

/**
 * 复制文本到剪贴板
 * @param val 需要复制的字符串
 */
export const copyText = async (val: string = '') => {
  // 添加剪贴板临时DOM
  const dom = document.createElement('div')
  dom.style.position = 'fixed'
  dom.style.top = '-99999px'
  dom.style.left = '-99999px'
  dom.style.width = '0'
  dom.style.height = '0'
  dom.className = 'clipboard'
  document.body.appendChild(dom)

  // 实例化剪贴板对象
  let clipboard: ClipboardJS | null = new ClipboardJS('.clipboard', {
    text: function () {
      return val
    }
  })

  // 复制成功回调
  clipboard?.on('success', function () {
    ElMessage({
      type: 'success',
      message: '复制成功',
      duration: 1000,
    })
    // 销毁剪贴板实例化对象
    clipboard?.destroy()
    clipboard = null
  })

  // 复制失败回调
  clipboard?.on('error', function () {
    ElMessage({
      type: 'error',
      message: '复制失败',
      duration: 1000,
    })
    // 销毁剪贴板实例化对象
    clipboard?.destroy()
    clipboard = null
  })

  // 模拟点击，触发剪贴板复制动作
  dom.click()

  // 移除剪贴板临时DOM
  document.body.removeChild(dom)
}

/**
 * 数组去重，基本数据类型
 * @param arr 需要去重的数组
 */
export const deduplicateBaseArray = <T>(arr: T[]): T[] => {
  // 数组不存在或者为空
  if (!arr?.length) {
    return []
  }
  // 数组只有一个元素
  if (arr.length === 1) {
    return arr
  }
  // 数组有多个元素
  return Array.from(new Set(arr))
}

/**
 * 数组去重，对象类型
 * @param arr 需要去重的数组
 * @param keyName {string} 对象内的某个属性名
 */
export const deduplicateObjectArray = <T>(arr: T[], keyName: string): T[] => {
  // 数组不存在或者为空
  if (!arr?.length) {
    return []
  }
  // 数组只有一个元素
  if (arr.length === 1) {
    return arr
  }
  // 数组有多个元素
  const res = new Map()
  return arr.filter((item: T) => !res.has(item[<keyof T>keyName]) && res.set(item[<keyof T>keyName], true))
}

/**
 * 获取列表第一项
 * @param list 需要处理的列表
 * @param defaultVal 如果拿不到，返回一个默认值
 * @return {T|null} 列表第一项或者null
 */
export const getListFirstItem = <T>(list: T[], defaultVal: unknown = null): unknown => {
  // 如果列表不存在或者列表不是数组
  if (!list || !Array.isArray(list)) {
    return defaultVal
  }
  // 如果列表为空
  if (!list.length) {
    return defaultVal
  }
  return list[0]
}

/**
 * 节流锁
 * 适用于ref对象，传值不用带.value
 */
export class Throttle {
  flag: Ref
  delay: number
  timer: number | null

  constructor(flag: Ref, delay: number = 200) {
    this.flag = flag
    this.delay = delay
    this.timer = null
  }

  // 获取锁状态
  check() {
    // this.flag.value ? console.log('节流锁已上锁') : console.log('节流锁未上锁')
    return this.flag.value
  }

  // 上锁
  lock() {
    // console.log('节流锁上锁')
    this.flag.value = true
  }

  // 解锁
  unlock(delay: number = this.delay) {
    // 如果有定时器，则先关掉原来的，重新开一个
    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = null
    }

    // 启动定时器
    this.timer = <number><unknown>setTimeout(() => {
      // console.log('节流锁解锁')
      this.flag.value = false
      this.timer = null
    }, delay)
  }
}

/**
 * 更新当前页码的列表
 * 根据当前页码和每页大小等分页数据，从全部数据的总列表中截取出来，形成子列表
 * @param allList 全部数据的总列表
 * @param pageNum 当前页码
 * @param pageSize 每页大小（每页展示的项目数量）
 */
export const updateCurrentPageList = <T>(allList: T[] = [], pageNum: number = 1, pageSize: number = 5) => {
  // 源列表不是数组或者空数据
  if (!allList?.length) {
    return []
  }

  // 源列表有数据

  // 如果当前页码比总页码数大，则当前页码降到最后一页，防止页码溢出
  const maxPageNum = Math.ceil((allList?.length ?? 0) / pageSize)
  pageNum = Math.min(pageNum, maxPageNum)

  // 左闭右开区间
  // 1. 起始索引（含）
  const startIndex = (pageNum - 1) * pageSize
  // 2. 终止索引（不含）
  const endIndex = pageNum * pageSize

  // 截取对应区间的列表数据
  return allList.slice(startIndex, endIndex) || []
}

// 搜索模式
export enum searchModeEnum {
  PRECISE = 'precise',
  FUZZY = 'fuzzy',
}

/**
 * 在对象列表里搜索
 * 支持精准搜索和模糊搜索
 * @param list 搜索范围，需要搜索的字符串或者对象列表
 * @param propName 每个对象需要匹配哪个属性
 * @param word 搜索文本内容
 * @param mode 搜索模式，精准匹配还是模糊匹配
 */
export const searchInList = function (list: any[], propName: string, word: string, mode: searchModeEnum) {
  // 数据合法校验
  if (!list?.length) {
    return []
  }
  if (!propName) {
    return []
  }
  if (!word) {
    return list
  }
  // mode不正确就默认为模糊匹配
  if (mode !== searchModeEnum.FUZZY && mode !== searchModeEnum.PRECISE) {
    mode = searchModeEnum.FUZZY
  }

  // 从列表里筛选包含搜索文本的项目
  return list.filter((item) => {
    if (mode === searchModeEnum.FUZZY) {
      // 模糊匹配
      return item[propName].includes(word)
    } else {
      // 精准匹配
      return item[propName] === word
    }
  })
}

/**
 * 使用dayjs格式化日期时间
 * 默认格式为YYYY-MM-DD HH:mm:ss
 */
export const formatDate = (datetime: any, datetimeFormat: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  return dayjs(datetime).format(datetimeFormat) ?? ''
}
/**
 * 列表排序
 * @params tableData: 全部数据列表
 * @params tableData: 排序的列, 默认为 ''
 * @params tableData: 排序的方式， 'descending' | 'ascending' | ''
 */
export const handleTableSort = <T>(tableData: T[], orderCol: string, orderType: string) => {
  if (!orderCol || !orderType) {
    return tableData||[]
  }
  return (tableData||[])?.sort((a, b) => {
    // 保证空数据在最后
    if (isEmpty(a[orderCol as keyof T])) return 1
    if (isEmpty(b[orderCol as keyof T])) return -1
    if (orderCol.includes('Time')) {
      if (orderType === 'descending') {
        // @ts-ignore
        return dayjs(b[orderCol]).isBefore(dayjs(a[orderCol])) ? -1 : 1
      } else {
        // @ts-ignore
        return dayjs(b[orderCol]).isBefore(dayjs(a[orderCol])) ? 1 : -1
      }
    } else if (orderCol.includes('Ip') || orderCol.includes('ip') || orderCol.includes('IP')) {
        // @ts-ignore
        const ip1 = a[orderCol]?.split('.').map(item => (+item));
        // @ts-ignore
        const ip2 = b[orderCol]?.split('.').map(item => (+item));
        for (let i = 0; i < 4; i++) {
          if (ip1[i] === ip2[i]) {
            continue;
          }
          return orderType === 'descend' ? ip2[i] - ip1[i] : ip1[i] - ip2[i];
        }
        return 0
    } else if (typeof a[orderCol as keyof typeof a] === 'string') {
      if ((a[orderCol as keyof typeof a] + '').includes('%') || (b[orderCol as keyof typeof b] + '').includes('%')) {
        const nextVal = (a[orderCol as keyof typeof a] + '')?.split('%')[0] ?? 0
        const preVal = (b[orderCol as keyof typeof b] + '')?.split('%')[0] ?? 0
        return orderType === 'descending' ? (+preVal) - (+nextVal) : (+nextVal) - (+preVal)
      } else if ((a[orderCol as keyof typeof a] + '').includes('/') || (b[orderCol as keyof typeof b] + '').includes('/')) {
        const nextVal = (a[orderCol as keyof typeof a] + '')?.split('/')[0] ?? 0
        const preVal = (b[orderCol as keyof typeof b] + '')?.split('/')[0] ?? 0
        return orderType === 'descending' ? (+preVal) - (+nextVal) : (+nextVal) - (+preVal)
      } else if ((a[orderCol as keyof typeof a] + '').includes('-') && (b[orderCol as keyof typeof b] + '').includes('-')) {
        // 用于菜单编号
        return orderType === 'descending' ?  (a[orderCol as keyof typeof a] + '').localeCompare(b[orderCol as keyof typeof b] + '') : (b[orderCol as keyof typeof b] + '').localeCompare(a[orderCol as keyof typeof a] + '')
      } else {
        const nextVal = (a[orderCol as keyof typeof a] as string) || 0
        const preVal = (b[orderCol as keyof typeof b] as string) || 0
        return orderType === 'descending' ? (+preVal) - (+nextVal) : (+nextVal) - (+preVal)
      }
    } else {
      const nextVal = (a[orderCol as keyof typeof a] as string) || 0
      const preVal = (b[orderCol as keyof typeof b] as string) || 0
      return orderType === 'descending' ? (+preVal) - (+nextVal) : (+nextVal) - (+preVal)
    }
  }) || []
}

/**
 * 将当天的分钟数转换成24小时制的时分字符串HH:mm
 * @param {number} minute 分钟数，当天0时0分就是0，8时30分就是8*60+30=510
 * @return {string} HH:mm格式的字符串
 */
export const min2str = (minute: number = 0): string => {
  // 24点和0点做区别
  if (minute === 24 * 60) {
    return '24:00'
  }
  const start = dayjs('00:00', 'HH:mm')
  const time = start.add(minute, 'minute')
  return time.format('HH:mm') ?? '00:00'
}

/**
 * 将当天的24小时制的时分字符串HH:mm转换成分钟数
 * @param {string} str HH:mm格式的字符串
 * @return {number} 分钟数，当天0时0分就是0，8时30分就是8*60+30=510
 */
export const str2min = (str: string): number => {
  const start = dayjs('00:00', 'HH:mm')
  const time = dayjs(str, 'HH:mm')
  return time.diff(start, 'minute') ?? 0
}

/**
 * 时间段列表 将接口数据转换成组件数据
 * @param {number[]} disableTimeSlots 接口数据 从0点开始的分钟数列表
 * @return {{startWorkTimeList: string[], endWorkTimeList: string[]}} 组件数据 两个字符串列表 表示允许或者禁止时间段的开始位置
 * @example 举例 00:00-01:00 这段时间禁止，其余时间允许 disableTimeSlots [0, 30, 60] startWorkTimeList ['01:00'] endWorkTimeList ['24:00']
 */
export const convertTimeSlotApiToComponent = (disableTimeSlots: number[]): {
  startWorkTimeList: string[],
  endWorkTimeList: string[]
} => {
  // console.log('disableTimeSlots', disableTimeSlots)

  const result: {
    startWorkTimeList: string[],
    endWorkTimeList: string[]
  } = {
    startWorkTimeList: [],
    endWorkTimeList: [],
  }

  // console.log('disableTimeSlots.length', disableTimeSlots.length)
  // 全部允许
  if (!disableTimeSlots.length) {
    result.startWorkTimeList = ['00:00']
    result.endWorkTimeList = ['24:00']
    return result
  }

  // 接口数据 禁止时间段的分钟数列表 升序排序
  // const forbiddenList = disableTimeSlots.sort((a, b) => a - b)
  // console.log('forbiddenList', forbiddenList)

  // 完整的分钟数列表
  let minuteList: number[] = []
  for (let i = 0; i <= 24 * 60 - 30; i = i + 30) {
    minuteList.push(i)
  }
  // console.log('minuteList', minuteList)

  const minuteSet = new Set(minuteList)
  disableTimeSlots?.forEach((item) => {
    minuteSet.delete(item)
  })
  // 允许时间段的分钟数列表 升序排序
  const allowedList = Array.from(minuteSet).sort((a, b) => a - b)
  // console.log('allowedList', allowedList)

  // 允许时间段的区间两边端点
  let endpointList = []
  if (allowedList.length) {
    // 第一个元素必定是允许时间段的开始位置
    endpointList.push(allowedList[0])
    // 中间允许时间段的开始位置
    for (let i = 0; i < allowedList.length - 1; i++) {
      if (allowedList[i] + 30 !== allowedList[i + 1]) {
        endpointList.push(allowedList[i] + 30)
        endpointList.push(allowedList[i + 1])
      }
    }
    // 最后一个元素必定是禁止时间段的开始位置
    endpointList.push((allowedList.at(-1) ?? 24 * 60 - 30)! + 30)
  }
  // console.log('endpointList', endpointList)

  // 将分钟数转换成时分字符串
  const strList = endpointList.map((minute: number) => {
    return min2str(minute)
  })
  // console.log('strList', strList)

  // 按顺序依次放进两个组件数据列表中
  for (let i = 0; i < strList.length; i = i + 2) {
    result.startWorkTimeList.push(strList[i])
    result.endWorkTimeList.push(strList[i + 1])
  }

  // console.log('result.startWorkTimeList', result.startWorkTimeList, 'result.endWorkTimeList', result.endWorkTimeList)
  return result
}

/**
 * 时间段列表 将组件数据转换成展示文本
 * @param {string[]} sList 组件数据 字符串列表 表示允许时间段的开始位置
 * @param {string[]} eList 组件数据 字符串列表 表示禁止时间段的开始位置
 * @return {string[]} 展示给用户看的字符串列表
 */
export const convertTimeSlotComponentToDisplay = (sList: string[], eList: string[]): string[] => {
  // 一些不符合的情况，直接返回空数组
  if (!sList || !Array.isArray(sList) || sList.length < 1 || sList.length !== eList.length) {
    return []
  }
  // 轮流抽取两个列表的字符串，拼接成可读的时间段格式，例如 08:30-17:30
  return sList.map((item: string, index: number): string => {
    return item + '-' + eList[index]
  })
}

/**
 * 时间段列表 将组件数据转换成接口数据
 * @param {string[]} startWorkTimeList 组件数据 字符串列表 表示允许时间段的开始位置
 * @param {string[]} endWorkTimeList 组件数据 字符串列表 表示禁止时间段的开始位置
 * @return {number[]} 接口数据 从0点开始的分钟数列表
 */
export const convertTimeSlotComponentToApi = (startWorkTimeList: string[], endWorkTimeList: string[]): number[] => {
  // console.log('startWorkTimeList', startWorkTimeList, 'endWorkTimeList', endWorkTimeList)
  // 结果列表
  let result: number[] = []
  // 将禁止时间段内按半小时拆分并放入结果列表
  const append = (start: number, end: number) => {
    for (let i = start; i <= end; i += 30) {
      result.push(i)
    }
  }
  // 列表字符串转分钟数并做升序排序
  const convert = (list: string[]): number[] => {
    return list
      .map((item: string) => str2min(item))
      .sort((a: number, b: number) => a - b)
  }

  // 全部禁止
  if (!startWorkTimeList.length || !startWorkTimeList.length) {
    append(0, 24 * 60)
    // console.log('全部禁止', result)
    return result
  }

  // 组件数据两个列表转换成分钟数并做升序排序
  const sList: number[] = convert(startWorkTimeList)
  const eList: number[] = convert(endWorkTimeList)

  if (!sList.length || !eList.length) {
    return []
  }
  // console.log('sList', sList, 'eList', eList)

  // 全部允许
  if (sList.at(0) === 0 && eList.at(0) === 24 * 60) {
    // console.log('全部允许', [])
    return []
  }

  // 轮流从两个列表里读取分钟数
  // 确定每个禁止时间段

  // 0点右边是禁止时间段
  if (sList[0] !== 0 && eList[0] !== 0) {
    append(0, sList[0] - 30)
    // console.log('0点是禁止时间', result)
  }
  // 中间禁止时间段
  for (let i = 0; i < eList.length - 1; i++) {
    // console.log('eList[' + i + ']', eList[i], 'sList[' + (i + 1) + ']', sList[i + 1])
    append(eList[i], sList[i + 1] - 30)
  }
  // console.log('中间禁止时间段', result)
  // 24点左边是禁止时间段
  if (eList.at(-1) !== 24 * 60) {
    append(eList.at(-1)!, 24 * 60)
    // console.log('24点左边是禁止时间段', result)
  }

  // console.log('result', result)
  return result
}

/**
 * params: enum
 * 将enum转换为select选项的list
 */
export const enum2Options = <T>(e: Record<string, T>): { name: string, value: T }[] => {
  const arr = Object.keys(e) || []
  // @ts-ignore
  const nameArr: string[] = arr.filter(item => +item != item)
  return nameArr.map(item => {
    return {
      name: item,
      value: e[item as keyof typeof e],
    }
  })
}
/**
 * @params val 需要翻译的枚举值
 * @params e 枚举对象
 * 寻找enum e中val值的翻译
 */
export const findValueInEnum = (val: string | null | undefined | number, e: { [key: string]: string | number }) => {
  if ((val ?? -1) === -1) {
    return ''
  }
  const arr = Object.keys(e) || []
  // @ts-ignore
  const nameArr: string[] = arr.filter(item => +item != item)
  const name = nameArr.find(item => {
    const str = e[item as keyof typeof e] + ''
    return str.includes(',') ? str.split(',').includes(val + '') : str === (val + '')
  })
  return name || ''
}

/**
 * @params val 翻译是否状态的枚举，0 | 1 | true | false
 * 寻找enum e中val值的翻译
 */
export const findValueInStatus = (val?: string | number | boolean) => {
  if (isEmpty(val)) {
    return ''
  }
  if ([0, '0', false].includes(val!)) {
    return '否'
  }
  if ([1, '1', true].includes(val!)) {
    return '是'
  }
  if (['ENABLE'].includes(val as string)) {
    return '启用'
  }
  if (['DISABLE'].includes(val as string)) {
    return '停用'
  }
  return ''
}

/**
 * 彩色打印监听内容
 * @param val 新值
 * @param oldVal 旧值
 */
export const logWatchColorfully = (val: any, oldVal: any) => {
  // 柯里化函数，美化函数的调用形式
  return (prop: string, desc: string, bgColor: string) => {
    console.log(
      `%c${prop} ${desc}`,
      `background-color: ${bgColor}; color: #fff`,
      '\n',
      '新值',
      toRaw(val),
      '\t\t\t',
      '旧值',
      toRaw(oldVal)
    )
  }
}

/**
 * 将URL改成和页面同源的地址
 * 修改失败则不做改动，并抛出异常
 * @param {string} url URL字符串
 * @return {string} 新的URL字符串
 */
export const changeAudioUrlOrigin = (url: string = ''): string => {
  try {
    if (import.meta.env.MODE.includes('production')) {
      const info = new URL(url)
      info.protocol = location.protocol
      info.hostname = location.hostname
      info.port = location.port
      return info.href
    } else {
      return url
    }
  } catch (e) {
    throw e
  }
}

/**
 * 获取浏览器类型
 * @return {string} 浏览器名称类型
 */
export const getBrowserType = (): string => {
  const ua: string = navigator.userAgent

  if (ua.indexOf("Opera") > -1 || ua.indexOf("OPR") > -1) {
    return 'Opera'
  } else if (ua.indexOf("compatible") > -1 && ua.indexOf("MSIE") > -1) {
    return 'IE'
  } else if (ua.indexOf("Edge") > -1 || ua.indexOf("Edg") > -1) {
    // Edge里的UA确实是Edg，不是Edge，没有第二个e
    return 'Edge'
  } else if (ua.indexOf("Firefox") > -1) {
    return 'Firefox'
  } else if (ua.indexOf("Safari") > -1 && ua.indexOf("Chrome") == -1) {
    return 'Safari'
  } else if (ua.indexOf("Chrome") > -1 && ua.indexOf("Safari") > -1) {
    return 'Chrome'
    // @ts-ignore
  } else if (!!window.ActiveXObject || "ActiveXObject" in window) {
    return 'IE>=11'
  } else {
    return 'Unknown'
  }
}

/**
 * 移除字符串前缀
 *
 * 柯里化函数形式，先传前缀匹配数组，再传字符串本身
 * 调用形式举例：
 * removeStrPrefix(['C1_', 'C2_', 'C3_'])('C2_PT3_abc123')
 * 返回PT3_abc123
 * removeStrPrefix(['C1_', 'C2_', 'C3_'])('abc123')
 * 返回abc123
 *
 * 如果在给定的前缀匹配数组里，能匹配到，那么就可以将这个前缀移除；
 * 如果没有匹配到，那么保持不变
 * @param {string[]} prefixArgs 前缀匹配数组
 * @return 形参为原字符串的函数
 */
export const removeStrPrefix = (prefixArgs: string[] = []) => {
  // 对前缀匹配数组进行去重
  const set = new Set<string>(prefixArgs)
  const arr = Array.from(set)

  /**
   * @param {string} str 原字符串
   * @return {string} 处理前缀后的结果字符串
   */
  return (str: string = ''): string => {
    for (let i = 0; i < arr.length; i++) {
      if (str.substring(0, arr[i].length) === arr[i]) {
        return str.substring(arr[i].length)
      }
    }
    return str
  }
}

/**
 * 比较两个列表是否相同，元素顺序不考虑
 * @param {T[] | undefined} list1 第一个列表
 * @param {T[] | undefined} list2 第二个列表
 * @return {boolean} 两个列表是否相同
 */
export const compareUnorderedLists = <T>(list1: T[] | undefined, list2: T[] | undefined): boolean => {
  if (!list1 && !list2) return true;
  if (!list1 || !list2) return false;
  if (list1.length !== list2.length) return false;
  const set1 = new Set(list1);
  const set2 = new Set(list2);
  return [...set1].every(item => set2.has(item));
}

/**
 * 替换对象的同名属性
 *
 * 源对象里有的而目标对象没有的属性，不会赋值，只会复制都有的（同名的）属性
 * 因为这里使用了Pick进行类型约束
 * @param target 目标对象，想要替换更新一些同名属性
 * @param source 源对象，会从该对象提取一些同名属性
 */
export const assignMatchingProperties = <T, K extends keyof T>(target: T, source: Pick<T, K>): void => {
  for (const key in source) {
    if (source[key] !== undefined) {
      target[key] = source[key];
    }
  }
}
