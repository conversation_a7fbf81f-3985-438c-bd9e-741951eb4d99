import Janus from "janus-gateway"
// import Janus from "@/lib/janus.js"
import adapter from 'webrtc-adapter'
import { ElMessageBox } from 'element-plus'
import {
  ConstructorOptions,
  DestroyOptions,
  InitOptions,
  JSEP,
  PluginHandle,
  PluginOptions,
  RemoteTrackMetadata,
  SipEventMessage,
} from '@/type/janus'

// Janus实例对象列表，按自定义的实例ID查找
const janusList: {
  [id: string]: Janus | undefined
} = {}
// SIP通话实例对象列表，按自定义的实例ID查找
const sipcallList: {
  [id: string]: PluginHandle | undefined
} = {}

/**
 * 内部方法，检查实例ID是否合法并可用
 */
const checkInstanceId = (id: string = '') => {
  // 必须要有实例ID
  if (id === '') {
    console.error('请设置一个正确的的实例ID')
    console.log('已完成初始化的实例的ID有：', Object.keys(janusList))
    return false
  }
  return true
}

/**
 * 初始化Janus并创建实例对象
 * @param id 实例ID
 * @param options Janus配置
 */
const init = (id: string = '', options: ConstructorOptions) => {
  // 浏览器是否支持WebRTC
  if (!Janus.isWebrtcSupported()) {
    console.error('浏览器不支持WebRTC')
    ElMessageBox.alert('浏览器不支持WebRTC，无法使用SIP电话功能', '浏览器不支持WebRTC', {
      autofocus: true,
      confirmButtonText: '确定',
      callback: () => {
      },
    }).then(() => {
    }).catch(() => {
    })
    return
  }

  Janus.init(<InitOptions>{
    debug: false,
    dependencies: Janus.useDefaultDependencies({
      // 启用WebRTC-Adapter
      adapter,
    }),
    callback: function () {
      console.log('***', 'Janus 初始化成功')
      // 必须要有实例ID才能继续操作
      if (!checkInstanceId(id)) {
        return
      }

      janusList[id] = new Janus(<ConstructorOptions>{
        server: options.server,
        iceServers: options.iceServers,
        // all （默认值）尝试所有连接模式，按优先级降序排序：直连——STUN——TURN
        // relay 强制使用TURN
        iceTransportPolicy: 'all',
        // iceTransportPolicy: 'relay',
        success: function () {
          console.log('***', 'Janus 实例已创建')
          options.success && options.success()
        },
        error: function (error: any) {
          console.error('***', 'Janus 实例出现错误', error)
          options.error && options.error(error)
        },
        destroyed: function () {
          console.log('***', 'Janus 实例已销毁 destroyed',)
          options.destroyed && options.destroyed()
        },
      })
    },
  })
}

/**
 * 销毁实例
 * @param id 实例ID
 */
const destroy = (id: string = '') => {
  // 必须要有实例ID才能继续操作
  if (!checkInstanceId(id)) {
    return
  }

  janusList[id] && janusList[id]?.destroy && janusList[id]?.destroy(<DestroyOptions>{
    cleanupHandles: true,
    notifyDestroyed: true,
    unload: true,
    success: function () {
      console.log('***', 'Janus 实例已销毁 destroy success')
    },
    error: (error: string) => {
      console.error('***', 'Janus 实例销毁失败', error)
    }
  })
  sipcallList[id] = undefined
  janusList[id] = undefined
}

/**
 * 加载SIP插件
 * @param id 实例ID
 * @param options SIP插件配置
 */
const attach = (id: string = '', options: PluginOptions) => {
  // 必须要有实例ID才能继续操作
  if (!checkInstanceId(id)) {
    return
  }

  janusList[id] && janusList[id]?.attach(<PluginOptions>{
    ...options,
    plugin: options?.plugin ?? "janus.plugin.sip",
    opaqueId: options?.opaqueId ?? ("sip-" + Janus.randomString(12)),
    success: function (handle: PluginHandle) {
      console.log('***', 'Janus SIP 插件加载成功')
      sipcallList[id] = handle
      options.success && options.success(handle)
    },
    error: function (error: string) {
      console.error('***', 'Janus SIP 插件加载失败', error)
      options.error && options.error(error)
    },
    consentDialog: function (on: boolean) {
      console.log('***', '浏览器媒体开关', on)
      options.consentDialog && options.consentDialog(on)
    },
    webrtcState: function (isConnected: boolean) {
      console.log('***', 'WebRTC 是否连接', isConnected)
      options.webrtcState && options.webrtcState(isConnected)
    },
    // iceState: function (state: 'checking' | 'connected' | 'failed' | 'disconnected' | 'closed') {
    iceState: function (state: RTCIceConnectionState) {
      console.log('***', 'ICE 状态', state)
      // @ts-ignore
      options.iceState && options.iceState(state)
    },
    mediaState: function (medium: 'audio' | 'video', receiving: boolean, mid?: number) {
      console.log('***', '媒体流状态', '类型', medium, '开关', receiving, '媒体ID', mid)
      options.mediaState && options.mediaState(medium, receiving, mid)
    },
    slowLink: function (uplink: boolean, lost: number, mid: string) {
      console.log('***', '连接问题', '数据流方向', uplink, '丢包', lost, '媒体ID', mid)
      options.slowLink && options.slowLink(uplink, lost, mid)
    },
    onmessage: function (message: SipEventMessage, jsep?: JSEP) {
      console.log('###', '收到消息', 'message', message, 'jsep', jsep)
      options.onmessage && options.onmessage(message, jsep)
    },
    onlocaltrack: function (track: MediaStreamTrack, on: boolean) {
      console.log('***', '本地媒体流更新', 'track', track, '开关', on)
      options.onlocaltrack && options.onlocaltrack(track, on)
    },
    onremotetrack: function (track: MediaStreamTrack, mid: string, on: boolean, metadata?: RemoteTrackMetadata) {
      console.log('***', '远程媒体流更新', 'track', track, '媒体ID', mid, '开关', on, '元数据', metadata)
      options.onremotetrack && options.onremotetrack(track, mid, on, metadata)
    },
    ondataopen: function () {
      console.log('***', 'Data Open')
      options.ondataopen && options.ondataopen()
    },
    ondata: function () {
      console.log('***', 'Data')
      options.ondata && options.ondata()
    },
    oncleanup: function () {
      console.log('***', '清理完毕')
      options.oncleanup && options.oncleanup()
    },
    ondetached: function () {
      console.log('***', 'Janus SIP 插件已退出')
      options.ondetached && options.ondetached()
    },
  })
}

/**
 * 检查浏览器是否支持WebRTC
 */
const isWebrtcSupported = () => {
  return Janus.isWebrtcSupported()
}

/**
 * 生成随机字符串
 * @param {number} len
 */
const randomString = (len: number) => {
  return Janus.randomString(len)
}

export {
  janusList,
  sipcallList,
}
export default {
  init,
  destroy,
  attach,
  isWebrtcSupported,
  randomString,
}
