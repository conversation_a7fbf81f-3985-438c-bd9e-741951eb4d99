import { WebSocketConfig } from '@/type/websocket'
import { SeatWebSocketMsg } from '@/type/seat'

export class wsUtil {
  // websocket 实例对象
  ws: WebSocket | null
  // websocket 配置信息
  config: WebSocketConfig
  // 已连接
  connected: boolean
  // 心跳保活定时器
  timerKeepalive: number | null

  /**
   * 构造函数
   * 使用new关键字实例化
   * @param {WebSocketConfig} config 配置信息
   */
  constructor(config: WebSocketConfig) {
    console.log('------', 'websocket', 'new', config)
    this.ws = null
    this.config = config
    this.connected = false
    this.timerKeepalive = null
    this.checkConfig(config)
  }

  /**
   * 检查配置
   * @param {WebSocketConfig} config 配置信息
   */
  checkConfig = (config: WebSocketConfig) => {
    let okay = true
    if (!config.url) {
      console.warn('------', 'websocket', '初始化', '需要正确的', '服务器地址', 'url', config.url)
      okay = false
    }
    if (!config.onOpen || !config.onError || !config.onClose || !config.onMessage) {
      !config.onOpen && console.warn('------', 'websocket', '初始化', '需要事件监听处理', 'onOpen')
      !config.onError && console.warn('------', 'websocket', '初始化', '需要事件监听处理', 'onError')
      !config.onClose && console.warn('------', 'websocket', '初始化', '需要事件监听处理', 'onClose')
      !config.onMessage && console.warn('------', 'websocket', '初始化', '需要事件监听处理', 'onMessage')
      okay = false
    }
    return okay
  }
  /**
   * 创建连接
   */
  connect = () => {
    // 连接存在，直接退出
    if (this.ws) {
      console.warn('------', 'websocket', '连接已存在')
      return
    }
    // 连接不存在，创建连接
    // 开始连接
    this.ws = this.config.protocol
      ? new WebSocket(this.config.url, this.config.protocol)
      : new WebSocket(this.config.url)
    // 监听事件
    this.listenEvent()
    // 启动心跳保活定时任务
    this.startKeepalive()
  }
  /**
   * 断开连接
   */
  disconnect = () => {
    // 连接不存在，无需操作
    if (!this.ws) {
      console.warn('------', 'websocket', '连接不存在，无需断开操作')
      return
    }
    // 连接存在，主动断开连接
    this.ws?.close()
    setTimeout(() => {
      // 取消监听事件
      this.cancelListenEvent()
    }, 500)
  }
  /**
   * 监听事件
   * websocket有四个事件：
   * open 已连接
   * error 连接错误，error后接着close
   * close 已断开
   * message 收到消息
   */
  listenEvent = () => {
    if (!this.ws) {
      console.warn('------', 'websocket', '没有正确初始化，无法监听事件')
      return
    }
    console.log('------', 'websocket', '监听事件')
    this.ws.addEventListener('open', this.handleOn)
    this.ws.addEventListener('error', this.handleError)
    this.ws.addEventListener('close', this.handleClose)
    this.ws.addEventListener('message', this.handleMessage)
  }
  /**
   * 取消监听事件
   * websocket有四个事件：
   * open 已连接
   * error 连接错误，error后接着close
   * close 已断开
   * message 收到消息
   */
  cancelListenEvent = () => {
    if (!this.ws) {
      console.warn('------', 'websocket', '没有正确初始化，无法取消监听事件')
      return
    }
    console.log('------', 'websocket', '取消监听事件')
    this.ws.removeEventListener('open', this.handleOn)
    this.ws.removeEventListener('error', this.handleError)
    this.ws.removeEventListener('close', this.handleClose)
    this.ws.removeEventListener('message', this.handleMessage)
  }
  /**
   * 处理连接打开
   */
  handleOn = (event: Event) => {
    console.log('------', 'websocket', 'open', '连接已打开', JSON.parse(JSON.stringify(event)))
    this.connected = true
    // 调用自定义事件处理
    if (this.ws) {
      // 修改this指向，将WebSocketUtil修改成WebSocket
      this.config.onOpen.call(this.ws, event)
    }
  }
  /**
   * 处理连接错误
   */
  handleError = (event: Event) => {
    console.log('------', 'websocket', 'error', '连接发生错误', JSON.parse(JSON.stringify(event)))
    this.connected = false
    // 调用自定义事件处理
    if (this.ws) {
      // 修改this指向，将WebSocketUtil修改成WebSocket
      this.config.onError.call(this.ws, event)
    }
  }
  /**
   * 处理连接关闭
   */
  handleClose = (event: CloseEvent) => {
    console.log('------', 'websocket', 'close', '连接已关闭', JSON.parse(JSON.stringify(event)),
      'code', event.code, 'reason', event.reason, 'wasClean', event.wasClean)
    // 关闭心跳保活定时任务
    this.stopKeepalive()
    this.connected = false
    // 调用自定义事件处理
    if (this.ws) {
      // 修改this指向，将WebSocketUtil修改成WebSocket
      this.config.onClose.call(this.ws, event)
    }
  }
  /**
   * 处理收到消息
   */
  handleMessage = (event: MessageEvent) => {
    // 不打印心跳保活消息
    if (!event?.data?.includes('keepalive')) {
      console.log('------', 'websocket', 'message', '收到消息', event?.data)
    }
    this.connected = true
    // 调用自定义事件处理
    if (this.ws) {
      // 修改this指向，将WebSocketUtil修改成WebSocket
      this.config.onMessage.call(this.ws, event)
    }
  }
  /**
   * 发送消息
   */
  send = (msg: SeatWebSocketMsg | string) => {
    let str: string
    if (typeof msg === 'string') {
      str = msg
    } else {
      str = JSON.stringify(msg)
    }
    // 已连接上才会发送消息
    this.connected && this.ws?.send(str)
    // 不打印心跳保活消息
    if (!str?.includes('keepalive')) {
      console.log('------', 'websocket', 'send', '发送消息', str)
    }
  }
  /**
   * 启动心跳保活定时任务
   * 没有设置间隔时长默认40秒
   */
  startKeepalive = () => {
    // 先关闭旧的定时器（若有）
    this.stopKeepalive()
    // 再启动新的定时器
    this.timerKeepalive = <number><unknown>setTimeout(() => {
      // 已连接上才会执行心跳保活
      if (this.connected) {
        // console.log('------', 'websocket', '发送一次心跳保活', 'keepalive')
        this.config.handleKeepalive && this.config.handleKeepalive()
      }
    }, (this.config.keepaliveSecond ?? 40) * 1000)
    // console.log('------', 'websocket', '心跳保活已启动', '间隔时长', this.config.keepaliveSecond ?? 40, '秒')
  }
  /**
   * 关闭心跳保活定时任务
   */
  stopKeepalive = () => {
    if (this.timerKeepalive !== undefined && this.timerKeepalive !== null) {
      clearTimeout(this.timerKeepalive)
      this.timerKeepalive = null
      // console.log('------', 'websocket', '心跳保活已关闭')
    }
  }
  /**
   * 重置心跳保活定时任务
   */
  resetKeepalive = () => {
    this.startKeepalive()
  }
}
