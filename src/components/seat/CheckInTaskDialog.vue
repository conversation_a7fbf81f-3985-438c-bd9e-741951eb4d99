<template>
  <!--签入任务弹窗-->
  <el-dialog
    v-model="dialogVisible"
    class="seat-workbench-dialog check-in-task-dialog"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="onClose"
  >
    <template #header>
      <div class="form-dialog-header">
        签入任务
      </div>
    </template>

    <!--弹窗主体-->
    <div class="form-dialog-main">
      <div class="form-dialog-content">
        <TransferBox
          v-model:leftList="availableTaskList"
          v-model:rightList="checkedTaskList"
          leftTitle="待选任务"
          rightTitle="已选任务"
          :width="5"
          name="taskName"
          value="id"
          unit=""
          :checkAllVisible="true"
        />
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="onClickCancel">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" :loading="loadingConfirm" @click="onClickConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import { seatWorkbenchAccountModel } from '@/api/seat'
import { RelatedTaskItem } from '@/type/clue'
import TransferBox from '@/components/TransferBox.vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { TaskManageItem } from '@/type/task'
import { SeatLogActionEnum, SeatLogTypeEnum, SeatStatusEnum } from '@/type/seat'
import { useSeatPhoneStore } from '@/store/seat-phone'
import { Throttle } from '@/utils/utils'
import { useSeatInfoStore } from '@/store/seat/seat-info'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = defineProps<{
  visible: boolean,
  availableTaskList?: RelatedTaskItem[],
  checkedTaskList?: RelatedTaskItem[],
}>()
const emits = defineEmits([
  'close',
  'update',
])

const userStore = useUserStore()
const { account } = storeToRefs(userStore)
const seatPhoneStore = useSeatPhoneStore()
const seatInfoStore = useSeatInfoStore()
const { seatStatus, seatTaskList } = storeToRefs(seatInfoStore)

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)

watch(() => props.visible, (val) => {
    dialogVisible.value = val
    // 每次显示弹窗时
    if (val) {
      // 更新两侧任务列表
      updateTaskList()
    }
  }
)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 弹窗 开始 ----------------------------------------

/**
 * 处理弹窗组件的关闭事件
 */
const onClose = () => {
  // console.log('处理弹窗组件的关闭事件')
  emits('close')
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  // console.log('关闭弹窗')
  dialogVisible.value = false
}
/**
 * 点击确定按钮
 */
const onClickConfirm = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '签入签出任务弹窗 点击确定按钮',
  })
  // console.log('点击确定按钮')
  submit()
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '签入签出任务弹窗 点击取消按钮',
  })
  // console.log('点击取消按钮')
  closeDialog()
}

// ---------------------------------------- 弹窗 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)

/**
 * 重置表单
 */
const resetForm = () => {
  // console.log('重置表单')
  availableTaskList.value = []
  checkedTaskList.value = []
}
/**
 * 提交表单
 */
const submit = async () => {
  // console.log('提交表单')
  // 提交节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 签入签出任务 具体业务处理
  await checkInAndOutTask()

  // 关闭弹窗
  closeDialog()
  // 提交节流锁解锁
  throttleConfirm.unlock()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 任务 开始 ----------------------------------------

// 可选任务列表
const availableTaskList = ref<TaskManageItem[]>([])
// 已选任务列表
const checkedTaskList = ref<TaskManageItem[]>([])

/**
 * 更新可选和已选的任务列表
 */
const updateTaskList = async () => {
  // 右侧已选
  checkedTaskList.value = JSON.parse(JSON.stringify(props.checkedTaskList || []))

  // 左侧可选
  // 全部列表
  const list: TaskManageItem[] = JSON.parse(JSON.stringify(props.availableTaskList || []))
  // 剔除右侧已经选中的，剩下的就是可选的
  availableTaskList.value = list.filter((task: TaskManageItem) => {
    return !checkedTaskList.value.find((item: TaskManageItem) => {
      return item?.id === task?.id
    })
  })
}
/**
 * 签入签出任务 具体业务处理
 */
const checkInAndOutTask = async () => {
  // 将新的已选任务列表和旧的已选任务列表进行比较，多余的是签入，缺失的是签出
  // 签出任务接口参数指的是已选任务列表的最终结果
  // 签入任务接口参数指的是新增的已选任务

  // 已签入 旧数据
  const oldCheckedIdList = (props.checkedTaskList ?? []).map((task: RelatedTaskItem) => {
    return task?.id ?? -1
  })
  const oldCheckedIdSet = new Set(oldCheckedIdList)
  oldCheckedIdSet.delete(-1)

  // 已签入 新数据
  const newCheckedIdList = checkedTaskList.value.map((task: RelatedTaskItem) => {
    return task?.id ?? -1
  })
  const newCheckedIdSet = new Set(newCheckedIdList)
  newCheckedIdSet.delete(-1)

  // 判断需要签入的任务
  const addSet = new Set(newCheckedIdList)
  Array.from(addSet.values()).forEach((id: number) => {
    // 旧的有，新的也有，那就排除，不需要重复签入
    if (oldCheckedIdSet.has(id)) {
      addSet.delete(id)
    }
  })
  // 剩下的就是旧的没有，新的有，需要签入
  const addList = Array.from(addSet)

  // 判断需要签出的任务
  const deleteSet = new Set(oldCheckedIdList)
  Array.from(deleteSet.values()).forEach((id: number) => {
    // 旧的有，新的有，不需要签出
    if (newCheckedIdSet.has(id)) {
      deleteSet.delete(id)
    }
  })
  // 剩下的就是旧的有，新的没有，需要签出
  const deleteList = Array.from(deleteSet)

  console.log('addSet', addSet, 'deleteSet', deleteSet)

  try {
    // 请求接口
    if (addList.length && deleteList.length) {
      // 只有签入，没有签出

      // 签入的参数是新增已选
      await seatWorkbenchAccountModel.checkIn({
        taskIds: addList ?? [],
        callSeatStatus: checkedTaskList.value.length ? SeatStatusEnum.HUMAN_MACHINE_IDLE : SeatStatusEnum.MANUAL_DIRECT_IDLE,
      })

      seatPhoneStore.report({
        action: SeatLogActionEnum['手动签入签出任务'],
        desc: '手动签入签出任务',
      })

    } else {
      // 没有签入，没有签出
      // 没有签入，有签出
      // 有签入，有签出

      // 签出的参数是剩余已选（最终已选）
      await seatWorkbenchAccountModel.checkOut({
        taskIds: newCheckedIdList ?? [],
        callSeatStatus: checkedTaskList.value.length ? SeatStatusEnum.HUMAN_MACHINE_IDLE : SeatStatusEnum.MANUAL_DIRECT_IDLE,
      })

      seatPhoneStore.report({
        action: SeatLogActionEnum['手动签入签出任务'],
        desc: '手动签入签出任务',
      })

    }
    ElMessage({
      message: `${account.value} 成功更新签入任务`,
      type: 'success',
    })
    ElMessage({
      message: `通话中请勿强制刷新、关闭页面`,
      type: 'warning',
    })

    // 更新签入任务列表
    seatInfoStore.updateSeatTaskList(JSON.parse(JSON.stringify(checkedTaskList.value)))

    // 更新坐席状态和通话类型
    seatInfoStore.updateSeatStatusByTaskList()
    seatPhoneStore.updateCallType()

    // 更新坐席统计数据
    seatPhoneStore.needUpdateWorkbenchStatistics = true

    // 关闭弹窗
    closeDialog()
    emits('update')
  } catch (e) {
  }
}

// ---------------------------------------- 任务 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
