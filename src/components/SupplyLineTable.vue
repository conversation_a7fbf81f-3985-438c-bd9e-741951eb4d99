<template>
  <div class="tw-flex tw-flex-col tw-overflow-hidden tw-bg-[#fff]">
    <div v-if="props.showSelectColumn" class="tw-mb-[6px] tw-px-[16px] tw-flex">
      <ColumnsSelection v-model:columnsSelected="selectColumns" :columnsAll="columnsAll"/>
    </div>
    <el-table
      stripe
      :data="tableTempData"
      border
      class="tw-flex-grow"
      :header-cell-style="tableHeaderStyle"
      @sort-change="handleSortChange"
    >
      <el-table-column v-if="needLeftCol" align="center" fixed="left" min-width="46">
        <template #default="{ row }">
          <slot name="left-col" :row="row"></slot>
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('lineName')" align="left" fixed="left" prop="lineName" label="线路名称" min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
          <div @click="copyText(row.lineName)" class="tw-truncate tw-cursor-pointer">
            {{ row.lineName || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('lineType')" align="center" fixed="left" prop="lineType" :formatter="formatterEmptyData" label="线路类型" width="80" show-overflow-tooltip>
        <template #default="{ row }">
          {{ getSupplierLineTypeText(row?.lineType) }}
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('lineNumber')" align="left" fixed="left" prop="lineNumber" :formatter="formatterEmptyData" label="线路编号" min-width="160" show-overflow-tooltip/>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('callLineSupplierName')" property="callLineSupplierName" label="供应商名称" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData">
        <template #default="{ row }">
          <el-button v-if="row.callLineSupplierName && props.canGoSupplier" type="primary" link @click="goSupplierRoute(row.callLineSupplierName)">{{ row.callLineSupplierName }}</el-button>
          <template v-else>{{ row.callLineSupplierName || '-' }}</template>
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('callLineSupplierNumber')" property="callLineSupplierNumber" label="供应商编号" align="left" min-width="160" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('displayCallNumber')" align="left" prop="displayCallNumber" :formatter="formatterEmptyData" label="外显号码" min-width="120" show-overflow-tooltip/>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('enableStatus')" align="center" prop="enableStatus" label="启用状态" width="80">
        <template #default="{ row }">
          <span v-if="row.enableStatus" class="status-box" :class="row.enableStatus === 'ENABLE' ? 'green-status' : 'orange-status'">
            {{ row.enableStatus === 'ENABLE' ? '启用' : '停用' }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column v-if="props.showDetails && (!props.showSelectColumn || selectColumns.includes('lineGateways'))" align="left" prop="lineGateways" label="线路网关" width="160">
        <template #default="{ row }">
          <TooltipBox :title="`【${row?.lineName || '-'}】的线路网关`" :content="row.lineGateways" contentName="name" contentValue="id"/>
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('concurrentLimit')" align="left" prop="concurrentLimit" label="并发限制" width="120" sortable="custom" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.concurrentLimit) }}
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('prefix')" align="left" prop="prefix" label="前缀" width="120" :formatter="formatterEmptyData" show-overflow-tooltip/>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('masterCallNumber')" align="left" prop="masterCallNumber" label="主叫号码" width="120" :formatter="formatterEmptyData" show-overflow-tooltip/>
      <!-- <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('isForEncryptionPhones')" align="left" prop="isForEncryptionPhones" label="数据传输" min-width="120" show-overflow-tooltip >
        <template v-slot="{row}">
          {{ row.isForEncryptionPhones ? '加密' : '普通' }}
        </template>
      </el-table-column> -->
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('cityCodeGroups')" property="cityCodeGroups" label="运营商支持范围" align="left" min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.cityCodeGroups" class="tw-cursor-pointer tw-text-[--el-color-primary]" @click="handleScopeDetails(row)">{{ translateScope(row.cityCodeGroups) }}</div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('secondIndustries')" align="left" prop="secondIndustries" label="适用行业" min-width="160">
        <template #default="{ row }">
          <TooltipBox v-if="row.secondIndustries" :title="`【${row.lineName}】的适用行业`" :content="row.secondIndustries"/>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('disableTimeSlots')" align="left" prop="disableTimeSlots" label="拨打时间" min-width="160">
        <template #default="{ row }">
          <TagsBox
            :tagsArr="concatTimeList(minutes2Time(row.disableTimeSlots || [], true))"
            tagsName="拨打时间"
            :tagsNum="2"
            type=""
            effect="light"
          >
          </TagsBox>
        </template>
      </el-table-column>

      <el-table-column v-if="(!props.showSelectColumn || selectColumns.includes('pending')) && !props.showEditPending" property="pending" label="挂起状态" align="center" min-width="80" :formatter="formatterEmptyData">
        <template #default="{ row }">
          <div class="status-box" :class="!!row.pending ? 'orange-status' : 'green-status'">
            {{ !!row.pending ? '已挂起' : '未挂起' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('lineAccessType')" align="center" prop="lineAccessType" label="接入类型" width="80">
        <template #default="{ row }">
          {{ supplierLineAccessTypeMap.get(row.lineAccessType)?.text ?? '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('caps')" property="caps" label="CAPS" sortable="custom" align="left" min-width="80">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.caps) }}
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('dialingRestrictions')" property="dialingRestrictions" label="接通限频" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData">
        <template #default="{ row }">
          {{ translateDialingRestrictions(row) }}
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('callingRestrictions')" property="callingRestrictions" label="拨打限频" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData">
        <template #default="{ row }">
          {{ translateCallingRestrictions(row) }}
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('outboundTypes')" property="outboundTypes" label="支持外呼种类" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData">
        <template #default="{ row }">
          {{ translateOutbound(row.outboundTypes) }}
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('billingCycle')" property="billingCycle" label="计费周期" sortable="custom" show-overflow-tooltip align="left" min-width="80">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.billingCycle ? row.billingCycle + 's': '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('unitPrice')" property="unitPrice" label="线路单价" sortable="custom" :formatter="formatterEmptyData" show-overflow-tooltip align="left" min-width="120">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ (row.unitPrice??-1)!==-1 ? row.unitPrice + '元': '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('updateTime')" property="updateTime" label="最后使用日期" sortable="custom" align="center" min-width="160" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss') || '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="!props.showSelectColumn || selectColumns.includes('tenantLines')" align="left" prop="tenantLines" label="商户线路" width="240" >
        <template #default="{ row }">
          <TooltipBox :title="`【${row.lineName}】的商户线路`" :num="2" :content="row.tenantLines" contentName="lineName" contentValue="lineNumber"/>
        </template>
      </el-table-column>
      <el-table-column v-if="props.showEditPending" property="pending" label="挂起状态" align="center" fixed="right" min-width="80" :formatter="formatterEmptyData">
        <template #default="{ row }">
          <el-switch
            v-model="row.pending"
            inline-prompt
            active-text="已挂起"
            inactive-text="未挂起"
            @click="changePending(row)"
          />
        </template>
      </el-table-column>
      <el-table-column align="right" label="操作" fixed="right" :width="showDetails ? '96' : '50'">
        <template #default="{ row }">
          <slot name="operate" :row="row"></slot>
        </template>
      </el-table-column>
      <!--空数据提示-->
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      v-if="props.showPagination"
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total || 0"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
    <el-dialog
      v-model="scopeVisible"
      width="960px"
      align-center
      :close-on-click-modal="true"
    >
      <template #header>
        <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">运营商及外呼支持范围</div>
      </template>
      <el-scrollbar
        :max-height="'calc(100vh - 200px)'"
      >
      <CitySettingBox
        :taskRestrictData="scopeData"
        :selectedOperatorList="selectedOperatorList"
        @update:data="handleCityUpdate"
        :readonly="true"
      />
      </el-scrollbar>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="scopeVisible=false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>

</template>

<script lang="ts" setup>
import { supplierLineAccessTypeMap, supplierOperatorMap, supplierOperatorEnum, supplierLineOutboundTypeList, supplierLineOutboundTypeEnum } from '@/assets/js/map-supplier'
import { RestrictModal, OperatorEnum, RestrictModalOrigin, } from '@/type/common'
import { GatewayItem, LineInfo } from '@/type/gateway'
import { formatterEmptyData, formatNumber, minutes2Time, copyText, } from '@/utils/utils'
import { SupplierLineInfoItem, } from '@/type/line'
import { computed, watch, defineAsyncComponent, ref, reactive, onActivated, onMounted, onUnmounted, onDeactivated} from 'vue'
import { gatewayModel } from '@/api/gateway'
import TooltipBox from '@/components/TooltipBox.vue'
import dayjs from 'dayjs'
import TagsBox from '@/components/TagsBox.vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import PaginationBox from '@/components/PaginationBox.vue'
import Confirm from '@/components/message-box'
import { supplierModel } from '@/api/supplier'
import { ElMessage } from 'element-plus'
import ColumnsSelection from '@/components/ColumnsSelection.vue'
import { useGlobalStore } from '@/store/globalInfo'
import { getSupplierLineTypeText } from '@/utils/line'
import { tableHeaderStyle } from '@/assets/js/constant'
import { onBeforeRouteLeave } from 'vue-router'
import router from '@/router'

const globalInfo = useGlobalStore()
// 组件性能消耗较大，动态引入
const CitySettingBox = defineAsyncComponent({
  loader:() => {
    return import('@/components/CitySettingBox.vue')
  }
})
// props和emits
const emits = defineEmits(['update:visible', 'update:table'])
const props = withDefaults(defineProps<{
  needLeftCol?: boolean
  tableData: LineInfo[]
  showDetails?: boolean
  showPagination?: boolean
  showEditPending?: boolean
  showSelectColumn?: boolean
  canGoSupplier?: boolean
}>(), {
  needLeftCol: false,
  showDetails: false,
  showPagination: false,
  showEditPending: false,
  showSelectColumn: false,
  canGoSupplier: false
})

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  globalInfo.pageSize = s
}
const search = () => {
  emits('update:table')
}
// 列表-列名筛选
const columnsAll = computed(() => {
  const res = [
    {name: '线路名称',value: 'lineName', disabled: true},
    {name: '线路编号',value: 'lineNumber', disabled: true},
    {name: '线路类型',value: 'lineType'},
    {name: '供应商名称',value: 'callLineSupplierName',},
    {name: '供应商编号',value: 'callLineSupplierNumber',},
    {name: '外显号码',value: 'displayCallNumber',},
    {name: '启用状态',value: 'enableStatus',},
    {name: '并发限制',value: 'concurrentLimit',},
    {name: '前缀',value: 'prefix',},
    {name: '主叫号码',value: 'masterCallNumber',},
    {value: 'isForEncryptionPhones',name: '数据传输',},
    {name: '运营商支持范围',value: 'cityCodeGroups',},
    {name: '适用行业',value: 'secondIndustries',},
    {name: '拨打时间',value: 'disableTimeSlots',},
    {name: '挂起状态',value: 'pending',},
    {name: '接入类型',value: 'lineAccessType',},
    {name: 'CAPS',value: 'caps',},
    {value: 'dialingRestrictions',name: '接通限频',},
    {value: 'callingRestrictions',name: '拨打限频',},
    {value: 'outboundTypes',name: '支持外呼种类',},
    {value: 'billingCycle',name: '计费周期',},
    {value: 'unitPrice',name: '线路单价',},
    {value: 'updateTime',name: '最后使用日期',},
    {value: 'tenantLines',name: '商户线路',},
  ]
  if (props.showDetails) {
    res.push(...[
      {name: '线路网关',value: 'lineGateways',},
    ])
  }
  return res
})
const selectColumns = ref<string[]>(columnsAll.value?.map(item => item.value) || [])

// 表格 -- 翻译【线路网关】
const gatewayList = ref<GatewayItem[]>([])

// 表格 -- 翻译【运营商及外呼支持范围】
const translateScope = (scope: {
  cityCodes: string[], id: number, serviceProvider: supplierOperatorEnum
}[]) => {
  const res: string[] = []
  scope && scope?.map(item => {
    const provinceCodes = new Set<string>([])
    item.cityCodes?.map(city => {
      provinceCodes.add(city.slice(0, 2) + '0000')
    })
    res.push(
      `${supplierOperatorMap.get(item.serviceProvider)?.text || ''}:
      ${provinceCodes?.size || 0}省${item.cityCodes?.length || 0}市`
    )
  })
  return res.join(';')
}
// 点击【运营商及外呼支持范围】进入详情展示
const scopeVisible = ref(false)
const scopeData = reactive<RestrictModal>(new RestrictModalOrigin())
const selectedOperatorList = ref<('全部' | OperatorEnum)[]>([])
const currentScopeKey = ref('')
const handleCityUpdate = (data: RestrictModal , operators: ('全部' | OperatorEnum)[]) => {
  Object.assign(scopeData, data)
  selectedOperatorList.value = operators
}
// 点击运营商支持范围
const handleScopeDetails = (row: SupplierLineInfoItem) => {
  if (row.cityCodeGroups && row.cityCodeGroups.length > 0) {
    Object.assign(scopeData, new RestrictModalOrigin())
    currentScopeKey.value = row.lineNumber || ''
    row.cityCodeGroups?.map(item => {
      const provinceCodes = new Set<string>([])
      item.cityCodes?.map(city => {
        provinceCodes.add(city.slice(0, 2) + '0000')
      })
      // @ts-ignore
      scopeData[supplierOperatorMap.get(item.serviceProvider as supplierOperatorEnum)?.province] = [...provinceCodes]?.join(',') || undefined
      // @ts-ignore
      scopeData[supplierOperatorMap.get(item.serviceProvider as supplierOperatorEnum)?.city] = item.cityCodes?.join(',') || undefined
    })
    scopeVisible.value = true
  }
}

// 点击跳转供应商管理路由
const goSupplierRoute = (name: string) => {
  name && router.push({
    name: 'LineSupplier',
    query: {
      callLineSupplierName: name
    }
  })
}

// 表格 -- 翻译【频率限制】- 接通
const translateDialingRestrictions = (row: SupplierLineInfoItem) => {
  let str1 = ''
  if (row.dialingRestrictions && row.dialingRestrictions.length > 0) {
    row.dialingRestrictions.map(item => {
      const [times, hours] = item?.split('-')
      str1 += `${times}次/${hours}小时;`
    })
    str1 = `${str1}`
  }
  return str1 || '-'
}
// 表格 -- 翻译【频率限制】- 拨打
const translateCallingRestrictions = (row: SupplierLineInfoItem) => {
  let str2 = ''
  if (row.callingRestrictions && row.callingRestrictions.length > 0) {
    row.callingRestrictions.map(item => {
      const [times, hours] = item?.split('-')
      str2 += `${times}次/${hours}小时;`
    })
    str2 = `${str2}`
  }
  return str2 || '-'
}
// 表格 -- 翻译【支持外呼种类】
const translateOutbound = (outboundTypes: supplierLineOutboundTypeEnum[]) => {
  const res: string[] = []
  outboundTypes?.map(outbound => {
    const val = Object.values(supplierLineOutboundTypeList).find(item => item.val === outbound)?.text || ''
    val && res.push(val)
  })
  return res.join(',') || '-'
}
// 拼接开始结束时间
const concatTimeList = (params: {start: string[], end: string[]}) => {
  const staArr = params.start || []
  const endArr = params.end || []
  if (!staArr || staArr.length < 1 || staArr.length !== endArr.length ) return []
  return staArr.map((item, index) => {
    return item + '-' + endArr[index]
  })
}
/**
 * 点击线路挂起开关
 */
 const changePending = async (row: SupplierLineInfoItem) => {
  let notes = ''
  if (row?.notes) {
    let regex = /【(.+?)】/g;
    let options = row?.notes.match(regex)
    notes = options?.join('') || ''
  }
  Confirm({
    text: `<p>您确定要${row.pending ? '挂起' : '取消挂起'}【${row?.lineName || ''}】?</p>` + (
      notes ? `<p style="margin-top:6px;color:#E54B17;font-weight:600;">备注：${notes || ''}</p>` : ''
    ),
    type: 'warning',
    title: `${row.pending ? '挂起' : '取消挂起'}确认`,
    confirmText: `${row.pending ? '挂起' : '确认'}`
  }).then(async () => {
    try {
      await supplierModel.switchLine({
        supplyLineNumber: row.lineNumber!,
        pendingStatus: row.pending!,
      })
      ElMessage({
        type: 'success',
        message: '操作成功',
      })
    } catch (err) {
      ElMessage({
        type: 'error',
        message: '操作失败',
      })
    }
  }).catch(() => {
  }).finally(() => {
    emits('update:table')
  })
}
// 用于对于keepalive，记录排序
const prop = ref('')
const order = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  prop.value = params.prop
  order.value = params.order
  // emits('update:sortInfo', params)
}

const init = async () => {
  if (props.showDetails) {
    gatewayList.value = await gatewayModel.getGatewayList() || []
  }
  pageSize.value = globalInfo.pageSize || 20
}
onMounted(() => {
  init()
})

onActivated(() => {
  init()
})
const tableData = ref<LineInfo[]>(props.tableData || [])
const tableTempData = computed(() => {
  let data: SupplierLineInfoItem[] = tableData.value || []
  if (prop.value && order.value) {
    data = tableData.value?.sort((a, b) => {
      if (prop.value.includes('Time')) {
        if (order.value === 'descending') {
          // @ts-ignore
          return dayjs(b[prop.value]).isBefore(dayjs(a[prop.value])) ? -1 : 1
        } else {
          // @ts-ignore
          return dayjs(b[prop.value]).isBefore(dayjs(a[prop.value])) ? 1 : -1
        }
      } else {
        // @ts-ignore
        return order.value === 'descending' ? b[prop.value] - a[prop.value] : a[prop.value] - b[prop.value]
      }
    }) || []
  }
  return props.showPagination ?
  data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
  : data
})
watch(() => props.tableData, () => {
  tableData.value = props.tableData || []
  total.value = tableData.value.length || 0
}, {immediate: true, deep: true})


const clearAllData = () => {
  Object.assign(scopeData, new RestrictModalOrigin())
  // @ts-ignore
  tableData.value = null
  // @ts-ignore
  gatewayList.value = null
}
onDeactivated(() => {
  clearAllData()
})
onUnmounted(() => {
  clearAllData()
})
onBeforeRouteLeave(() => {
  clearAllData()
})
</script>
<style lang="postcss" type="text/postcss" scoped>
.el-table {
  font-size: var(--el-font-size-base);
  :deep(.cell) {
    padding: 0 8px;
  }
  :deep(.caret-wrapper) {
    display: none;
  }
}
</style>
