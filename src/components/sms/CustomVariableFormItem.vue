<template>
  <!--所有自定义变量-->
  <el-form-item
    v-for="(variableItem, variableIndex) in customVariableList"
    :key="variableItem.variableName"
    :label="variableItem.variableName+'：'"
    required
  >
    <template v-if="variableItem.variableType===SmsVariableColumnTypeEnum['数字']">
      <el-input-number
        v-model.trim="customVariableList[variableIndex].variableValue"
        placeholder="请输入数字"
        clearable
        @input="onInputCustomVariableItem"
        @change="onChangeCustomVariableItem"
      />
    </template>
    <template v-else-if="variableItem.variableType===SmsVariableColumnTypeEnum['文本']">
      <el-input
        v-model.trim="customVariableList[variableIndex].variableValue"
        type="text"
        placeholder="请输入文本"
        clearable
        @input="onInputCustomVariableItem"
        @change="onChangeCustomVariableItem"
      />
    </template>
    <template v-else-if="variableItem.variableType===SmsVariableColumnTypeEnum['日期']">
      <el-date-picker
        v-model.trim="customVariableList[variableIndex].variableValue"
        type="date"
        class="search-datetime"
        clearable
        placeholder="请选择日期"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        :shortcuts="dateShortcuts"
        @input="onInputCustomVariableItem"
        @change="onChangeCustomVariableItem"
      />
    </template>
    <template v-else-if="variableItem.variableType===SmsVariableColumnTypeEnum['时间']">
      <el-time-picker
        v-model.trim="customVariableList[variableIndex].variableValue"
        class="search-datetime"
        clearable
        placeholder="请选择时间"
        value-format="HH:mm:ss"
        @input="onInputCustomVariableItem"
        @change="onChangeCustomVariableItem"
      />
    </template>
    <template v-else-if="variableItem.variableType===SmsVariableColumnTypeEnum['姓氏']">
      <el-input
        v-model.trim="customVariableList[variableIndex].variableValue"
        type="text"
        placeholder="请输入姓氏"
        clearable
        @input="onInputCustomVariableItem"
        @change="onChangeCustomVariableItem"
      />
    </template>
    <template v-else-if="variableItem.variableType===SmsVariableColumnTypeEnum['城市']">
      <el-cascader
        v-model.trim="customVariableList[variableIndex].variableValue"
        placeholder="请选择城市"
        clearable
        filterable
        :options="cityCodeOptions"
        :props="cityCodeProps"
        :show-all-levels="true"
        @input="onInputCustomVariableItem"
        @change="onChangeCustomVariableItem"
      />
    </template>
    <template v-else-if="variableItem.variableType===SmsVariableColumnTypeEnum['金额']">
      <div class="tw-flex tw-flex-row tw-items-center">
        <el-input-number
          v-model.trim="customVariableList[variableIndex].variableValue"
          placeholder="请输入金额"
          clearable
          :min="0"
          :step="0.01"
          @input="onInputCustomVariableItem"
          @change="onChangeCustomVariableItem"
        />
        <span class="tw-mx-[8px]">元</span>
      </div>
    </template>
  </el-form-item>
</template>

<script setup lang="ts">
import { SmsVariableColumnTypeEnum } from '@/type/merchant'
import { VariableSmsPojoItem } from '@/type/sms'
import { useGlobalStore } from '@/store/globalInfo'
import { nextTick, ref, watch } from 'vue'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  list: VariableSmsPojoItem[],
}>(), {
  list: () => ([]),
})
const emits = defineEmits([
  'update'
])

const globalStore = useGlobalStore()

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 自定义变量 开始 ----------------------------------------

// 自定义变量列表
const customVariableList = ref<VariableSmsPojoItem[]>([])

/**
 * 自定义变量，输入变化
 */
const onInputCustomVariableItem = (val: any) => {
  console.log('自定义变量，输入变化', val)
  // 更新表单
  const list = customVariableList.value.map((item: VariableSmsPojoItem) => ({
    variableName: item.variableName,
    variableType: item.variableType,
    variableValue: item.variableValue,
  }))
  emits('update', list)
}
/**
 * 自定义变量，表单值变化
 */
const onChangeCustomVariableItem = (val: any) => {
  console.log('自定义变量，表单值变化', val)
  // 更新表单
  const list = customVariableList.value.map((item: VariableSmsPojoItem) => ({
    variableName: item.variableName,
    variableType: item.variableType,
    variableValue: item.variableValue,
  }))
  emits('update', list)
}

// ---------------------------------------- 自定义变量 结束 ----------------------------------------

// ---------------------------------------- 日期 开始 ----------------------------------------

// 日期选择器 快捷选项
const dateShortcuts = ref<{ text: string, value: Date, }[]>([
  { text: '今天', value: new Date() },
])

// ---------------------------------------- 日期 结束 ----------------------------------------

// ---------------------------------------- 城市 开始 ----------------------------------------

// 城市 级联选择器 配置项
const cityCodeProps = { multiple: false, emitPath: false }
// 城市 级联选择器 数据
const cityCodeOptions = ref<{
  label: string, value: string, children: { label: string, value: string }[]
}[]>([])

/**
 * 更新城市级联选择器数据
 */
const updateCityCodeOptions = () => {
  cityCodeOptions.value = []
  globalStore.provinceAllMap && Object.keys(globalStore.provinceAllMap)?.length > 0
  && Object.keys(globalStore.provinceAllMap).map(item => {
    const arr = item.split(',')
    cityCodeOptions.value.push({
      label: arr[0],
      value: arr[1],
      children: globalStore.provinceAllMap[item].map(child => {
        const childArr = child.split(',')
        return {
          label: childArr[0],
          value: childArr[1],
        }
      })
    })
  })
}

// ---------------------------------------- 城市 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.list, async (val) => {
  // 每次显示弹窗时
  if (val) {
    // 更新表单数据
    await nextTick()
    customVariableList.value = JSON.parse(JSON.stringify(props.list))
    // 更新其他数据
    updateCityCodeOptions()
  }
}, { immediate: true, deep: true })

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
