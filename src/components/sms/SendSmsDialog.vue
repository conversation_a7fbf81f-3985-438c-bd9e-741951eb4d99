<template>
  <el-dialog
    :model-value="smsStore.sendSmsDialogVisible"
    width="600px"
    class="seat-workbench-dialog"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        发送短信
      </div>
    </template>

    <div class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="90px"
        >
          <el-form-item label="号码：">
            {{ smsStore.sendSmsDialogForm.phoneNumber ?? '-' }}
          </el-form-item>
          <el-form-item label="短信模板：" prop="smsTemplateId">
            <el-select
              v-model.number="selectedSmsTemplateItem"
              value-key="id"
              placeholder="请选择"
              clearable
              @change="onChangeSmsTemplateId"
            >
              <el-option
                v-for="smsTemplateItem in smsTemplateList"
                :key="smsTemplateItem.id"
                :label="smsTemplateItem.templateName"
                :value="smsTemplateItem"
              />
            </el-select>
          </el-form-item>
          <!--所有自定义变量-->
          <CustomVariableFormItem :list="form.variableSmsPojoList || []" @update="onUpdateVariableSmsPojoList" />
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :disabled="loadingConfirm" :icon="CloseBold" @click="onClickCancel">
          取消
        </el-button>
        <el-button :loading="loadingConfirm" type="primary" :icon="Select" @click="onClickConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref, watch } from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { deduplicateObjectArray, Throttle } from '@/utils/utils'
import { ElMessage, FormRules } from 'element-plus'
import to from 'await-to-js'
import { SmsSendParam, VariableSmsPojoItem } from '@/type/sms'
import { useSmsStore } from '@/store/sms'
import { MerchantSmsTemplateItem, SmsTemplateParams, SmsTemplateStatusEnum, SystemVariableEnum } from '@/type/merchant'
import { merchantSmsTemplateModel } from '@/api/merchant'
import { useUserStore } from '@/store/user'
import { smsSendModel } from '@/api/sms'
import CustomVariableFormItem from '@/components/sms/CustomVariableFormItem.vue'
import { FollowUpTypeEnum } from '@/type/clue'
import { seatWorkbenchCallModel } from '@/api/seat'
import { useSeatInfoStore } from '@/store/seat/seat-info'
import { storeToRefs } from 'pinia'

// ---------------------------------------- 通用 开始 ----------------------------------------

const userStore = useUserStore()
const smsStore = useSmsStore()
const seatInfoStore = useSeatInfoStore()
const { currentSeat } = storeToRefs(seatInfoStore)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = () => ({
  callId: undefined,
  smsTemplateId: undefined,
  variableSmsPojoList: [],
  callSeatId: undefined,
  callTeamId: undefined,
  callRecordId: undefined,
})
// 表单数据
const form: SmsSendParam = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  smsTemplateId: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (typeof value !== 'number') {
        callback(new Error('短信模板不能为空'))
      } else if (selectedSmsTemplateItem.value.templateStatus !== SmsTemplateStatusEnum['启用']) {
        callback(new Error('短信模板异常'))
      } else if (!!selectedSmsTemplateItem.value?.isPending) {
        callback(new Error('短信通道异常'))
      } else {
        callback()
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
      })
    }
  })
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 处理参数
  const params: SmsSendParam = {
    clueId: form.clueId ?? undefined,
    smsTemplateId: form.smsTemplateId ?? undefined,
    variableSmsPojoList: form.variableSmsPojoList?.length ? form.variableSmsPojoList : [],
    callSeatId: form.callSeatId ?? undefined,
    callTeamId: form.callTeamId ?? undefined,
    // callRecordId: form.callRecordId ?? undefined,
  }

  // 请求接口
  let err: any, res: any
  switch (smsStore.sendSmsDialogForm.type) {
    case FollowUpTypeEnum['人机协同']:
      ([err, res] = await to(smsSendModel.sendSmsInHumanMachine(params)))
      break
    case FollowUpTypeEnum['人工直呼']:
      ([err, res] = await to(smsSendModel.sendSmsInDirectCall(params)))
      break
    case FollowUpTypeEnum['直发短信']:
      ([err, res] = await to(smsSendModel.sendSmsInClueDetail(params)))
      break
    default:
      ([err, res] = await to(smsSendModel.sendSmsInClueDetail(params)))
      break
  }

  // 返回失败结果
  if (err) {
    ElMessage({
      message: '提交失败',
      type: 'error',
    })
    // 节流锁解锁
    throttleConfirm.unlock()
    return
  }

  // 返回成功结果
  // 判断是否包含违禁词
  if (res?.length) {
    ElMessage({
      message: `变量【${res.at(0)}】存在违禁词【${res.at(1)}】，无法发送短信`,
      type: 'warning',
      duration: 5000,
    })
  } else {
    ElMessage({
      message: '已提交',
      type: 'success',
    })
    // 关闭弹窗
    closeDialog()
  }
  // 节流锁解锁
  throttleConfirm.unlock()
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
  selectedSmsTemplateItem.value = {}
}
/**
 * 更新表单
 */
const updateForm = () => {
  Object.assign(form, formDefault(), smsStore.sendSmsDialogForm)
  form.callTeamId = currentSeat.value.callTeamId ?? undefined
  form.callSeatId = currentSeat.value.id ?? undefined
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  smsStore.hideSendSmsDialog()
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击确定按钮
 */
const onClickConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 短信模板 开始 ----------------------------------------

// 列表，全部，接口数据
const smsTemplateList = ref<MerchantSmsTemplateItem[]>([])
// 当前选中的短信模板
const selectedSmsTemplateItem = ref<MerchantSmsTemplateItem>({})

/**
 * 更新全部列表
 */
const updateSmsTemplateList = async () => {
  // 处理参数
  const params: SmsTemplateParams = {
    groupId: userStore.groupId ?? '',
  }

  // 请求接口
  const [err, res] = <[any, MerchantSmsTemplateItem[]]>await to(merchantSmsTemplateModel.getSmsTemplate(params))

  // 返回失败结果
  if (err) {
    ElMessage.error('短信模板列表' + '获取失败')
    smsTemplateList.value = []
    return
  }

  // 返回成功结果
  // 更新全部列表
  smsTemplateList.value = res?.length ? res : []
}
/**
 * 短信模板，选中变化
 * @param val 选中的短信模板
 */
const onChangeSmsTemplateId = (val: MerchantSmsTemplateItem) => {
  form.smsTemplateId = val.id ?? undefined
  // 排除系统内置变量
  let allList: VariableSmsPojoItem[] = val.variableUsed?.length ? <VariableSmsPojoItem[]>val.variableUsed : []
  // 其余的都是自定义变量
  const filterList: VariableSmsPojoItem[] = allList.filter((item: VariableSmsPojoItem) => {
    return !Object.values(SystemVariableEnum).includes(<SystemVariableEnum>item.variableName ?? '')
  })
  // 格式化成表单格式
  const customList: VariableSmsPojoItem[] = filterList.map((item: VariableSmsPojoItem) => ({
    variableName: item.variableName,
    variableType: item.variableType,
    variableValue: null,
  }))
  // 去重并更新表单
  form.variableSmsPojoList = deduplicateObjectArray(customList, 'variableName')
  // 将预设的变量参数列表，填充到表单中
  fillListFromPreset()
}

// ---------------------------------------- 短信模板 结束 ----------------------------------------

// ---------------------------------------- 自定义变量 开始 ----------------------------------------

// 预设的变量参数列表
const presetVariableList = ref<VariableSmsPojoItem[]>([])

/**
 * 自定义变量组件 更新列表
 * @param list 新列表
 */
const onUpdateVariableSmsPojoList = (list: VariableSmsPojoItem[]) => {
  // console.log('onUpdateVariableSmsPojoList', list)
  form.variableSmsPojoList = list
}
/**
 * 查询待发送短信里已经填好的自定义变量
 */
const findVariableByCallRecordId = async () => {
  // 处理参数
  const params: SmsSendParam = {
    callRecordId: smsStore.sendSmsDialogForm.callRecordId ?? undefined
  }

  // 请求接口
  const [err, res] = <[any, VariableSmsPojoItem[]]>await to(seatWorkbenchCallModel.findVariableByCallRecordId(params))

  // 返回失败结果
  if (err) {
    ElMessage.error('自定义变量参数' + '获取失败')
    return
  }

  // 返回成功结果
  // 更新全部列表
  presetVariableList.value = res?.length ? res : []
  fillListFromPreset()
}
/**
 * 将预设的变量参数列表，填充到表单中
 */
const fillListFromPreset = () => {
  if (form.variableSmsPojoList?.length) {
    presetVariableList.value.forEach((item: VariableSmsPojoItem) => {
      const index = form.variableSmsPojoList!.findIndex((item2: VariableSmsPojoItem) => item2.variableName === item.variableName)
      if (index !== -1) {
        form.variableSmsPojoList![index].variableValue = item.variableValue
      }
    })
  }
}

// ---------------------------------------- 自定义变量 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => smsStore.sendSmsDialogVisible, async (val) => {
  // 每次显示弹窗时
  if (val) {
    // 更新表单数据
    await nextTick()
    resetForm()
    updateForm()
    // 更新其他数据
    await updateSmsTemplateList()
    if (smsStore.sendSmsDialogForm.type !== FollowUpTypeEnum['直发短信']) {
      await findVariableByCallRecordId()
    }
  } else {
    resetForm()
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
