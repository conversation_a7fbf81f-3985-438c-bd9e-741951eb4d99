<template>
  <svg aria-hidden="true" :style="svgStyle">
    <use :href="symbolId" :fill="props.color" />
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  name: string;
  color?: string;
}>()

const symbolId = computed(() => `#icon-${props.name}`)
const svgStyle = computed(() => {
  return {
    fill: props.color || 'transparent',
    color: 'inherit',
  }
})
// 通过DOM操作，将颜色放进svg里
// let svgHtml = document.querySelector(symbolId.value)
// svgHtml?.querySelector('path')?.setAttribute('fill', color)
</script>
