<template>
  <el-tooltip :trigger="props.trigger || 'hover'" :show-after="500" :disabled="!props.content || props.content.length <= 1">
    <template #content>
      <div class="tw-max-w-[40vw] tw-min-w-[220px]">
        <div class="tw-text-left tw-truncate tw-border-b tw-border-gray-400 tw-pb-[4px]">{{props.title || ''}}</div>
        <div v-if="props.content && props.content.length > 0" class="tw-grid tw-mt-[4px] tw-gap-x-1" :class="'tw-grid-cols-' + props.num">
          <div v-for="item in props.content" :key="translateContentName(item, 1)" class="tw-text-left tw-truncate">{{ translateContentName(item, 0) }}</div>
        </div>
      </div>
    </template>
    <div v-if="!props.useSlot" class="tw-text-left tw-truncate">{{ props.content?.map(item => translateContentName(item, 0)).join('、') || '-' }}</div>
    <slot v-else></slot>
  </el-tooltip>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, watch, onMounted, onUnmounted, onActivated } from 'vue'
// 组件入参props
const props = withDefaults(defineProps<{
  title: string,
  content: string[] | {[key: string]: string}[] | null | undefined, // tooltip内容数组
  contentName?: string, // tooltip内容，作为显示的属性
  contentValue?: string, // tooltip内容，作为key的属性，若无则用contentName
  trigger?: string, // tooltip触发方式
  num?: number, // tooltip grid布局的列数
  useSlot?: boolean, // 是否使用slot代替当前的显示
}>(), {
  trigger: 'hover',
  num: 3,
  useSlot: false
})
// emit
const emits = defineEmits([
  'update:value',
])
const translateContentName = (item: string | {[key: string]: string}, type: number) => {
  if (!item) return ''
  if(typeof item === 'string') {
    return item
  } else {
    if (props.contentName) {
      return type === 0 ? item[props.contentName] || '' : item[props.contentValue!] || item[props.contentName] || ''
    }
  }
  return ''
}
</script>

<style lang="postcss" type="text/postcss" scoped>
.input-number-dom :deep(.el-input-group__append) {
  padding: 0 8px;
}

</style>
