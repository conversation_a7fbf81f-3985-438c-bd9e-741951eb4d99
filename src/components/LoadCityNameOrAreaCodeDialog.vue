<template>
  <el-dialog
    v-model="visible"
    width="600px"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
    @close="cancel()"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">{{ '导入' + dialogTitle }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 160px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="80px"
        ref="editRef"
      >
      <template v-if="stepNo===1 && props.type==='cityName'">
        <el-form-item label="匹配类型：" prop="account">
          <el-radio-group v-model="editData.type" class="tw-ml-[6px]">
            <el-radio :label="0">精准匹配</el-radio>
            <el-radio :label="1">模糊匹配</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="editData.type===0" label="城市名称：" prop="cityNames">
          <el-input v-model="editData.cityNames" type="textarea" clearable placeholder="请输入城市名称，支持批量匹配，英文逗号分开" @keydown.enter.prevent="recognitionAction"></el-input>
        </el-form-item>
        <el-form-item v-if="editData.type===1" label="城市名称：" prop="cityName">
          <el-input v-model="editData.cityName" clearable placeholder="请输入城市名称（模糊匹配），不支持批量匹配" @keydown.enter.prevent="recognitionAction"></el-input>
        </el-form-item>
      </template>
      <template v-if="stepNo===1 && props.type==='areaCode'">
        <el-form-item label="城市区号：" prop="codes">
          <el-input v-model="editData.codes" type="textarea" clearable placeholder="请输入城市区号，英文逗号分开" @keydown.enter.prevent="recognitionAction"></el-input>
        </el-form-item>
      </template>
      <div v-if="stepNo===2" class="tw-my-[12px]">
        <div class="tw-flex tw-w-full tw-justify-between">
          <span>识别结果：</span>
          <span class="info-title">共{{ resultList?.length || 0 }}个城市</span>
        </div>
        <div class="tw-grid tw-items-center tw-grid-cols-3 tw-gap-[4px] tw-w-full tw-mt-[6px]">
          <el-tag
            v-if="resultList && resultList?.length > 0"
            v-for="item in resultList"
            class="tw-overflow-hidden"
            :key="item.city"
            closable
            @close="delTag(item)"
          >
            {{ `${item.provinceName?.slice(0, 2) || ''}-${item.cityName || ''}` }}
          </el-tag>
          <span v-else>-</span>
        </div>
      </div>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel(stepNo===2)" :icon="CloseBold">取消</el-button>
        <el-button v-if="stepNo===1" type="primary" @click="recognitionAction">
          <el-icon :size="14"><SvgIcon name="filter" color="#fff"></SvgIcon></el-icon>
          <span>识别结果</span>
        </el-button>
        <el-button v-if="stepNo===2 && resultList && resultList?.length > 0" type="primary" @click="confirm" :icon="Select">确认勾选</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, watch, reactive, } from 'vue'
import { CloseBold, Select, } from '@element-plus/icons-vue'
import type { FormInstance, } from 'element-plus'
import { useGlobalStore } from '@/store/globalInfo'
import { ElMessage, } from 'element-plus'

const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean,
  type: string,
}>();

const visible = ref(props.visible)
const dialogTitle = computed(() => {
  const titleObj: Record<string, string> = {
    'areaCode': '区号',
    'cityName': '城市名称',
  }
  return props.type ? titleObj[props.type] : '区号'
})

watch(() => props.visible, n => {
  visible.value = n
  stepNo.value = 1
  editData.codes = ''
  editData.cityNames = ''
  editData.cityName = ''
  resultList.value = null
})
const editData = reactive<{
  codes: string,
  type: number,
  cityNames: string,
  cityName: string
}>({
  codes: '',
  type: 0,
  cityNames: '',
  cityName: ''
})
const editRef = ref<FormInstance  | null>(null)

const cancel = (goBack: boolean = false) => {
  if (goBack) {
    stepNo.value = 1
    return
  }
  visible.value = false
  emits('update:visible', false)
  editRef.value && editRef.value.clearValidate()
}

const formatCityNames = () => {
  // 将输入字符串分割成数组，使用中文、英文逗号作为分隔符
  const cityNamesArr = editData.cityNames?.trim()?.split(/，|,/);
  return cityNamesArr?.flatMap(cityName => {
    if (!cityName) return []
    if (!cityName.endsWith('市') && cityName.length === 2) {
      return [cityName + '市'];
    }
    return [cityName];
  }) || []
}
const globalStore = useGlobalStore()
const rules = {
  codes: [
    { required: true, message: '请输入区号', trigger: 'blur' },
  ],
  cityNames: [
    { required: true, message: '请输入城市名称', trigger: 'blur' },
  ],
  cityName: [
    { required: true, message: '请输入城市名称', trigger: 'blur' },
  ],
}

const stepNo = ref(1)
const resultList = ref<null | {
  province: string,
  city: string,
  cityName: string,
  provinceName: string,
}[]>(null)
// 识别地区code
const recognitionAction = () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      resultList.value = []
      if (props.type === 'areaCode') {
        const cityCodeMap = globalStore.getCityNumberMap
        resultList.value = editData.codes?.split(',')?.flatMap(item => {
          const code = !item.startsWith('0') ? ('0' + item) : item
          const data = cityCodeMap.get(code)
          return data && data?.length > 0 ? data : []
        }) || []
      }
      if(props.type === 'cityName' && editData.type === 0) {
        const cityNameList = globalStore.getCityNameList
        const names =  formatCityNames()
        resultList.value = cityNameList?.flatMap(item => {
          return names.includes(item.cityName) ? [item] : []
        }) || []
        if (resultList.value.length < names.length) {
          ElMessage.warning(`存在未识别或重复的城市名称`)
        }
      }

      if(props.type === 'cityName' && editData.type === 1) {
        const cityNameList = globalStore.getCityNameList
        resultList.value = cityNameList?.flatMap(item => {
          return item.cityName.includes(editData.cityName) ? [item] : []
        }) || []
      }
      if (resultList.value?.length > 0) {
        stepNo.value = 2
      } else {
        ElMessage.warning(`未识别到${dialogTitle.value}`)
      }
      
    }
  })
}

const delTag = (item: {
  province: string,
  city: string,
  cityName: string,
  provinceName: string,
}) => {
  resultList.value?.splice(resultList.value?.findIndex(v => v.cityName ===item.cityName), 1)
}
const confirm = () => {
  if (stepNo.value !== 2 || !resultList.value || resultList.value?.length === 0) return
  const provincesCodes: string[] = []
  const cityCodes: string[] = []
  resultList.value?.forEach(item => {
    provincesCodes.push(item.province)
    cityCodes.push(item.city)
  })
  emits('confirm', cityCodes, [...new Set(provincesCodes)])
  cancel()
}
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    padding: 0;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>
