<template>
  <div class="tw-w-full tw-flex tw-flex-col">
    <div class="tw-flex tw-h-[30px]">
      <el-button v-if="['HH:mm',].includes(props.format)" link type="primary" @click="addOne()">添加</el-button>
      <el-button link type="primary" @click="selectAll">全选</el-button>
      <el-button type="danger" link @click="delAll">清空</el-button>
    </div>
    <div class="tw-w-full">
      <template v-if="['HH:mm',].includes(props.format)">
        <div v-for="(item, index) in startWorkTimeList" :key="startWorkTimeList.length + '' + index" class="tw-flex tw-mb-[6px]">
          <el-time-select
            v-model="startWorkTimeList[index]"
            class="tw-w-[200px]"
            placeholder="开始"
            :start="props.defaultStart"
            :step="props.timeStep"
            :end="props.defaultEnd"
            :max-time="endWorkTimeList[index]"
            editable
            :clearable="false"
            @blur="handleTimeChange"
          />
          <span class="tw-mx-[8px]">-</span> 
          <el-time-select
            v-model="endWorkTimeList[index]"
            class="tw-w-[200px] "
            placeholder="结束"
            editable
            :start="props.defaultStart"
            :min-time="startWorkTimeList[index]"
            :step="props.timeStep"
            :end="props.defaultEnd"
            :clearable="false"
            @blur="handleTimeChange"
          />
          <el-button :disabled="startWorkTimeList?.length <= 1" link :type="startWorkTimeList?.length <= 1 ? 'default':'danger'" class="tw-ml-[8px]" @click="delOne(index)">删除</el-button>
        </div>
      </template>
      <template v-else>
        <div v-for="(item, index) in startWorkTimeList" class="tw-mb-[4px] tw-flex" :key="item">
          <TimePickerBox
            v-model:start="startWorkTimeList[index]"
            v-model:end="endWorkTimeList[index]"
            :clearable="false"
            :format="props.format"
            :type="typeObj[props.format] || 'daterange'"
            :showShortcuts="false"
            @update:start="handleTimeChange"
            @update:end="handleTimeChange"
          />
          <el-button link type="danger" class="tw-ml-[8px]" @click="delOne(index)">删除</el-button>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { reactive, shallowRef } from 'vue'
import TimePickerBox from './TimePickerBox.vue'
// 组件入参props
const props = withDefaults(defineProps<{
  startWorkTimeList: (string | undefined)[] | undefined,
  endWorkTimeList: (string | undefined)[]  | undefined,
  format?: string,
  timeStep?: string,
  separator?: string,
  defaultStart: string,
  defaultEnd: string,
}>(), {
  format: 'YYYY-MM-DD HH:mm:ss',
  timeStep: '00:30',
})
// emit
const emits = defineEmits([
  'update:startWorkTimeList',
  'update:endWorkTimeList',
])

const startWorkTimeList = shallowRef( props.startWorkTimeList ? props.startWorkTimeList : [])
const endWorkTimeList = shallowRef( props.endWorkTimeList ? props.endWorkTimeList : [])


const typeObj: Record<string, string> = {
  'HH:mm': 'timerange',
  'HH:mm:ss': 'timerange',
  'YYYY-MM-DD HH:mm:ss': 'datetimerange',
  'YYYY-MM-DD': 'daterange',
}

const delAll = () => {
  selectAll()
}
const delOne = (index: number) => {
  if (index >= 0 && startWorkTimeList.value.length >= 1 && endWorkTimeList.value.length >= 1) {
    startWorkTimeList.value?.splice(index, 1)
    endWorkTimeList.value?.splice(index, 1)
  } else {
    delAll()
  }
}
const addOne = () => {
  startWorkTimeList.value?.push(undefined)
  endWorkTimeList.value?.push(undefined)
}
const selectAll = () => {
  startWorkTimeList.value = [props.defaultStart]
  endWorkTimeList.value = [props.defaultEnd]
  handleTimeChange()
}

const handleTimeChange = () => {
  emits('update:startWorkTimeList', startWorkTimeList.value)
  emits('update:endWorkTimeList', endWorkTimeList.value)
}

</script>

<style lang="postcss" type="text/postcss">
.date-box {
  flex-grow: 1;
  flex-shrink: 1;
  
}

.popper-dom {
  .el-picker-panel {
    line-height: 22px;
    .el-picker-panel__sidebar {
      width: 100px;
      
    }
    .el-picker-panel__shortcut {
      font-size: 13px;
    }
    .el-picker-panel__body {
      margin-left: 100px;
      
    }
  }
  .el-date-table th {
    padding: 3px;
  }
  .el-date-table td {
    padding: 0;
  }
  .el-date-range-picker__content {
    padding: 8px;
    .el-picker-panel__icon-btn {
      margin-top: 4px;
    }
    .el-date-range-picker__header div {
      font-size: 14px;
    }
  }
}
.date-box.el-input__wrapper {
  justify-content: space-between;
  overflow: hidden;
  .el-range-input {
    padding-left: 2%;
    text-align: left;
  }
  .el-range__icon{
    margin: 1px -10px 1px 0;
    box-shadow: 0 0 0 1px var(--el-input-border-color,var(--el-border-color)) inset;
    border-radius: 0 4px 4px 0;
    background-color: #f5f7fa;
    padding: 1px 4px;
    order: 8;
    width: 30px;
  }
  .el-range-separator {
    flex-grow: 0;
    flex-shrink: 0;
    width: 24px;
    padding-right: 8px;
  }
}
.date-box.is-active {
  .el-range__icon {
    color: #fff;
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    background-color: var(--el-color-primary);
  }
}
</style>
