<template>
  <div class="audio-box" @mouseup="handleDragEnd" v-loading="loading">
    <div class="audio-main">
      <el-icon class="tw-cursor-pointer" :size="24" @click="goNext(-1, tempAudio.id as number)"><SvgIcon name="pre" color="#eee" /></el-icon>
      <el-icon class="tw-cursor-pointer" :size="24" @click="handlePause">
        <SvgIcon v-if="isPaused === 'pause'" name="play" color="#eee" />
        <SvgIcon v-else name="stop" color="#eee" />
      </el-icon>
      <el-icon class="tw-cursor-pointer" :size="24" @click="goNext(1, tempAudio.id as number)"><SvgIcon name="next" color="#eee" /></el-icon>
      <div class="tw-w-1/2 tw-h-full tw-flex tw-items-center" @mousedown="handleDragStart">
        <el-slider
          v-model="currentTime"
          @input="handleTimeChange"
          :step	="0.1"
          :show-tooltip="false"
          :max="tempDuration"
          size="small"
        />
      </div>

      <div class="tw-text-[#eee] tw-w-[80px] tw-flex tw-justify-around">
        <span class="tw-inline-block tw-text-left">{{ Math.round(currentTime*10)/10 || 0 }}</span>
        <span>/</span>
        <span>{{ tempDuration.toFixed(1) || 0 }}</span>
      </div>
      <el-dropdown  @command="changeRate" class="tw-cursor-pointer">
        <span><el-icon class="tw-cursor-pointer" :size="24" color="#eee"><SvgIcon name="play-rate" /></el-icon></span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item v-for="item in rateList" :command="item" :key="item">
              <span v-if="tempRate===item" class="tw-text-[#409eff] tw-flex tw-items-center">{{ item }}<el-icon color="#409eff" class="tw-ml-0.5"><Select /></el-icon></span>
              <span v-else>{{ item }}</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-icon class="tw-cursor-pointer" :size="24" @click="download" color="#eee"><SvgIcon name="download"/></el-icon>
      <el-icon class="tw-cursor-pointer" :size="21" @click="copyUrl" color="#eee"><SvgIcon name="link2" /></el-icon>
      <el-icon class="tw-cursor-pointer" :size="24" @click="close" color="#eee" ><SvgIcon name="close" /></el-icon>
    </div>
    <div class="audio-detail">
      {{ `【${tempAudio.name}】的音频：${tempAudio.content}` }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { Select } from '@element-plus/icons-vue'
import { ref, reactive, onMounted, watch, onBeforeUnmount } from 'vue'
import { scriptAudioModel } from '@/api/speech-craft'
import { exportFileByBlob } from '@/utils/export'
import { copyText } from '@/utils/utils'
import { trace } from '@/utils/trace'

interface AudioItem {
  id?: number,
  name?: string | null // 语料名称
  content?: string | null// 文字内容
  audioPath?: string | null // 音频文件URL
  isPlayed?: boolean
}
const props = defineProps<{
  tempAudio: AudioItem;
  playRate: number;
  audioStatus: 'pause' | 'play' | 'none';
}>();
const tempAudio = reactive<AudioItem>(props.tempAudio || {
  id: -1,
  name: '',
  content: '',
  audioPath: '',
})
const emits = defineEmits(['close', 'update:playRate', 'update:audio', 'update:audioStatus'])
// 音频倍速
const tempRate = ref(props.playRate || 1)
const rateList = [1, 1.5, 2]
// 音频slider总长
const tempDuration = ref<number>(0)
// 音频slider当前位置
const currentTime = ref<number>(0)
// const audioRef = ref<HTMLAudioElement | null>(null)
const isPaused = ref<'pause' | 'play' | 'none' | 'drag'>('none')
const audioRef = ref<HTMLAudioElement|null>(new Audio())
const loading = ref(false)
onMounted(() => {
  if (tempAudio.audioPath && audioRef.value) {
    audioRef.value.loop = true
    // audioRef.value.src = 'http://ai.system.bountech.com/marketfront/file/ai-speech/ai/audio_record/77f0721b-25cd-4192-824d-89cb0262739f/77f0721b-25cd-4192-824d-89cb0262739f_问候语_0.wav'
    audioRef.value.src = tempAudio.audioPath + '?rand='+Math.random()
  }
  audioRef.value?.addEventListener('loadstart', () => {
    loading.value = true
    // 加载超过5秒默认清loading
    setTimeout(() => {
      loading.value = false
    }, 5000)
  })
  // 监听音频加载完毕，更新音频参数进行播放
  audioRef.value?.addEventListener('loadedmetadata', () => {
    const r = audioRef.value?.duration || 0
    tempDuration.value =  Math.round(r*10)/10
    audioRef.value && (audioRef.value.playbackRate = tempRate.value)
    currentTime.value = 0
    loading.value = false
    audioRef.value?.play()
  })
  audioRef.value?.addEventListener('timeupdate', () => {
    const cur = audioRef.value?.currentTime
    if (cur) {
      currentTime.value = Math.round(cur * 10)/10
    } else {
      currentTime.value = 0
    }
  })
  // 监听音频的播放和暂停，更新外部的audioStatus。当为拖拽时，需要等到拖拽结束再更新
  audioRef.value?.addEventListener('pause', () => {
    if (isPaused.value !== 'drag') {
      isPaused.value = 'pause'
      emits('update:audioStatus', 'pause')
    }
  })
  audioRef.value?.addEventListener('play', () => {
    if (isPaused.value !== 'drag'){
      isPaused.value = 'play'
      emits('update:audioStatus', 'play')
    }
  })
  // 播放完毕音频本身便会暂停，无需手动操作
  // audioRef.value?.addEventListener('ended', () => {
  //   console.log('ended', isPaused.value);
  //   if (isPaused.value !== 'pause'){
  //     isPaused.value = 'pause'
  //     emits('update:audioStatus', 'pause')
  //   }
  // })
})
// 处理直接修改进度条的时间变化，当不是拖拽时，修改时间转化为播放
const handleTimeChange = (val: number) => {
  if (audioRef.value && isPaused.value !== 'drag') {
    audioRef.value && (audioRef.value.currentTime = Math.round((val??0) * 10)/10)
    setTimeout(() => {
      if (isPaused.value !== 'play' && isPaused.value !== 'drag' && audioRef.value) {
        audioRef.value.play()
      }
    }, 100)
  }
}
/** 音频进度条 拖拽修改时间 开始 */
const handleDragStart = () => {
  isPaused.value = 'drag'
  audioRef.value && audioRef.value.pause()
}
const timer = ref<ReturnType<typeof setTimeout> | null>(null)
const handleDragEnd = () => {
  if (isPaused.value === 'drag') {
    if (timer.value) clearTimeout(timer.value)
    timer.value = setTimeout(() => {
      isPaused.value = 'play'
      if (audioRef.value) {
        audioRef.value.currentTime = Math.round((currentTime.value??0) * 10)/10
        audioRef.value.play()
      }
      timer.value = null
    }, 0)
  }
}
/** 音频进度条 拖拽修改时间 结束 */
watch(() => props.tempAudio.audioPath, n => {
  if (audioRef.value && n) {
    audioRef.value.src = n + '?rand='+Math.random()
    tempRate.value = props.playRate || 1
    audioRef.value.playbackRate = tempRate.value
    currentTime.value = 0
    Object.assign(tempAudio, props.tempAudio)
    // 'http://ai.system.bountech.com/marketfront/file/ai-speech/ai/audio_record/dbfb0d5f-2519-473e-820d-2de45476e07d/dbfb0d5f-2519-473e-820d-2de45476e07d_主流程1_0.wav'
  }
})
watch(() => props.audioStatus, n => {
  if (isPaused.value === props.audioStatus) return
  if (n === 'play') {
    audioRef.value?.play()
  } else {
    audioRef.value?.pause()
  }
})
onBeforeUnmount(() => {
  audioRef.value && audioRef.value.pause()
  audioRef.value = null
})
const handlePause = () => {
  if (audioRef.value) {
    if(audioRef.value?.paused ) {
      isPaused.value = 'play'
      audioRef.value && audioRef.value.play()
     } else {
      audioRef.value && audioRef.value.pause()
     }
  }
}
const changeRate = (val: number) => {
  if (audioRef.value) {
    audioRef.value.playbackRate = val
    tempRate.value = val
    emits('update:playRate', val)
  }
}
const goNext = (flag: -1 | 1, aId: number) => {
  emits('update:audio', flag, aId )
}
const download = async () => {
  try {
    trace({ page: `话术编辑/审核-音频组件-下载音频-单个`, params: tempAudio })
    const res = await scriptAudioModel.patchDownloadAudio([tempAudio?.id ?? -1]) as BlobPart
    exportFileByBlob(res, tempAudio.name + '.wav', 'wav')
  } catch (e) {
  }
}
const copyUrl = () => {
  const url = audioRef.value?.src?.split('?')[0] || ''
  url && copyText(url)
  trace({ page: `话术编辑/审核-音频组件-复制音频地址`, params: tempAudio })
}

const close = () => {
  if (audioRef.value) {
    audioRef.value.src = ''
  }
  audioRef.value = null
  emits('close')
}
</script>

<style lang="postcss" type="text/postcss" scoped>
.audio-box {
  position: fixed;
  background-image: linear-gradient(to right, #113238, #0e434e);
  border-radius: 0 0 27px 27px;
  box-shadow: 5px 5px 10px -4px #63645e;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 600px;
  z-index: 99;
  padding-bottom: 5px;
  box-sizing: border-box;
  .audio-main {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 0 5px;
    height: 40px;
    line-height: 20px;
  }
  .audio-icon {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #eee;
    border-radius: 0 0 27px 27px;
    border-radius: 50%;
    line-height: 24px;
    width: 24px;
    height: 24px;
    display: none;
  }
  .audio-detail {
    display: none;
    text-align: justify;
    color: #eee;
    margin: 10px;
    line-height: 24px;
  }
  &:hover .audio-detail{
    display: block;
  }
  .el-slider--small {
    height: 20px;
    :deep(.el-slider__button-wrapper) {
      top: -7px;
      height: 16px;
      width: 16px;
    }
    :deep(.el-slider__runway) {
      height: 10px;
      box-sizing: content-box;
      border-top: 15px solid transparent;
      border-bottom: 15px solid transparent;
      background-clip: padding-box;
    }
    :deep(.el-slider__bar) {
      height: 10px;
      border-radius: 0;
    }
  }
  :deep(.el-slider__button) {
    width: 15px;
    box-sizing: border-box;
    height: 15px;
    border: none;
  }
}
</style>
