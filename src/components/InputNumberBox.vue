<template>
  <el-input
    v-model="val"
    :placeholder="props.placeholder || '请输入'"
    @blur="formatVal"
    class="input-number-dom"
    clearable
    @keyup.enter="handleEnter"
  >
    <template v-if="!!props.append" #append>{{ props.append }}</template>
  </el-input>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, watch, onMounted, onUnmounted, onActivated } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage, } from 'element-plus'
// 组件入参props
const props = withDefaults(defineProps<{
  value?: number | null | string, // 选项列表中代表value的属性
  append?: string,  // 后缀，如秒、次等
  placeholder?: string, // 选择框占位符
  min?: number, // 输入数字最值
  max?: number,
  precision?: number, // 支持输入小数位数
}>(), {
  append: '',
  min: 0,
  precision: 0
})
// emit
const emits = defineEmits([
  'update:value', 'enter'
])

const val = ref<any>(props.value) // 选中的数据

// 更新选中数据,并判断是否需要继续聚焦搜索框，暴露给父组件
const formatVal = () => {
  // 空、null、undefined转化为undefined，无需后续处理
  if ((val.value??'aaa') === 'aaa' || val.value === '') {
    val.value = undefined
  } else {
    // 字符串，去除非数字字符
    val.value = +((val.value + '').replace(/[^0-9-.]/g, ''))
    // 防止出现多个.-
    if (isNaN(val.value)) {
      val.value = undefined
    } else {
      // 最值限制，直接变为边界值
      if (props.max && val.value && val.value > props.max) {
        val.value = props.max
      }
      if ((props.min??-1)!==-1 && val.value < props.min) {
        val.value = props.min
      }
      // 小数位限制，保留props.precision位小数
      if (val.value) {
        const p = Math.pow(10, props.precision)
        val.value = Math.floor(val.value * p) / p
      }
    }
  }
  emits('update:value', val.value)
}

const handleEnter = () => {
  formatVal()
  emits('enter', val.value)
}

/** watch开始 */
// 监听入参，更新选中数据和选项变化
watch(props, () => {
  val.value = props.value
  if(props.max && props.min > props.max) {
    ElMessage({
      type: 'warning',
      message: '最值有误'
    })
  }
}, {deep: true, immediate: true})
</script>

<style lang="postcss" type="text/postcss" scoped>
.input-number-dom :deep(.el-input-group__append) {
  padding: 0 8px;
}

</style>
