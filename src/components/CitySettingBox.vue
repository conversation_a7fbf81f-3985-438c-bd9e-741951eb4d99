<template>
  <!--组件容器-->
  <div class="block-setting-container">
    <!--顶部按钮容器-->
    <div class="tw-w-full tw-h-[64px] tw-relative tw-box-border tw-border-b-[1px] tw-border-b-[#E1E3E6] tw-border-t-[1px] tw-border-t-white tw-flex tw-items-center">
      <!-- 顶部操作按钮容器 -->
      <div v-if="allOperatorList?.length===5" class="tw-flex tw-items-center tw-z-10 tw-absolute tw-right-1 tw-top-[14px] tw-text-[13px]">
        <!--复制-->
        <el-button class="tw-flex tw-items-center tw-ml-1" @click="copyAll">
          <el-icon :size="14" color="var(--el-color-primary)">
            <SvgIcon name="copy" color="inherit"></SvgIcon>
          </el-icon>
          <span class="tw-text-[--el-color-primary]">复制全部</span>
        </el-button>
        <!--粘贴-->
        <el-tooltip v-if="!props.readonly && copyAllRestrictData.time.length > 0" effect="dark" trigger="hover" :show-after="500">
          <template #content>
            <div class="tw-text-left">{{ '粘贴信息创建时间：' + copyAllRestrictData.time }}</div>
          </template>
          <el-button class="tw-flex tw-items-center tw-ml-1" @click="pasteAll">
            <el-icon :size="14" color="var(--el-color-primary)">
              <SvgIcon name="copy" color="inherit"></SvgIcon>
            </el-icon>
            <span class="tw-text-[--el-color-primary]">粘贴</span>
          </el-button>
        </el-tooltip>
        <!-- 清空 -->
        <el-button v-if="!props.readonly" class="tw-text-[--el-color-primary] tw-flex tw-items-center tw-ml-1" @click="removeAllTab" link type="danger">
          <el-icon :size="14"><SvgIcon name="delete"/></el-icon>
          <span>清空</span>
        </el-button>
      </div>
      <!--运营商标签卡容器-->
      <span
        v-for="item in selectedOperatorList"
        class="block-tab-box tw-text-[14px]"
        :class="{'active-block-tab-box': activeOperator == item}"
        :key="item"
        :name="item"
        @click.capture="activeOperator=item"
      >
        <span class="tw-flex tw-items-center tw-text-[14px]">
          <img class="tw-w-[24px] tw-h-[24px]" :src="imgMap[item] || wz" :alt="item"/>
          <span class="tw-ml-[8px] tw-font-[600]">{{ item }}</span>
        </span>
        <span>{{ translateTabName(item) }}</span>
        <el-icon v-if="selectedOperatorList && selectedOperatorList.length > 1 && !props.readonly" class="close-btn" size="20" color="#fff" @click="removeTab(item)">
          <Close />
        </el-icon>
      </span>
      <!--创建-->
      <div>
        <el-dropdown v-if="!props.readonly && unselectedRestOperatorList.length > 0" @command="addTabs">
          <el-icon :size="20" class="tw-cursor-pointer tw-ml-1" color="var(--el-color-primary)">
            <SvgIcon name="add-circle"></SvgIcon>
          </el-icon>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="item in unselectedRestOperatorList" :command="item">
                <span class="tw-flex tw-items-center tw-text-[13]"><img class="tw-w-[20px] tw-h-[20px]" :src="imgMap[item] || wz" :alt="item"/><span class="tw-ml-[8px]">{{  item }}</span></span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
      <!--当前标签卡的按钮容器-->
      <div class="tw-flex tw-justify-end tw-p-[8px] tw-items-center tw-border-x-[#E1E3E6] tw-border-x-[1px]">
        <!-- 导入城市 -->
        <el-button v-if="!!props.loadByCity && !props.readonly" type="primary" :disabled="activeOperator === OperatorEnum['未知']" link @click="loadCity('cityName')">
          导入城市
        </el-button>
        <!-- 导入区号 -->
        <el-button v-if="!!props.loadByCity && !props.readonly" type="primary" :disabled="activeOperator === OperatorEnum['未知']" link @click="loadCity('areaCode')">
          导入区号
        </el-button>
        <!--复制-->
        <el-button type="primary" :disabled="activeOperator === OperatorEnum['未知']" link @click="copy">
          复制当前所选区域
        </el-button>

        <!--粘贴-->
        <el-tooltip
          v-if="!props.readonly && copyRestrictData.city?.length > 0"
          effect="dark"
          trigger="hover"
          :show-after="500"
        >
          <template #content>
            <div class="tw-text-left">可粘贴信息</div>
            <div class="tw-text-left">{{ '创建时间：' + copyRestrictData.time }}</div>
            <div class="tw-text-left">
              {{ `简要信息：${copyRestrictData.province.length}省${copyRestrictData.city.length}市` }}
            </div>
          </template>
          <el-button type="primary" link @click="paste">
            粘贴区域
          </el-button>
        </el-tooltip>
        <!--反选-->
        <el-button v-if="!props.readonly" type="primary" link :disabled="activeOperator === OperatorEnum['未知']" @click="reverseSelectData">
          反选
        </el-button>

        <!--全选-->
        <el-checkbox
          v-if="!props.readonly"
          :model-value="tempOperatorProvinces.length === provinceList.length && tempOperatorCities.length === cityList.length"
          :indeterminate="tempOperatorProvinces.length > 0 && tempOperatorCities.length < cityList.length"
          @change="selectAllData"
          :disabled="activeOperator === OperatorEnum['未知']"
          class="tw-ml-[16px] tw-text-[13px]"
        >
          全选
        </el-checkbox>
      </div>
      <!--省份城市列表的网格布局-->
      <template v-if="activeOperator != OperatorEnum['未知']">
        <el-row class="tw-overflow-y-auto tw-relative">
          <!--三列排布-->
          <el-col v-for="num in 3" :key="num" :span="8" class="tw-border-[#E1E3E6] tw-border-[1px] tw-py-[24px] tw-pl-[16px]">
            <div  v-if="!!props.needUnknown && num === 1" class="tw-grid tw-grid-cols-2 tw-gap-x-[6px] tw-ml-[16px]">
              <div
                class="normal-box unknown"
                :class="{'active-box': tempOperatorProvinces.includes(unknownCode)}"
                @click="selectUnknown"
              >未知</div>
            </div>
            
            <div
              v-for="item in ZhMap.filter(item => item.en >= enGrid[num-1] && (!enGrid[num] || item.en < enGrid[num]))"
              :key="item.en"
              class="tw-flex tw-w-full tw-items-start tw-grow-0"
              :class="{'tw-hidden': !filterProvinceByEn(item)}"
            >

              <!--省份音序-->
              <span class="tw-text-[#969799] tw-leading-[30px] tw-w-[12px]">{{ item.en.toUpperCase() }}</span>

              <!--省份-->
              <span class="tw-grid tw-grid-cols-2 tw-grow tw-ml-[16px]">
                <!--省份名称容器-->
                <div
                  v-for="province in filterProvinceByEn(item)"
                  :key="province"
                  class="normal-box"
                  :class="{'active-box': tempOperatorProvinces.includes(province.split(',')[1])}"
                >
                <!-- <el-tooltip :content="tempOperatorProvinces.includes(province.split(',')[1]) ? '点击清空当前省' : '点击全选当前省'" effect="dark" trigger="hover" :show-after="500"> -->
                <span class="province-text" :class="props.readonly ? '' : 'tw-cursor-pointer'" @click="selectAllCities(provinceAllMap[province], province, tempOperatorProvinces.includes(province.split(',')[1]) ? 0 : 1)">
                <!--省份名称文本-->
                  <span>{{ translateProvinceName(province.split(',')[0]) }}</span>
                  <!--省份选中数据文本-->
                  <span v-if="getProvinceSelectedNum(province)">{{ ' ' + getProvinceSelectedNum(province) }}</span>
                </span>
                <!--展开折叠箭头图标-->
                <el-icon :size="15" @click="showPopover($event, province)"><ArrowDown /></el-icon>
                </div>
              </span>
            </div>
          </el-col>
          <!--省份的城市列表弹出框，公用-->
        <el-popover
          v-model:visible="cityPopoverVisible"
          trigger="click"
          placement="bottom"
          :virtual-ref="cityPopoverReference"
          virtual-triggering
          width="621px"
        >
          <!--城市列表容器-->
          <template v-if="provincePopover">
            <div class="tw-border-b-[#E1E3E6] tw-border-b-[1px] tw-pb-[12px] ">
              <!--城市名称-->
              <span class="tw-mr-[12px] tw-text-[15px] tw-font-[600] tw-text-[#313233]">{{ provincePopover?.split(',')[0] }}</span>
              <!--城市全选清空按钮-->
              <template v-if="!props.readonly">
                <el-button type="primary" link @click="selectAllCities(provinceAllMap[provincePopover], provincePopover, 1)">全选</el-button>
                <el-button type="primary" link @click="selectAllCities(provinceAllMap[provincePopover], provincePopover, 0)">清空</el-button>
              </template>
            </div>
            <div>
              <!--遍历城市列表-->
              <el-checkbox-group
                :model-value="tempOperatorCities"
                :disabled="props.readonly"
                class="tw-grid tw-grid-cols-4 tw-py-[12px]"
              >
                <el-checkbox
                  v-for="city in provinceAllMap[provincePopover]"
                  class="tw-truncate tw-text-[#313233]"
                  :key="city.split(',')[1]"
                  :label="city.split(',')[1]"
                  @change="() => handleSelectCity(city, provincePopover!)"
                >{{ city.split(',')[0] }}</el-checkbox>
              </el-checkbox-group>
            </div>
          </template>
          <el-empty v-else></el-empty>
        </el-popover>
        </el-row>
        
      </template>
      <el-row v-else>
        <el-col :span="6">
          <span class="normal-box active-box tw-cursor-not-allowed tw-max-[200px]">全部</span>
        </el-col>
      </el-row>
  </div>
  <LoadCityNameOrAreaCodeDialog v-model:visible="loadVisible" :type="loadType" @confirm="confirmLoadCity"/>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch, shallowRef, shallowReactive, defineAsyncComponent, nextTick } from 'vue'
import { ArrowDown, Close, CloseBold } from '@element-plus/icons-vue'
import { OperatorEnum, OperatorObj, RestrictModal, ZhMap } from '@/type/common'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useGlobalStore } from '@/store/globalInfo'
import SvgIcon from '@/components/SvgIcon.vue'
import yd from '@/assets/img/移动.png'
import lt from '@/assets/img/联通.png'
import dx from '@/assets/img/电信.png'
import xn from '@/assets/img/虚拟.png'
import wz from '@/assets/img/未知.png'
import qb from '@/assets/img/全部.png'
import Confirm from '@/components/message-box'

// 动态引入组件
const LoadCityNameOrAreaCodeDialog = defineAsyncComponent(() => import('./LoadCityNameOrAreaCodeDialog.vue'))

const props = withDefaults(defineProps<{
  // 城市选择数据
  taskRestrictData: RestrictModal
  // 当前选择运营商的列表，传空数组就行，每次切换编辑需要将该值清空；如果readonly那该值会自动根据taskRestrictData调整；
  selectedOperatorList: (OperatorEnum | '全部')[]
  // 是否只读（禁止编辑）
  readonly?: boolean
  // 全部可选的运营商
  allOperatorList?: (OperatorEnum | '全部')[]
  // 是否包含未知
  needUnknown?: boolean
  // 是否支持通过城市名称或者城市区号导入
  loadByCity?: boolean
}>(), {
  readonly: false,
  allOperatorList: () => ['全部', ...Object.values(OperatorEnum)],
})
const emits = defineEmits([
  'update:data',
])
/** 常量 */
const enGrid = ['a', 'h', 's'] // 用于页面分列，共三列，控制每列起始省份字母

const unknownCode = '000000' // 针对非未知的运营商，添加未知code，用于对接无法获取明文号码的区域
// 全部运营商的图png
const imgMap = {
  '移动': yd,
  '联通': lt,
  '电信': dx,
  '全部': qb,
  '虚拟': xn,
  '未知': wz,
}

/** */
// 当前显示的运营商的名称
const activeOperator = shallowRef<(OperatorEnum | '全部')>(props.selectedOperatorList[0] || '全部')
// 全部可选择的运营商
const allOperatorList = ref<(OperatorEnum | '全部')[]>(props.allOperatorList)
// 组件维护的运营商省市数据
const taskRestrictData = shallowReactive<RestrictModal>(props.taskRestrictData)
// 当前运营商选中的省份
const tempOperatorProvinces = ref<string[]>([])
// 当前运营商选中的城市
const tempOperatorCities = ref<string[]>([])
// 已添加的运营商列表，在标签卡上展示的
const selectedOperatorList = ref<(OperatorEnum | '全部')[]>(props.selectedOperatorList || [])
// 可添加的运营商列表
const unselectedRestOperatorList = computed(() => {
  return allOperatorList.value.filter(v => !selectedOperatorList.value.includes(v as OperatorEnum | '全部')) as (OperatorEnum | '全部')[]
})

/** 全局变量 */
const globalStore = useGlobalStore()
const provinceList = computed(() => {
  if (!props.needUnknown) {
    return (globalStore.getProvinceList || []).filter(item => !item.includes(unknownCode))
  } else {
    return globalStore.getProvinceList || []
  }
})
  
// 变量-复制某运营商下的省份城市信息
const copyRestrictData = computed(() => {
  return globalStore.copyRestrictData
})
// 变量-复制全部运营商下的省份城市信息
const copyAllRestrictData = computed(() => {
  return globalStore.copyAllRestrictData
})
// 变量-全部省份城市Map
const provinceAllMap = reactive<{ [propName: string]: string[] }>({})
// 变量-全部城市list
const cityList = computed(() => {
  return Object.values(provinceAllMap)?.flat() || []
})

/** 功能函数 开始 */
// 导入区号
const loadVisible = ref(false)
const loadType = ref('cityName')
const loadCity = (type: 'cityName' | 'areaCode') => {
  loadType.value = type
  loadVisible.value = true
}
const confirmLoadCity = (cityCodes: string[], provincesCodes: string[]) => {
  tempOperatorCities.value = [...new Set([...tempOperatorCities.value, ...cityCodes])]
  tempOperatorProvinces.value = [...new Set([...tempOperatorProvinces.value, ...provincesCodes])]
}
// 复制当前运营商数据
const copy = () => {
  if (tempOperatorCities.value?.length > 0 && tempOperatorProvinces.value?.length > 0) {
    globalStore.copyRestrictAction(tempOperatorProvinces.value, tempOperatorCities.value)
    ElMessage({
      type: 'success',
      message: '复制成功'
    })
  } else {
    ElMessage({
      type: 'warning',
      message: '复制信息为空'
    })
  }
}
// 粘贴至当前运营商
const paste = () => {
  ElMessageBox.confirm(
    `确认要粘贴【${copyRestrictData.value.time}】的${copyRestrictData.value.province.length}省${copyRestrictData.value.city.length}市数据`,
    '粘贴确认',
    {
      confirmButtonText: '确认粘贴',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 如果复制存在未知，通过props.needUnknown进行判断
    if (!props.needUnknown) {
      tempOperatorProvinces.value = copyRestrictData.value.province?.filter(item => !item.includes(unknownCode))
      tempOperatorCities.value = copyRestrictData.value.city?.filter(item => !item.includes(unknownCode))
    } else {
      tempOperatorProvinces.value = copyRestrictData.value.province
      tempOperatorCities.value = copyRestrictData.value.city
    }
    ElMessage({
      type: 'success',
      message: '粘贴成功',
    })
  }).catch(() => {
    ElMessage({
      type: 'warning',
      message: '粘贴失败',
    })
  })
}
// 复制全部运营商数据
const copyAll = () => {
  globalStore.copyAllRestrictAction(taskRestrictData)
  ElMessage({
    type: 'success',
    message: `复制成功`,
  })
}
// 粘贴至全部运营商数据
const pasteAll = () => {
  ElMessageBox.confirm(
    `确认要粘贴来自【${copyAllRestrictData.value.time}】创建的信息`,
    '粘贴确认',
    {
      confirmButtonText: '确认粘贴',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    Object.assign(taskRestrictData, copyAllRestrictData.value)
    selectedOperatorList.value = []
    emits('update:data', taskRestrictData, selectedOperatorList.value)
    ElMessage({
      type: 'success',
      message: '粘贴成功',
    })
  }).catch(() => {
    ElMessage({
      type: 'warning',
      message: '粘贴失败',
    })
  })
}
/** 增加运营商tab操作 */
const addTabs = (targetName: OperatorEnum | '全部') => {
  // 如果标签卡已经存在，不会添加
  if (selectedOperatorList.value.includes(targetName)) {
    return
  }
  if (selectedOperatorList.value.includes('全部') || targetName === '全部') {
    Confirm({
      text: `您确定要切换至【${targetName}】，您之前的选项将不再被保留`,
      type: 'warning',
      title: '更改确认'
    }).then(() => {
      Object.assign(taskRestrictData, {
        allRestrictProvince: null,
        allRestrictCity: null,
        ydRestrictProvince: null,
        ydRestrictCity: null,
        ltRestrictProvince:  null,
        ltRestrictCity:  null,
        dxRestrictCity: null,
        dxRestrictProvince: null,
        virtualRestrictCity: null,
        virtualRestrictProvince: null,
        unknownRestrictCity: null,
        unknownRestrictProvince: null,
      })
      selectedOperatorList.value = [targetName]
      activeOperator.value = targetName
    }).catch(() => {}).finally(() => {
      if (targetName === OperatorEnum['未知']) {
        selectAllData(true)
      }
      emits('update:data', taskRestrictData, selectedOperatorList.value)
    })
  } else {
    selectedOperatorList.value.push(targetName)
    activeOperator.value = targetName
    if (targetName === OperatorEnum['未知']) {
      selectAllData(true)
    }
    emits('update:data', taskRestrictData, selectedOperatorList.value)
  }
  
}
/** 删除运营商tab操作 */
const removeTab = (targetName: OperatorEnum | '全部') => {
  if (!selectedOperatorList.value || selectedOperatorList.value.length <= 1) {
    return ElMessage({
      type: 'warning',
      message: '请勿删除所有运营商'
    })
  }
  selectedOperatorList.value = selectedOperatorList.value.filter(item => item != targetName)
  if (activeOperator.value == targetName) {
    activeOperator.value = selectedOperatorList.value[0] || undefined
  }
  taskRestrictData[OperatorObj[targetName].province as keyof RestrictModal] = null
  taskRestrictData[OperatorObj[targetName].city as keyof RestrictModal] = null
  emits('update:data', taskRestrictData, selectedOperatorList.value)
}
/** 删除全部运营商tab操作 */
const removeAllTab = () => {
  Object.keys(OperatorObj).map(item => {
    if (activeOperator.value === item) {
      taskRestrictData[OperatorObj[item].province as keyof RestrictModal] = null
      taskRestrictData[OperatorObj[item].city as keyof RestrictModal] = null
    } else {
      if (!!OperatorObj[item as OperatorEnum | '全部']) {
        taskRestrictData[OperatorObj[item as OperatorEnum | '全部'].province as keyof RestrictModal] = null
        taskRestrictData[OperatorObj[item as OperatorEnum | '全部'].city as keyof RestrictModal] = null
      }
    }
  })
  selectedOperatorList.value = [activeOperator.value]
  tempOperatorProvinces.value = []
  tempOperatorCities.value = []
  emits('update:data', taskRestrictData, selectedOperatorList.value)
}
/**
 * 点击某个城市
 * @param city 城市信息
 * @param province 省份信息
 */
const handleSelectCity = (city: string, province: string) => {
  // 如果禁止编辑，则不响应点击
  if (props.readonly) {
    return
  }
  const code = city.split(',')[1].trim()
  // 检验代码是否非空
  if (!code) return
  // 判断该城市是否已经选中
  const index = tempOperatorCities.value.indexOf(code)
  if (index >= 0) {
    // 已经选中，本次点击应从选中的城市列表中移除
    tempOperatorCities.value.splice(index, 1)
    // 如果当前省份所有城市在选中的城市列表里全都找不到
    if (!provinceAllMap[province].find(c => tempOperatorCities.value.includes(c.split(',')[1]))) {
      // 则从选中的省份列表中移除当前省份
      tempOperatorProvinces.value = tempOperatorProvinces.value.filter(item => item != province.split(',')[1])
    }
  } else {
    // 没有选中，本次点击将该城市加入到选中的城市列表中
    tempOperatorCities.value.push(code)
    // 将该省份加入到选中的省份列表中，并且用Set做数组去重，保证省份唯一不重复
    tempOperatorProvinces.value = [...new Set([...tempOperatorProvinces.value, province.split(',')[1]])]
  }
}
/**
 * 选中当前省份的所有城市（省内全选）
 * @param provinces 当前省份的城市列表信息
 * @param province 当前省份信息
 * @param type 操作类型 0 清空 1 全选
 */
const selectAllCities = (provinces: string[], province: string, type: number) => {
  // 如果禁止编辑，则不响应点击
  if (props.readonly) {
    return
  }

  // 当前省份所有城市的行政区划代码列表
  const provinceCodes = provinces.map(item => item.split(',')[1])
  if (type === 0) { // 0: 清空, 1: 全选
    tempOperatorCities.value = tempOperatorCities.value.filter(item => !provinceCodes.includes(item))
    tempOperatorProvinces.value = tempOperatorProvinces.value.filter(item => item != province.split(',')[1])
  } else {
    // 将当前省份和它的所有城市添加到对应的选中列表里，并做数组去重
    tempOperatorCities.value = [...new Set([...tempOperatorCities.value, ...provinceCodes])]
    tempOperatorProvinces.value = [...new Set([...tempOperatorProvinces.value, province.split(',')[1]])]
  }
}

/**
 * 选中运营商下的未知，仅props.needUnknown为true生效
 */
const selectUnknown = () => {
  if (props.readonly || !props.needUnknown) {
    return
  }
  if (tempOperatorProvinces.value.includes(unknownCode)) {
    tempOperatorCities.value = tempOperatorCities.value.filter(item => item != unknownCode)
    tempOperatorProvinces.value = tempOperatorProvinces.value.filter(item => item != unknownCode)
  } else {
    tempOperatorCities.value = [...new Set([...tempOperatorCities.value, unknownCode])]
    tempOperatorProvinces.value = [...new Set([...tempOperatorProvinces.value, unknownCode])]
  }
}

/** 当前省份，触发下拉城市列表 */
const cityPopoverVisible = ref(false)
const provincePopover = ref<null | string>(null)
const cityPopoverReference = ref()
const showPopover = (event: PointerEvent, province : string) => {
  if (cityPopoverVisible.value) {
    return (cityPopoverVisible.value = false)
  }
  provincePopover.value = province;
  cityPopoverVisible.value = true
  nextTick(() => {
    cityPopoverReference.value = event.target
  });
}

/** 当前标签卡，全选 */
const selectAllData = (isSelectAll?: boolean) => {
  if (!isSelectAll && tempOperatorCities.value.length === cityList.value.length && tempOperatorProvinces.value.length === provinceList.value.length) {
    tempOperatorProvinces.value = []
    tempOperatorCities.value = []
  } else {
    tempOperatorProvinces.value = provinceList.value.map(item => item.split(',')[1])
    tempOperatorCities.value = cityList.value.map(item => item.split(',')[1])
  }
}
/** 当前标签卡，反选 */
const reverseSelectData = () => {
  // 当前选中的省份行政区划代码Set
  // const selectedProvinceSet = new Set(...tempOperatorProvinces.value)
  // 当前选中的城市行政区划代码Set
  // console.log('tempOperatorCities.value', tempOperatorCities.value)
  const selectedCitySet = new Set(tempOperatorCities.value)
  // console.log('selectedCitySet', selectedCitySet)

  // 一、反选省份
  // 注意三种情况
  // 1. 该省全选，反选后移除该省
  // 2. 该省空白，反选后添加该省
  // 1. 该省部分选中，反选后保持不变（也可以添加进Set自动去重）
  // 将所有省份的行政区划代码放进Set里
  const provinceSet = new Set(provinceList.value.map(item => item.split(',')[1]))
  // 遍历省份
  Object.keys(provinceAllMap).forEach((province) => {
    // 省份行政区划代码
    const provinceCode = province.split(',')[1]
    // console.log('province', province, provinceAllMap[province])
    // 当前省份的城市数
    const cityTotal = provinceAllMap[province].length
    // 该省份里选中的城市数
    let citySelectedCount = 0
    // 只有当前省份里所有城市都能在选中的城市列表里匹配到，才认为这个省份是全选
    provinceAllMap[province].forEach((city) => {
      // 城市行政区划代码
      const cityCode = city.split(',')[1]
      // 该城市选中，则计数递增
      if (selectedCitySet.has(cityCode)) {
        citySelectedCount++
      }
    })
    // console.log('citySelectedCount', citySelectedCount)
    // 开始判断城市选中数和总数的三种情况
    if (citySelectedCount >= cityTotal) {
      // 全选
      // 移除该省份
      provinceSet.delete(provinceCode)
    } else {
      // 空白或者部分选中
      // 添加该省份
      provinceSet.add(provinceCode)
    }
  })
  // 省份遍历完，得到应该选中的省份Set
  // 更新选中列表
  // console.log('provinceSet finish', provinceSet)
  tempOperatorProvinces.value = [...provinceSet]

  // 二、反选城市
  // 将所有城市的行政区划代码放进Set里
  const citySet = new Set(cityList.value.map(item => item.split(',')[1]))
  // console.log('citySet', citySet)
  // 选中城市Set和所有城市Set取差集
  const tempSet = new Set<string>()
  citySet.forEach((item) => {
    !selectedCitySet.has(item) && tempSet.add(item)
  })
  // console.log('tempSet', tempSet)
  // 将Set更新到选中的城市列表
  tempOperatorCities.value = [...tempSet]
}
/** 功能函数 结束 */

/** 显示函数 开始 */
// 通过首字母过滤省份
const filterProvinceByEn = (item: {
  en: string, min: string, max: string | null
}) => {
  const list = Object.keys(provinceAllMap)?.filter(item => !item.includes(unknownCode)) || []
  const res = list.filter(v => v.localeCompare(item.min) >= 0 && (!item.max || v.localeCompare(item.max) < 0))
  return res && res.length > 0 ? res : null
}
// 将运营商tab显示为 运营商x省x市
const translateTabName = (item: OperatorEnum | '全部') => {
  const provinceStr = taskRestrictData[OperatorObj[item].province as keyof RestrictModal]
  const provinceNum = provinceStr && provinceStr.trim() ? provinceStr.trim().split(',').length : 0
  const cityStr = taskRestrictData[OperatorObj[item].city as keyof RestrictModal]
  const cityNum = cityStr && cityStr.trim() ? cityStr.trim().split(',').length : 0
  return provinceNum && cityNum ? `（${provinceNum}省 ${cityNum}市）` : '0省 0市'
}
// 将省份名称显示格式化
const translateProvinceName = (name: string) => {
  if (name.length <= 4){
    return name.replace(/[省市]/, '')
  } else {
    return name.slice(0, 2)
  }
}
// 获取当前省份已选城市数量
const getProvinceSelectedNum = (province: string) => {
  let provinceNum: number = 0
  provinceAllMap[province] && provinceAllMap[province].map(city => {
    if (tempOperatorCities.value.includes(city.split(',')[1])) {
      provinceNum++
    }
  })
  return provinceNum > 0 ? `(${provinceNum === provinceAllMap[province].length ? '全' : provinceNum})` : ''
}
/** 显示函数 结束 */

watch([tempOperatorProvinces, tempOperatorCities], () => {
  taskRestrictData[OperatorObj[activeOperator.value].province as keyof RestrictModal] = tempOperatorProvinces.value.join(',')
  taskRestrictData[OperatorObj[activeOperator.value].city as keyof RestrictModal] = tempOperatorCities.value.join(',')
  emits('update:data', taskRestrictData, selectedOperatorList.value)
}, {
  deep: true
})
watch(() => activeOperator.value, () => {
  const str = taskRestrictData[OperatorObj[activeOperator.value].city as keyof RestrictModal]
  if (str) {
    const str2 = taskRestrictData[OperatorObj[activeOperator.value].province as keyof RestrictModal]
    tempOperatorProvinces.value = str2 ? str2.split(',') : []
    tempOperatorCities.value = str ? str.split(',') : []
  } else {
    if (activeOperator.value !== OperatorEnum['未知']) {
      tempOperatorProvinces.value = []
      tempOperatorCities.value = []
    }
  }
}, {
  immediate: true,
  deep: true
})
/** 初始换函数-用于获取全部省份城市Map */
const init = async () => {
  // 获取全部省份城市Map
  const data = await globalStore.getProvinceInfo()
  Object.assign(provinceAllMap, data)
  if (!props.needUnknown) {
    delete provinceAllMap['未知,000000']
  }
  
  // 获取入参
  selectedOperatorList.value = props.selectedOperatorList || []
  allOperatorList.value = props.allOperatorList || []
  if (selectedOperatorList.value?.length > 0 && !props.readonly) {
    activeOperator.value = (activeOperator.value &&selectedOperatorList.value.includes(activeOperator.value)) ? activeOperator.value : selectedOperatorList.value.at(-1)!
  } else {
    // 读取已选择省份城市的数据
    selectedOperatorList.value = []
    Object.values(OperatorObj).map(item => {
      if (taskRestrictData[item.province as keyof RestrictModal] && taskRestrictData[item.city as keyof RestrictModal]) {
        selectedOperatorList.value.push(item.name as OperatorEnum | '全部')
      }
    })
    selectedOperatorList.value = selectedOperatorList.value?.length > 0 ? selectedOperatorList.value : ['全部']
    activeOperator.value = selectedOperatorList.value[0]
  }
  const str = taskRestrictData[OperatorObj[activeOperator.value]?.city as keyof RestrictModal] || ''
  if (str) {
    const str2 = taskRestrictData[OperatorObj[activeOperator.value].province as keyof RestrictModal]
    tempOperatorProvinces.value = str2 ? str2.split(',') : []
    tempOperatorCities.value = str ? str.split(',') : []
  } else {
    tempOperatorProvinces.value = []
    tempOperatorCities.value =[]
    if(activeOperator.value === OperatorEnum['未知']) {
      selectAllData(true)
    }
  }
}
watch([() => props.taskRestrictData, () => props.readonly], () => {
  init()
}, {
  deep: true,
  immediate: true
})


// 将组件的属性和方法暴露出去，给其他组件调用
defineExpose({
  activeOperator,
  selectedOperatorList,
  addTabs,
  removeTab,
  selectAllData,
})
</script>

<style lang="postcss">
.block-setting-container {
  width: 100%;
  min-width: 910px;
  position: relative;
  .el-button [class*=el-icon]+span {
    margin-left: 3px;
  }
  .block-tab-box {
    width: 106px;
    height: 64px;
    box-sizing: border-box;
    border: 1px solid #E1E3E6;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    border-radius: 2px 2px 0 0;
    position: relative;
    margin-right: 8px;
    color: #969799;
    background-color: #fff;
    padding: 0;
    .close-btn {
      position: absolute;
      cursor: pointer;
      top: 0;
      right: 0;
      border-radius: 0 2px 0 0;
      background-color: #C8C9CC;
    }
  }
  .active-block-tab-box {
    border-bottom-color: #fff;
    color: var(--el-color-primary);
    border-top: 2px solid var(--el-color-primary);
  }
  .normal-box {
    font-size: 13px;
    line-height: 30px;
    height: 30px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #969799;
    width: 70%;
    flex-grow: 1;
    padding: 0;
    border-radius: 4px;
    margin: 0 5% 24px;
    border: 1px solid #C8C9CC;
    transition: width 0.3s;
    &.unknown {
      cursor: pointer;
      margin-left: 16px;
      text-align: left;
      justify-content: flex-start;
      padding-left: 12px;
    }
    .province-text {
      padding-left: 8px;
      flex-grow: 1;
      flex-shrink: 10;
      text-align: left;
      overflow-x: hidden;
      white-space: nowrap;
    }
    .el-icon {
      width: 26px;
      padding-right: 8px;
      height: 100%;
      flex-grow: 0;
      flex-shrink: 0;
    }
  }
  .active-box {
    color: var(--el-color-primary);
    width: 90%;
    background-color: #ecf5ff;
    border: 1px solid var(--el-color-primary);
    &.unknown {
      justify-content: center;
      padding-right: 12px;
    }
  }
}
.normal-city {
  font-size: 14px;
  line-height: 30px;
  box-sizing: border-box;
  height: 30px;
  width: 50px;
  margin-right: 10px;
  border-radius: 10px;
  background-color: #fff;
  border: 1px solid #000;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
