import { CorpusConditionEnum, CorpusConditionItem, CorpusConditionSubItem } from '@/type/corpus'
import { findValueInEnum } from '@/utils/utils'

/**
 * 校验分支、语料中的满足/排除条件
 * @param data 条件数组
 * @param required 是否必要
 * @returns string | null, string表示校验不通过的错误内容，返回null表示校验通过，
 */
export const checkCorpusRules = (data?: CorpusConditionItem[], required: boolean = false) => {
  if (!data || !data?.length) {
    return required ? '请至少添加一个规则' : null;
  }
  let errMsg = ''
  data.some(item => {
    if (!item.semListPacks || item.semListPacks?.length < 1) {
      errMsg = '请至少添加一个规则';
      return true;
    }
    return item.semListPacks.some(subItem => {
      if (!subItem.semListPackType) {
        errMsg = '请选择规则类型';
        return true
      }
      if (!subItem.singlePhraseList || subItem.singlePhraseList?.length < 1) {
        errMsg = '请输入' + (subItem.semListPackType === CorpusConditionEnum['本句短语命中'] ? '命中补充短语' : '命中语义');
        return true;
      }
      // @ts-ignore
      if (subItem.semListPackType === CorpusConditionEnum['历史语义命中'] && isNaN(subItem.minNum) && isNaN(subItem.maxNum)) {
        errMsg = '请至少添加一个命中次数';
        return true;
      }
      return subItem.singlePhraseList.some(subSubItem => {
        if (!subSubItem.word && subItem.semListPackType === CorpusConditionEnum['本句短语命中']) {
          errMsg = '请输入命中短语';
          return true;
        } else if (!subSubItem.id && subItem.semListPackType !== CorpusConditionEnum['本句短语命中']){
          errMsg = '请输入命中补充语义';
          return true;
        } else {
          return false
        }
      })
    })
  })
  return errMsg || null
}


/** 翻译语料中的命中条件至列表 */
export const translateCorpusRules = (data: CorpusConditionItem[]) => {
  if (!data || data.length < 1) return [];
  const res: string[][] = []
  data.map(item => {
    const strArr: string[] = item.semListPacks?.map(subItem => {
      const str = subItem.singlePhraseList?.map(subSubItem => {
        if (subItem.semListPackType === CorpusConditionEnum['本句短语命中']) {
          return subSubItem.word
        } else {
          return subSubItem.semantic || ''
        }
      })?.join('、') || ''
      const minMaxStr = (typeof subItem.minNum == 'number' && typeof subItem.maxNum == 'number')
        ? `${subItem.minNum}~${subItem.maxNum}次`
        : typeof subItem.minNum == 'number' ? `≥${subItem.minNum}次` : typeof subItem.maxNum == 'number' ? `≤${subItem.maxNum}次` : ''
      return `${findValueInEnum(subItem.semListPackType, CorpusConditionEnum) || ''}：${str}${minMaxStr}`
    })
    res.push(strArr)
    return
  })
  return res
}
