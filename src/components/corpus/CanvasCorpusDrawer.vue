<template>
  <el-drawer
    v-model="dialogVisible"
    class="tw-z-[100] dialog-form"
    :size="drawerWidth"
    with-header
    :before-close="cancel"
    :close-on-click-modal="isChecked"
  >
    <template #header>
      <div class="tw-flex tw-w-full tw-items-center tw-h-[48px]">
        <span class="tw-shrink-0 tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ findValueInEnum(addData.corpusType, CorpusTypeEnum) }}</span>
      </div>
    </template>
    <el-scrollbar
      ref="scrollRef"
      :max-height="'calc(100vh - 80px)'"
      wrap-class="tw-p-[12px] tw-bg-[#f2f3f5]"
      view-class="tw-text-[13px]"
    >
      <el-form
        :model="addData"
        :rules="rules"
        label-width="90px"
        ref="editRef"
        :disabled="isChecked"
        scroll-to-error
        :scroll-into-view-options="{ behavior: 'smooth', block: 'center' }"
      >
        <div class="tw-my-[6px] tw-flex tw-justify-between">
          <div class="tw-font-[600] tw-text-left  tw-text-[14px]">
            基础设置
          </div>
          <el-button v-if="!isChecked && addData.corpusType && [CorpusTypeEnum['主动流程-普通语料'], CorpusTypeEnum['主动流程-连接语料']].includes(addData.corpusType)" link type="primary" @click="loadFromCorpus">导入已有语料</el-button>
        </div>
        <div class="tw-bg-white tw-px-[12px] tw-py-[8px] tw-rounded-[4px] tw-mb-[12px]">
          <el-form-item label="语料名称：" prop="name">
            <el-input v-model.trim="addData.name" clearable placeholder="请输入语料名称（40字以内）" maxlength="40"/>
          </el-form-item>
          <el-form-item v-if="isConnect" label="连接到：" prop="connectType">
            <el-select v-model="addData.connectType" placeholder="请选择连接到" style="width:100%">
              <el-option
                v-for="item in connectTypeOptions"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-if="addData.connectType === ConnectTypeEnum['指定主动流程']" label="主动流程：" prop="connectCorpusId">
            <el-select
              v-model="addData.connectCorpusId"
              placeholder="选择要连接到的主动流程"
              style="width:100%;"
            >
              <el-option
                v-for="item in masterProcessOptions"
                :key="item.id"
                :label="currentCanvas?.id === item.id ? `${item.name}（当前流程）` : item.name "
                :value="item.headCorpusId"
              />
            </el-select>
          </el-form-item>
          <template v-if="addData.isHead && addData.corpusType === CorpusTypeEnum['深层沟通-普通语料']">
            <el-form-item v-if="dialogVisible && addData.semCombineEntity?.satisfySemConditions" label-width="0" prop="satisfySemConditions">
              <MultCorpusRuleBox
                v-model:data="addData.semCombineEntity.satisfySemConditions"
                :readonly="isChecked"
                required
                title="满足条件"
              />
            </el-form-item>
            <el-form-item v-if="dialogVisible && addData.semCombineEntity?.excludeSemConditions" label-width="0" prop="excludeSemConditions">
              <MultCorpusRuleBox
                v-model:data="addData.semCombineEntity.excludeSemConditions"
                :readonly="isChecked"
                title="排除条件"
              />
            </el-form-item>
          </template>
          <!-- 文字内容
           【v1.0的普通语料、挂机语料】-------- 中仅展示一个输入框
           【v2.0中非挂机预料】------ 展示多个输入框
           【连接非挂机语料】 -------- 无文字内容
           -->
          <el-form-item v-if="(!multiContentVersion && isNormal) || (isConnect && addData.connectType === ConnectTypeEnum['挂机'])" label="文字内容：" prop="scriptMultiContents">
            <el-input v-if="addData.scriptMultiContents && addData.scriptMultiContents[0]?.scriptUnitContents[0]" v-model="addData.scriptMultiContents[0].scriptUnitContents[0].content" type="textarea" :autosize="{minRows: 3, maxRows: 6}" placeholder="请输入语料的文本内容300字以内）" clearable maxlength="300"/>
          </el-form-item>
          <el-form-item v-if="isNormal && !!multiContentVersion" label="文字内容：" prop="scriptMultiContents">
            <div v-if="addData.scriptMultiContents?.length" class="tw-w-full" v-for="(item, index) in addData.scriptMultiContents || []" :key="index">
              <EditableContent
                ref="editContentRef"
                :editable="!isChecked"
                closeable
                v-model:needUpdate="needUpdate"
                :corpus="addData"
                :maxLength="300"
                v-model:content="addData.scriptMultiContents[index].scriptUnitContents"
              />
            </div>
          </el-form-item>
          <!-- 客户回复 -->
          <el-form-item v-if="isNormal"  label="客户回答：" prop="branchList">
            <el-select v-model="isUnifyResponse" placeholder="选择客户回答类型" style="width:100%;" @change="handleSelectBranchType">
              <el-option key="统一回复" label="统一回复" :value="true"></el-option>
              <el-option key="自定义" label="自定义" :value="false"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="isNormal" label="语境类型：" prop="isOpenContext">
            <div class="tw-flex tw-flex-col tw-items-start tw-w-full">
              <el-radio-group v-model="addData.isOpenContext" :disabled="isUnifyResponse" class="tw-ml-[6px]">
                <el-radio :label="true">开放</el-radio>
                <el-radio :label="false">封闭</el-radio>
              </el-radio-group>
              <div v-if="!!addData.isOpenContext" class="tw-flex tw-items-center">
                <span class="tw-w-[80px] tw-shrink-0 tw-grow-0 tw-text-right">开放范围：</span>
                <el-radio-group v-model="addData.openScopeType" class="tw-ml-[6px]">
                  <el-radio :label="OpenScopeTypeEnum['全部']">全部</el-radio>
                  <el-radio :label="OpenScopeTypeEnum['自定义']">自定义</el-radio>
                </el-radio-group>
              </div>
              <el-form-item v-if="!!addData.isOpenContext && addData.openScopeType === OpenScopeTypeEnum['自定义']" class="tw-w-[600px]" label-width="80px" label="选择分组：" prop="groupOpenScope">
                <SelectBox 
                  v-model:selectVal="addData.groupOpenScope"
                  :options="groupOptions"
                  name="name"
                  val="id"
                  placeholder="请选择知识库分组"
                  filterable
                  class="tw-grow"
                  canSelectAll
                  multiple
                >
                </SelectBox>
                <el-button class="tw-ml-[4px]" link type="primary" @click="copyCanvasOpenScope">复用{{addData.corpusType === CorpusTypeEnum['深层沟通-普通语料'] ? '深层沟通' : '主动流程'}}</el-button>
              </el-form-item>
            </div>
          </el-form-item>
          <el-form-item v-show="isNormal && !isUnifyResponse"  label="自定义支线：">
            <div class="tw-w-full tw-justify-between tw-flex tw-items-center">
              <div>
                <el-checkbox class="tw-ml-[6px]" label="沉默" :model-value="!!silenceBranchPrior?.name" @change="handleSelectSilence">沉默</el-checkbox>
                <el-checkbox label="未命中" :model-value="true" disabled>未命中</el-checkbox>
              </div>
              
              <div v-if="!isChecked">
                <el-button type="primary" :icon="Plus" link @click="copyBranch()">复制普通分支</el-button>
                <el-button type="primary" :icon="Plus" link @click="editBranch(CorpusPriorTypeEnum['普通分支'])">新增普通分支</el-button>
                <el-button type="primary" :icon="Plus" link @click="editBranch(CorpusPriorTypeEnum['查询分支'])">新增查询分支</el-button>
                <el-button type="danger" link @click="deleteAllBranch()">
                  <el-icon :size="16" color="inherit"><SvgIcon name="delete"></SvgIcon></el-icon>
                  <span>清空分支</span>
                </el-button>
              </div> 
            </div>
          </el-form-item>
          
          <div v-show="isNormal && !isUnifyResponse" class="tw-w-full tw-text-[var(--el-font-size-base)]">
            <!-- <div>优先顺序：</div> -->
            <el-row class="tw-font-[600] tw-bg-[#f7f8fa] tw-text-[#626366] tw-py-[2px] tw-pr-[6px] tw-border-y-[1px] tw-border-b-[#e5e7eb]">
              <el-col :span="3">优先级</el-col>
              <el-col :span="10" class="tw-text-left">名称</el-col>
              <el-col :span="4" class="tw-justify-center">类型</el-col>
              <el-col :span="4" class="tw-justify-center">返回排除</el-col>
              <el-col :span="3" class="tw-justify-end">操作</el-col>
            </el-row>

            <!-- 查询分支，支持排序，不支持返回排除 -->
            <div class="info-sort-box">
              <el-row v-for="(row, $index) in queryBranchPriorList" class="tw-py-[4px] tw-border-b-[1px] tw-border-b-[#e5e7eb]">
                <el-col :span="3">
                  <div class="handle tw-cursor-pointer">
                    <el-icon><SvgIcon name="drag" color="var(--primary-black-color-400)"/></el-icon>
                  </div>
                </el-col>
                <el-col :span="10" class="tw-flex tw-justify-start">
                  <div class="tw-cursor-pointer" @click="editBranch(CorpusPriorTypeEnum['查询分支'] , $index, true)">{{ row.name }}</div>
                </el-col>
                <el-col :span="4">
                  <span class="status-box-mini" :class="row.color ? row.color + '-status': 'blue-status'">查询分支</span>
                </el-col>
                <el-col :span="4" class="tw-justify-center">-</el-col>
                <el-col :span="3" class="tw-justify-end">
                  <el-button v-if="!isChecked" type="primary" link @click="editBranch(CorpusPriorTypeEnum['查询分支'] ,$index)">编辑</el-button>
                  <span v-else class="tw-cursor-pointer tw-text-[var(--el-color-primary)] tw-mx-[10px]" @click="editBranch(CorpusPriorTypeEnum['查询分支'] , $index, true)">查看</span>
                  <el-button :type="!isChecked ? 'danger':'default'" :disabled="isChecked" link @click="delBranch(CorpusPriorTypeEnum['查询分支'] ,$index)">删除</el-button>
                </el-col>
              </el-row>
            </div>

             <!-- 普通分支 + 知识库分组，支持排序+返回排除 -->
             <div class="normal-sort-box">
              <el-row v-for="(row, $index) in generalBranchAndGroupPriorList" class="tw-py-[4px] tw-border-b-[1px] tw-border-b-[#e5e7eb]">
                <el-col :span="3">
                  <div class="handle tw-cursor-pointer">
                    <el-icon><SvgIcon name="drag" color="var(--primary-black-color-400)"/></el-icon>
                  </div>
                </el-col>
                <el-col :span="10" class="tw-flex tw-justify-start">
                  <div v-if="row.type === CorpusPriorTypeEnum['知识库']">{{ row.name }}</div>
                  <div v-if="row.type === CorpusPriorTypeEnum['普通分支']"  class="tw-cursor-pointer" @click="editBranch(CorpusPriorTypeEnum['普通分支'] ,$index, true)">{{ row.name }}</div>
                </el-col>
                <el-col :span="4" class="tw-flex tw-justify-center">
                  <span v-if="row.type === CorpusPriorTypeEnum['知识库']" class="status-box-mini green-status">
                    知识库
                  </span>
                  <span v-else-if="row.type === CorpusPriorTypeEnum['普通分支']" class="status-box-mini blue-status">
                    普通分支
                  </span>
                  <span v-else>-</span>
                </el-col>
                <el-col :span="4" class="tw-justify-center">
                  <el-switch
                    v-model="row.backExcluded"
                    inline-prompt
                    active-text="开启"
                    inactive-text="关闭"
                  />
                </el-col>
                <el-col :span="3" class="tw-justify-end">
                  <template v-if="row.type === CorpusPriorTypeEnum['普通分支']">
                    <el-button v-if="!isChecked" type="primary" link @click="editBranch(CorpusPriorTypeEnum['普通分支'] ,$index)">编辑</el-button>
                    <span v-else class="tw-cursor-pointer tw-text-[var(--el-color-primary)] tw-mx-[10px]" @click="editBranch(CorpusPriorTypeEnum['普通分支'] ,$index)">查看</span>
                    <el-button :type="!isChecked ? 'danger':'default'" :disabled="isChecked" link @click="delBranch(CorpusPriorTypeEnum['普通分支'] ,$index)">删除</el-button>
                  </template>
                  <template v-else>-</template>
                </el-col>
              </el-row>
            </div>
            <!-- 沉默，仅支持沉默删除，排序、返回排除均不支持 -->
            <el-row v-if="silenceBranchPrior" class="tw-py-[4px] tw-border-b-[1px] tw-border-b-[#e5e7eb]">
              <el-col :span="3">
                <div class="handle tw-cursor-not-allowed">
                  <el-icon><SvgIcon name="drag2" color="var(--primary-black-color-200)"/></el-icon>
                </div>
              </el-col>
              <el-col :span="10" class="tw-flex tw-justify-start"><div class="tw-truncate">{{ silenceBranchPrior.name }}</div></el-col>
              <el-col :span="4"><span class="status-box-mini gray-status">沉默分支</span></el-col>
              <el-col :span="4" class="tw-justify-center">-</el-col>
              <el-col :span="3" class="tw-justify-end">
                <el-button type="default" disabled link>编辑</el-button>
                <el-button :type="!isChecked ? 'danger':'default'" link @click="delBranch(CorpusPriorTypeEnum['沉默'], 0)">删除</el-button>
              </el-col>
            </el-row>
            <!-- 未命中，不支持操作 -->
            <el-row v-if="missBranchPrior" class="tw-py-[4px] tw-border-b-[1px] tw-border-b-[#e5e7eb]">
              <el-col :span="3">
                <div class="handle tw-cursor-not-allowed">
                  <el-icon><SvgIcon name="drag2" color="var(--primary-black-color-200)"/></el-icon>
                </div>
              </el-col>
              <el-col :span="10" class="tw-flex tw-justify-start"><div class="tw-truncate">{{ missBranchPrior.name }}</div></el-col>
              <el-col :span="4"><span class="status-box-mini gray-status">未命中</span></el-col>
              <el-col :span="4" class="tw-justify-center">-</el-col>
              <el-col :span="3" class="tw-justify-end">
                <el-button type="default" disabled link>编辑</el-button>
                <el-button type="default" disabled link>删除</el-button>
              </el-col>
            </el-row>
          </div>
        </div>
        <CorpusOtherSettings :corpusData="addData" @update:corpusData="handleCorpusOtherDataChange"></CorpusOtherSettings>
      </el-form>
    </el-scrollbar>
    
    <template #footer>
      <div >
        <div v-if="isNormal || (!isNormal && addData.connectType === ConnectTypeEnum['挂机'])" class="tw-float-left">
          <el-button :loading="loading" type="primary" @click="confirm(true)">{{isChecked ? '查看' : ''}}打断设置</el-button>
        </div>
        <div class="tw-float-right">
          <el-button @click="cancel" :icon="CloseBold">{{isChecked ? '关闭' : '取消'}}</el-button>
          <el-button v-if="!isChecked" :loading="loading" type="primary" :icon="Select" @click="confirm(false)">确定</el-button>
        </div>
      </div>
    </template>
    <BranchEditDialog
      v-model:visible="branchEditDialogVisible"
      :readonly="branchEditDialogReadonly"
      :branchData="branchData"
      @confirm="confirmBranch"
    ></BranchEditDialog>
    <CopyBranchDialog v-model:visible="copyBranchVisible" :corpusId="addData.id!!" @confirm="confirmCopyBranch"/>
    <LoadCorpusDialog
      v-model:visible="loadVisible"
      :data="addData!!"
      @confirm="confirmLoadCorpus"
    />
    <CorpusContentSettingDrawer v-model:visible="corpusContentVisible" :corpusId="corpusId!" @close="handleCloseOuter" fromEdit/>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, nextTick, toRaw, defineAsyncComponent, } from 'vue'
import Sortable from "sortablejs";
import { ScriptCorpusItem, ScriptBranch, BranchDataOrigin, 
  CorpusPriorItem, InfoQueryValueItem, ScriptCorpusOtherItem, CorpusPriorTypeEnum,
  corpusTypeOption, CorpusTypeEnum, ConnectTypeEnum, OpenScopeTypeEnum,
} from '@/type/speech-craft'
import { ElMessage, } from 'element-plus'
import { pickAttrFromObj, findValueInEnum, enum2Options } from '@/utils/utils'
import { handleCorpusMultiContent, compareCorpusMultiContent } from '@/utils/script'
import type { FormInstance, } from 'element-plus'
import { scriptCorpusModel, scriptCanvasModel, } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import { CloseBold, Select, Plus } from '@element-plus/icons-vue'
import SelectBox from '@/components/SelectBox.vue'
import to from 'await-to-js';
import { checkCorpusRules } from './constant'
import { trace } from '@/utils/trace';

const BranchEditDialog = defineAsyncComponent({ loader:() => { return import('@/components/corpus/BranchEditDialog.vue')}})
const CorpusOtherSettings = defineAsyncComponent({ loader:() => { return import('./CorpusOtherSettings.vue')}})
const EditableContent = defineAsyncComponent({loader: () => import('@/components/editable-content/Index.vue')})
const CorpusContentSettingDrawer = defineAsyncComponent({loader: () => import('./CorpusContentSettingDrawer.vue')})
const MultCorpusRuleBox = defineAsyncComponent({ loader:() => { return import('@/components/corpus/MultCorpusConditionBox.vue')}})
const CopyBranchDialog = defineAsyncComponent({ loader:() => { return import('@/components/corpus/CopyBranchDialog.vue')}})
const LoadCorpusDialog  = defineAsyncComponent({ loader:() => { return import('@/components/corpus/LoadCorpusDialog.vue')}})

// props & emits
const props = defineProps<{
  visible: boolean;
  corpusData: ScriptCorpusItem;
}>();
const emits = defineEmits(['update:data', 'update:visible'])

const drawerWidth = ref(window.innerWidth > 1400 ? '70%' : '900px')



/** 常量 */
const scriptStore = useScriptStore()
const editId = scriptStore.id
const currentCanvas = scriptStore.currentCanvas
const isChecked = scriptStore.isChecked
const multiContentVersion = scriptStore.multiContentVersion

/** 变量 */
const loading = ref(false)
const dialogVisible = ref(props.visible)
const addData = reactive<ScriptCorpusItem>(props.corpusData)
const editRef = ref<FormInstance  | null>(null)


// 选项信息
const groupOptions = ref<{
  id: number,
  name: string
}[] | null>([])
const masterProcessOptions = ref<{
  id: number,
  name: string,
  headCorpusId: number,
}[]>([]) // 主流程列表
const infoQueryKeyMap = ref<Map<string, string>>(new Map())
const infoQueryValueMap = ref<Map<string, InfoQueryValueItem>>(new Map())
// 连接到列表
const connectTypeOptions = computed(() => Object.values(enum2Options(ConnectTypeEnum)).filter(item => {
  const arr = [ConnectTypeEnum['挂机'], ConnectTypeEnum['指定主动流程']]
  if (addData.corpusType?.includes('KNOWLEDGE_')) {
    return [...arr, ConnectTypeEnum['回原主动流程']].includes(item.value)
  } else if (addData.corpusType?.includes('MASTER_')){
    return arr.includes(item.value)
  } else {
    return true
  }
}))
// 语料类型computed
const isNormal = computed(() => {
  return !!addData.corpusType?.includes('_ORDINARY')
})
const isConnect = computed(() => {
  return !!addData.corpusType?.includes('_CONNECT')
})

/** 处理函数开始 */

/** 从其他语料导入语料信息，但不会导入文字内容和语料名称 */
const loadVisible = ref(false)
const loadFromCorpus = () => {
  loadVisible.value = true
}
// 其他语料导入语料信息
const confirmLoadCorpus = (loadData: ScriptCorpusItem) => {
  if (!loadData) return ElMessage.warning('获取语料信息失败')
  // 其他设置可以直接通过Object.assign覆盖
  Object.assign(addData, pickAttrFromObj(loadData, isNormal.value ? [
    'isOpenContext', 'openScopeType', 'groupOpenScope',
    'maxWaitingTime', 'aiLabels', 'aiIntentionType', 'eventTriggerValueIds',
    'listenInOrTakeOver', 'smsTriggerName',
  ] : [
    'connectType', 'connectCorpusId', 'aiLabels', 'aiIntentionType', 'eventTriggerValueIds', 'smsTriggerName',
  ]))

  if (isNormal.value) {
    /** 初始化优先级的4个列表 */
    queryBranchPriorList.value = []
    generalBranchAndGroupPriorList.value = []
    silenceBranchPrior.value = null
    // 读取数据时，将priorGroup拆分成需要排序拖拽的三个部分，
    // 语料本身的priorGroup数据缺少一些需要展示的参数，需要从分支、知识库分组中获取
    // 需要将导入语料的普通查询分支id置空，沉默和未命中使用原语料的，
    // 原属性priorGroup和branchList不再交互时使用，保存时再将三个数组合并成priorGroup和branchList需要的字段；
    loadData.priorGroup?.priorList?.forEach(item => {
      if (!item.type) return
      switch(item.type) {
        case CorpusPriorTypeEnum['沉默']: {
          const data = loadData.branchList?.find(b => b.id === item.id) || {}
          silenceBranchPrior.value = {
            ...item,
            ...data, preCorpusId: addData.id, 
            id: props.corpusData.priorGroup?.priorList?.find(v => v.type === CorpusPriorTypeEnum['沉默'])?.id || undefined
          }; break;
        };
        case CorpusPriorTypeEnum['查询分支']: {
          const data = loadData.branchList?.find(b => b.id === item.id) || {}
          queryBranchPriorList.value?.push({ ...data, ...item, preCorpusId: addData.id, id: undefined})
          break;
        };
        case CorpusPriorTypeEnum['普通分支']: {
          const data = loadData.branchList?.find(b => b.id === item.id) || {}
          generalBranchAndGroupPriorList.value?.push({ ...data, ...item, preCorpusId: addData.id, id: undefined})
          break;
        };
        case CorpusPriorTypeEnum['知识库']: {
          const data = groupOptions.value?.find(b => b.id === item.id) || {}
          generalBranchAndGroupPriorList.value?.push({ ...data, ...item,})
          break;
        };
      }
    })

    /** 初始化客户回答以及对应开放范围 */
    isUnifyResponse.value = !!(!loadData.branchList || loadData.branchList.length < 1 || loadData.branchList.find(item => item.name === '统一回复'))
    // 记录已有的统一回复的分支id，使用当前语料统一回复分支或新建一个
    unifyBranch.value = isUnifyResponse.value ? unifyBranch.value || {
      name: '统一回复', scriptId: addData.scriptId
    } : null
    // 如果是统一回复，需要将开放范围转换为关闭（历史数据存在开启，但并无效果）
    if(isUnifyResponse.value) {
      addData.isOpenContext = false
      addData.openScopeType = undefined
    }
  }
  // 分支、优先级数据，需要将数据转换位多个分支数据
  ElMessage.success(`已成功导入【${loadData.name}】的分支信息和其他设置，确定后生效`)
}

/** 语料开放范围为自定义 + 该画布自定义开放范围存在，支持用户复用画布的开放范围 */
const copyCanvasOpenScope = async () => {
  // 查询当前画布的开放范围
  const [_, res] = await to(scriptCanvasModel.findCanvasOpenScopeByCanvasId({canvasId: scriptStore.currentCanvas?.id!}))
  if (!res || !res.length) return ElMessage.warning(
    `当前${addData.corpusType === CorpusTypeEnum['深层沟通-普通语料'] ? '深层沟通' : '主动流程'}为封闭，不可复用`
  )
  const canvasOpenScope = res.map(item => item.id!) || []
  addData.groupOpenScope = [...canvasOpenScope]
}

/** 文字内容多段 */
const editContentRef = ref()

// 接受【其他设置】组件emits【update:corpusData】更新参数
const handleCorpusOtherDataChange = (val: ScriptCorpusOtherItem) => {
  Object.assign(addData, val)
}

// 初始化分支拖拽，区分为普通分支和查询分支两个拖拽初始化
const sortableNormalDom = ref()
const sortableInfoDom = ref()
const initSortable = async () => {
  if (isChecked) return
  await nextTick()
  const dom1 = document.querySelector('.normal-sort-box')  as HTMLElement
  !sortableNormalDom.value && (sortableNormalDom.value = dom1 && Sortable.create(
    dom1, {
    animation: 300,
    sort: !isChecked,
    handle: ".handle",
    forceFallback: true,
    onEnd: async (evt) => {
      const newIndex = evt.newIndex as number
      const oldIndex = evt.oldIndex as number
      if (oldIndex !== newIndex && generalBranchAndGroupPriorList.value) {
        const currRow = generalBranchAndGroupPriorList.value[oldIndex]
        generalBranchAndGroupPriorList.value.splice(oldIndex, 1);
        generalBranchAndGroupPriorList.value.splice(newIndex, 0, currRow);
        const arr = toRaw(generalBranchAndGroupPriorList.value)
        generalBranchAndGroupPriorList.value = []
        await nextTick()
        generalBranchAndGroupPriorList.value = arr
      }
    },
  }))
  await nextTick()
  const dom2 = document.querySelector('.info-sort-box')  as HTMLElement
  !sortableInfoDom.value && (sortableInfoDom.value = dom2 && new Sortable(
    dom2, {
    animation: 300,
    sort: !isChecked,
    handle: ".handle",
    forceFallback: true,
    onEnd: async (evt) => {
      const newIndex = evt.newIndex as number
      const oldIndex = evt.oldIndex as number
      if (oldIndex !== newIndex && queryBranchPriorList.value) {
        const currRow = queryBranchPriorList.value[oldIndex]
        queryBranchPriorList.value.splice(oldIndex, 1);
        queryBranchPriorList.value.splice(newIndex, 0, currRow);
        const arr = toRaw(queryBranchPriorList.value)
        queryBranchPriorList.value = []
        await nextTick()
        queryBranchPriorList.value = arr
      }
    },
  }))
}
/** 
 * 分支弹窗模块
 */
// 分支类型是否统一回复
const isUnifyResponse = ref<boolean>(!!(!addData.branchList || addData.branchList.length < 1 || addData.branchList.find(item => item.name === '统一回复')))
const queryBranchPriorList = ref<CorpusPriorItem[] | null>(null) // 信息拆线呢分支
const generalBranchAndGroupPriorList = ref<CorpusPriorItem[] | null>(null) // 普通分支、知识库分组
const silenceBranchPrior = ref<CorpusPriorItem | null>(null) // 沉默分支
const missBranchPrior = ref<CorpusPriorItem | null>(null) // 未命中
const unifyBranch = ref<ScriptBranch | null>(null)

// 监听统一回复切换，联动当前分支数据变化
const handleSelectBranchType = (n: boolean) => {
  if (n) {
    addData.isOpenContext = false
    addData.openScopeType = undefined
  }
}
// 是否选中沉默分支
const handleSelectSilence = (n: boolean) => {
  if (!n) {
    silenceBranchPrior.value = null
  } else {
    silenceBranchPrior.value = {
      name: '沉默', preCorpusId: addData.id || undefined, type: CorpusPriorTypeEnum['沉默']
    }
  }
}
// 当前选中分支列表
const branchEditDialogVisible = ref(false) // 分支弹窗Visible
const activeBranchIndex = ref<number>(-1) // 编辑分支数据内容
const branchEditDialogReadonly = ref(false) // 分支弹窗Visible
const activeBranchType = ref<CorpusPriorTypeEnum>(CorpusPriorTypeEnum['普通分支'])
const branchData = computed(() => {
  if (activeBranchIndex.value < 0) return new BranchDataOrigin(activeBranchType.value, addData.id, addData.scriptId)
  if (activeBranchType.value === CorpusPriorTypeEnum['普通分支']) {
    return generalBranchAndGroupPriorList.value![activeBranchIndex.value]
  }
  if (activeBranchType.value === CorpusPriorTypeEnum['查询分支']) {
    return queryBranchPriorList.value![activeBranchIndex.value]
  }
  return null
})

// 编辑分支： type: 1: 普通分支；2：查询分支
const editBranch = (type: CorpusPriorTypeEnum, index?: number, readonly: boolean = false) => {
  activeBranchType.value = type
  if (typeof index === 'number' && index >= 0) {
    activeBranchIndex.value = index
  } else {
    activeBranchIndex.value = -1
    const len = (generalBranchAndGroupPriorList.value?.filter(item => item.type === CorpusPriorTypeEnum['普通分支']) || []).length + (queryBranchPriorList.value || []).length
    if (len >= 20) {
      return ElMessage.warning('当前分支数量已达上限')
    }
  }
  branchEditDialogReadonly.value = readonly
  branchEditDialogVisible.value = true
}

const copyBranchVisible = ref(false)
const copyBranch = () => {
  const len = (generalBranchAndGroupPriorList.value?.filter(item => item.type === CorpusPriorTypeEnum['普通分支']) || []).length + (queryBranchPriorList.value || []).length
  if (len >= 20) {
    return ElMessage.warning('当前分支数量已达上限')
  }
  activeBranchType.value = CorpusPriorTypeEnum['普通分支']
  activeBranchIndex.value = -1
  copyBranchVisible.value = true
}

// 编辑分支弹窗-确认创建\编辑-回调函数
const confirmBranch = (branch: CorpusPriorItem) => {
  if (!!loading.value) return
  loading.value = true
  if (branch.type === CorpusPriorTypeEnum['普通分支'] && generalBranchAndGroupPriorList.value) {
    activeBranchIndex.value >= 0
      ? (Object.assign(generalBranchAndGroupPriorList.value[activeBranchIndex.value], pickAttrFromObj(branch, ['name', 'preCorpusId', 'semCombineEntity', 'backExcluded'])))
      : generalBranchAndGroupPriorList.value?.unshift(branch)
  }
  if (branch.type === CorpusPriorTypeEnum['查询分支'] && queryBranchPriorList.value) {
    activeBranchIndex.value >= 0
      ? (Object.assign(queryBranchPriorList.value[activeBranchIndex.value], pickAttrFromObj(branch, ['name', 'preCorpusId', 'color', 'infoQueryValueIds', 'queryField'])))
      : queryBranchPriorList.value?.unshift(branch)
  }
  activeBranchIndex.value = -2
  branchEditDialogVisible.value = false
  initSortable()
  loading.value = false
}

// 复制普通分支弹窗-回调函数（支持多个）
const confirmCopyBranch = (branches: CorpusPriorItem[]) => {
  if (!branches?.length) return
  if (!generalBranchAndGroupPriorList.value?.length) {
    generalBranchAndGroupPriorList.value = branches
  } else {
    generalBranchAndGroupPriorList.value = [...branches, ...generalBranchAndGroupPriorList.value]
  }
}

// 清空分支
const deleteAllBranch = () => {
  queryBranchPriorList.value = []
  generalBranchAndGroupPriorList.value = generalBranchAndGroupPriorList.value?.filter(item => item.type === CorpusPriorTypeEnum['知识库']) || []
  silenceBranchPrior.value = null
}

// 记录用户删除分支的id（新建的分支，删除没有id）
const delBranch = async (type: CorpusPriorTypeEnum, index: number) => {
  switch (type) {
    case CorpusPriorTypeEnum['查询分支']: {
      queryBranchPriorList.value?.splice(index, 1)
      return
    }
    case CorpusPriorTypeEnum['普通分支']: {
      generalBranchAndGroupPriorList.value?.splice(index, 1)
      return
    }
    case CorpusPriorTypeEnum['知识库']: {
      generalBranchAndGroupPriorList.value?.splice(index, 1)
      return
    }
    case CorpusPriorTypeEnum['沉默']: {
      silenceBranchPrior.value = null
      return
    }
  }
}
// 删除分支列表存储，执行map轮询删除
const delBranchAction = async (ids: number[]) => {
  try {
    await Promise.all(
      ids.map(async (item: number) => await scriptCorpusModel.deleteScriptBranch({
        id: item,
        scriptId: addData.scriptId!,
      }))
    )
  } catch (err) {
    ElMessage({
      message: '分支删除失败',
      type: 'error',
    })
  }
}
/** 分支弹窗模块 */

/** 规则校验 */
// 分支校验
const checkBranch =  (rule: any, value: any, callback: any) => {
  // 不是普通语料，则没有分支概念
  if (!isNormal.value) return callback()
  // 普通语料统一回复，不需要验证分支
  if (isNormal.value && isUnifyResponse.value) return callback()
  const branchList =  [
    ...(queryBranchPriorList.value || []),
    ...generalBranchAndGroupPriorList.value?.filter(item => item.type === CorpusPriorTypeEnum['普通分支']) || [],
    silenceBranchPrior.value,
    missBranchPrior.value,
  ].flatMap(item => {
    return item ? [pickAttrFromObj(item, [
      'id', 'name', 'scriptId', 'preCorpusId', 'color', 'queryField', 'infoQueryValueIds',
      'semCombineEntity',
    ])] : []
  })
  if (isNormal.value && !isUnifyResponse.value && (!branchList || branchList.length < 1)) {
    return callback(new Error('请选择分支'))
  }
  if (branchList.length > 20) {
    return callback(new Error('请选择分支，不得超过20个'))
  }
  return callback()
}

// 语料名称校验
const validateName = (rule: any, value: any, callback: any) => {
  const reg = /[$#_~?&\/]/
  if (reg.test(value)) {
    callback(new Error('语料名称请勿出现特殊符号（$#~_?、&/等)'))
  }
  if (value.length > 40 || value.length < 2) {
    callback(new Error('语料名称长度为2-40'))
  }
  callback()
}
// 内容名称 校验函数
const validateContent = (rule: any, value: any, callback: any) => {
  if (!addData.scriptMultiContents || addData.scriptMultiContents.length < 1) {
    return callback(new Error('请输入文字内容'))
  }
  const val = addData.scriptMultiContents[0].scriptUnitContents?.reduce((a, b) => a + (b.content || ''), '') || ''
  if (!val) {
    return callback(new Error('请输入文字内容'))
  }
  const reg = /[$]/
  if (reg.test(val)) {
    return callback(new Error('文字内容请勿出现特殊符号$'))
  }
  return callback()
}
const rules = {
  name: [
    { required: true, message: '请输入语料名称', trigger: 'blur' },
    { validator: validateName, trigger: 'blur' },
  ],
  satisfySemConditions: [
    { validator: (rule: any, value: any, callback: any) => {
      const errMsg = checkCorpusRules(addData.semCombineEntity.satisfySemConditions, true)
      return errMsg ? callback(new Error(errMsg)) : callback()
    }, trigger: ['change', 'blur']},
  ],
  exclusionList: [
    { validator: (rule: any, value: any, callback: any) => {
      const errMsg = checkCorpusRules(addData.semCombineEntity.excludeSemConditions, false)
      return errMsg ? callback(new Error(errMsg)) : callback()
    }, trigger: ['change', 'blur']},
  ],
  connectCorpusId: [
    { required: true, message: '请选择连接的主动流程', trigger: 'change' },
  ],
  scriptMultiContents: [
    { required: true, message: '请输入文字内容', trigger: 'blur' },
    { validator: validateContent, trigger: 'blur' },
  ],
  branchList: [
    { validator: checkBranch, trigger: ['change', 'blur']},
  ],
  maxWaitingTime: [
    { required: true, message: '请输入最长等待时间', trigger: 'blur' },
  ],
  listenInOrTakeOver: [
    { required: true, message: '请选择是否支持转人工', trigger: 'change' },
  ],
  smsTriggerName: [
    { required: true, message: '请输入触发点名称名', trigger: 'blur' },
    { max: 8, message: '触发点名称名不能超过8个字符', trigger: 'blur' },
  ],
  isOpenContext: [
    { required: true, message: '请选择语境类型', trigger: 'change' },
    { validator: (rule: any, value: any, callback: any) => {
      if (addData.isOpenContext && !addData.openScopeType) {
        return callback(new Error('请选择开放类型'))
      }
      return callback()
    }, trigger: ['change']},
  ],
  groupOpenScope: [
    { required: true, message: '请选择知识库分组开放范围', trigger: 'change' },
  ],
}
/** 规则校验 */

// 底部确认处理函数
const corpusContentVisible = ref(false)
const corpusId = ref<number | null>(null)
const confirm = async (goContentSetting?: boolean) => {
  // 查看模式，查看打断设置
  if (goContentSetting && addData.id && isChecked) {
    corpusId.value = addData.id!
    corpusContentVisible.value = true
    return
  }

  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const params: ScriptCorpusItem = pickAttrFromObj(addData, corpusTypeOption[addData.corpusType!].keys)
      if (params.connectType !== ConnectTypeEnum['指定主动流程']) {
        params.connectCorpusId = null
      }
      if (isNormal.value) {
        if (!isUnifyResponse.value) {
          // 针对普通语料，分支信息直接从优先级的四个list中生成，防止优先级和分支不一致
          params.branchList = [
            ...(queryBranchPriorList.value || []),
            ...generalBranchAndGroupPriorList.value?.filter(item => item.type === CorpusPriorTypeEnum['普通分支']) || [],
            silenceBranchPrior.value,
            missBranchPrior.value,
          ].flatMap(item => {
            return item ? [pickAttrFromObj(item, [
              'id', 'name', 'scriptId', 'preCorpusId', 'color', 'queryField', 'infoQueryValueIds',
              'semCombineEntity',
            ])] : []
          })
        } else {
          // 分支list如原数据有统一回复则直接使用
          params.branchList = [unifyBranch.value || {
            name: '统一回复', scriptId: addData.scriptId
          }]
        }
        // 更新开放范围三个字段：isOpenContext、openScopeType、groupOpenScope
        params.openScopeType = !!addData.isOpenContext ? addData.openScopeType : undefined
        params.groupOpenScope =  !!addData.isOpenContext && addData.openScopeType === OpenScopeTypeEnum['自定义'] ? addData.groupOpenScope
          :(!!addData.isOpenContext && addData.openScopeType === OpenScopeTypeEnum['全部'] ? groupOptions.value?.map(item => item.id) : undefined)
      }
      // if--------------针对非挂机的连接语料，即没有文字内容，需要将已有的文字内容清空
      // elseif----------针对
      // ------------------1）针对v1.0版本话术
      // ------------------2）挂机的连接语料
      // ----------------需要删除非第一段的文字内容（文字内容如果存在第一段必有数据）
      if (isConnect.value && params.connectType !== ConnectTypeEnum['挂机']) {
        params.smsTriggerName = undefined
        params.scriptMultiContents = params.id && params.scriptMultiContents && params.scriptMultiContents[0] && params.scriptMultiContents[0]?.id ? compareCorpusMultiContent([{
          corpusId: params.id,
          id: params.scriptMultiContents ? params.scriptMultiContents[0].id : undefined,
          deleted: true,
          scriptUnitContents: [],
        }], params.scriptMultiContents, {
          id: params.id,
          corpusType: params.corpusType!,
        }) : undefined
      } else if ((!multiContentVersion && isNormal.value) || (isConnect.value && addData.connectType === ConnectTypeEnum['挂机'])) {
        if (addData.scriptMultiContents && addData.scriptMultiContents[0] && addData.scriptMultiContents[0].scriptUnitContents) {
          addData.scriptMultiContents[0].scriptUnitContents = addData.scriptMultiContents[0].scriptUnitContents.flatMap((item, index) => {
            if (index === 0) {
              return [item]
            } else if(item.id) {
              return [{
                ...item,
                deleted: true,
              }]
            } else {
              return []
            }
          })
        }
      }
      
      // 保存语料
      const [err, resultData] = await to(corpusTypeOption[addData.corpusType!].api(params)) as [any, ScriptCorpusItem]
      await trace({ page: addData.id ? `话术编辑-编辑语料(${addData.scriptId}-${addData.id})` : `话术编辑(${addData.scriptId})-新增语料`, params: params })
      if (resultData) {
        // 删除语料中的分支
        // 切换成统一回复需要删除原语料已有的非统一回复分支
        const delBranchList = props.corpusData.id ? props.corpusData.branchList?.flatMap(item => {
          return item.id && (!resultData.branchList || resultData.branchList.findIndex(branch => branch.id === item.id) === -1) ? [item.id] : []
        }) || [] : []
        delBranchList?.length > 0 && delBranchAction(delBranchList)
        const {id, branchList, corpusType, scriptMultiContents, name, connectType, isHead, aiIntentionType, eventTriggerValueIds, listenInOrTakeOver, smsTriggerName, } = resultData
        // 根据语料返回，更新画布
        emits('update:data', {
          id, branchList,corpusType, scriptMultiContents, name, isHead, connectType, eventTriggerValueIds, listenInOrTakeOver, smsTriggerName,
          connectCorpusId: addData.connectCorpusId,
          corX: addData.corX,
          corY: addData.corY,
          aiIntentionType,
        })
        // 普通语料，需要调用接口更新【知识库、分支】优先级
        if (isNormal.value) {
          // 如不是统一回复，直接获取并更新优先级
          if (!isUnifyResponse.value) {
            const priorList = [
              ...(queryBranchPriorList.value || []),
              ...(generalBranchAndGroupPriorList.value || []),
              silenceBranchPrior.value,
              missBranchPrior.value,
            ].flatMap(item => {
              if (!item) return []
              if (!item.id) {
                const row = branchList?.find(v => v.name === item.name)
                return [{
                  type: item.type!,
                  id: row?.id!,
                  backExcluded: item.backExcluded!,
                }]
              }
              return [{
                type: item.type!,
                id: item.id!,
                backExcluded: item.backExcluded!,
              }]
            })
            const res = await to(scriptCorpusModel.saveCorpusPriorInfo({
              id: addData.priorGroup?.id || undefined,
              corpusId: id!,
              priorList: priorList,
              scriptId: addData.scriptId!,
            }))
            await trace({ page: `话术编辑-保存优先级(${addData.scriptId}-${addData.id})`, params: res[1] })
          }
          // 如是统一回复，需要删除已有的优先级id
          if(isUnifyResponse.value && addData.priorGroup?.id) {
            await to(scriptCorpusModel.deletePriorGroup({
              id: addData.priorGroup?.id,
            }))
          }
        }
        // 如点击的是前往打断设置，需要跳转打断抽屉，并提示不同内容
        if (goContentSetting && id) {
          corpusId.value = id || addData.id!
          corpusContentVisible.value = true
          ElMessage.success('语料编辑内容已保存')
        } else {
          ElMessage.success('操作成功')
          cancel()
        }
      }
      loading.value = false
    }
  })
}
// 底部取消处理函数
const cancel = () => {
  dialogVisible.value = false
  sortableNormalDom.value?.destroy()
  sortableInfoDom.value?.destroy()
  sortableNormalDom.value = null
  sortableInfoDom.value = null
  emits('update:visible' , false)
}

/** 通过语句设置暴露的方法，判断是否需要关闭弹窗 */
const handleCloseOuter = (val: null | ScriptCorpusItem) => {
  // 从打断设置返回时，由于之前保存的优先级和分支信息不一定为最新的，再重新通过打断设置的传参更新下
  if (!!val) {
    Object.assign(addData, val)
    init()
  } else {
    cancel()
  }
}

const needUpdate = ref(false) // 初始化更新多文本
// 初始化选项、拖拽
const init = async() => {
  try {
    // 选项数据
    groupOptions.value = await scriptStore.getKnowledgeGroupOptions()
    masterProcessOptions.value = await scriptStore.getProcessOptions()
    const infoQueryList = await scriptStore.getInfoQueryOptions()
    infoQueryKeyMap.value = new Map()
    infoQueryValueMap.value = new Map()
    infoQueryList.map(item => {
      infoQueryKeyMap.value.set(item.infoFieldName, item.fieldDefinition)
      item.infoQueryValues?.map(v => {
        infoQueryValueMap.value.set(v.value!, v)
      })
    })
    
    /** 初始化优先级的4个列表 */
    queryBranchPriorList.value = []
    generalBranchAndGroupPriorList.value = []
    missBranchPrior.value = {
      type: CorpusPriorTypeEnum['未命中'],
      name: '未命中',
      scriptId: addData.scriptId,
      preCorpusId: addData.id
    }
    silenceBranchPrior.value = null
    // 读取数据时，将priorGroup拆分成需要排序拖拽的三个部分，
    // 语料本身的priorGroup数据缺少一些需要展示的参数，需要从分支、知识库分组中获取
    // 原属性priorGroup和branchList不再交互时使用，保存时再将三个数组合并成priorGroup和branchList需要的字段；
    addData.priorGroup?.priorList?.forEach(item => {
      if (!item.type) return
      switch(item.type) {
        case CorpusPriorTypeEnum['沉默']: {
          const data = addData.branchList?.find(b => b.id === item.id) || {}
          silenceBranchPrior.value = {...item, ...data}; break;
        };
        case CorpusPriorTypeEnum['未命中']: {
          missBranchPrior.value && (missBranchPrior.value.id = item.id); break;
        };
        case CorpusPriorTypeEnum['查询分支']: {
          const data = addData.branchList?.find(b => b.id === item.id) || {}
          queryBranchPriorList.value?.push({ ...data, ...item,})
          break;
        };
        case CorpusPriorTypeEnum['普通分支']: {
          const data = addData.branchList?.find(b => b.id === item.id) || {}
          generalBranchAndGroupPriorList.value?.push({ ...data, ...item,})
          break;
        };
        case CorpusPriorTypeEnum['知识库']: {
          const data = groupOptions.value?.find(b => b.id === item.id) || {}
          generalBranchAndGroupPriorList.value?.push({ ...data, ...item,})
          break;
        };
      }
    })

    /** 初始化客户回答以及对应开放范围 */
    isUnifyResponse.value = !!(!addData.branchList || addData.branchList.length < 1 || addData.branchList.find(item => item.name === '统一回复'))
    // 记录已有的统一回复的分支id，防止统一回复来回切换导致丢失id
    unifyBranch.value = isUnifyResponse.value ? addData.branchList?.find(item => item.name === '统一回复') || null : null
    // 如果是统一回复，需要将开放范围转换为关闭（历史数据存在开启，但并无效果）
    if(isUnifyResponse.value) {
      addData.isOpenContext = false
      addData.openScopeType = undefined
    }

    /** 多语句文本数据处理 */
    addData.scriptMultiContents = handleCorpusMultiContent(addData.scriptMultiContents, editId, {
      id: addData.id,
      corpusType: addData.corpusType!,
    })

    /** 深层沟通头节点-命中排除数据处理 */
    if (addData.corpusType === CorpusTypeEnum['深层沟通-普通语料'] && addData.isHead) {
      if (!addData.semCombineEntity) {
        addData.semCombineEntity = { satisfySemConditions: [], excludeSemConditions: [] }
      } else if(!addData.semCombineEntity.satisfySemConditions || !addData.semCombineEntity.excludeSemConditions) {
        addData.semCombineEntity.satisfySemConditions = addData.semCombineEntity.satisfySemConditions || []
        addData.semCombineEntity.excludeSemConditions = addData.semCombineEntity.excludeSemConditions || []
      }
    }

    needUpdate.value = true
    corpusContentVisible.value = false
  } catch (err) {
    ElMessage({
      message: '获取选项数据有误',
      type: 'error',
    })
  }
}
/** 处理函数结束 */

/** watch开始 */ 
watch([() => addData.isOpenContext, () => addData.openScopeType, () => addData.groupOpenScope], (n) => {
  if (!addData.isOpenContext) {
    generalBranchAndGroupPriorList.value = generalBranchAndGroupPriorList.value?.filter(item => item.type !== CorpusPriorTypeEnum['知识库']) || []
    return 
  }
  if (addData.openScopeType === OpenScopeTypeEnum['全部']) {
    const extraList = groupOptions.value?.flatMap(item => {
      if (!generalBranchAndGroupPriorList.value?.find(v => v.id === item.id)) {
        return [{
          type: CorpusPriorTypeEnum['知识库'],
          id: item.id,
          name: item.name
        }]
      }
      return []
    }) || []
    generalBranchAndGroupPriorList.value = [...(generalBranchAndGroupPriorList.value || []), ...extraList]
  } else {
    generalBranchAndGroupPriorList.value = generalBranchAndGroupPriorList.value?.filter(item => !(item.id && item.type === CorpusPriorTypeEnum['知识库'] && !addData.groupOpenScope?.includes(item.id))) || []
    const extraList = groupOptions.value?.flatMap(item => {
      if (addData.groupOpenScope?.includes(item.id) && !generalBranchAndGroupPriorList.value?.find(v => v.id === item.id) ) {
        return [{
          type: CorpusPriorTypeEnum['知识库'],
          id: item.id,
          name: item.name
        }]
      }
      return []
    }) || []
    generalBranchAndGroupPriorList.value = [...(generalBranchAndGroupPriorList.value || []), ...extraList]
  }
}, )
// 监听visible
const scrollRef = ref()
watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if(props.visible) {
    Object.assign(addData, props.corpusData)
    init()
    initSortable()
    setTimeout(() => {
      editRef.value && editRef.value.clearValidate()
      scrollRef.value?.scrollTo({top: 0})
    }, 200)
  } else {
    sortableNormalDom.value?.destroy()
    sortableInfoDom.value?.destroy()
    sortableNormalDom.value = null
    sortableInfoDom.value = null
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.dialog-form {
  .el-col {
    display: flex;
    align-items: center;
    height: 32px;
    padding: 0 6px;
  }
}
.el-input-number .el-input__inner {
  text-align: left;
}
.text {
  font-size: var(--el-font-size-base);
  line-height: 24px;
  margin-right: 10px;
  text-align: left;
}
.status-box-mini {
  margin: 0 auto;
}
.handle {
  display: flex;
  align-items: center;
  padding-left: 12px;
}
</style>
