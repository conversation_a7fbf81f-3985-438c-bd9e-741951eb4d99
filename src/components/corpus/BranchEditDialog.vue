<template>
  <el-dialog
    v-model="dialogVisible"
    width="800px"
    @close="cancel"
    :close-on-click-modal="isChecked || props.readonly"
    align-center
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ title }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="branchData"
        :rules="rules"
        label-width="80px"
        :disabled="isChecked || props.readonly"
        ref="branchEditRef"
      >
        <el-form-item label="分支名称：" prop="name">
          <el-input v-model="branchData.name" clearable placeholder="请输入分支名称（40字以内）" maxlength="40"/>
        </el-form-item>
        <el-form-item v-if="branchData.type === CorpusPriorTypeEnum['普通分支'] && dialogVisible && branchData.semCombineEntity?.satisfySemConditions" label-width="0" prop="satisfySemConditions">
          <MultCorpusRuleBox
            v-model:data="branchData.semCombineEntity.satisfySemConditions"
            :readonly="isChecked"
            required
            title="满足条件"
          />
        </el-form-item>
        <el-form-item v-if="branchData.type === CorpusPriorTypeEnum['普通分支'] && dialogVisible && branchData.semCombineEntity?.excludeSemConditions" label-width="0" prop="excludeSemConditions">
          <MultCorpusRuleBox
            v-model:data="branchData.semCombineEntity.excludeSemConditions"
            :readonly="isChecked"
            title="排除条件"
          />
        </el-form-item>
        <el-form-item v-if="branchData.type === CorpusPriorTypeEnum['查询分支']" label="分支颜色：" prop="color">
          <div v-if="!isChecked" class="tw-flex tw-items-center tw-justify-start">
            <span
              v-for="item in x6ColorList"
              class="tw-rounded-[2px] tw-relative tw-w-[32px] tw-h-[32px] tw-mr-[12px] tw-cursor-pointer tw-shadow tw-flex tw-justify-center tw-items-center tw-border-box"
              :class="{'active-color-box': branchData.color === item.value}"
              :style="{backgroundColor: item.name}"
              :key="item.value"
              @click="branchData.color=item.value"
            >
              <span v-if="branchData.color === item.value" class="triangle">
                <el-icon :size="12" :color="item.name"><Select /></el-icon>
              </span>
            </span>
          </div>
          <span v-else>
            <span
              class="tw-rounded-[2px] active-color-box tw-relative tw-w-[32px] tw-h-[32px] tw-mr-[12px] tw-cursor-pointer tw-shadow tw-flex tw-justify-center tw-items-center tw-border-box"
              :style="{backgroundColor: x6ColorList.find(i => i.value === branchData.color)?.name}"
            >
              <span class="triangle">
                <el-icon :size="12" :color="x6ColorList.find(i => i.value === branchData.color)?.name"><Select /></el-icon>
              </span>
            </span>
          </span>
        </el-form-item>
        <el-form-item v-if="branchData.type === CorpusPriorTypeEnum['查询分支']" label="信息字段：" prop="queryField">
          <div v-for="item in queryFieldKeys" class="tw-flex tw-justify-between tw-items-center tw-gap-x-[8px] tw-w-full tw-mb-[4px]">
            <el-select
              v-model="item.key" 
              clearable 
              placeholder="信息字段名" 
              class="tw-grow"
              @change="item.value=''"
            >
            <el-option
              v-for="ii in keyList(item)"
              :key="ii.infoFieldName"
              :label="`${ii.infoFieldName}(${ii.infoFieldName})`"
              :value="ii.infoFieldName"
            />
            </el-select>
            <el-select
              v-model="item.value" 
              clearable 
              placeholder="信息字段值" 
              class="tw-grow"
            >
              <el-option
                v-for="vv in valueList(item.key)"
                :key="vv.value"
                :label="`${vv.definition}(${vv.value})`"
                :value="vv.value"
              />
            </el-select>
            <el-button :disabled="queryFieldKeys?.length <= 1" link :type="queryFieldKeys?.length > 1 ? 'primary':'default'" @click="delQuery(item)">
              删除
            </el-button>
          </div>
          <div>
            <el-button link type="primary" :disabled="queryFieldKeys?.length >= infoQueryOriginList.length" @click="addQuery((queryFieldKeys.length + 1) || 1)">
              <el-icon><SvgIcon name="add1"></SvgIcon></el-icon>
              <span>新增事件字段</span>
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer" v-if="!isChecked">
        <el-button @click="cancel" :icon="CloseBold">{{!props.readonly ? '取消' : '关闭'}}</el-button>
        <el-button v-if="!props.readonly" :loading="loading" type="primary" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, defineAsyncComponent, } from 'vue'
import { CorpusPriorItem, InfoQueryItem, BranchDataOrigin, CorpusPriorTypeEnum, } from '@/type/speech-craft'
import { x6ColorList, } from '@/components/x6/config'
import { pickAttrFromObj, enum2Options } from '@/utils/utils'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import type { FormInstance, } from 'element-plus'
import { useScriptStore } from '@/store/script'
import { checkCorpusRules }  from './constant'

const MultCorpusRuleBox = defineAsyncComponent({ loader:() => { return import('@/components/corpus/MultCorpusConditionBox.vue')}})

const scriptStore = useScriptStore()
const isChecked = scriptStore.isChecked
const emits = defineEmits(['confirm', 'update:visible', 'close'])
const props = defineProps<{
  visible: boolean;
  branchData: CorpusPriorItem | null | undefined;
  readonly: boolean;
}>();
const title = computed(() => props.branchData?.id ? '编辑分支' : '创建分支')
const dialogVisible = ref(props.visible)
const loading = ref(false)
const branchData = reactive<CorpusPriorItem>(props.branchData || new BranchDataOrigin(CorpusPriorTypeEnum['普通分支'], undefined, undefined))
const queryFieldKeys = ref<{
  key: string, value: string, id: number,
}[]>([])
const addQuery = (id: number) => {
  queryFieldKeys.value.push({
    key: '', value: '', id
  })
}
const delQuery = (item: {
  key: string, value: string, id: number
}) => {
  queryFieldKeys.value = queryFieldKeys.value.filter(v => !(v.id === item.id))
}


const infoQueryOriginList = ref<InfoQueryItem[]>([])
const keyList = (row: {
  key: string, value: string, id: number,
}) => {
  if (branchData.queryField) {
    return infoQueryOriginList.value.filter(item => !Object.keys(branchData.queryField!).includes(item.infoFieldName) || row.key === item.infoFieldName)
  } else {
    return infoQueryOriginList.value
  }
}
const valueList = (key: string) => {
  return infoQueryOriginList.value.find(item => item.infoFieldName === key)?.infoQueryValues || []
}
const initOptions = async () => {
  try {
    loading.value = true
    Object.assign(branchData, props.branchData ? props.branchData : new BranchDataOrigin(CorpusPriorTypeEnum['普通分支'], undefined, undefined))
    if (branchData.type === CorpusPriorTypeEnum['查询分支']) {
      branchData.color = branchData.color || 'red'
      queryFieldKeys.value = []
      branchData.queryField && Object.keys(branchData.queryField).map((item, index) => {
        queryFieldKeys.value[index] = {
          key: item,
          value: branchData.queryField![item],
          id: index + 1
        }
      })
      if (!branchData.queryField || Object.keys(branchData.queryField).length < 1) {
        queryFieldKeys.value = [{ key: '', value: '', id: 1}]
      }
      infoQueryOriginList.value = await scriptStore.getInfoQueryOptions() as InfoQueryItem[] || []
    } else {
      if (!branchData.semCombineEntity) {
        branchData.semCombineEntity = { satisfySemConditions: [], excludeSemConditions: [] }
      } else if(!branchData.semCombineEntity.satisfySemConditions || !branchData.semCombineEntity.excludeSemConditions) {
        branchData.semCombineEntity.satisfySemConditions = branchData.semCombineEntity.satisfySemConditions || []
        branchData.semCombineEntity.excludeSemConditions = branchData.semCombineEntity.excludeSemConditions || []
      }
    }
    loading.value = false
  } catch(err) {
    loading.value = false
    ElMessage({
      message: '初始化数据失败',
      type: 'error',
    })
  }
}

const checkQueryField = (rule: any, value: any, callback: any) => {
  if ((branchData.queryField && Object.keys(branchData.queryField).length > 0)) {
    return callback()
  }
  return callback(new Error('请选择信息字段'))
}
const rules = {
  name: [
    { required: true, message: '请输入分支名称', trigger: 'blur' },
  ],
  satisfySemConditions: [
    { validator: (rule: any, value: any, callback: any) => {
      const errMsg = checkCorpusRules(branchData.semCombineEntity?.satisfySemConditions, true)
      return errMsg ? callback(new Error(errMsg)) : callback()
    }, trigger: ['change', 'blur']},
  ],
  excludeSemConditions: [
    { validator: (rule: any, value: any, callback: any) => {
      const errMsg = checkCorpusRules(branchData.semCombineEntity?.excludeSemConditions, false)
      return errMsg ? callback(new Error(errMsg)) : callback()
    }, trigger: ['change', 'blur']},
  ],
  color: [
    { required: true, message: '请选择信息查询分支颜色', trigger: ['change']},
  ],
  queryField: [
    { validator: checkQueryField, trigger: ['change']},
  ],
}
const branchEditRef = ref<FormInstance  | null>(null)
const confirm = async () => {
  if (!!loading.value) return
  loading.value = true
  // 查询信息
  branchData.queryField = {} as {
    [key: string]: string
  }
  branchData.infoQueryValueIds = []
  branchData.queryField = {}
  queryFieldKeys.value?.length>0 && queryFieldKeys.value.forEach(v => {
    if (v.key && v.value && branchData.queryField) {
      branchData.queryField[v.key] = v.value
      const id = valueList(v.key).find(c => c.value === v.value)?.id || undefined
      id && branchData.infoQueryValueIds?.push(id)
      !id && ElMessage({
        message: '未找到信息值的id',
        type: 'error'
      })
    }
  })
  if (!branchEditRef.value) {
    loading.value = false
    return
  }
  branchEditRef.value && branchEditRef.value.validate((valid) => {
    if (valid) {
      const params = pickAttrFromObj(branchData, ['id', 'name', 'scriptId', 'preCorpusId', 'type', 'backExcluded' ])
      if (branchData.type === CorpusPriorTypeEnum['查询分支']) {
        params.color = branchData.color
        params.queryField = branchData.queryField
        params.infoQueryValueIds = branchData.infoQueryValueIds
      } else {
        params.semCombineEntity = branchData.semCombineEntity
      }
      ElMessage.warning('操作完成，确定后生效')
      dialogVisible.value = false
      emits('confirm', params)
    } else {
      loading.value = false
    }
  })
}
const cancel = () => {
  branchEditRef.value && branchEditRef.value.clearValidate()
  emits('update:visible', false)
  emits('close')
}
watch(() => props.visible, () => {
  dialogVisible.value  = props.visible
  props.visible && initOptions()
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
  :deep(.el-form-item__content) {
    font-size: var(--el-font-size-base);
  }
  .triangle {
    background-color: #fff;
    width: 15px;
    height: 15px;
    position: absolute;
    right: 1px;
    top: 1px;
    -webkit-clip-path: polygon(0 0, 100% 100%, 100% 8%, 92% 0);
    clip-path: polygon(0 0, 100% 100%, 100% 8%, 92% 0);
    .el-icon {
      position: absolute;
      right: -2px;
      top: -2px;
      z-index: 8;
    }
  }
  /* .active-color-box {
    &::before {
      content: "";
      position: absolute;
      top: -3px;
      left: -3px;
      right:-3px;
      bottom: -3px;
      border-radius: 4px;
      box-shadow: 0px 0px 5px 1px rgba(0,0,0,0.5) 
    }
  } */
}
</style>