<template>
  <el-drawer
    v-model="dialogVisible"
    :size="drawerWidth"
    class="dialog-form"
    @close="cancel()"
    with-header
    :close-on-click-modal="isChecked"
  >
    <template #header>
      <div class="tw-flex tw-w-full tw-items-center tw-h-[48px]">
        <!-- 抽屉头部，语句音频播放 -->
        <span class="tw-shrink-0 tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ title }}</span>
        <AudioMode
          v-if="!!tempAudio?.audioPath"
          class="tw-mx-auto"
          keep-origin
          :audioUrl="tempAudio?.audioPath"
          :audioName="tempAudio?.contentName || ''"
          v-model:audioStatus="audioStatus"
          v-model:audioVolume="audioVolume"
          @close="handleAudioPlay()"
        >
        </AudioMode>
      </div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 80px)'"
      wrap-class="tw-p-[12px] tw-bg-[#f2f3f5]"
      view-class="tw-text-[13px]"
    >
      <!-- 语料名称、语句组成展示 -->
      <div class="tw-text-left tw-py-[14px] tw-bg-[#fff] tw-flex">
        <span class="tw-w-[75px] tw-text-right">语料名称：</span>
        <span class="info-title">{{corpusData?.name}}</span>
      </div>
      <el-table
        :data="corpusData?.scriptMultiContents ? corpusData?.scriptMultiContents[0]?.scriptUnitContents : []"
        row-key="scriptUnitContentId"
        scrollbar-always-on
        :header-cell-style="tableHeaderStyle"
      >
        <template #empty>
          暂无数据
        </template>
        <el-table-column label="语句名称" prop="contentName" align="left" width="160">
          <template #default="{ row }">
            <div class="tw-text-left" :class="row.contentName === tempAudio?.contentName ? 'tw-text-[#165DFF]' : 'tw-text-[#313233]'">{{ row.contentName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="文字内容" align="left" prop="content" min-width="240"/>
        <el-table-column label="音频" align="center" prop="audioPath" width="80">
          <template #default="{ row }">
            <el-button v-if="row?.id && tempAudio?.id == row.id && audioStatus == 'play'" link type="primary" :disabled="!row.audioPath" @click="handleAudioPlay(row)">
              <el-icon :size="20"><svg-icon name="stop-circle"></svg-icon></el-icon>
            </el-button>
            <el-button v-else :type="row.audioPath ? 'primary':'default'" :disabled="!row.audioPath" link @click="handleAudioPlay(row)">
              <el-icon :size="20"><svg-icon name="play-circle"></svg-icon></el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-form
        v-loading="loading"
        :model="corpusData"
        class="tw-bg-white tw-p-[12px] tw-pt-[1px] tw-relative"
        :disabled="isChecked"
        label-width="75px"
      >
        <!-- 打断设置 -->
        <div class="tw-font-[600] tw-text-left tw-my-[6px] tw-text-[14px]">打断设置</div>
        <el-form-item label=" " :label-width="1" :prop="isHangup ? 'dataListEnd' : 'dataList'">
          <el-table
            :data="isHangup ? interruptConfigData?.dataListEnd || [] : interruptConfigData?.dataList || []"
            row-key="scriptUnitContentId"
            scrollbar-always-on
            :header-cell-style="tableHeaderStyle"
          >
            <template #empty>
              暂无数据
            </template>
            <el-table-column label="语句名称" prop="contentName" align="left" min-width="120">
              <template #default="{ row }">
                <div class="tw-text-left" :class="row.contentName === tempAudio?.contentName ? 'tw-text-[#165DFF]' : 'tw-text-[#313233]'">{{ row.contentName }}</div>
              </template>
            </el-table-column>
            <!-- 非挂机语料-语句打断设置：打断设置 -->
            <template v-if="!isHangup">
              <el-table-column label="打断设置" align="left" prop="" width="220">
                <template #default="{ row }">
                  <el-select
                    v-model="row.interruptType"
                    placeholder="选择打断类型"
                    style="width:100%"
                  >
                    <el-option
                      v-for="item in interruptOption"
                      :key="item.value"
                      :label="item.name"
                      :value="item.value"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="" align="left" prop="" min-width="120">
                <template #header>
                  <div class="tw-flex tw-items-center tw-justify-start">
                    <span>打断时间</span>
                    <el-tooltip content="打断时间：语句播放至所设置的秒数后，才允许被打断。">
                      <el-icon :size="14" color="var(--primary-black-color-400)" class="tw-ml-[2px]">
                        <SvgIcon name="warning"/>
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <template #default="{ row }">
                  <InputNumberBox
                    v-if="row.interruptType && row.interruptType !== InterruptTypeEnum['不允许打断']"
                    v-model:value="row.allowedInterruptTime"
                    append="秒后"
                    :precision="0"
                    :min="0"
                    :max="300"
                  />
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column v-if="isBaseOrNormal" label="打断垫句" align="left" prop="preInterruptCorpusId" min-width="160">
                <template #default="{ row }">
                  <el-select
                    v-if="row.interruptType && [InterruptTypeEnum['支持核心短语打断但不回复'], InterruptTypeEnum['支持核心短语打断并回复']].includes(row.interruptType)"
                    v-model="row.preInterruptCorpusId"
                    clearable
                    placeholder="关闭"
                    style="width:100%"
                  >
                    <el-option
                      v-for="item in interruptCorpusList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column v-if="isBaseOrNormal" label="续播垫句" align="left" prop="preContinueCorpusIdForInterrupt" min-width="200">
                <template #default="{ row }">
                  <el-select
                    v-if="row.interruptType && row.interruptType !== InterruptTypeEnum['不允许打断']"
                    v-model="row.preContinueCorpusIdForInterrupt"
                    placeholder="关闭"
                    style="width:100%"
                    clearable
                  >
                    <el-option
                      v-for="item in continueCorpusList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                  <span v-else>-</span>
                </template>
              </el-table-column>
            </template>
            <!-- 挂机语料-语句打断设置 -->
            <template v-else>
              <el-table-column label="打断设置" align="left" prop="interruptType" width="200">
                <template #default="{ row }">
                  <el-select
                    v-model="row.interruptType"
                    placeholder="选择打断类型"
                    style="width:100%"
                  >
                    <!-- 后端取值为布尔 -->
                    <el-option
                      v-for="item in interruptOption"
                      :key="item.value"
                      :label="item.name"
                      :value="item.value"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <!-- allowedInterruptEnd为true，即指定语料打断时，该字段必填 -->
              <el-table-column label="指定语料" align="left" prop="" min-width="160">
                <template #default="{ row }">
                  <SelectBox 
                    v-if="row.interruptType === InterruptTypeEnum['指定语料打断']"
                    :options="hangupCorpusList"
                    v-model="row.interruptCorpusIdsForEnd"
                    name="name"
                    val="id"
                    class="tw-w-full"
                    placeholder="请选择指定语料"
                    filterable 
                    multiple
                  >
                  </SelectBox>
                  <span v-else>-</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
        </el-form-item>

        <!-- 返回设置:仅画布普通语料支持设置 -->
        <template v-if="isNormal" >
          <div class="tw-font-[600] tw-text-left tw-my-[6px] tw-text-[14px]">返回设置</div>
          <el-form-item label="播放设置：" prop="returnType">
            <el-radio-group v-model="returnConfigData.returnPlayDefault" class="tw-ml-[6px]">
              <el-radio :label="true">默认未命中</el-radio>
              <el-radio :label="false">自定义播放</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="!!returnConfigData.returnPlayDefault" label="续播垫句：" prop="returnType">
            <el-select
              v-model="returnConfigData.preContinueCorpusIdBeforeDefault"
              placeholder="选择续播垫句，默认关闭"
              style="width:100%"
              clearable
            >
              <el-option
                v-for="item in continueCorpusList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <template v-if="!returnConfigData.returnPlayDefault" >
            <div class="info-title tw-text-left">请根据被打断时播放的语料，设置对应的返回逻辑</div>
            <el-table
              :data="returnConfigData?.dataList"
              scrollbar-always-on
              :header-cell-style="tableHeaderStyle"
            >
              <template #empty>
                暂无数据
              </template>
              <el-table-column label="语句名称" prop="contentName" align="left" width="160">
                <template #default="{ row }">
                  <div class="tw-text-left" :class="row.contentName === tempAudio?.contentName ? 'tw-text-[#165DFF]' : 'tw-text-[#313233]'">{{ row.contentName }}</div>
                </template>
              </el-table-column>
              <el-table-column label="续播垫句" align="left" prop="preContinueCorpusIdForReturn" min-width="200">
                <template #default="{ row }">
                  <el-select
                    v-model="row.preContinueCorpusIdForReturn"
                    placeholder="关闭"
                    style="width:100%"
                    clearable
                  >
                    <el-option
                      v-for="item in continueCorpusList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="续播类型" align="left" prop="corpusReturnType" width="160">
                <template #default="{ row }">
                  <el-select
                    v-model="row.corpusReturnType"
                    placeholder="选择续播类型"
                    style="width:100%"
                  >
                    <el-option
                      v-for="item in returnTypeOption(!row.scriptUnitContentId)"
                      :key="item.value"
                      :label="item.name"
                      :value="item.value"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="承接语料" align="left" prop="interrupt2" min-width="200">
                <template #default="{ row }">
                  <el-select
                    v-if="row.corpusReturnType === ReturnTypeEnum['承接']"
                    v-model="row.preUndertakeCorpusId"
                    placeholder="承接语料"
                    style="width:100%"
                  >
                    <el-option
                      v-for="item in undertakeCorpusList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                  <span v-else>-</span>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </template>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer" >
        <div class="tw-float-left">
          <!-- 保存并返回语料设置，仅当从语料设置页面进入展示 -->
          <el-button v-if="!!props.fromEdit" :loading="loading" type="primary" @click="confirm(false)">语料设置</el-button>
        </div>
        <div class="tw-float-right">
          <el-button @click="cancel()" :icon="CloseBold">{{isChecked ? '关闭' : '取消'}}</el-button>
          <template v-if="!isChecked">
            <!-- 保存并关闭页面 -->
            <el-button :loading="loading" type="primary" @click="confirm(true)" :icon="Select">确定</el-button>
          </template>
        </div>
      </span>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, defineAsyncComponent, onUnmounted } from 'vue'
import { CorpusReturnConfig, ContentInterruptConfigItem, ContentHangUpInterruptConfigItem, AudioItem,
  InterruptNormalTypeEnum, InterruptHangUpTypeEnum, InterruptTypeEnum, ScriptCorpusItem, ReturnTypeEnum, ConnectTypeEnum, CorpusTypeEnum } from '@/type/speech-craft'
import { ElMessage, } from 'element-plus'
import { scriptCorpusModel, } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import { CloseBold, Select } from '@element-plus/icons-vue'
import to from 'await-to-js';
import { tableHeaderStyle } from '@/assets/js/constant'
import { enum2Options, pickAttrFromObj, } from '@/utils/utils'
import InputNumberBox from '@/components/InputNumberBox.vue'
import SelectBox from '@/components/SelectBox.vue'
import { traceApi } from '@/utils/trace';

// props & emits
const emits = defineEmits(['update:visible', 'close'])
const props = defineProps<{
  visible: boolean;
  corpusId: number | undefined;
  fromEdit?: boolean // 是否从编辑语料抽屉过来的，用于控制底部按钮的显示
}>();

const AudioMode = defineAsyncComponent({
  loader:() => {
    return import('@/components/AudioMode.vue')
  }
})

/** 常量 */
const scriptStore = useScriptStore() // 话术信息
const scriptId = scriptStore.id // 话术id
const isChecked = scriptStore.isChecked // true: 话术查看, false: 话术编辑
const drawerWidth = ref(window.innerWidth > 1400 ? '70%' : '900px')
const title = computed(() => `【${corpusData.value?.name || ''}】打断设置`)
const isNormal = computed(() => {
  return !!corpusData.value?.corpusType?.includes('_ORDINARY')
})
const isHangup = computed(() => {
  return corpusData.value?.connectType && [ConnectTypeEnum['挂机']].includes(corpusData.value.connectType)
})
const isBaseOrNormal = computed(() => {
  return corpusData.value?.corpusType && [
    CorpusTypeEnum['主动流程-普通语料'],
    CorpusTypeEnum['深层沟通-普通语料'],
    CorpusTypeEnum['基本问答'],
    CorpusTypeEnum['最高优先'],
  ].includes(corpusData.value.corpusType)
})
const interruptOption = computed(() => {
  return isHangup.value ? enum2Options(InterruptHangUpTypeEnum) : enum2Options(InterruptNormalTypeEnum)
})

const returnTypeOption = (isEnd: boolean) => {
  return isEnd ? [
    { value: ReturnTypeEnum['承接'], name: '承接',},
    { value: ReturnTypeEnum['播放未命中'], name: '播放未命中',},
  ] : [
    { value: ReturnTypeEnum['重播'], name: '重播',},
    { value: ReturnTypeEnum['承接'], name: '承接',},
  ]
}

/** 变量 */
const loading = ref(false)
const dialogVisible = ref(false)
const interruptConfigData = reactive<{
  scriptCorpusId: number | undefined;
  dataList: ContentInterruptConfigItem[] | null,
  dataListEnd: ContentHangUpInterruptConfigItem[] | null,
}>({
  scriptCorpusId: undefined,
  dataList: [],
  dataListEnd: [],
}) // 打断设置
const returnConfigData = reactive<CorpusReturnConfig>({
  scriptCorpusId: undefined,
  returnPlayDefault: true,
  preContinueCorpusIdBeforeDefault: undefined,
  dataList: [],
}) // 返回设置
const corpusData = ref<null | ScriptCorpusItem>(null) // 语料内容

/** 处理函数开始 */
// 底部确认、取消
const confirm = async (closeOuter: boolean) => {
  if (isChecked) {
    cancel(closeOuter)
    return
  } 
  loading.value = true
  /** 校验打断设置并将读取数据，去除不需要的参数 */
  const errMsgArr: string[] = []
  const interuptParams: CorpusReturnConfig = {
    scriptCorpusId: interruptConfigData.scriptCorpusId,
    dataList: isHangup.value ? interruptConfigData.dataListEnd?.map(item => {
      if (!item.interruptType) {
        errMsgArr.push(`【${item.contentName}】请选择打断设置`)
      }
      if (item.interruptType === InterruptTypeEnum['指定语料打断'] && (!item.interruptCorpusIdsForEnd || item.interruptCorpusIdsForEnd.length === 0)) {
        errMsgArr.push(`【${item.contentName}】请选择指定语料`)
      }
      return {
        scriptUnitContentId: item.scriptUnitContentId,
        interruptType: item.interruptType,
        interruptCorpusIdsForEnd: item.interruptCorpusIdsForEnd,
      }
    }) : interruptConfigData.dataList?.map(item => {
      if (!item.interruptType) {
        errMsgArr.push(`【${item.contentName}】请选择打断设置`)
      }
      // if (item.interruptType && item.interruptType !== InterruptTypeEnum['不允许打断'] && !item.allowedInterruptTime) {
      //   errMsgArr.push(`【${item.contentName}】请选择最短播放时长`)
      // }
      return item.interruptType === InterruptTypeEnum['不允许打断'] ? {
        interruptType: item.interruptType,
        scriptUnitContentId: item.scriptUnitContentId,
      } : {
        interruptType: item.interruptType,
        allowedInterruptTime: item.allowedInterruptTime,
        preInterruptCorpusId: (item.interruptType && [InterruptTypeEnum['支持核心短语打断但不回复'], InterruptTypeEnum['支持核心短语打断并回复']].includes(item.interruptType)) 
          ? item.preInterruptCorpusId : undefined,
        preContinueCorpusIdForInterrupt: item.preContinueCorpusIdForInterrupt,
        scriptUnitContentId: item.scriptUnitContentId,
      }
    })
  }
  if (errMsgArr.length > 0) {
    loading.value = false
    return ElMessage.warning(errMsgArr.join(`\n`))
  }
  const err1 = await traceApi(
    `话术编辑-${isHangup.value ? '挂机语料' : '非挂机语料'}打断设置`,
    interuptParams,
    isHangup.value ? scriptCorpusModel.saveInterruptConfigForEnd : scriptCorpusModel.saveInterruptConfig
  )
  if (err1) {
    loading.value = false
    isNormal.value && ElMessage.error('打断设置保存失败')
    return
  }
  /** 校验返回设置并将读取数据，去除不需要的参数 */
  if (isNormal.value) {
    const params:CorpusReturnConfig = pickAttrFromObj(returnConfigData, [
      'returnPlayDefault', 'scriptCorpusId'
    ])
    const errMsgArr2: string[] = []
    // 【if】如返回类型为自定义，还需传参dataList和【语料已播放完】的设置
    // 【else】如返回类型为播放未命中，还需传参续播垫句
    if (!returnConfigData.returnPlayDefault) {
      // 由于最后一个【语料已播放完】的设置，需从dataList中移除，并添加到params的最外层
      params.dataList = returnConfigData.dataList?.slice(0, -1)?.map(item => {
        if (item.corpusReturnType === ReturnTypeEnum['承接'] && !item.preUndertakeCorpusId) {
          errMsgArr2.push(`【${item.contentName}】请选择承接语料`)
        }
        return {
          scriptUnitContentId: item.scriptUnitContentId,
          corpusReturnType: item.corpusReturnType,
          preContinueCorpusIdForReturn: item.preContinueCorpusIdForReturn,
          preUndertakeCorpusId: item.corpusReturnType === ReturnTypeEnum['承接'] ? item.preUndertakeCorpusId : undefined,
        }
      })
      const endInfo = returnConfigData.dataList?.at(-1)
      params.corpusReturnType = endInfo?.corpusReturnType
      params.preContinueCorpusIdForReturn = endInfo?.preContinueCorpusIdForReturn
      params.preUndertakeCorpusId = params.corpusReturnType === ReturnTypeEnum['承接']
        ? endInfo?.preUndertakeCorpusId
        : undefined
      if (params.corpusReturnType === ReturnTypeEnum['承接'] && !params.preUndertakeCorpusId) {
        errMsgArr2.push(`【语料已播放完】请选择承接语料`)
      }
    } else { 
      params.preContinueCorpusIdBeforeDefault = returnConfigData.preContinueCorpusIdBeforeDefault
      params.dataList = []
    }
    if (errMsgArr2 && errMsgArr2.length) {
      loading.value = false
      return ElMessage.warning(errMsgArr2.join(`\n`))
    }
    const err2 = await traceApi(
      `话术编辑-返回设置`,
      params,
      scriptCorpusModel.saveReturnConfig
    )
    if (err2) {
      loading.value = false
      ElMessage.success('打断设置保存成功')
      return ElMessage.error('返回设置保存失败')
    } else {
      ElMessage.success('操作成功')
    }
  }
  loading.value = false
  cancel(closeOuter)
}
const cancel = (closeOuter: boolean = false) => {
  dialogVisible.value = false
  emits('update:visible', false)
  emits('close', !closeOuter ? currentCorpusData.value || null : null)
}
/** 处理函数结束 */

const tempAudio = ref<AudioItem | null>(null)
const audioStatus = ref<'pause' | 'play' | 'none'>('none')
const audioVolume = ref<number>(70)
const handleAudioPlay = async (row?: AudioItem) => {
  if (!row) {
    tempAudio.value = null
    audioStatus.value = 'none'
    return
  }
  if (tempAudio.value && tempAudio.value?.id === row?.id && tempAudio.value?.audioPath === row?.audioPath) {
    tempAudio.value = row || null
    audioStatus.value = audioStatus.value == 'play' ? 'pause' : 'play'
  } else {
    tempAudio.value = row
    audioStatus.value = 'play'
    if (!row.isPlayed && !isChecked) {
      const [err] = await to(scriptCorpusModel.updateCorpusAudioStatus({id: tempAudio.value?.id!}))
      if (!err) {
        tempAudio.value.isPlayed = true
        updatePlayStatus(tempAudio.value.id as number)
      }
    }
  }
}

const updatePlayStatus = (id: number) => {
  if (id && corpusData.value?.scriptMultiContents && corpusData.value?.scriptMultiContents[0]?.scriptUnitContents) {
    const contents = corpusData.value?.scriptMultiContents[0]?.scriptUnitContents || []
    const index = contents.findIndex(item => item.id === id)
    if (index !== -1) {
      corpusData.value.scriptMultiContents[0].scriptUnitContents[index].isPlayed = true
    }
  }
}

/** 初始化 */
const hangupCorpusList = ref<ScriptCorpusItem[] | null>([]) // 最高优先挂机语料
const interruptCorpusList = ref<ScriptCorpusItem[] | null>([]) // 打断垫句
const continueCorpusList = ref<ScriptCorpusItem[] | null>([]) // 续播垫句
const undertakeCorpusList = ref<ScriptCorpusItem[] | null>([]) // 承接语料
const currentCorpusData = ref<ScriptCorpusItem | null>(null)
const init = async () => {
  if (!props.corpusId) {
    return 
  }
  loading.value = true
  // 查询当前语料的语句信息，将打断返回设置从语料返回中提取出来
  const [err, res] = await to(scriptCorpusModel.findMasterCorpus({corpusId: props.corpusId}))
  currentCorpusData.value = res || null
  if (!err && res) {
    corpusData.value = pickAttrFromObj(res, [
      'id', 'name', 'corpusType', 'connectType',
    ])
    Object.assign(returnConfigData, pickAttrFromObj(res, [
      'returnPlayDefault', 'preContinueCorpusIdBeforeDefault',
    ]))
    if (!res.scriptMultiContents || res.scriptMultiContents.length === 0) {
      ElMessage.warning('当前语料不存在语句信息')
    }
    interruptConfigData.scriptCorpusId = corpusData.value?.id
    returnConfigData.scriptCorpusId = corpusData.value?.id
    interruptConfigData.dataList = []
    interruptConfigData.dataListEnd = []
    returnConfigData.dataList = []
    corpusData.value.scriptMultiContents =  res.scriptMultiContents || []
    corpusData.value.scriptMultiContents[0].scriptUnitContents.forEach(item => {
      // 获取语句的打断设置，如无，设置默认不允许打断
      interruptConfigData.dataList?.push({
        scriptUnitContentId: item.id,
        interruptType: item.interruptType ?? undefined,
        allowedInterruptTime: item.allowedInterruptTime,
        preInterruptCorpusId: item.preInterruptCorpusId,
        contentName: item.contentName,
        preContinueCorpusIdForInterrupt: item.preContinueCorpusIdForInterrupt,
      })

      interruptConfigData.dataListEnd?.push({
        scriptUnitContentId: item.id,
        interruptType: item.interruptType ?? undefined,
        interruptCorpusIdsForEnd: item.interruptCorpusIdsForEnd || [],
        contentName: item.contentName
      })
      // 获取语句的返回设置，如返回设置无，默认设置为重播
      returnConfigData.dataList?.push({
        scriptUnitContentId: item.id,
        contentName: item.contentName,
        corpusReturnType: item.corpusReturnType || ReturnTypeEnum['重播'],
        preUndertakeCorpusId: item.preUndertakeCorpusId,
        preContinueCorpusIdForReturn: item.preContinueCorpusIdForReturn,
      })
    })
    // 获取非语句的返回设置，在语料最外层数据放到list中便于列表展示，名称设置为【语料已播放完】
    returnConfigData.dataList.push({
      contentName: '语料已播放完',
      corpusReturnType: res.corpusReturnType || ReturnTypeEnum['播放未命中'],
      preContinueCorpusIdForReturn: res.preContinueCorpusIdForReturn,
      preUndertakeCorpusId: res.preUndertakeCorpusId,
    })
  }

  // 挂机的连接、基本问答、最高优先，打断配置选项有差异
  if (isHangup.value) {
    // 获取最高优先的挂机语料
    const [err, res] = await to(scriptCorpusModel.findPriorList({scriptId: scriptId}))
    hangupCorpusList.value = (res || []).filter(item => item.connectType === ConnectTypeEnum['挂机'] && item.id !== corpusData.value?.id)
  } else if (isBaseOrNormal.value) {
    // 查询打断垫句、续播垫句
    const [err1, res1] = await to(scriptCorpusModel.findPreCorpusList({
      scriptId: scriptId,
      type: CorpusTypeEnum['打断垫句']
    }))
    interruptCorpusList.value = res1 || []
    const [err2, res2] = await to(scriptCorpusModel.findPreCorpusList({
      scriptId: scriptId,
      type: CorpusTypeEnum['续播垫句'],
    }))
    continueCorpusList.value = res2 || []
  }
  // 普通语料，可配置返回设置，需要获取承接语料列表
  if (isNormal.value) {
    const [err, res] = await to(scriptCorpusModel.findPreCorpusList({
      scriptId: scriptId,
      type: CorpusTypeEnum['承接语料'],
    }))
    undertakeCorpusList.value = res || []
  }
  loading.value = false
}
const clearData = () => {
  corpusData.value = null
  interruptConfigData.dataList = null
  interruptConfigData.dataListEnd = null
  returnConfigData.dataList = null
  hangupCorpusList.value = null
  interruptCorpusList.value = null
  continueCorpusList.value = null
  undertakeCorpusList.value = null
  tempAudio.value = null
  audioStatus.value = 'none'
}

/** watch开始 */ 
// 监听入参弹窗visible，对addData数据进行更新和处理
watch(() => props.visible, n => {
  dialogVisible.value = n
  if(n) {
    init()
  } else {
    clearData()
  }
})
onUnmounted(() => {
  clearData()
})
/** watch结束 */
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
.text {
  font-size: 14px;
  line-height: 24px;
  margin-right: 10px;
  text-align: left;
}
.el-table {
  font-size: var(--el-font-size-base);
  :deep(.cell) {
    padding: 0 8px;
  }
}
</style>