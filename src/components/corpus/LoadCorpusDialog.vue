<template>
  <el-dialog
    v-model="dialogVisible"
    class="dialog-form"
    width="540px"
    @close="cancel"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">导入已有语料</div>
    </template>
    <el-form
      v-loading="loading"
      :model="editData"
      :rules="rules"
      :disabled="isChecked"
      label-width="80px"
      ref="editRef"
    >
      <div class="tw-text-[13px] tw-font-[600] tw-text-[var(--el-color-danger)] tw-text-left tw-mt-[12px] tw-mb-[6px] tw-ml-[12px]">注：不会导入语料名称、文字内容和打断设置</div>
      <el-form-item label="选择语料：" prop="corpusId">
        <el-select
          v-model="editData.corpusId"
          filterable
          placeholder="请选择语料"
          style="width:100%"
        >
          <el-option
            v-for="item in corpusOptions"
            :key="item.corpusId"
            :label="item.name"
            :value="item.corpusId"
          >
            <span class="tw-text-[13px]">{{ item.name }}</span>
            <span class="tw-float-right tw-leading-[32px] tw-text-[#969799] tw-text-[13px]">{{ item.canvasName }}</span>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer" v-if="!isChecked">
        <el-button @click="dialogVisible=false">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, onUnmounted,} from 'vue'
import to from 'await-to-js'
import { scriptCanvasModel, scriptCorpusModel } from '@/api/speech-craft'
import { ElMessage, type FormInstance } from 'element-plus'
import { useScriptStore } from '@/store/script'
import { ScriptCorpusItem, CorpusTypeEnum } from '@/type/speech-craft'

const scriptStore = useScriptStore()
const editId = scriptStore.id
const isChecked = scriptStore.isChecked

const loading = ref(false)
const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  data?: ScriptCorpusItem;
}>();
const dialogVisible = ref(props.visible)
const editData = reactive<{
  corpusId?: number,
}>({
  corpusId: undefined
})
const corpusOptions = ref<{
  corpusId: number,
  name: string,
  corpusType: CorpusTypeEnum
  canvasName: string
}[] | null>(null)

const rules = {
  corpusId: [
    { required: true, message: '请选择语料', trigger: 'change' },
  ],
}

const editRef = ref<FormInstance>()
const confirm = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const [err, data] = await to(scriptCorpusModel.findMasterCorpus({
        corpusId: editData.corpusId!
      }))
      loading.value = false
      if (!data) return
      emits('confirm', data)
      cancel()
    }
  })
}
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  emits('update:visible', false)
}

const init = async () => {
  loading.value = true
  if (!scriptStore.currentCanvas?.id) return ElMessage.warning('请先选择画布')
  const [err, data] = await to(scriptCanvasModel.findAllMasterCanvas({
    scriptId: editId
  }))
  corpusOptions.value = (data || []).flatMap(item => {
    return (Object.values(item.canvasCorpusDataMap) || []).flatMap(v => {
      if (v?.corpusType === props.data?.corpusType && v?.corpusId !== props.data?.id) {
        return {
          canvasName: item.name || '',
          corpusId: v.corpusId,
          corpusType: v.corpusType,
          name: v.name,
        }
      } else {
        return []
      }
    })
  })

  loading.value = false
}

onUnmounted(() => {
  corpusOptions.value = null
})

watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    init()
    Object.assign(editData, {
      corpusId: undefined
    })
    editRef.value && editRef.value.clearValidate()
  } else {
    corpusOptions.value = null
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
</style>
