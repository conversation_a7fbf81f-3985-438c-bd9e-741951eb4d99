<template>
  <el-dialog
    v-model="dialogVisible"
    class="dialog-form"
    width="600px"
    @close="cancel"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">复制普通分支</div>
    </template>
    <el-form
      :model="editData"
      :rules="rules"
      :disabled="isChecked"
      label-width="80px"
      ref="editRef"
    >
      <el-form-item label="选择分支：" prop="branchIds">
        <SelectBox 
          v-model:selectVal="editData.branchIds"
          :options="branchList"
          name="branchName"
          val="branchId"
          placeholder="请选择需要复制的分支"
          filterable
          :limitNum="10"
          suffix="corpusName"
          class="tw-grow"
          multiple
        >
        </SelectBox>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer" v-if="!isChecked">
        <el-button @click="dialogVisible=false">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, onUnmounted,} from 'vue'
import to from 'await-to-js'
import { scriptBranchModel, } from '@/api/speech-craft'
import type { FormInstance } from 'element-plus'
import { useScriptStore } from '@/store/script'
import { ScriptNormalBranchItem } from '@/type/speech-craft'
import { CorpusPriorTypeEnum } from '@/type/speech-craft'
import SelectBox from '@/components/SelectBox.vue'

const scriptStore = useScriptStore()
const editId = scriptStore.id
const isChecked = scriptStore.isChecked

const loading = ref(false)
const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  corpusId?: number;
}>();
const dialogVisible = ref(props.visible)
const editData = reactive<{
  branchIds?: number[],
}>({
  branchIds: undefined
})
const branchList = ref<ScriptNormalBranchItem[] | null>(null)
  

const rules = {
  branchId: [
    { required: true, message: '请选择需要复制的分支', trigger: 'change' },
  ],
}

const editRef = ref<FormInstance>()
const confirm = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      const data = branchList.value?.flatMap(item => {
        if (item.branchId && editData.branchIds?.includes(item.branchId)) {
          return [{
            preCorpusId: props.corpusId,
            type: CorpusPriorTypeEnum['普通分支'],
            scriptId: editId,
            name: item?.branchName || '',
            semCombineEntity: item.semCombineEntity,
            backExcluded: false
          }]
        } else {
          return []
        }
      })
      if (!data || !data.length) return
      emits('confirm', data)
      cancel()
    }
  })
}
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  emits('update:visible', false)
}

const init = async () => {
  loading.value = true
  const [err, data] = await to(scriptBranchModel.findList({ scriptId: editId }))
  branchList.value = data || []
  loading.value = false
}

onUnmounted(() => {
  branchList.value = null
})

watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    init()
    Object.assign(editData, {
      branchIds: undefined
    })
    editRef.value && editRef.value.clearValidate()
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
</style>
