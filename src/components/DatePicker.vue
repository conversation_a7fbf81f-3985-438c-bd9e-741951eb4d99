<template>
  <div v-if="showShortcut">
    <div class="tw-flex tw-items-center">
      <span class="tw-w-[80px] tw-text-right">拨打时段：</span>
      <el-button class="tw-ml-0.5" link type="primary" @click="handleSelectAllTime">全选</el-button>
      <el-button class="tw-ml-0.5" link type="primary" @click="handleClearAllTime">清空</el-button>
    </div>
  </div>
  <div class="time-box tw-flex tw-items-end tw-w-full tw-flex-wrap tw-mb-[-8px] tw-pl-[12px]">
    <el-tooltip
      v-for="(item, index) in timePickerList"
      :key="item"
      :show-after="500"
    >
      <template #content>
        <div>
          {{ item + "，" + (WEEKMAP.get(dayjs(item).format('ddd')) || '') }}
        </div>
      </template>
      <span
        :class="{'active': timeArr.includes(item)}"
        @mouseenter="isMouseDown && handleChange(item)"
        @click="handleChange(item)"
        class="tw-w-[20px] tw-h-[20px] tw-text-[12px] tw-border-[1px] tw-box-border tw-border-[#c9c9c9] hover:tw-scale-y-110 tw-relative tw-cursor-pointer tw-leading-[20px] tw-my-[3px]"
      >
        {{ dayjs(item).format('DD') }}
      </span>
    </el-tooltip>
    <div v-if="editable" class="tw-self-start tw-ml-[6px]">
      <el-button v-if="!editTimeStatus" link type="primary" @click="handleStartEdit">修改</el-button>
      <template v-else>
        <el-button link type="primary" @click="handleTimeArrChange">确认</el-button>
        <el-button link type="primary" @click="handleCancelTime">取消</el-button>
      </template>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive, onMounted, computed, } from 'vue'
import dayjs from 'dayjs';
import { WEEKMAP } from '@/assets/js/constant'
// emits和props
const emits = defineEmits(['update:dates'])
const props = defineProps<{
  dates: string[],
  showShortcut?: boolean,
  editable?: boolean,
  beginTime?:string | Date,
  endTime?:string | Date,
}>();

// 获取时间开始结束时间，
const timePickerList = computed(() => {
  const beginTime = props.beginTime ? dayjs(props.beginTime).format('YYYY-MM-DD') : dayjs().add(-30, 'day').format('YYYY-MM-DD')
  const endTime = props.endTime ? dayjs(props.endTime).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD')
  const res: string[] = []
  let startDate = beginTime
  while(!dayjs(startDate).isAfter(dayjs(endTime))) {
    res.push(startDate)
    startDate = dayjs(startDate).add(1, 'day').format('YYYY-MM-DD')
  }
  return res
})
// 选中数据
const timeArr = ref<string[]>(props.dates)
const originTimeArr = ref<string[]>([])
// 组件显示信息
const showShortcut = computed(() => props.showShortcut || false) // 头部【全部、清空】显示
const editable = computed(() => props.editable || false) // 是否可编辑模式：即需要点击编辑再能进入编辑状态
const editTimeStatus = ref(false)  // 可编辑模式下，是否编辑状态
// 用户修改选中数据click后执行函数
const handleChange = (item: string) => {
  // 可编辑模式下，非编辑状态下，不更新数据
  if (editable.value && !editTimeStatus.value) return
  const index = timeArr.value.indexOf(item)
  if (index > -1) {
    timeArr.value.splice(index, 1)
  } else {
    timeArr.value.push(item)
    timeArr.value.sort((a, b) => dayjs(a).isAfter(dayjs(b)) ? 1 : -1)
  }
  !editable.value && handleTimeArrChange()
}
// 更新选择数据
const handleTimeArrChange = () => {
  editable.value && (editTimeStatus.value = false)
  emits('update:dates', timeArr.value)
}
// 可编辑模式下- 进入编辑
const handleStartEdit = () => {
  editable.value && (editTimeStatus.value = true)
  originTimeArr.value = [...timeArr.value]
}
// 可编辑模式下，取消执行函数
const handleCancelTime = () => {
  timeArr.value = [...originTimeArr.value]
  editTimeStatus.value = false
}
// 全选执行函数
const handleSelectAllTime = () => {
  timeArr.value = [...timePickerList.value]
  handleTimeArrChange()
}
// 全清空执行函数
const handleClearAllTime = () => {
  timeArr.value = []
  handleTimeArrChange()
}

// 监听器 - 入参变化
watch(() => props.dates, () => {
  timeArr.value = props.dates
}, {
  deep: true,
  immediate: true
})

// 进入组件执行
const isMouseDown = ref(false) // 鼠标按下
onMounted(async () => {
  // 鼠标按住连续选中数据
  let box = document.querySelector('.time-box');
  box?.addEventListener('mousedown', (e) => {
    isMouseDown.value = true
    document.addEventListener('mouseup', upHandler);
  });
  function upHandler(){
    isMouseDown.value = false
    document.removeEventListener('mouseup', upHandler);
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.time-box {
  user-select: none;
}
.active {
  background-color: var(--el-color-primary);
  color: #fff;
}
</style>
