<template>
  <div v-if="showShortcut">
    <div class="tw-flex tw-items-center tw-text-[13px]">
      <span class="tw-w-[90px] tw-text-right">{{props.name || '拨打时段'}}：</span>
      <el-button class="tw-ml-0.5" link type="primary" @click="handleSelectAllTime">全选</el-button>
      <el-button class="tw-ml-0.5" link type="primary" @click="handleClearAllTime">清空</el-button>
    </div>
  </div>
  <div class="time-box tw-flex tw-items-end tw-mt-[6px] tw-w-full tw-flex-wrap tw-mb-[-8px] tw-pl-[12px]" >
    <span
      v-for="item in timePickerListOptions"
      :key="item.begin"
      class="tw-w-[20px] tw-h-[16px] tw-text-[14px] tw-border-[1px] tw-box-border tw-border-[#c9c9c9] hover:tw-scale-y-110 tw-relative tw-cursor-pointer tw-mb-[16px]"
      :class="{'active': timeArr.includes(item.val)}"
      @mouseenter="isMouseDown && handleChange(item)"
      @click="handleChange(item)"
    >
      <span v-if="item.begin== timePickerListOptions[0].begin" class="tw-absolute tw-top-[17px] tw-left-[-20px] tw-leading-[14px]">{{ item.begin }}</span>
      <span v-if="item.begin && ![timePickerListOptions[0].begin, timePickerListOptions.slice(-1)[0].begin].includes(item.begin) && item.begin.includes(':00')" class="tw-absolute tw-leading-[16px] tw-top-[17px] tw-left-[-10px]">{{ item.begin.split(':')[0]}}</span>
      <span v-if="item.end== timePickerListOptions.slice(-1)[0].end" class="tw-absolute tw-top-[17px] tw-right-[-20px] tw-leading-[14px]">{{ item.end }}</span>
    </span>
  </div>
  <div v-if="editData.startWorkTimeList && editData.startWorkTimeList.length > 0 && showResult" class="tw-flex tw-flex-wrap tw-items-center tw-mt-1 tw-text-[13px]">
    <div class="tw-w-[90px] tw-text-right tw-mr-[12px]">选中时间段：</div>
    <el-tag
      v-for="(item, index) in editData.startWorkTimeList"
      :key="item"
      class="tw-mr-[4px] tw-my-[2px]"
    >{{ item + ' - ' +  editData.endWorkTimeList[index]}}</el-tag>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive, onMounted, computed, } from 'vue'
import { timePickerOptions } from '@/assets/js/constant'
import { useUserStore } from '@/store/user'
import { ElMessage } from 'element-plus';

// emits和props
const emits = defineEmits(['update'])
const props = defineProps<{
  name?: string
  startWorkTimeList?: string[],
  endWorkTimeList?: string[],
  showShortcut?: boolean,
  showResult?: boolean,
  beginTime?:string,
  endTime?:string,
}>();

// 获取时间开始结束时间，
const useInfo = useUserStore()
const { accountType } = useInfo
const timePickerListOptions = computed(() => {
  const arr = Object.values(timePickerOptions)
  const beginTime = props.beginTime ?? (accountType === 1 ? useInfo.timeRange[0] : '08:00')
  const endTime = props.endTime ?? (accountType === 1 ? useInfo.timeRange[1] : '22:00')
  const sta = Object.values(timePickerOptions).findIndex(item => item.begin ===beginTime)
  const end = Object.values(timePickerOptions).findIndex(item => item.end === endTime)
  return arr.slice(sta, end + 1)
})
// 选中数据
const editData = reactive<{
  startWorkTimeList: string[],
  endWorkTimeList: string[]
}>({
  startWorkTimeList: props.startWorkTimeList || [],
  endWorkTimeList: props.endWorkTimeList || [],
})
const timeArr = ref<number[]>([])
// 组件显示信息
const showShortcut = computed(() => props.showShortcut || false) // 头部【全部、清空】显示
const showResult = computed(() => props.showResult || false) // 底部【结果】显示
const editTimeStatus = ref(false)  // 可编辑模式下，是否编辑状态
// 用户修改选中数据click后执行函数
const handleChange = (item: {
 begin: string,
 end: string,
 val: number,
}) => {
  const index = timeArr.value.indexOf(item.val)
  if (index > -1) {
    timeArr.value.splice(index, 1)
  } else {
    timeArr.value.push(item.val)
  }
  handleTimeArrChange()
}
// 更新选择数据
const handleTimeArrChange = () => {
  const arr = timeArr.value.sort((a, b) => a-b).map(item => {
    // @ts-ignore
    return timePickerOptions[item]
  }) || []
  editData.startWorkTimeList = []
  editData.endWorkTimeList = []
  while( arr && arr.length > 0) {
    const tempVal = arr.shift()
    const len = editData.endWorkTimeList.length
    if (len > 0 && tempVal.begin == editData.endWorkTimeList[len -1]) {
      editData.endWorkTimeList[len -1] = tempVal.end
    } else {
      editData.startWorkTimeList.push(tempVal.begin)
      editData.endWorkTimeList.push(tempVal.end)
    }
  }
  emits('update', editData.startWorkTimeList, editData.endWorkTimeList)
}

// 全选执行函数
const handleSelectAllTime = () => {
  timeArr.value = timePickerListOptions.value.map(item => item.val)
  handleTimeArrChange()
}
// 全清空执行函数
const handleClearAllTime = () => {
  timeArr.value = []
  handleTimeArrChange()
}
const updateTimeArr = () => {
  timeArr.value = []
  editData.startWorkTimeList.map((timeVal, index) => {
    const options = Object.values(timePickerListOptions.value)
    const index1 = options.findIndex(item => item.begin === editData.startWorkTimeList[index])
    const index2 = options.findIndex(item => item.end === editData.endWorkTimeList[index])
    if (index1 !== -1 && index2 !== -1) {
      timeArr.value.push(...options.slice(index1, index2 + 1).map(item => item.val))
    } else {
      ElMessage.warning('当前选择超出账号时间范围！')
    }
  })
}
// 监听器 - 入参变化
watch([() => props.startWorkTimeList, () => props.endWorkTimeList], n => {
  editData.startWorkTimeList = n[0] || []
  editData.endWorkTimeList = n[1] || []
  updateTimeArr()
}, {
  deep: true
})

// 进入组件执行
const isMouseDown = ref(false) // 鼠标按下
onMounted(async () => {
  timeArr.value = []
  editData.startWorkTimeList.map((timeVal, index) => {
    const options = Object.values(timePickerOptions)
    const index1 = options.findIndex(item => item.begin === editData.startWorkTimeList[index])
    const index2 = options.findIndex(item => item.end === editData.endWorkTimeList[index])
    timeArr.value.push(...options.slice(index1, index2 + 1).map(item => item.val))
  })
  // 鼠标按住连续选中数据
  let box = document.querySelector('.time-box');
  box?.addEventListener('mousedown', (e) => {
    isMouseDown.value = true
    document.addEventListener('mouseup', upHandler);
  });
  function upHandler(){
    isMouseDown.value = false
    document.removeEventListener('mouseup', upHandler);
  }
  await useInfo.updateUserTimeRange()
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.time-box {
  user-select: none;
}
.active {
  background-color: var(--el-color-primary);
}
.el-tag {
  padding: 0 6px;
  background-color: #ebf2ff;
  color: #165DFF;
}
</style>
