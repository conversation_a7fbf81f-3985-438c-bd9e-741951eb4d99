<template>
  <el-dialog
    v-model="dialogVisible"
    width="540px"
    align-center
    class="dialog-form"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">时间范围修改</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-pr-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="90px"
        scroll-to-error
        :scroll-into-view-options="{ behavior: 'smooth', block: 'center' }"
        ref="editRef"
      >
        <el-form-item v-if="dialogVisible" prop="startWorkTimeList" :label="['HH:mm', 'HH:mm:ss' ].includes(props.format) ? '时间范围：' : '日期范围：'" :rules="rules" ref="timeRef">
          <MutiTimeRangeSelectBox
            v-model:startWorkTimeList="editData.startWorkTimeList"
            v-model:endWorkTimeList="editData.endWorkTimeList"
            :format="props.format"
            separator="-"
            :defaultStart="props.defaultStart"
            :defaultEnd="props.defaultEnd"
          />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button type="primary" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, } from 'vue'
import type { FormInstance, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import MutiTimeRangeSelectBox from '@/components/MutiTimeRangeSelectBox.vue'

const emits = defineEmits(['update:visible', 'confirm'])

const props = defineProps<{
  format: string;
  editData: {
    startWorkTimeList: string[],
    endWorkTimeList: string[],
  },
  defaultStart: string,
  defaultEnd: string
  visible: boolean,
}>();

const validTimeList = (rule: any, value: any, callback: any) => {
  if (!editData.startWorkTimeList || editData.startWorkTimeList.length === 0) {
    callback(new Error('时长选择不可为空'))
  }
  editData.startWorkTimeList.forEach((item1, index1: number) => {
    if (!editData.startWorkTimeList[index1] || !editData.endWorkTimeList[index1]) {
      return callback(new Error('开始结束时间不能为空'))
    }
    // @ts-ignore
    if (editData.startWorkTimeList[index1]! < props.defaultStart || editData.endWorkTimeList[index1]! > props.defaultEnd) {
      return callback(new Error(`选择时间超出可选范围(${props.defaultStart}至${props.defaultEnd})`))
    }
    if (['HH:mm', 'HH:mm:ss'].includes(props.format) && editData.startWorkTimeList[index1] == editData.endWorkTimeList[index1]) {
      return callback(new Error('开始结束时间不能相同'))
    }
    editData.startWorkTimeList.forEach((item2, index2: number) => {
      if (index1 >= index2) {
        return
      }
      if (!editData.startWorkTimeList[index2] || !editData.endWorkTimeList[index2]) {
        return callback(new Error('开始结束时间不能为空'))
      }
      if (!(editData.startWorkTimeList[index1]! >= editData.endWorkTimeList[index2]! || editData.startWorkTimeList[index2]! >= editData.endWorkTimeList[index1]!)) {
        return callback(new Error('时间存在交集，请检查！'))
      }
    })
  })
  return callback()
}

const rules = [
  { validator: validTimeList, trigger: ['change'] },
]
const dialogVisible = ref(props.visible)
const editData = reactive<{
    startWorkTimeList: (string | undefined)[],
    endWorkTimeList: (string | undefined)[],
  }>(JSON.parse(JSON.stringify(props.editData)))
const editRef = ref<FormInstance  | null>(null)

const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

const confirm = () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      emits('confirm', editData)
      cancel()
    }
  })
}
watch(() => props.visible, n => {
  dialogVisible.value = props.visible
  if (n) {
    Object.assign(editData, JSON.parse(JSON.stringify(props.editData)))
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.info-data-box-inner {
  padding: 4px 8px;
  gap: 0;
}
</style>
