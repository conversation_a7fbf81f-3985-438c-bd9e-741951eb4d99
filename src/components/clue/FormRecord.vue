<template>
  <el-form
    v-if="!props.readonly"
    :model="editData"
    ref="formInfoRef"
    label-position="top"
    label-width="100px"
    :rules="rules" class="tw-w-full"
  >
  <div :class="props.gridNum > 1 ? `tw-grid tw-grid-cols-${props.gridNum} tw-gap-1` : ''" class="tw-mt-[8px]">
    <el-form-item
      v-for="(item, index) in formSetting"
      :key="item.id"
      :label="(index+1) + '. ' + (item.requiredField ? '[必要]' : '') + item.collectionItemName + '：'"
      :prop="item.id+''"
    >
      <el-input
        v-if="[FormItemEnum['短文本（20字符内）'], FormItemEnum['长文本（500字符内）']].includes(item.collectionItemType)"
        v-model="editData[item.id!]"
        :placeholder="translatePlaceholder(item)"
        clearable
        :maxlength="item.collectionItemType===FormItemEnum['短文本（20字符内）'] ? 20 : 60"
        :type="item.collectionItemType===FormItemEnum['短文本（20字符内）'] ? 'text' : 'textarea'"
        @blur="updateData(+item.id!)"
      />
      <el-select
        v-if="[FormItemEnum['单选'], FormItemEnum['多选']].includes(item.collectionItemType)"
        v-model="editData[item.id!]"
        :placeholder="translatePlaceholder(item)"
        :multiple="FormItemEnum['多选']===item.collectionItemType"
        @change="updateData(+item.id!)"
        filterable
        style="width:100%"
        clearable
      >
        <el-option
          v-for="v in item.options"
          :key="v"
          :label="v"
          :value="v"
        />
      </el-select>
      <el-cascader
        v-if="[FormItemEnum['级联（单选）']].includes(item.collectionItemType)"
        v-model="editData[item.id!]"
        :options="item.cascadeOptions?.map(item => JSON.parse(item))"
        :placeholder="translatePlaceholder(item)"
        style="width:100%"
        :props="cascaderProps"
        filterable
        @change="updateData(+item.id!)"
        separator="-"
        clearable
      />
      <el-date-picker
        v-if="[FormItemEnum['日期'], FormItemEnum['时间']].includes(item.collectionItemType)"
        v-model="editData[item.id!]"
        @change="updateData(+item.id!)"
        class="tw-flex-grow"
        :format="timeType[item.collectionItemType]?.format"
        :type="timeType[item.collectionItemType]?.type"
        :clearable="false"
      />
      <TimePickerBox
        v-if="[FormItemEnum['日期范围'], FormItemEnum['时间范围'], FormItemEnum['日期时间范围']].includes(item.collectionItemType)"
        v-model:start="editData[item.id!][0]"
        v-model:end="editData[item.id!][1]"
        @change="updateData(+item.id!)"
        :clearable="false"
        :format="timeType[item.collectionItemType+'']?.format"
        :type="timeType[item.collectionItemType+'']?.type"
      />
    </el-form-item>
  </div>
  </el-form>
  <div v-else class="tw-mt-1 tw-gap-[12px]" :class="props.gridNum > 1 ? `tw-grid tw-grid-cols-${props.gridNum}` : 'tw-flex tw-flex-col'">
    <div v-for="(item, index) in formSetting" :key="item.id">
      <div class="info-title-deep tw-text-left tw-mb-[4px]">{{ (index+1) + '. ' + (item?.requiredField ? '[必要]' : '') + item?.collectionItemName + '：' }}</div>
      <el-input :model-value="getFormContent(item.id!)" disabled></el-input>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, } from 'vue'
import { FormRules } from 'element-plus'
import { CollectionFormItem, FormItemEnum, } from '@/type/clue'
import dayjs from 'dayjs'
import TimePickerBox from '@/components/TimePickerBox.vue'
const emits = defineEmits(['update:formRecordContent'])
const props = withDefaults(defineProps<{
  formRecordContent?: {id?: number, formCollectionId: number, content: string}[], // 表单记录
  formSetting?: CollectionFormItem[] // 表单设置
  requiredField?: boolean, // 是否需要必填校验
  readonly?: boolean,
  gridNum?: number
}>(), {
  requiredField: false,
  readonly: false,
  gridNum: 1,
  formSetting: () => [],
  formRecordContent: () => [],
})
interface CollectionFormExternItem extends  CollectionFormItem {
  value?: string
}

// 表单收集项数据，用于向外更新数据
const formRecordContent = ref(props.formRecordContent || [])

// 将表单收集项数据，转换为表格填写的对象数据，方便表格校验
const editData = ref<{
  [key: string]: any
}>({}) 

const rules = ref<FormRules>({})
const formInfoRef = ref()
const cascaderProps = {
  value: 'label'
}

// 不同时间选择类型，对应的TimePickerBox需要的入参
const timeType: {[key: string]: {type: string, format: string}} = {
  [FormItemEnum['日期']]: { type: 'date', format: 'YYYY-MM-DD',},
  [FormItemEnum['时间']]: { type: 'datetime', format: 'YYYY-MM-DD HH:mm:ss',},
  [FormItemEnum['日期范围']]: { type: 'daterange', format: 'YYYY-MM-DD',},
  [FormItemEnum['时间范围']]: { type: 'timerange', format: 'YYYY-MM-DD HH:mm:ss',},
  [FormItemEnum['日期时间范围']]: { type: 'datetimerange', format: 'YYYY-MM-DD HH:mm:ss',},
}
// 占位符Placeholder
const translatePlaceholder = (item: CollectionFormItem) => {
  switch(item.collectionItemType) {
    case FormItemEnum['短文本（20字符内）']: return `请输入${item.collectionItemName}（20字符内）`;
    case FormItemEnum['长文本（500字符内）']: return `请输入${item.collectionItemName}（500字符内）`;
    case FormItemEnum['单选']: return `请选择${item.collectionItemName}`;
    case FormItemEnum['多选']: return `请选择${item.collectionItemName}（多选）`;
    case FormItemEnum['日期']: return `请选择${item.collectionItemName}`;
    case FormItemEnum['时间']: return `请选择${item.collectionItemName}`;
    case FormItemEnum['日期范围']: return `请选择${item.collectionItemName}`;
    case FormItemEnum['时间范围']: return `请选择${item.collectionItemName}`;
    case FormItemEnum['日期时间范围']: return `请选择${item.collectionItemName}`;
  }
}

// 表单设置，设置数据，包含表单项的类型、选项等
const formSetting = ref<CollectionFormExternItem[]>(props.formSetting || [])

// 根据入参，初始化表单
const init = async () => {
  // 入参读取
  formRecordContent.value = props.formRecordContent || [] // 表单收集项列表
  formSetting.value = props.formSetting || [] // 表单设置列表

  // 将入参表单信息填充到editData，若无则都放空值
  editData.value = {}
  rules.value = {}
  formSetting.value?.map(item => {
    // 通过表单设置初始化editData数据

    // 从表单收集项列表找到该表单项的收集信息
    const row = props.formRecordContent?.find(v => +v.formCollectionId === item.id && !!item.id)
    if ([FormItemEnum['日期范围'], FormItemEnum['时间范围'], FormItemEnum['日期时间范围']].includes(item.collectionItemType)) {
      // 如果存在表单收集项信息，则读取，否则填充[undefined, undefined]
      if (row?.content) {
        const arr = row?.content.split(',')
        editData.value[item.id!] = [
          arr[0] ? dayjs(arr[0]).format(timeType[item.collectionItemType].format) : undefined,
          arr[1] ? dayjs(arr[1]).format(timeType[item.collectionItemType].format) : undefined,
        ]
      } else {
        editData.value[item.id!] = [undefined, undefined]
      }
      
    } else if(item.collectionItemType === FormItemEnum['级联（单选）']) {
      // 如果存在表单收集项信息，则读取，否则填充[]，级联选项存储格式为a-b,转换为数据为[a,b]
      editData.value[item.id!] = row?.content ? row?.content.split('-') : []
    } else if(item.collectionItemType === FormItemEnum['多选']) {
      editData.value[item.id!] = row?.content ? row?.content.split(',') : []
    } else if ([FormItemEnum['日期'], FormItemEnum['时间']].includes(item.collectionItemType)) {
      editData.value[item.id!] = row?.content ? dayjs(row?.content).format(timeType[item.collectionItemType].format) : undefined
    } else {
      editData.value[item.id!] = row?.content ? row?.content : undefined
    }
    // 通过表单信息设置初始化rules
    if (props.requiredField) {
      rules.value[item.id!] = [
        { required: !!item.requiredField, message: translatePlaceholder(item), trigger: ['blur', 'change']}
      ]
    }
    item.value = row?.content || ''
    return item
  })
}
// 表格label，对表单设置存在requiredField添加[必要]
const getFormContent = (id: number)=> {
  const row = props.formRecordContent?.find(v => +v.formCollectionId === id && !!id)
  return row?.content || ''
}

// 更新表单收集项数据，并暴露给父组件。
const updateData = (id: number) => {
  // 通过当前更新的表单项id，找到formRecordContent需要更新的index
  let index = formRecordContent.value?.findIndex(item => item.formCollectionId === id)
  // 通过当前更新的表单项id，找到formSetting表单设置的信息，选项、类型等
  const item = formSetting.value.find(item => item.id === id)

  // 如果formRecordContent没有改项，则为其创建的
  if (index === -1) {
    index = formRecordContent.value.length
    formRecordContent.value.push({
      formCollectionId: id,
      content: ''
    })
  }
  // 根据表单设置，更新formRecordContent对应的数据
  if (item) {
    const content = editData.value[+item.id!]
    if ([FormItemEnum['日期范围'], FormItemEnum['日期时间范围'], FormItemEnum['多选']].includes(item.collectionItemType)) {
      formRecordContent.value[index].content = content && content[0] ? content.join(',') : ''
    } else if ([FormItemEnum['时间范围'], ].includes(item.collectionItemType)){
      formRecordContent.value[index].content = content && content[0] && content[1] ? content.join(',') : ''
    } else if ([FormItemEnum['级联（单选）']].includes(item.collectionItemType)) {
      formRecordContent.value[index].content = content && content[0] ? content.join('-') : ''
    } else if ([FormItemEnum['日期'], FormItemEnum['时间']].includes(item.collectionItemType)) {
      formRecordContent.value[index].content = content ? dayjs(content).format(timeType[item.collectionItemType].format) : ''
    } else {
      formRecordContent.value[index].content = content || ''
    }
    emits('update:formRecordContent', formRecordContent.value)
  }
  
}
const validForm = () => {
  return formInfoRef.value.validate()
}
defineExpose({
  validForm,
})
init()
watch([() => props.formRecordContent, () => props.formSetting, () => props.readonly], () => {
  init()
}, {deep: true,})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form--label-top {
  .el-form-item {
    margin-bottom: 10px;
    &:first-child {
      margin-top: 0px;
    }
    :deep(.el-form-item__label) {
      margin-bottom: 3px;
    }
  }
}
</style>
