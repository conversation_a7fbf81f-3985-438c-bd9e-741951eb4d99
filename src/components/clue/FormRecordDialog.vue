<template>
  <el-dialog
    v-model="visible"
    class="dialog-form"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">{{props.title}}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 160px)'"
      wrap-class="tw-px-[12px]"
    >
      <FormRecord v-model:formRecordContent="fromCollectionContentList" :readonly="type==='check'" ref="formRecordRef" key="dialogFormRecord" :formSetting="formSetting"/>
    </el-scrollbar>
    <template #footer>
      <span v-if="type === 'edit'" class="dialog-footer">
        <el-button @click="visible=false" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
      <span v-else>
        <el-button @click="visible=false" :icon="CloseBold">关闭</el-button>
        <!-- 对于check模式，仅在商户端可以编辑，运营端不可以编辑 -->
        <el-button v-if="type === 'check' && accountType" :loading="loading" type="primary" @click="type='edit'">
          <el-icon><SvgIcon name="edit" color="#fff"></SvgIcon></el-icon>
          <span>编辑</span>
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, watch, reactive, defineAsyncComponent, } from 'vue'
import { ElMessage, dialogEmits, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import to from 'await-to-js';
import { FormRecordItem, CollectionFormItem, } from '@/type/clue'
import { clueManagerModel, formRecordModel } from '@/api/clue'
import { useUserStore } from '@/store/user'

const FormRecord = defineAsyncComponent({loader: () => import('./FormRecord.vue')})
const emits = defineEmits(['update:visible', 'confirm'])
const props = withDefaults(defineProps<{
  formRecord: FormRecordItem,
  formSetting: CollectionFormItem[],
  visible: boolean,
  type?: 'check' | 'edit' | 'view', // 'check':查看，禁用表单，可点击编辑进入edit； | 'edit'： 编辑| 'view'： 内容预览，无法进入编辑，但可以修改值
  title?: string,
}>(), {
  visible: false,
  formSetting: () => [],
  formRecord: () => {return {fromCollectionContentList: []}},
  type: 'edit',
  title: '表单编辑',
  showEdit: false
})
class FormRecordOriginItem {
  callSeatId = undefined
  clueId = undefined
  formRecordId = undefined
  fromCollectionContentList = []
}
const fromCollectionContentList = ref<{id?: number, formCollectionId: number, content: string}[]>([])
const formSetting = ref(props.formSetting || [])
const formRecordRef = ref()
const userInfo = useUserStore()
const { accountType } = userInfo
const visible = ref(false)
const type = ref(props.type)
watch(() => props.visible, n => {
  visible.value = n
  if (n) {
    type.value = props.type
    formSetting.value = props.formSetting
    fromCollectionContentList.value = JSON.parse(JSON.stringify(props.formRecord?.fromCollectionContentList || []))
  }
}, {immediate: true })
const loading = ref(false)
const cancel = () => {
  emits('update:visible', false)
}
// const updateFormRecord = (val: {formCollectionId: number, content: string}[]) => {
//   formRecordData.fromCollectionContentList = val
// }
const confirm = async () => {
  formRecordRef.value.validForm().then(async () => {
    loading.value = true
    const [err, _] = await to(formRecordModel.saveFormRecord({
      fromCollectionContentList: fromCollectionContentList.value,
      callSeatId: props.formRecord.callSeatId,
      clueId: props.formRecord.clueId,
      formId: props.formRecord.formId,
    }))
    if (!err) {
      emits('confirm')
      ElMessage({
        type: 'success',
        message: '操作成功！'
      })
      cancel()
    }
    loading.value = false
  })
}
</script>

<style lang="postcss" type="text/postcss" scoped>
</style>
