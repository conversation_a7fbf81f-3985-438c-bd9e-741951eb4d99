<template>
  <el-dialog
    v-model="dialogVisible"
    width="540px"
    @close="cancel"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">编辑导入号码</div>
    </template>
    <el-form
      :model="addData"
      :rules="phoneRules"
      label-width="80px"
      ref="addFormRef"
    >
      <el-form-item v-if="!addData.id" label="号码：" prop="phone">
        <el-input
          v-model="addData.phone"
          :disabled="!!addData.id"
          clearable
          placeholder="请输入号码"
          @keyup.enter="confirm()"
        />
      </el-form-item>
      <el-form-item  label="有效时间：">
        <el-radio-group v-model="expirationType">
          <el-radio v-if="props.groupType === 'white' || !!addData.id" :label="0">永久</el-radio>
          <el-radio v-if="props.groupType.includes('black') && !addData.id" :label="2">固定时长</el-radio>
          <el-radio :label="1">自定义</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="expirationType===1" label="到期时间：" prop="expireDate">
        <el-date-picker
          v-model="addData.expireDate"
          style="width: 100%"
          format="YYYY-MM-DD"
          :shortcuts="shortcuts"
          :disabled-date="disabledFn"
          placeholder="请选择过期时间"
          type="date"
          :clearable="false"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, reactive, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import dayjs from 'dayjs'
import { useUserStore } from '@/store/user'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { WhiteBlackListItem, } from '@/type/dataFilter'
import to from 'await-to-js'
import { merchantBlacklistModel, whitelistModel, blacklistModel } from '@/api/data-filter'
import type { FormInstance } from 'element-plus'
import { phoneRules, BlackPhoneOrigin } from './constant'
import { shortcuts } from '@/utils/constant'
import { trace } from '@/utils/trace'

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  editData?: WhiteBlackListItem;
  groupId: string; // 分组id
  groupType: string; // black | white | merchant-black
}>();
const loading = ref(false)
const userStore = useUserStore()
const dialogVisible = ref(props.visible)
const addData = reactive<WhiteBlackListItem>(new BlackPhoneOrigin())
const expirationType = ref(0)
const addFormRef = ref<FormInstance | null>(null)

const confirmSaveAction = async () => {
  loading.value = true
  const params: {
    phone?: string,
    expireDate?: string,
    id?: number,
    blackGroupId?: string,
  } = {
    id: addData.id || undefined,
    
  }
  if (expirationType.value === 0) {
    params.expireDate = '2400-01-01 00:00:00'
  } else if (expirationType.value === 1) {
    params.expireDate = dayjs(addData.expireDate).format('YYYY-MM-DD HH:mm:ss')
  } else if (expirationType.value === 2) {
    params.expireDate = undefined
  }
  
  let err: null | Error
  const apiObj: Record<string, {
    name: string,
    api: [Function, Function]
  }> = {
    'merchant-black': {
      name: '商户黑名单',
      api: [merchantBlacklistModel.importSinglePhone, merchantBlacklistModel.saveSinglePhone]
    },
    'white': {
      name: '白名单',
      api: [whitelistModel.importSinglePhone, whitelistModel.saveSinglePhone ]
    },
    'black': {
      name: '黑名单',
      api: [blacklistModel.importSinglePhone, blacklistModel.saveSinglePhone ]
    },
  }
  if (!addData.id) {
    params.blackGroupId = props.groupType.includes('black') ? props.groupId : undefined // 进黑名单导入有，白名单和编辑黑名单都没有
    params.phone = addData.phone
    trace({ page: `${apiObj[props.groupType]?.name || ''}-单个号码导入`, params })
    const res = await to(apiObj[props.groupType]?.api[0](params))
    err = res[0]
  } else {
    trace({
      page: `${apiObj[props.groupType]?.name || ''}-编辑导入号码`,
      params,
    })
    const res = await to(apiObj[props.groupType]?.api[1](params))
    err = res[0]
  }
  loading.value = false
  if (!err) {
    ElMessage.success('操作成功')
    emits('confirm')
    cancel()
  }
}
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const confirm = () => {  
  addFormRef.value && addFormRef.value.validate((valid) => {
    if (valid) {
      confirmSaveAction()
    }
  })
}

// 到期时间选择限制只能选进入往后
const disabledFn = (date: Date) => {
  return dayjs(date).isBefore(dayjs().endOf('day')) || dayjs(date).isAfter(dayjs('2400-01-01').endOf('day'))
}

watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n) {
    addFormRef.value?.clearValidate()
    Object.assign(addData, props.editData || new BlackPhoneOrigin())
    if (addData.id) {
      expirationType.value = dayjs(addData.expireDate).format('YYYY-MM-DD') === '2400-01-01' ? 0 : (!!addData.id ? 1 : 2)
    } else {
      expirationType.value = props.groupType === 'white' ? 0 : 2
    }
  }
  if (!n) {
    Object.assign(addData, new BlackPhoneOrigin())
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.el-form {
  color: var(--primary-black-color-600);
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
.el-input-number .el-input__inner {
  text-align: left;
}
</style>