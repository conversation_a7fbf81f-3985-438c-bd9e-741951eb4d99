<template>
  <!-- 号码搜索模块 -->
  <div class="tw-grid tw-grid-cols-2 tw-gap-[12px] tw-mb-[12px] tw-w-full">
    <div
      class="batch-search-box"
      @click="handleSearchMode(1)"
    >
      <el-icon :size="26" color="#fff"><SvgIcon name="single-search"></SvgIcon></el-icon>
      <div class="tw-ml-[12px]">
        <div class="form-dialog-header">单个搜索</div>
        <div class="info-title tw-mt-[4px]">您可手动输入单个手机号码进行搜索</div>
      </div>
      <el-icon :size="26" class="after-icon" color="var(--primary-black-color-500)"><Right /></el-icon>
    </div>
    <div
      class="batch-search-box"
      @click="handleSearchMode(2)"
    >
      <el-icon :size="26" color="#fff"><SvgIcon name="batch-search"></SvgIcon></el-icon>
      <div class="tw-ml-[12px]">
        <div class="form-dialog-header">批量搜索</div>
        <div class="info-title tw-mt-[4px]">批量号码搜索，推荐您使用复制粘贴功能</div>
      </div>
      <el-icon :size="26" class="after-icon" color="var(--primary-black-color-500)"><Right /></el-icon>
    </div>
  </div>
  <el-table
    v-show="showTable"
    :data="props.tableData"
    v-loading="props.loading"
    class="tw-grow"
    row-key="id"
    :header-cell-style="tableHeaderStyle"
    stripe
  >
    <template #empty>
      <el-empty v-if="!props.tableData || props.tableData.length < 1" description="暂无数据" />
    </template>
    <el-table-column label="号码ID" property="id" align="left" :formatter="formatterEmptyData" min-width="120"></el-table-column>
    <el-table-column label="省市" property="id" align="left" show-overflow-tooltip :formatter="formatterEmptyData" min-width="160">
      <template #default="{ row }">
        {{ `${(row.province || '') + ' '}${row.city || ''}` || '-' }}
      </template>
    </el-table-column>
    <el-table-column property="startDate" label="导入时间" align="center" min-width="160" :formatter="formatterEmptyData">
      <template #default="{ row }">
        {{ row.startDate ? dayjs(row.startDate).format('YYYY-MM-DD HH:mm:ss') : '-' }}
      </template>
    </el-table-column>
    <el-table-column property="expireDate" label="过期日期" align="center" min-width="160" :formatter="formatterEmptyData">
      <template #default="{ row }">
        {{ row.expireDate ? dayjs(row.expireDate).format('YYYY-MM-DD HH:mm:ss') : '-' }}
      </template>
    </el-table-column>
    <el-table-column label="操作" min-width="120" align="right" fixed="right">
      <template #default="{ row }">
        <el-button type="primary" link @click="edit(row)">编辑</el-button>
        <el-button type="danger" link @click="del(row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <SearchPhoneDialog
    v-model:visible="visible"
    :type="searchType"
    @confirm="search"
  />
</template>

<script lang="ts" setup>
import { reactive, computed, ref, defineAsyncComponent, } from 'vue'
import { filterPhone, formatterEmptyData, copyText } from '@/utils/utils'
import { Right } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { tableHeaderStyle } from '@/assets/js/constant'
const SearchPhoneDialog = defineAsyncComponent({loader: () => import('./SearchPhoneDialog.vue')})
const emits = defineEmits(['search', 'edit', 'del'])
const props = defineProps<{
  tableData: any[];
  loading: boolean;
}>();

// 搜索
const showTable = ref(false)
const search = (phone: string) => {
  if (phone) {
    showTable.value = true
    emits('search', phone)
  }
}
const edit = (row: any) => {
  emits('edit', row)
}
const del = (row: any) => {
  emits('del', row)
}

const searchType = ref(1)
const visible = ref(false)
/** 右侧列表 - 搜索号码 */ 
const handleSearchMode = (type: number) => {
  searchType.value = type
  visible.value =true
}
</script>

<style scoped lang="postcss" type="text/postcss">
.batch-search-box {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #F5F7FA;
  border-radius: 4px;
  border: 1px solid #F5F7FA;
  position: relative;
  .after-icon {
    display: none;
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
  }
  &:hover {
    border-color: var(--el-color-primary);
    .after-icon {
      display: inline-flex;
    }
  }
}
.el-table {
  font-size: 13px;
}
</style>
