<template>
  <div class="tw-relative tw-grow" :style="{width: props.width || '100%', minHeight:  props.height || '500px', height: props.height || '500px'}">
    <div class="chart-title">{{ props.title }}</div>
    <div ref="barChartRef" :style="{width: props.width || '100%', minHeight:  props.height || '500px', height: props.height || '500px'}" @click="handleClick"></div>
    
    <div v-if="!(props.data?.filter(item => !!item.value).length>0)" description="暂无数据" class="tw-absolute tw-left-0 tw-top-0 tw-w-full tw-h-full tw-z-10 tw-bg-white tw-flex tw-justify-center tw-items-center">
      <el-empty />
    </div>
  </div>
  <slot></slot>
</template>

<script lang="ts" setup>
import { onDeactivated, onMounted, onUnmounted, ref, watch, } from 'vue'
import * as echarts from "echarts";
import { ECharts } from "echarts";
import { formatNumber } from '@/utils/utils'

const colorArr = [
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#0167FF' },
    { offset: 1, color: '#019FFF' }
  ]),
  '#3DF', '#f4d47a', '#a0d28e'
]

const props = defineProps<{
  data: {
    name: string,
    value: number[],
  }[],
  legends: string[], 
  title: string,
  width?: string,
  height?: string,
  xName?: string,
  yName?: string,
  horizontal?: boolean,
  scrollSpan?: number,
}>();
const emits = defineEmits(['click'])
const isHorizontal = ref(props.horizontal || false)
const barChartRef = ref<HTMLElement>()
let myChart: ECharts | null = null
const init = () => {
  // 如果DOM存在并且实例化对象未初始化
  if (barChartRef.value && !myChart) {
    // 初始化图表对象
    myChart = echarts.init(barChartRef.value)
  }

  // 设置图表参数
  const option = {
    // title: {
    //   text: props.title,
    //   textStyle: {
    //     color: '#313233',
    //     fontSize: 14,
    //     fontWeight: 600,
    //   }
    // },
    grid: {
      left: 50,
      right: 50,
      width: 'calc(100% - 100px)',
    },
    dataZoom: scrollSpan.value > 0 ? [
      {
        type: "inside",
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
        minValueSpan: scrollSpan.value - 1,
        maxValueSpan: scrollSpan.value,
        filterMode: 'none',
      },

      {
        type: 'slider',
        bottom: 10,
        show: true,
        minValueSpan: scrollSpan.value - 1,
        maxValueSpan: scrollSpan.value,
        // startValue: 0,
        // endValue: 5,
        handleSize: 0,
        height: 7,
        moveHandleSize: 5,
        brushSelect: false,
        fillerColor: '#d4d6d9',
        dataBackground: {
          lineStyle: { opacity: 0 },
          areaStyle: { opacity: 0 },
        },
        borderColor: '#fff',
        borderRadius: 2,
        handleStyle: {
          opacity: 0,
        },
        realtime: false,
        showDetail: false,
        filterMode: 'none',
        zoomLock: true,
      },
    ] : undefined,
    yAxis: {},
    xAxis: {},
    legend: props.legends?.length > 1 ? {
      data: props.legends,
      right: 36,
      top: 14,
      textStyle: {
        color: '#000',
        fontSize: 13,
      }
    } : undefined,
    tooltip: {
      trigger: 'axis',
      position: function (point: number[]) {
        return [point[0] - 90, point[1] + 30];
      },
      padding: 8,
      borderWidth: 0,
      borderColor: 'none',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      axisPointer: {
        lineStyle: {
          color: '#999',
        },
      },
      formatter: function (val: {
        name: string,
        seriesName: string,
        value: number,
      }[]) {
        let res: string = ''
        val.forEach((item) => {
          const str = (item.seriesName.includes('率') || item.seriesName.includes('占比')) ? formatNumber(item.value * 100, 2) + '%' : formatNumber(item.value, 2)
          res += `<div class="tw-text-left tw-truncate">${item.seriesName || ''}：${str || ''}</div>`
        })
        return `
        <div class="tw-text-white tw-text-p-[13px] tw-flex tw-flex-col tw-leading-[18px] tw-min-w-[250px]">
          <div class=" tw-border-b tw-border-gray-400 tw-pb-[4px]">
            <div class="tw-text-left tw-truncate">${props.xName || ''}：${val[0].name}</div>
            </div>
          <div class="tw-mt-[4px] tw-grid tw-grid-cols-2" >${res}</div>
        </div>
        `
      }
    },
    barMaxWidth: 38,
    barMinWidth: 22,
    series: props.legends.map((item, index) => {
      return {
        name: item,
        type: 'bar',
        label: {
          show: true,
          position: isHorizontal.value ? 'inside' : 'top',
          color: isHorizontal.value ? '#fff' : '#626366',
          fontSize: 12,
          formatter: (params: {value: number}) => formatNumber(params.value || 0, 1),
        },
        emphasis: {
          disabled: true,
          label: {
            fontSize: 20
          }
        },
        itemStyle: {
          color: colorArr[index],
          borderRadius: isHorizontal.value ? [0, 0, 0, 0] : [4, 4, 0, 0],
        },
        barGap: '5%',
        barCategoryGap: '15%',
        data: props.data.map(v => v.value[index]),
      }
    }),
  };
  option[isHorizontal.value ? 'yAxis' : 'xAxis'] = {
    type: 'category',
    data: props.data.map(item => item.name),
    axisTick: {
      alignWithLabel: true
    },
    axisLabel: {
      show: true, 
      interval: 0,
      fontSize: 12,
      width: window.innerWidth > 1600 ? 72 : 60,
      overflow: 'truncate',
      truncate: '...',
    },
  }
  option[isHorizontal.value ? 'xAxis' : 'yAxis'] = {
    type: 'value',
    axisLabel: {
      width: 50,
      overflow: 'truncate',
      interval: 0,
      truncate: '...',
      formatter: (val: number) => {
        const valAbs = Math.abs(val)??0
        if (valAbs >= Math.pow(10, 8)) {
          return (val/Math.pow(10, 8)).toFixed(1) + '亿'
        } else if(valAbs >= Math.pow(10, 6)) {
          return (val/Math.pow(10, 4)) + '万'
        } else if(valAbs >= Math.pow(10, 4)) {
          return (val/Math.pow(10, 4)).toFixed(1) + '万'
        } else {
          return val
        }
      }
    },
  }
  // 刷新图表
  if (option && myChart) {
    myChart.clear()
    myChart.setOption(option)
    window.addEventListener('resize', resize)
  }
}
watch(
  props,
  () => {
    if (barChartRef.value) {
      init()
      resize()
    } else {
      // 外部使用i-if可能barChartRef为null
      const timer = setTimeout(() => {
        init()
        resize()
        clearTimeout(timer)
      }, 100)
    }
  }, {
    immediate: true
  }
)
const handleClick = () => {
  emits('click')
}
/**
 * 组件离开后
 */
onDeactivated(() => {
  window.removeEventListener('resize', resize)
})

/**
 * 组件卸载后
 */
onUnmounted(() => {
  window.removeEventListener('resize', resize)
})

/**
 * 重新绘制图表的规格尺寸
 */
const scrollSpan = ref(props.scrollSpan ? (window.innerWidth > 1600 ? (props.scrollSpan + 2) : props.scrollSpan) : 0)
const resize = () => {
  if (myChart) {
    const oldScrollSpan = scrollSpan.value
    if (props.scrollSpan && props.scrollSpan > 0) {
      scrollSpan.value = window.innerWidth > 1600 ? (props.scrollSpan + 2) : props.scrollSpan
    } else {
      scrollSpan.value = 0
    }
    if (oldScrollSpan != scrollSpan.value) {
      barChartRef.value && init()
    }
    myChart.resize()
  }
}
</script>

