<template>
  <div class="tw-relative tw-flex" :style="{width: props.width || '100%', minHeight:  props.height || '500px', height: props.height || '500px'}">
    <div class="chart-title">{{ props.title }}</div>
    <div ref="barCategoryChartRef" :style="{width: props.width || '100%', minHeight:  props.height || '500px', height: props.height || '500px'}" @click="handleClick"></div>
    
    <div v-if="!(props.data?.length>0)" description="暂无数据" class="tw-absolute tw-left-0 tw-top-0 tw-w-full tw-h-full tw-z-10 tw-bg-white tw-flex tw-justify-center tw-items-center">
      <el-empty />
    </div>
  </div>
  <slot></slot>
</template>

<script lang="ts" setup>
import { onDeactivated, onMounted, onUnmounted, ref, watch, onActivated, } from 'vue'
import * as echarts from "echarts";
import { ECharts } from "echarts";
import { formatNumber } from '@/utils/utils'
const props = defineProps<{
  data: {
    name: string,
    value: {[key: string]: number},
  }[],
  title: string,
  legends: string[],
  width?: string,
  height?: string,
  xName?: string,
  yName?: string,
  horizontal?: boolean,
  scrollSpan?: number,
}>();
const emits = defineEmits(['click'])
const isHorizontal = ref(props.horizontal || false)
const barCategoryChartRef = ref<HTMLElement>()
let myChart: ECharts | null = null
const init = () => {
  // 如果DOM存在并且实例化对象未初始化
  if (barCategoryChartRef.value && !myChart) {
    // 初始化图表对象
    myChart = echarts.init(barCategoryChartRef.value)
  }

  // 设置图表参数
  const option = {
    grid: {
      left: 50
    },
    dataZoom: scrollSpan.value > 0 ? [
      {
        type: 'slider',
        bottom: 10,
        show: true,
        minValueSpan: scrollSpan.value - 1,
        maxValueSpan: scrollSpan.value,
        height: 7,
        moveHandleSize: 5,
        brushSelect: false,
        fillerColor: '#d4d6d9',
        dataBackground: {
          lineStyle: { opacity: 0 },
          areaStyle: { opacity: 0 },
        },
        borderColor: '#fff',
        borderRadius: 2,
        handleSize: 5,
        handleStyle: {
          opacity: 0,
        },
        realtime: false,
        showDetail: false,
        filterMode: 'none',
      },
    ] : undefined,
    yAxis: {},
    xAxis: {},
    tooltip: {
      trigger: 'axis',
      padding: 8,
      borderWidth: 0,
      borderColor: 'none',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      axisPointer: {
        lineStyle: {
          color: '#999',
        },
      },
      formatter: function (valArr: {
        name: string,
        seriesName: string,
        value: number,
      }[]) {
        let res: string = ''
        let total = 0
        valArr.map(params => {
          const str = (params.seriesName.includes('率') || params.seriesName.includes('占比')) ? formatNumber(params.value * 100, 2) + '%' : formatNumber(params.value, 2)
          total = total + params.value
          res = res + `<div class="tw-mt-[4px]" >
            <div class="tw-text-left tw-truncate">${params.seriesName}：${str || ''}</div>
          </div>`
        })
        return `
        <div class="tw-text-white tw-text-p-[13px] tw-flex tw-flex-col tw-leading-[18px] tw-min-w-[250px]">
          <div class=" tw-border-b tw-border-gray-400 tw-pb-[4px] tw-grid tw-grid-cols-3 tw-gap-x-[4px]">
            <div class="tw-text-left tw-truncate tw-col-span-2">${props.xName || ''}：${valArr[0].name}</div>
            <div class="tw-text-left tw-truncate">合计：${total}</div>
          </div>
          <div class="tw-mt-[4px] tw-grid tw-grid-cols-3 tw-gap-x-[4px]"> ${res}</div>
        </div>
        `
      }
    },
    color: ['#165DFF', '#36cbcb', '#4ecb73', '#fbd337', '#da416f', '#36cbcb', '#975fe4', '#5254cf',],
    barMaxWidth: 38,
    barMinWidth: 24,
    series: props.legends.map(item => {
      return {
        name: item,
        type: 'bar',
        stack: 'total',
        label: {
          show: true,
          fontSize: 12,
          formatter: (params: {value: number}) => {
            const val = params.value
            const valAbs = Math.abs(val)??0
            if (valAbs >= Math.pow(10, 8)) {
              return (val/Math.pow(10, 8)).toFixed(2) + '亿'
            } else if(valAbs >= Math.pow(10, 4)) {
              return (val/Math.pow(10, 4)).toFixed(2) + '万'
            } else {
              return val
            }
          },
        },
        emphasis: {
          disabled: true,
          label: {
            fontSize: 20,
            focus: 'series'
          }
        },
        // itemStyle: {
        //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        //     { offset: 0, color: '#0167FF' },
        //     { offset: 1, color: '#019FFF' }
        //   ]),
        //   borderRadius: isHorizontal.value ? [0, 0, 0, 0] : [4, 4, 0, 0],
        // },
        data: props.data.map(v => v.value[item]),
      }
    })
  };
  option[isHorizontal.value ? 'yAxis' : 'xAxis'] = {
    type: 'category',
    data: props.data.map(item => item.name),
    axisTick: {
      alignWithLabel: true
    },
    axisLabel: {
      show: true, 
      interval: '0',
      fontSize: 12,
      width: window.innerWidth > 1400 ? 72 : 60,
      overflow: 'truncate',
      truncate: '...',
    },
  }
  option[isHorizontal.value ? 'xAxis' : 'yAxis'] = {
    type: 'value',
    axisLabel: {
      width: 50,
      overflow: 'truncate',
      truncate: '...',
      formatter: (val: number) => {
        const valAbs = Math.abs(val)??0
        if (valAbs >= Math.pow(10, 8)) {
          return (val/Math.pow(10, 8)).toFixed(1) + '亿'
        } else if(valAbs >= Math.pow(10, 6)) {
          return (val/Math.pow(10, 4)) + '万'
        } else if(valAbs >= Math.pow(10, 4)) {
          return (val/Math.pow(10, 4)).toFixed(1) + '万'
        } else {
          return val
        }
      }
    },
  }
  // 刷新图表
  if (option && myChart) {
    myChart.clear()
    myChart.setOption(option)
    resize()
  }
}
watch(
  props,
  () => {
    if (barCategoryChartRef.value) {
      init()
    }
    
  }, {
    immediate: true
  }
)
const handleClick = () => {
  emits('click')
}
/**
 * 组件挂载后
 */
 onMounted(() => {
  if (barCategoryChartRef.value) {
    // 立马绘制图表
    init()
    window.addEventListener('resize', resize)
  }
})
onActivated(() => {
  window.removeEventListener('resize', resize)
})
/**
 * 组件离开后
 */
onDeactivated(() => {
  window.removeEventListener('resize', resize)
})

/**
 * 组件卸载后
 */
onUnmounted(() => {
  window.removeEventListener('resize', resize)
})

/**
 * 重新绘制图表的规格尺寸
 */
const scrollSpan = ref(props.scrollSpan ? (window.innerWidth > 1400 ? (props.scrollSpan + 2) : props.scrollSpan) : 0)
const timer = ref<ReturnType<typeof setTimeout> | null>(null)
const resize = () => {
  if (myChart) {
    const oldScrollSpan = scrollSpan.value
    if (props.scrollSpan && props.scrollSpan > 0) {
      scrollSpan.value = window.innerWidth > 1400 ? (props.scrollSpan + 2) : props.scrollSpan
    } else {
      scrollSpan.value = 0
    }
    if (oldScrollSpan != scrollSpan.value) {
      barCategoryChartRef.value && init()
    }
    
    timer.value && clearTimeout(timer.value)
    timer.value = setTimeout(() => {
      myChart?.resize()
      timer.value = null
    }, 200)
  }
}
</script>

