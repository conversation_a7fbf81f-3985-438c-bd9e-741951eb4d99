<template>
  <div class="tw-relative tw-flex tw-items-center tw-h-full tw-w-full">
    <div class="total-num" :style="`right: calc(50% + ${(legendWidth - 10) / 2}px`">
      <el-tooltip
        effect="dark"
        placement="bottom"
        :content="'总接通数: '+total"
      >
        <div class="tw-text-[24px] tw-font-[600]">{{ total }}</div>
      </el-tooltip>
    </div>
    <div class="chart-title">
      <span>{{ props.title }}</span>
      <TooltipBox v-if="props.tooltipContent && props.tooltipContent?.length>0" :title="props.title" :num="1" :content="props.tooltipContent" :useSlot="true">
        <el-icon :size="12" class="tw-cursor-pointer tw-ml-[2px]" color="var(--primary-black-color-400)"><SvgIcon name="warning" /></el-icon>
      </TooltipBox>
    </div>
    <div class="chart-options">
      <slot name="options"></slot>
    </div>
    <div id="pie-chart-container" ref="chartDom" :style="{width: '100%', height: '100%', margin: 'auto'}">
    </div>
    <el-empty v-if="!(props.data?.length>0)" description="暂无数据" class="tw-absolute tw-left-0 tw-top-0 tw-w-full tw-h-full tw-z-[8] tw-bg-white" />
  </div>
</template>

<script lang="ts" setup>
import { onDeactivated, onMounted, onUnmounted, onActivated, ref, watch, computed, } from 'vue'
import * as echarts from "echarts";
import { ECharts } from "echarts";
import { CategoryChartItem } from '@/type/task'
import TooltipBox from '@/components/TooltipBox.vue'
import { useGlobalStore } from '@/store/globalInfo'

const props = defineProps<{
  data: CategoryChartItem[],
  title: string,
  yName?: string,
  tooltipContent?: string[],
  richWidth?: number[]
}>();

const globalInfo = useGlobalStore()
const isMobile = globalInfo.isMobile

// 图表DOM
const chartDom = ref<HTMLElement | null>(null)
// 图表实例化对象
let myChart: ECharts | null = null
// 总数
const total = ref(0)

const legendWidth = computed(() => {
  return props.richWidth ? props.richWidth?.reduce((a, b) => a + b, 50) : (isMobile ? 120 : 160)
})

// 若图表数据更新，则重新绘制图表
watch(
  props,
  () => {
    if (chartDom.value) {
      init()
      resize()
    }
  }
)

/**
 * 组件挂载后
 */
onMounted(() => {
  if (chartDom.value) {
    // 立马绘制图表
    init()
    window.addEventListener('resize', resize)
  }
})

onActivated(() => {
  if (chartDom.value) {
    // 立马绘制图表
    init()
    window.addEventListener('resize', resize)
  }
})

/**
 * 组件离开后
 */
onDeactivated(() => {
  window.removeEventListener('resize', resize)
  myChart?.clear()
})

/**
 * 组件卸载后
 */
onUnmounted(() => {
  window.removeEventListener('resize', resize)
  chartDom.value = null
  myChart?.dispose()
  myChart = null
})

/**
 * 重新绘制图表的规格尺寸
 */
const timer = ref<ReturnType<typeof setTimeout> | null>(null)
const resize = () => {
  timer.value && clearTimeout(timer.value)
  timer.value = setTimeout(() => {
    if(myChart) {
      myChart.resize()
      // const w = myChart.getWidth()
      // myChart.resize({
      //   width: w, 
      //   height: 2 * w / 3
      // })
    }
    timer.value = null
  }, 200)
}

/**
 * 绘制图表
 */
const init = () => {
  // 如果DOM存在并且实例化对象未初始化
  if (chartDom.value && !myChart) {
    // 初始化图表对象
    myChart = echarts.init(chartDom.value)
  }

  // 计算总数
  total.value = props.data.reduce((previousValue, currentItem) => {
    return previousValue + (currentItem?.num ?? 0)
  }, 0)

  // 设置图表参数
  const option = {
    tooltip: {
      trigger: 'item',
      padding: 8,
      borderWidth: 0,
      borderColor: 'none',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
    },
    grid: {
      top: 'middle'
    },
    color: ['#165DFF', '#F53F3F', '#FF7D00', '#F7BA1E', '#00B42A', '#14C9C9', '#3491FA', '#722ED1',],
    legend: {
      data: props.data.map(item => item.name),
      y: "center",
      icon: "roundRect",
      orient: "vertical",
      itemWidth: 16,
      itemHeight: 16,
      // selectedMode: false,
      // triggerEmphasis: true,
      bottom: 0,
      right: 0,
      formatter: (name: string) => {
        const currentItem = props.data.find(item => name === item.name)
        const str1 = currentItem?.value ? +(currentItem?.value?.toFixed(2)) + '%' : '0'
        const str2 = currentItem?.num || '0'
        return (
          "{a|" + name + "} {b|" + str1 + "} {c|" + str2 + "}"
        );
      },
      textStyle: {
        rich: {
          a: {
            fontSize: isMobile ? 11 : 13,
            lineHeight: isMobile ? 14 : 24,
            width: props.richWidth ? props.richWidth[0] : (isMobile ? 6 : 26),
            color: '#313233',
          },
          b: {
            fontSize: isMobile ? 11 : 13,
            lineHeight: isMobile ? 13 : 20,
            width: props.richWidth ? props.richWidth[1] : (isMobile ? 40 : 50),
            color: '#626366',
          },
          c: {
            fontSize: isMobile ? 11 : 13,
            lineHeight: isMobile ? 13 : 20,
            width: props.richWidth ? props.richWidth[2] : (isMobile ? 32 : 52),
            color: '#626366',
          }
        },
      }
    },
    series: [
      {
        name: props.yName || props.title,
        type: 'pie',
        emphasis: {
          scaleSize: 6, // 放大尺寸
          focus: 'series' // 聚焦到系列
        },
        right: legendWidth.value,
        left: 10,
        top: 30,
        bottom: 30,
        radius: ['60%', '100%'],
        gap: 2,
        // 标签
        label: {
          show: false,
          position: 'outside',
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        data: props.data,
        // 悬浮提示框
        tooltip: {
          show: true,
          formatter: (params: {
            name: string,
            data: { name: string, value: number, num: number }
            seriesName: string,
            value: number,
          }) => {
            const str1 = (params.seriesName.includes('率') || params.seriesName.includes('占比')) ? params.value.toFixed(1) + '%' : params.value.toFixed(1)
            return `${params.data.name ?? ''}: ${str1}&nbsp;&nbsp;${params.data.num ?? ''}`
          },
          textStyle: {
            color: '#fff',
          },
        }
      }
    ]
  };

  // 刷新图表
  if (option && myChart) {
    myChart.clear()
    myChart.setOption(option)
  }
}
</script>

<style scoped lang="postcss">
.total-num {
  position: absolute;
  top: 50%;
  transform: translate(50%, -50%);
  font-size: 28px;
  z-index: 8;
  cursor: pointer;
  @media screen and (max-width: 600px) {
    right: calc(50% + 60px);
    font-size: 15px;
  }
}
</style>
