<template>
  <div class="tw-relative" :style="{width:  props.width || '100%', minHeight:  props.height || '500px', height: props.height || '500px'}">
    <div v-if="props.title" class="chart-title">
      <span>{{ props.title }}</span>
      <TooltipBox v-if="props.tooltipContent && props.tooltipContent?.length>0" :title="props.title" :num="1" :content="props.tooltipContent" :useSlot="true">
        <el-icon :size="12" class="tw-cursor-pointer tw-ml-[2px]" color="var(--primary-black-color-400)"><SvgIcon name="warning" /></el-icon>
      </TooltipBox>
    </div>
    <div ref="lineChart2Ref" :style="{width:  props.width || '100%', minHeight:  props.height || '500px', height: props.height || '500px'}"></div>
    <slot></slot>
    <div v-if="!(props.data?.length>0)" description="暂无数据" class="tw-absolute tw-left-0 tw-top-0 tw-w-full tw-h-full tw-z-10 tw-bg-white tw-flex tw-justify-center tw-items-center">
      <el-empty />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onDeactivated, onMounted, onUnmounted, ref, watch, onActivated, computed } from 'vue'
import * as echarts from "echarts";
import { ECharts } from "echarts";
import { formatNumber } from '@/utils/utils'
import TooltipBox from '@/components/TooltipBox.vue'
import { RegionChartItem } from '@/type/task'
import { testStr } from '@/components/charts/constant'

const props = withDefaults(defineProps<{
  data: RegionChartItem[],
  title?: string,
  width?: string,
  height?: string,
  xName?: string,
  yName?: string,
  legends: string[],
  tooltipSort?: number[]
  tooltipContent?: string[],
}>(), {
  tooltipSort: () => [0, 1, 2],
})

// 图表DOM
const lineChart2Ref = ref<HTMLElement | null>(null)
// 图表实例化对象
let myChart: ECharts | null = null

const start = ref(0)
const end = ref(0)
const sampledData = computed(() => {
  const res = [];
  if (props.data?.length * (end.value - start.value) / 100 > 150) {
    for (let i = 0; i <= props.data?.length; i=i+1) {
      if (props.data[i]?.name?.at(-1) && +props.data[i]?.name?.at(-1)! % 2===0) {
        res.push(props.data[i])
      }
    }
    return res;
  } 
  return props.data
})
// 若图表数据更新，则重新绘制图表
watch(
  props,
  () => {
    if (lineChart2Ref.value) {
      init()
      resize()
    }
  }, {
    immediate: true,
    deep: true,
  }
)

/**
 * 组件挂载后
 */
onMounted(() => {
  if (lineChart2Ref.value) {
    // 立马绘制图表
    init()
    window.addEventListener('resize', resize)
  }
})
onActivated(() => {
  if (lineChart2Ref.value) {
    // 立马绘制图表
    init()
    window.addEventListener('resize', resize)
  }
})

/**
 * 组件离开后
 */
onDeactivated(() => {
  window.removeEventListener('resize', resize)
  myChart?.off('dataZoom')
  myChart?.clear()
})

/**
 * 组件卸载后
 */
onUnmounted(() => {
  window.removeEventListener('resize', resize)
  myChart?.off('dataZoom')
  lineChart2Ref.value = null
  myChart?.dispose()
  myChart = null
})

/**
 * 重新绘制图表的规格尺寸
 */
const resize = () => {
  if (myChart) {
    myChart.resize()
  }
}

/**
 * 绘制图表
 */
const init = () => {
  // 如果DOM存在并且实例化对象未初始化
  if (lineChart2Ref.value && !myChart) {
    // 初始化图表对象
    myChart = echarts.init(lineChart2Ref.value)
  }
  start.value = 0
  end.value = 100
  // 设置图表参数
  const option = {
    tooltip: {
      trigger: 'axis',
      padding: 8,
      borderWidth: 0,
      borderColor: 'none',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      axisPointer: {
        lineStyle: {
          color: '#165DFF',
        },
      },
      formatter: function (params: {
        name: string,
        value: string,
      }[]) {
        let name: string = params[0].name ?? '未知'
        const data = props.data.find(item =>  item.name === params[0].name)
        const strArr = Array.from({length: props.legends.length}).fill('暂无数据')
        if (data) {
          name = data.name
          for (let i = 0; i < props.legends.length; i++) {
            const item = props.tooltipSort[i]
            // @ts-ignore
            const val = data['value' + (item + 1)] as unknown as string | number
            if (typeof val === 'string') {
              strArr[i] = `<div class="tw-text-left tw-truncate">${val}</div>`
            }
            if (typeof val === 'number') {
              const dataStr = props.legends[item] + '：' + (testStr(props.legends[item])
            // @ts-ignore
              ? formatNumber(val * 100, 2) + '%'
              // @ts-ignore
              : formatNumber(val, 2))
               strArr[i] = `<div class="tw-text-left tw-truncate">${dataStr || ''}</div>`
            }
           
            
          }
        }
        return `
        <div class="tw-text-white tw-text-p-[13px] tw-flex tw-flex-col tw-leading-[18px] tw-min-w-[300px]">
          <div class="tw-border-b tw-border-gray-400 tw-pb-[4px]">
            <div class="tw-text-left tw-truncate">${props.xName || ''}：${name}</div>
          </div>
          <div class="tw-grid tw-grid-cols-2 tw-mt-[4px]" >
            ${strArr.join('')}
          </div>
        </div>
        `
      }
    },
    color: ['#165DFF'],
    // 图表四周边距
    grid: {
      // 左
      x: 50,
      // 上
      y: 80,
      // 右
      x2: 80,
      // 下
      y2: 60
    },
    xAxis: {
      name: props.xName || 'line',
      data: sampledData.value?.map(item => item.name),
      axisLabel: {
        show: true,
        interval: (index:number, value: string) => {
          if ((window.innerWidth > 1400 && value.endsWith('30')) || value.endsWith('00') || index === 0 || index+1 === sampledData.value?.length) {
            return true
          } else {
            return false
          }
        }
      }
    },
    yAxis: {
      type: 'value',
      name: props.yName,
      splitLine: {
        show: true,
      }
    },
    // 图表底部滚动条
    dataZoom: {
        type: 'slider',
        bottom: 10,
        show: true,
    },
    series: [
      {
        name: props.yName,
        type: 'line',
        sampling: props.data?.length * (end.value - start.value) / 100 > 750 ? 'lttb' : undefined,
        smooth: 0.5,
        areaStyle: {
          color: '#eef3fb'
        },
        connectNulls: true,
        label: {
          show: false,
          fontSize: 14,
          formatter: function (val: any) {
            const value = val?.data?.at(1) ?? 0
            if (!value) {
              return ''
            }
            return value + '%'
          },
        },
        data: sampledData.value?.map(item => item.value1 || 0),
        markArea: {
          itemStyle: {
            color: 'rgba(255, 173, 177, 0.4)'
          },
          
        }
      }
    ],
    animation: false,
  };

  // 刷新图表
  if (option && myChart) {
    myChart.clear()
    myChart.setOption(option)
    // @ts-ignore
    myChart.on('dataZoom', (params: {start: number, end: number}) => {
      start.value = params.start || 0;
      end.value = params.end || 100;
      // // 更新图表数据
      myChart?.setOption({
          xAxis: {
            data: sampledData.value?.map(item => item.name || '')
          },
          series: [{
            data: sampledData.value?.map(item => item.value1 || 0),
          }]
      });
    });
  }
}
</script>
