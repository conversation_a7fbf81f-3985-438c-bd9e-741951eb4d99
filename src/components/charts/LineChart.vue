<template>
  <div class="tw-relative" :style="{width:  props.width || '100%', minHeight:  props.height || '500px', height: props.height || '500px'}">
    <div class="chart-title">
      <span>{{ props.title }}</span>
      <TooltipBox v-if="props.tooltipContent && props.tooltipContent?.length>0" :title="props.title" :num="1" :content="props.tooltipContent" :useSlot="true">
        <el-icon :size="12" class="tw-cursor-pointer tw-ml-[2px]" color="var(--primary-black-color-400)"><SvgIcon name="warning" /></el-icon>
      </TooltipBox>
    </div>
    <div ref="lineChartRef" :style="{width:  props.width || '100%', minHeight:  props.height || '500px', height: props.height || '500px'}"></div>
    <slot></slot>
    <div v-if="!(props.data?.length>0) || !(props.data?.filter(item => !!item[1]).length>0)" description="暂无数据" class="tw-absolute tw-left-0 tw-top-0 tw-w-full tw-h-full tw-z-10 tw-bg-white tw-flex tw-justify-center tw-items-center">
      <el-empty />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onDeactivated, onMounted, onUnmounted, ref, watch, onActivated } from 'vue'
import * as echarts from "echarts";
import { ECharts } from "echarts";
import { formatNumber } from '@/utils/utils'
import TooltipBox from '@/components/TooltipBox.vue'
import { useGlobalStore } from '@/store/globalInfo'
import { onBeforeRouteLeave } from 'vue-router';

const props = withDefaults(defineProps<{
  data: any[],
  title: string,
  width?: string,
  height?: string,
  xName?: string,
  yName?: string,
  xList?: string[],
  needEmptyList?: boolean,
  tooltipContent?: string[],
}>(), {
  needEmptyList: true
})

const globalInfo = useGlobalStore()
const isMobile = globalInfo.isMobile

// 图表DOM
const lineChartRef = ref<HTMLElement | null>(null)
// 图表实例化对象
let myChart: ECharts | null = null

// 若图表数据更新，则重新绘制图表
watch(
  props,
  () => {
    if (lineChartRef.value) {
      init()
      resize()
    }
  }, {
    immediate: true
  }
)

/**
 * 组件挂载后
 */
onMounted(() => {
  if (lineChartRef.value) {
    // 立马绘制图表
    init()
    window.addEventListener('resize', resize)
  }
})
onActivated(() => {
  if (lineChartRef.value) {
    // 立马绘制图表
    init()
    window.addEventListener('resize', resize)
  }
})
/**
 * 组件离开后
 */
onDeactivated(() => {
  window.removeEventListener('resize', resize)
  myChart?.clear()
})

/**
 * 组件卸载后
 */
onUnmounted(() => {
  window.removeEventListener('resize', resize)
  lineChartRef.value = null
  myChart?.dispose()
  myChart = null
})
onBeforeRouteLeave(() => {
  window.removeEventListener('resize', resize)
  lineChartRef.value = null
  myChart?.dispose()
  myChart = null
})
/**
 * 重新绘制图表的规格尺寸
 */
const resize = () => {
  if (myChart) {
    myChart.resize()
  }
}

/**
 * 绘制图表
 */
const init = () => {
  const emptyList = []
  if (props.needEmptyList) {
    for (let i = 0; i < props.data.length; i++) {
      if (!props.data[i]) {
        const sta = i - 1 >= 0 ? i - 1 : 0;
        while (!props.data[i] && i < props.data.length) {
          i++
        }
        emptyList.push(
          [{
            name: '该时段无外呼任务',
            xAxis: props.xList?.at(sta) ?? 0
          }, {
            xAxis: props.xList?.at(i) ?? 0
          }]
        )
      }
    }
  }

  // 如果DOM存在并且实例化对象未初始化
  if (lineChartRef.value && !myChart) {
    // 初始化图表对象
    myChart = echarts.init(lineChartRef.value)
  }

  // 设置图表参数
  const option = {
    tooltip: {
      trigger: 'axis',
      padding: 8,
      borderWidth: 0,
      borderColor: 'none',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      axisPointer: {
        lineStyle: {
          color: '#165DFF',
        },
      },
      formatter: function (params: {
        name: string,
        seriesName: string,
        value: any[],
        dataIndex: number,
      }[]) {
        let str = ''
        let str2 = ''
        let str3 = ''
        const dataIndex = params?.at(0)?.dataIndex ?? 0
        const intervalStart = props.xList?.at(dataIndex) ?? ''
        const intervalEnd = props.xList?.at(dataIndex + 1) ?? ''
        const intervalStr = intervalStart + (intervalEnd ? ('~' + intervalEnd) : '')
        params.map(item => {
          const str1 = (item.seriesName.includes('率') || item.seriesName.includes('占比')) ? formatNumber(item.value[1], 2) + '%' : formatNumber(item.value[1], 2)
          str = `${item.seriesName}：${str1 ?? ''}`
          str2 = `外呼次数：${item.value[2]}`
          str3 = `接通次数：${item.value[3]}`
        })

        return `
        <div class="tw-text-white tw-text-p-[13px] tw-flex tw-flex-col tw-leading-[18px]">
          <div class="tw-text-left tw-truncate">
            ${props.xName || ''}：${intervalStr}
          </div>
          <div class="tw-text-left tw-truncate">
            ${str}
          </div>
          <div class="tw-text-left tw-truncate">
            ${str2}
          </div>
          <div class="tw-text-left tw-truncate">
            ${str3}
          </div>
        </div>
        `
      }
    },
    color: ['#165DFF'],
    // 图表四周边距
    grid: {
      // 左
      x: isMobile ? 30 : 50,
      // 上
      y: 80,
      // 右
      x2: isMobile ? 40 : 80,
      // 下
      y2: 60
    },
    xAxis: {
      name: props.xName,
      type: 'time',
      boundaryGap: false,
      data: props.xList,
      formatter: '{HH}:{mm}',
      maxInterval: isMobile ? 3600 * 3000 : 3600 * 1000
    },
    yAxis: {
      type: 'value',
      name: props.yName,
      splitLine: {
        show: true,
      },
    },
    axisLabel: {
      fontSize: isMobile ? 10 : 12
    },
    // 图表底部滚动条
    dataZoom: {
        type: 'slider',
        bottom: 10,
        show: true,
    },
    series: [
      {
        name: props.yName,
        type: 'line',
        areaStyle: {
          color: '#eef3fb'
        },
        connectNulls: true,
        label: {
          show: false,
          fontSize: 14,
          formatter: function (val: any) {
            const value = val?.data?.at(1) ?? 0
            if (!value) {
              return ''
            }
            return value + '%'
          },
        },
        itemStyle: {
          borderRadius: 5,
          borderColor: '#fff',
          borderWidth: 2
        },
        emphasis: {
          disabled: true,
          label: {
            fontSize: 20
          }
        },
        data: props.data,
        markArea: {
          itemStyle: {
            color: 'rgba(255, 173, 177, 0.4)'
          },
          data: props.needEmptyList ? emptyList : null
        }
      }
    ]
  };

  // 刷新图表
  if (option && myChart) {
    myChart.clear()
    myChart.setOption(option)
  }
}
</script>
