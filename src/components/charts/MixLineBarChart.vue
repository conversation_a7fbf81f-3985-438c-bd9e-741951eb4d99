<template>
  <div class="tw-relative" :style="{width:  props.width || '100%', minHeight:  props.height || '400px', height: props.height || '400px'}">
    <div v-if="!!props.title" class="chart-title">{{ props.title }}</div>
    <div
      ref="lineBarChartRef"
      :style="{width:  props.width || '100%', minHeight:  props.height || '400px', height: props.height || '400px'}"
    >
    </div>
    <slot></slot>
    <div v-if="!props.data?.length" description="暂无数据" class="tw-absolute tw-left-0 tw-top-0 tw-w-full tw-h-full tw-z-9 tw-bg-white tw-flex tw-justify-center tw-items-center">
      <el-empty />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, onDeactivated, onUnmounted, computed, onActivated } from 'vue'
import * as echarts from "echarts";
import { ECharts } from "echarts";
import { formatMsDuration, formatNumber } from '@/utils/utils'
import { onBeforeRouteLeave } from 'vue-router'
type ChartType = {
  xName?: string, // x轴， 若无则使用name
  name: string, // 显示名称
  value1: number, // bar
  value2: number, // line
  value3?: number, // 其他参数
}
const props = withDefaults(defineProps<{
  data: ChartType[],
  showLabel?: boolean,
  scrollSpan?: number, // 是否使用底部滚动条形式， 有数值表示是，否则使用默认形式
  title?: string,
  width?: string,
  height?: string,
  xName?: string,
  extraXLabel?: string,
  yName?: string,
  xRotate?: boolean,
  legends?: string[],
  tooltipSort?: number[]
}>(), {
  tooltipSort: () => [0, 1, 2],
  legends: () => ['', '', ''],
})

const lineBarChartRef = ref<HTMLElement | null>(null)
let myChart: ECharts | null = null
watch(
  props,
  async () => {
    if (lineBarChartRef.value) {
      await init()
      await resize()
    }
  }, {
    immediate: true,
  }
)

onMounted(() => {
  if (lineBarChartRef.value) {
    init()
    window.addEventListener('resize', resize)
  }
})
onActivated(() => {
  if (lineBarChartRef.value) {
    init()
    window.addEventListener('resize', resize)
  }
})
/**
 * 组件离开后
 */
onDeactivated(() => {
  window.removeEventListener('resize', resize)
  myChart?.clear()
})

/**
 * 组件卸载后
 */
onUnmounted(() => {
  window.removeEventListener('resize', resize)
  lineBarChartRef.value = null
  myChart?.dispose()
  myChart = null
})
onBeforeRouteLeave(() => {
  window.removeEventListener('resize', resize)
  lineBarChartRef.value = null
  myChart?.dispose()
  myChart = null
})


const scrollSpan = ref(props.scrollSpan ? (window.innerWidth > 1400 ? (props.scrollSpan + 2) : props.scrollSpan) : 0)
const resize = () => {
  if (myChart) {
    const oldScrollSpan = scrollSpan.value
    if (props.scrollSpan && props.scrollSpan > 0) {
      scrollSpan.value = window.innerWidth > 1400 ? (props.scrollSpan + 2) : props.scrollSpan
    } else {
      scrollSpan.value = 0
    }
    if (oldScrollSpan != scrollSpan.value) {
      lineBarChartRef.value && init()
    }
    myChart.resize()
  }
}
const init = () => {
  if (lineBarChartRef.value && !myChart) {
    myChart = echarts.init(lineBarChartRef.value)
  }
  const option = {
    grid: {
      // 左
      x: 50,
      // 上
      y: 80,
      // 右
      x2: 80,
      // 下
      y2: 60
    },
    tooltip: {
      trigger: 'axis',
      padding: 8,
      borderWidth: 0,
      borderColor: 'none',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      formatter: function (params: {
        name: string,
        seriesName: string,
        value: number,
      }[]) {
        const strArr: string[] = []
        let name = ''
        const data = props.data.find(item =>  item.xName ? item.xName === params[0].name : item.name === params[0].name) as ChartType
        if (data) {
          for (let i = 0; i<props.legends.length; i++) {
            const k = `value${i+1}`
            if (props.legends[i] && (props.legends[i].includes('率') || props.legends[i].includes('占比'))) {
               // @ts-ignore
              strArr[i] = formatNumber(data[k] * 100, 2) + '%'
            } else if(props.legends[i] && props.legends[i].includes('时长')) {
               // @ts-ignore
              strArr[i] = formatNumber(data[k], 2) + '秒'
            } else {
              // @ts-ignore
              strArr[i] = formatNumber(data[k], 2)
            }
          }
          name = data.name
        }
        const strExtra = props.extraXLabel ? `<div class="tw-text-left">${props.extraXLabel}</div>` : ''
        return `
        <div class="tw-text-white tw-text-p-[13px] tw-flex tw-flex-col tw-leading-[18px] tw-min-w-[300px]">
          <div class="tw-grid tw-grid-cols-2 tw-border-b tw-border-gray-400 tw-pb-[4px]">
            ${strExtra}
            <div class="tw-text-left tw-truncate">${props.xName || ''}：${name}</div>
          </div>
          <div class="tw-grid tw-grid-cols-2 tw-mt-[4px] tw-grid-flow-row" >
            <div class="tw-text-left tw-truncate">${props.legends[props.tooltipSort[0]??0] ? `${props.legends[props.tooltipSort[0]??0]}：${strArr[props.tooltipSort[0]??0] || ''}`:''}</div>
            <div class="tw-text-left tw-truncate">${props.legends[props.tooltipSort[1]??1] ? `${props.legends[props.tooltipSort[1]??1]}：${strArr[props.tooltipSort[1]??1] || ''}`:''}</div>
            <div class="tw-text-left tw-truncate">${props.legends[props.tooltipSort[2]??2] ? `${props.legends[props.tooltipSort[2]]??2}：${strArr[props.tooltipSort[2]??2] || ''}`:''}</div>
          </div>
        </div>
        `
      }
    },
    legend: {
      data: props.legends?.slice(0, 2),
      right: '20%',
      top: 10,
      selectedMode: !!props.showLabel,
    },
    dataZoom: props.xName && ['时间', '日期'].includes(props.xName)
    ? {
        type: 'slider',
        bottom: 10,
        show: true,
    }
    : props.scrollSpan && props.scrollSpan > 0 ? [
      {
        type: "inside",
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
        minValueSpan: scrollSpan.value - 1,
        maxValueSpan: scrollSpan.value,
        filterMode: 'none',
      },
      {
        type: 'slider',
        bottom: 10,
        show: true,
        minValueSpan: scrollSpan.value - 1,
        maxValueSpan: scrollSpan.value,
        height: 7,
        moveHandleSize: 5,
        brushSelect: false,
        fillerColor: '#d4d6d9',
        dataBackground: {
          lineStyle: { opacity: 0 },
          areaStyle: { opacity: 0 },
        },
        borderColor: '#fff',
        borderRadius: 2,
        handleSize: 5,
        handleStyle: {
          opacity: 0,
        },
        realtime: false,
        showDetail: false,
        filterMode: 'none',
        zoomLock: true,
      },
    ] : undefined,
    xAxis: {
      name: props.xName,
      type: 'category',
      data: props.data.map(item => item.xName??item.name) || [],
      axisTick: {
        alignWithLabel: true
      },
      nameTextStyle: {
        padding: 15
      },
      axisLabel: {
        show: true, 
        interval: props.xName && ['时间', '日期'].includes(props.xName) ? 'auto' : '0',
        rotate: props.xRotate ? -30 : undefined,
        fontSize: 12,
        width: 72,
        overflow: 'truncate',
        truncate: '...',
      },
    },
    yAxis: [
      {
        type: 'value',
        name: props.legends[0] || 'bar',
        position: 'left',
        alignTicks: true,
        axisLabel: {
          width: 72,
          overflow: 'truncate',
          truncate: '...',
          formatter: (val: number) => {
            if (props.legends[0] && (props.legends[0].includes('率') || props.legends[0].includes('占比'))) {
              return Math.round(val * 10000) / 100 + '%'
            } else if(props.legends[0] && props.legends[0].includes('时长')) {
              return formatNumber(val, 2) + 's'
            } else {
              const valAbs = Math.abs(val)??0
              if (valAbs >= Math.pow(10, 8)) {
                return (val/Math.pow(10, 8)).toFixed(1) + '亿'
              } else if(valAbs >= Math.pow(10, 6)) {
                return (val/Math.pow(10, 4)) + '万'
              } else if(valAbs >= Math.pow(10, 4)) {
                return (val/Math.pow(10, 4)).toFixed(1) + '万'
              } else {
                return val
              }
            }
          }
        },
      },
      {
        type: 'value',
        name: props.legends[1] || 'line',
        position: 'right',
        alignTicks: true,
        splitNumber: 5,
        // 使用滚动条：最值固定0-1（如有超过0-1则采用超出值）；
        // 默认：采用数值的最大最小值；
        max: (val: {min: number, max: number}) => {
          if (val && val.max) {
            return !props.scrollSpan ? val.max : Math.max(1, val.max);
          }
          return 1; 
        },

        min: (val: {min: number, max: number}) => {
          if (val && val.min) {
            return !props.scrollSpan ? val.min : Math.min(0, val.min||0);
          }
          return 0; 
        },
        axisLabel: {
          formatter: (val: number)  => {
            if (props.legends[1] && (props.legends[1].includes('率') || props.legends[1].includes('占比'))) {
              return Math.round(val * 10000) / 100 + '%'
            } else if(props.legends[1] && props.legends[1].includes('时长')) {
              return formatNumber(val, 2) + 's'
            } else {
              return val
            }
          }
        }
      }
    ],
    series: [
      {
        name: props.legends[0] || 'bar',
        type: 'bar',
        data: props.data.map(item => item.value1) || [],
        label: {
          show: !!props.showLabel,
          position: 'top',
          color: '#626366',
          fontSize: 14,
          formatter: (params: {value: number, seriesName: string}) => {
            return params.seriesName?.includes('时长') ? formatNumber(params.value, 2) + '秒' : formatNumber(params.value || 0)
          },
        },
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          borderColor: '#fff',
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#0167FF' },
            { offset: 1, color: '#019FFF' }
          ]),
        },
      },
      {
        name: props.legends[1] || 'line',
        type: 'line',
        yAxisIndex: 1,
        color: '#F7BA1E',
        // smooth: true,
        connectNulls: true,
        label: {
          show: !!props.showLabel,
          position: 'top',
          color: '#F7BA1E',
          fontWeight: 'bold',
          fontSize: 14,
          formatter: (params: {value: number, seriesName: string}) => {
            const str = params.seriesName.includes('率') || params.seriesName.includes('占比') ? formatNumber(params.value * 100, 2) + '%' : formatNumber(params.value, 2)
            return str
          },
        },
        itemStyle: {
          borderRadius: 5,
          borderColor: '#fff',
          borderWidth: 2
        },
        emphasis: {
          focus: 'series',
          scale: true,
          label: {
            fontSize: 20
          }
        },
        data: props.data.map(item => item.value2) || [],
      }
    ]
  };

  if (option && myChart) {
    myChart.clear()
    myChart.setOption(option)
  }
}
</script>
