<template>
  <div class="tags-box" v-if="tagsDataArr && tagsDataArr.length>0">
    <el-tooltip :disabled="tagsNum >= tagsDataArr?.length" trigger="click">
      <template #content>
        <div class="tw-max-w-[30vw]">
          <div v-if="tagsName" class="tw-border-b tw-border-gray-400 tw-pb-[4px]">
            <div class="tw-text-left tw-truncate">{{tagsName}}</div>
          </div>
          <p class="tw-mt-[4px] tw-grid tw-grid-cols-4 tw-text-left tw-gap-[5px]">
            <span v-for="item in tagsDataArr" class="tw-truncate">{{ item }}</span>
          </p>
        </div>
      </template>
      <div class="tw-flex tw-max-w-full tw-h-full tw-flex-nowrap tw-overflow-hidden tw-items-center" ref="tagsRef">
        <span v-if="tagsDataArr && tagsDataArr.length>0" class="tw-text-[12px] tw-flex tw-shrink tw-overflow-hidden tw-items-center tw-h-[22px]" :class="!!props?.wrap ? 'tw-flex-wrap tw-h-auto' : ''">
          <el-tag v-for="(item) in tagsDataArr" :key="item" size="small" :type="type" :effect="effect" class="tw-mx-[2px]" :class="{'tw-my-[4px]': props?.wrap??false}">{{ item }}</el-tag>
        </span>
        <span class="tw-grow-0 tw-shrink-0 tw-h-[20px] tw-leading-[20px]">{{ (tagsDataArr?.length &&  (tagsDataArr.length - tagsNum > 0) || showTotal) ? `(共${tagsDataArr.length}项)` : '' }}</span>
      </div>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onUnmounted, watch, computed, } from 'vue'
import { onBeforeRouteLeave } from 'vue-router';
const props = withDefaults(defineProps<{
  tagsArr: string[];
  tagsName: string;
  tagsNum?: number;
  showTotal?: boolean;
  type?: string; // type: 'success' | 'info' | 'warning' | 'danger' | ''
  effect?: string; // 'dark' | 'light' | 'plain'
  wrap?: boolean;
}>(), {
  type: 'info',
  tagsNum: 2,
  effect: 'plain',
  tagsName: '',
  showTotal: false,
  wrap: false,
})
const { tagsArr, tagsName, effect, type, showTotal } = props
const tagsDataArr = ref<string[] | null>([]); // 选中的标签列表 (已排序) 或 选中的标签名称列
watch(() => props.tagsArr, n => {
  tagsDataArr.value = n
}, {
  immediate: true,
  deep: true
})
onUnmounted(() => {
  tagsDataArr.value = null
})
onBeforeRouteLeave(() => {
  tagsDataArr.value = null
})
const tagsNum = computed(() => props.tagsNum)
</script>

<style lang="postcss" type="text/postcss" scoped>
.tags-box {
  width: 100%;
  box-sizing: border-box;
  position: relative;
  .el-tag--small {
    padding: 0 3px;
  }
}
</style>
