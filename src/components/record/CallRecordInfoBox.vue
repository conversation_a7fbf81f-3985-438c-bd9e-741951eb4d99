<template>
  <el-scrollbar v-if="rowNormalData?.recordId" class="tw-h-full" view-class="tw-pr-[6px] tw-pb-[6px] tw-list-none">
  <li>
    <span class="label">客户姓名：</span>
    <span class="left-content">
      <span>{{ rowNormalData?.name || '-' }}</span>
    </span>
  </li>
  <li>
    <span class="label">被叫号码：</span>
    <span class="left-content">
      <span>{{ rowNormalData?.recordId || '-' }}</span>
      <span v-if="rowNormalData?.recordId" class="tw-text-[var(--el-color-primary)] tw-cursor-pointer tw-ml-[6px]" @click="copy(rowNormalData?.recordId)">复制</span>
      <span
        v-if="rowNormalData?.recordId && props.showPlaintext && rowNormalData?.manualIntentionClass?.includes('A') && [RecordTypeEnum['人机协同']].includes(props?.recordType)"
        class="tw-text-[var(--el-color-primary)] tw-cursor-pointer tw-ml-[6px]"
        @click="showPlaintextAction(rowNormalData?.recordId)"
      >
        查看明文
      </span>
    </span>
  </li>
  <li v-if="accountType === 0">
    <span class="label">名单编号：</span>
    <span class="left-content">{{ rowNormalData?.recordId || '-' }} </span>
  </li>
  <li>
    <span class="label">所属省市：</span>
    <span class="left-content">
      <span>{{ (rowNormalData?.province || "") + (rowNormalData?.city || "") }}</span>
    </span>
  </li>
  <template  v-if="accountType === 0">
    <li>
      <span class="label">项目：</span>
      <span class="left-content">{{ rowNormalData?.account ? globalStore.projectGoupIdMap?.get(rowNormalData.account)?.programName || '-' : '-' }}</span>
    </li>
    <li>
      <span class="label">行业：</span>
      <span class="left-content">{{ rowNormalData?.account ? globalStore.projectGoupIdMap?.get(rowNormalData.account)?.secondIndustryName || '-' : '-' }}</span>
    </li>
    <li>
      <span class="label">产品：</span>
      <span class="left-content">{{ rowNormalData?.account ? globalStore.projectGoupIdMap?.get(rowNormalData.account)?.productName || '-' : '-' }}</span>
    </li>
    <li>
      <span class="label">供应线路：</span>
      <span class="left-content">{{ rowNormalData?.lineCode || '-' }}</span>
    </li>
    <li>
      <span class="label">线路编号：</span>
      <span class="left-content">{{ rowNormalData?.lineId || '-' }}</span>
    </li>
    <li>
      <span class="label">商户线路：</span>
      <span class="left-content">{{ rowNormalData?.merchantLineCode || '-' }}</span>
    </li>
    <li>
      <span class="label">商户名称：</span>
      <span class="left-content">{{ rowNormalData?.tenantName || '-' }}</span>
    </li>
    <li>
      <span class="label">所属账号：</span>
      <span class="left-content">{{ rowNormalData?.account || '-' }}</span>
    </li>
  </template>
  
  <template v-if="hasAiInfo">
    <li>
      <span class="label">任务名称：</span>
      <span class="left-content">{{ rowNormalData?.taskName || '-' }}</span>
    </li>
    <li>
      <span class="label">执行话术：</span>
      <span class="left-content">
        {{ speechCraftList?.find(item => item.scriptStringId == rowNormalData?.scriptStringId)?.scriptName || '-' }}
      </span>
    </li>
  </template>
  <li>
    <span class="label">外呼时间：</span>
    <span class="left-content">{{ rowNormalData?.callOutTime || '-' }}</span>
  </li>
  <li>
    <span class="label">接通时间：</span>
    <span class="left-content">{{ rowNormalData?.talkTimeStart || '-' }}</span>
  </li>
  <li>
    <span class="label">挂断时间：</span>
    <span class="left-content">{{ rowNormalData?.talkTimeEnd || '-' }}</span>
  </li>
  <li>
    <span class="label">通话时长：</span>
    <span class="left-content">{{ (rowNormalData?.callDuration??-1)>-1 ? formatDuration(rowNormalData?.callDuration!/1000) || '0' : '-' }}</span>
  </li>
  <template v-if="hasAiInfo">
    <li v-if="accountType === 0">
      <span class="label">等待时长：</span>
      <span class="left-content">{{ (rowNormalData?.waitmsec??-1)>-1 ? formatMsDuration(rowNormalData?.waitmsec!/1000)||'' : '-' }}</span>
    </li>
    <li>
      <span class="label">客户说话：</span>
      <span class="left-content">{{ rowNormalData?.sayCount??'-' }}</span>
    </li>
    <li>
      <span class="label">互动次数：</span>
      <span class="left-content">{{ rowNormalData?.cycleCount??'-' }}</span>
    </li>
  </template>
  
  <li>
    <span class="label">挂断方：</span>
    <span class="left-content">{{ findValueInEnum(rowNormalData?.whoHangup, HangupEnum)||'-' }}</span>
  </li>
  <template v-if="hasAiInfo">
    <li v-if="accountType === 0">
      <span class="label">挂机原因：</span>
      <span class="left-content">{{ rowNormalData?.cause || '-' }}</span>
    </li>
    <li v-if="accountType === 0">
      <span class="label">挂机码：</span>
      <span class="left-content">{{ rowNormalData?.errorCode || '-' }}</span>
    </li>
    <li v-if="accountType === 0">
      <span class="label">机器人IP：</span>
      <span class="left-content">{{ rowNormalData?.fsIp || '-' }}</span>
    </li>
    <li v-if="accountType === 0">
      <span class="label">中继IP：</span>
      <span class="left-content">{{ rowNormalData?.aiCallIp || '-' }}</span>
    </li>
  </template>
  <template v-if="hasManualInfo">
    <template v-if="props.recordType===RecordTypeEnum['人机协同']">
      <li>
        <span class="label">是否转人工：</span>
        <span class="left-content">{{ findValueInStatus(rowNormalData?.isTransToCallSeat) || '-' }}</span>
      </li>
      <li>
        <span class="label">转人工等待：</span>
        <span class="left-content">{{ formatDiffDuration(rowNormalData?.startPopWinTime, rowNormalData?.endPopWinTime) || '-' }}</span>
      </li>
      <li>
        <span class="label">漏接坐席：</span>
        <span class="left-content">
          <el-button v-if="rowNormalData?.misCallSeatIdsText" link type="primary" @click="checkMisCallSeat(rowNormalData)">{{ JSON.parse(rowNormalData?.misCallSeatIdsText)?.length??'-' }}</el-button>
          <span v-else>-</span>
        </span>
      </li>
      <li>
        <span class="label">未介入原因：</span>
        <span class="left-content">{{ rowNormalData?.noReceptionReason || '-' }}</span>
      </li>
      <li>
        <span class="label">监听时长：</span>
        <span class="left-content">{{ formatDiffDuration(rowNormalData?.startMonitorTime, rowNormalData?.endMonitorTime) || '-' }}</span>
      </li>
    </template>
    <li>
      <span class="label">接待时长：</span>
      <span class="left-content">
        {{ props.recordType===RecordTypeEnum['人机协同'] ?
        formatDiffDuration(rowNormalData?.startAnswerTime, rowNormalData?.endAnswerTime) || '-' 
        : ((rowNormalData?.callDuration??-1)>-1 ? formatDuration(rowNormalData?.callDuration!/1000) || '0' : '-') }}</span>
    </li>
    <li>
      <span class="label">接待坐席：</span>
      <span class="left-content">
          {{ callSeatList.find(item => item.id === rowNormalData?.callSeatId)?.account || '-' }}
      </span>
    </li>
    <li>
      <span class="label">所属坐席组：</span>
      <span class="left-content">
        {{ callTeamList?.find(item => item.id === rowNormalData?.callTeamId)?.callTeamName || '-' }}
      </span>
    </li>
    <li>
      <span class="label">话后处理时长：</span>
      <span class="left-content">
        <span class="left-content">{{ (rowNormalData?.postingDuration??-1)>-1 ? formatDuration(rowNormalData?.postingDuration!/1000)||'0' :'-' }}</span>
      </span>
    </li>
    <li>
      <span class="label">话后处理超时：</span>
      <span class="left-content">
        {{ findValueInStatus(rowNormalData?.isPostingOutOfTime) || '-' }}
      </span>
    </li>
  </template>
  <template v-if="hasAiInfo">
    <li>
      <span class="label">AI分类结果：</span>
      <span class="left-content">{{ rowNormalData?.intentionClass || "" }}</span>
    </li>
    <li>
      <span class="label">AI标签：</span>
      <span v-if="rowNormalData?.intentionLabels" class="tw-flex tw-flex-wrap">
        <el-tag
          v-for="tag in rowNormalData?.intentionLabels?.split(',')"
          :key="tag"
          type="info"
          class="tw-mr-0.5 tw-mb-0.5"
        >
          {{ tag }}
        </el-tag>
      </span>
      <span v-else>-</span>
    </li>
    <li v-if="accountType === 0">
      <span class="label">AI语义：</span>
      <span v-if="rowNormalData?.hitSemanticIds" class="left-content">
        {{ semanticMap ? translateHitSemantic(rowNormalData?.hitSemanticIds, semanticMap) : '-' }}
      </span>
      <span v-else>-</span>
    </li>
    <li v-if="accountType === 0">
      <span class="label">命中高级规则：</span>
      <span v-if="rowNormalData?.hitAdvanceIds" class="left-content">
        <el-button v-if="rowNormalData?.hitAdvanceIds" type="primary" link @click="checkAdvanceRule(rowNormalData)">
          {{ rowNormalData?.hitAdvanceIds?.split(',')?.length || '-' }}
        </el-button>
        <span v-else>-</span>
      </span>
      <span v-else>-</span>
    </li>
  </template>
  <template v-if="props?.recordType===RecordTypeEnum['人机协同']">
    <li>
      <span class="label">人工分类结果：</span>
      <span class="left-content">{{ rowNormalData?.manualIntentionClass || '-' }}</span>
    </li>
    <li>
      <span class="label">人工标签：</span>
      <span v-if="rowNormalData?.manualIntentionLabels" class="tw-flex tw-flex-wrap">
        <el-tag
          v-for="tag in rowNormalData?.manualIntentionLabels?.split(',')"
          :key="tag"
          type="info"
          class="tw-mr-0.5 tw-mb-0.5"
        >
        {{ tag }}
      </el-tag>
      </span>
      <span v-else>-</span>
    </li>
  </template>
    
  <template v-if="hasManualInfo">
    <li v-if="!props.readonly">
      <span class="label">表单收集：</span>
      <span class="left-content">
        <el-button v-if="rowNormalData?.clueId" :type="rowNormalData?.clueId ? 'primary': 'default'" :disabled="!rowNormalData?.clueId" link @click="checkFormRecord(rowNormalData)">查看</el-button>
        <template v-else>-</template>
      </span>
    </li>
    <li>
      <span class="label">跟进备注：</span>
      <span class="left-content">{{ rowNormalData?.followUpNote||'-' }}</span>
    </li>
    <li>
      <span class="label">跟进状态：</span>
      <span class="left-content">
        <span
          v-if="rowNormalData?.followUpStatus"
          class="status-box-mini"
          :class="getFollowStatusClass(rowNormalData?.followUpStatus)"
        >
          {{findValueInEnum(rowNormalData?.followUpStatus, FollowUpStatusEnum)}}
        </span>
        <span v-else>-</span>
      </span>
    </li>
  </template>
  <li v-if="hasAiInfo">
    <span class="label">转为线索：</span>
    <span class="left-content">
      <span>{{ findValueInStatus(rowNormalData?.isConvertToClue) || '-' }}</span>
      <el-button
        v-if="!(rowNormalData && rowNormalData?.isConvertToClue) && !props.readonly && accountType"
        class="tw-ml-[6px]"
        link
        type="primary"
        @click="convert2Clues(rowNormalData)"
      >转为线索</el-button>
    </span>
  </li>
  <li>
    <span class="label">触发短信：</span>
    <span class="left-content">
      <el-button v-if="rowNormalData.ifSendSms === '是'" link type="primary" @click="openSmsDialog()">是</el-button>
      <span v-else>否</span>
    </span>
  </li>
  </el-scrollbar>
  <el-empty v-else></el-empty>
  <RelatedListDialog
    title="漏接坐席"
    :list="seatMisList||[]"
    :gridNum="2"
    v-model:visible="seatVisible"
  />
  <AdvanceRuleDetailsDialog
    title="命中高级规则详情"
    :scriptId="rowNormalData?.speechCraftId"
    v-model:data="currentRuleIds"
  />
  <FormRecordDialog v-if="formViewVisible" title="表单查看" type="check" v-model:visible="formViewVisible" :formSetting="formSetting||[]" :formRecord="formRecordData"/>
  <TaskSmsDialog
    v-model:visible="smsVisible"
    :type="2"
    :data="currentRecord"
  />
  <PlaintextDialog v-model:data="showPlaintextPhone"/>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, watch, defineAsyncComponent, onUnmounted } from 'vue'
import { formatDuration, formatMsDuration, copyText, findValueInEnum, formatDiffDuration, findValueInStatus } from '@/utils/utils'
import { ElMessage } from 'element-plus'
import { TaskCallRecordItem, HangupEnum, RecordTypeEnum } from '@/type/task'
import { useUserStore } from '@/store/user'
import { SpeechCraftInfoItem, } from '@/type/speech-craft'
import { FormRecordItem, CollectionFormItem, FollowUpStatusEnum, } from '@/type/clue'
import { SeatTeam, SeatMember, } from '@/type/seat'
import { useTaskStore } from '@/store/taskInfo'
import { getFollowStatusClass } from '@/views/merchant/manual-call/components/constant'
import AdvanceRuleDetailsDialog from './AdvanceRuleDetailsDialog.vue'
import { formRecordModel, formSettingModel } from '@/api/clue'
import { useGlobalStore } from '@/store/globalInfo'
import { storeToRefs } from 'pinia'
import Confirm from '@/components/message-box'
import { aiOutboundTaskModel, } from '@/api/ai-report'
import dayjs from 'dayjs'
import { translateHitSemantic } from '@/views/merchant/call-record/constants'
import { AiSemantics } from '@/type/core-semantic'
import { scriptCoreSemanticModel } from '@/api/speech-craft'
import { onBeforeRouteLeave } from 'vue-router'
import { getCallTeamAndSeatOptions } from '@/views/merchant/call-record/constants'
import { MerchantProjectItem } from '@/type/merchant'
import TaskSmsDialog from '@/components/task/TaskSmsDialog.vue'

const FormRecordDialog = defineAsyncComponent({loader: () => import('@/components/clue/FormRecordDialog.vue')})
const RelatedListDialog = defineAsyncComponent({loader: () => import('@/components/RelatedListDialog.vue')})
const PlaintextDialog = defineAsyncComponent({loader: () => import('@/components/record/PlaintextDialog.vue')})
// 组件入参props
const props = defineProps<{
  recordData: TaskCallRecordItem | null,
  readonly?: boolean,
  showPlaintext?: boolean
  recordType: RecordTypeEnum
}>()
const emits = defineEmits(['updateRecord', 'check-form'])

// 全局变量
const userInfo  = useUserStore()
const taskStore = useTaskStore()
const globalStore = useGlobalStore()
const accountType = userInfo.accountType??1 // 用户角色，0运营；1商户

// 复制被叫号码
const copy =(val: string) => {
  copyText(val || '')
}

// 查看明文，仅支持人机协同号码查看
const showPlaintextPhone = ref<string>('')
const showPlaintextAction = (val: string) => {
  showPlaintextPhone.value = val
}

const hasAiInfo = computed(() => {
  return props.recordType && [RecordTypeEnum['AI外呼'], RecordTypeEnum['人机协同'],].includes(props?.recordType)
})
const hasManualInfo = computed(() => {
  return props?.recordType && [RecordTypeEnum['人工直呼'], RecordTypeEnum['人机协同'],].includes(props?.recordType)
})


const rowNormalData = ref<TaskCallRecordItem | null>(props.recordData)

// 转为线索
const convert2Clues = (row: TaskCallRecordItem) => {
  if (!row?.recordId) return
  const { loading } = storeToRefs(globalStore)
  Confirm({
    text: `您确定要将该号码转为线索吗？`,
    type: 'warning',
    title: '转为线索确认'
  }).then(async () => {
    loading.value = true
    const params = {
      groupId: userInfo.groupId,
      isToday: !dayjs(row.callOutTime).isBefore(dayjs().startOf('day')),
      recordId: row.recordId!,
    }
    props.recordType === RecordTypeEnum['AI外呼']
    ? await aiOutboundTaskModel.aiRecordConvert2Clues(params)
    : await aiOutboundTaskModel.mixRecordConvert2Clues(params)
    emits('updateRecord')
    loading.value = false
  }).catch(() => {})
}

/** 查询高级规则（使用后端接口，会记录历史规则） */
const currentRuleIds = ref<string | null>(null)
const checkAdvanceRule = async (row: TaskCallRecordItem) => {
  if (!row.hitAdvanceIds || !row.recordId) return
  currentRuleIds.value = row.hitAdvanceIds
}

/** 查询表单记录 */
const formViewVisible = ref(false)
class FormRecordOriginItem {
  callSeatId = undefined
  clueId = undefined
  formRecordId = undefined
  fromCollectionContentList = []
}
const formRecordData = reactive<FormRecordItem>(new FormRecordOriginItem())
const formSetting = ref<CollectionFormItem[] | null>([])
const checkFormRecord = async (row: TaskCallRecordItem) => {
  if (!row.clueId) {
    return ElMessage.warning('暂无表单数据！')
  }
  const data = await formRecordModel.getFormRecordByClueId({
    clueId: row.clueId!
  })
  data.clueId = data.clueId || row.clueId
  // 获取最新表单设置, 商户端读取缓存，运营端通过account获取
  if (accountType) {
    formSetting.value = await taskStore.getEnableFormSetting() || []
  } else {
    formSetting.value = await formSettingModel.getFormSettingInfoByAccount({account: row.account!})
  }
  Object.assign(formRecordData, data)
  formViewVisible.value = true
}

/** 查看漏接坐席 */
const seatVisible = ref(false)
const seatMisList = ref<string[] | null>([])
const callSeatList = ref<SeatMember[]>([])
const checkMisCallSeat = async (row: TaskCallRecordItem) => {
  if (!row.misCallSeatIdsText) return
  
  const arr: number[] =  JSON.parse(row.misCallSeatIdsText) || []
  seatMisList.value = arr.map(item => {
    const row = callSeatList.value.find(v => v.id === +item)
    return row ? `${row.account||''}` : ''
  })
  seatVisible.value = true
}


/** 查看触发短信弹窗 */
// 触发短信详情
const smsVisible = ref(false)
const currentRecord = ref<null | {callRecordId: string, callOutTime: string}>(null)
const openSmsDialog = () => {
  if (!rowNormalData.value || !rowNormalData.value.recordId || !rowNormalData.value.callOutTime) return ElMessage.warning('获取记录信息失败')
  currentRecord.value = {
    callRecordId: rowNormalData.value.recordId,
    callOutTime: rowNormalData.value.callOutTime!,
  }
  smsVisible.value = true
}

const findProjectInfo = async () => {
  if (!rowNormalData.value || !rowNormalData.value?.taskId || accountType) return
  const res = await aiOutboundTaskModel.findProgramByTaskIds([+rowNormalData.value?.taskId!])
  if (res) {
    const info = projectList.value?.find(item => item.id + '' === res[0]?.programId)
    if (info) {
      rowNormalData.value.programName = info.programName || ''
      rowNormalData.value.secondIndustryName = info.secondIndustryName || ''
      rowNormalData.value.productName = info.productName || ''
    }
  }
}
const callTeamList = ref<SeatTeam[] | null>([])
const speechCraftList = ref<SpeechCraftInfoItem[] | null>([])
const semanticMap = ref<Record<string, AiSemantics> | null>({})
const projectList = ref<MerchantProjectItem[] | null>([])
const init = async () => {
  speechCraftList.value = await taskStore.getAllScriptListOptions()
  if (hasManualInfo.value) {
    const res = await getCallTeamAndSeatOptions()
    callTeamList.value = res?.callTeamList || []
    callSeatList.value = res?.callSeatList || []
  }
  if (hasAiInfo.value && accountType === 0) {
  // 初始化全部行业语义对象
    semanticMap.value = await scriptCoreSemanticModel.findSemanticMap() as Record<string, AiSemantics>
  }
  if (accountType === 0) {
    await globalStore.updateProjectList()
  }
}
init()
/** watch开始 */
// 监听入参，更新选中数据和选项变化
watch(() => props.recordData, () => {
  props.recordData && (rowNormalData.value = props.recordData)
  findProjectInfo()
}, {deep: true, immediate: true})

const clearAllData = () => {
  rowNormalData.value = null
  callTeamList.value = null
  semanticMap.value = null
  speechCraftList.value = null
  formSetting.value = null
  seatMisList.value = null
}

onUnmounted(() => {
  clearAllData()
})

onBeforeRouteLeave(() => {
  clearAllData()
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.left-content {
  color: #323233;
  text-align: left;
  word-break: break-all;
}
.label {
  color: #646566;
  text-align: right;
  min-width: 80px;
  display: inline-block;
  flex-grow: 0;
  flex-shrink: 0;
}
li {
  margin-top: 10px;
  line-height: 22px;
  display: flex;
  justify-content: flex-start;
  .span {
    word-wrap: break-word
  }
}
.name-box {
  display: flex;
  width: 100%;
  height: 36px;
  padding: 8px 12px;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  background: #F0F2F5;
  position: relative;
  line-height: 20px;
  margin-bottom: 12px;
  color: var(--primary-black-color-600);
}
.rectangle-left {
  border-radius: 0px 2px 2px 0px;
  width: 2px;
  height: 24px;
  position: absolute;
  left: 0;
  top: 6px;
  background-color: var(--primary-orange-color)
}
</style>
