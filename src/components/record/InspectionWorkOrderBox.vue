<template>
  <div class="tw-flex tw-flex-col tw-h-full dialog-form">
    <div class="tw-text-left tw-text-[14px] tw-font-semibold tw-mb-[12px]">新建巡检工单</div>
  <el-form
    :model="editData"
    class="tw-grow"
    :rules="rules"
    label-width="80px"
    ref="editRef"
  >
    <el-form-item v-if="editData.id" label="工单ID：" prop="id">
      <span class="info-title">{{ editData.id }}</span>
    </el-form-item>
    <el-form-item label="问题类型：" prop="type">
      <el-select
        v-model="editData.type"
        filterable
        placeholder="请选择问题类型"
        style="width: 100%;"
      >
        <el-option
          v-for="item in enum2Options(InspecteTypeEnum)"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="问题描述：" prop="description">
      <el-input v-model="editData.description" :autosize="{minRows: 5}" type="textarea" show-word-limit clearable placeholder="请输入问题描述" :maxlength="200"></el-input>
    </el-form-item>
    <el-form-item label="负责人：">
      <el-select
        v-model="editData.principal"
        filterable
        placeholder="请选择问题负责人"
        style="width: 100%;"
      >
        <el-option v-for="item in accountList" :key="item.account" :label="`${item.account}(${item.name})`" :value="item.account" />
      </el-select>
    </el-form-item>
  </el-form>
  <el-button @click="submit" class="tw-mb-[12px]" :loading="loading" type="primary">提交</el-button>
  <el-button @click="cancel" class="tw-mb-[12px]">取消</el-button>
</div>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, watch, defineAsyncComponent, onUnmounted } from 'vue'
import { pickAttrFromObj, enum2Options } from '@/utils/utils'
import { trace } from '@/utils/trace'
import { ElMessage } from 'element-plus'
import { aiTeamModel, } from '@/api/user'
import { onBeforeRouteLeave } from 'vue-router'
import {  InspecteTypeEnum, InspecteStatusEnum, InspecteOrderItem, InspecteOrderItemOrigin } from '@/type/Inspection'
import { AccountItem } from '@/type/user'
import type { FormInstance } from 'element-plus'
import { inspectionModel } from '@/api/Inspection'
import to from 'await-to-js'
import { TaskCallRecordItem, RecordTypeEnum } from '@/type/task'


// 组件入参props
const props = defineProps<{
  data?: InspecteOrderItem | null,
  recordType: RecordTypeEnum // 通话类型
  recordData?: TaskCallRecordItem | null,
}>()
const emits = defineEmits(['close'])


const loading = ref(false)

const editData = reactive<Partial<InspecteOrderItem>>(new InspecteOrderItemOrigin())
const editRef = ref<FormInstance | null>(null)
const rules = {
  type: [
    { required: true, message: '请选择问题类型', trigger: 'change' },
  ],
  description: [
    { required: true, message: '请输入问题描述', trigger: 'blur' },
  ],
  // principal: [
  //   { required: true, message: '请选择问题负责人', trigger: 'change' },
  // ],
}

const submit = () => {
  if (!props.recordData || !props.recordData.recordId) return ElMessage.warning('获取记录ID失败')
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const params = pickAttrFromObj(editData, ['type', 'description', 'principal'])
      params.callRecordId = props.recordData?.recordId || ''
      params.callTime = props.recordData?.callOutTime || ''
      params.phoneType = props.recordType
      const [err,] = await to(inspectionModel.createInspectionWorkOrder(params))
      loading.value = false
      if (!err) {
        ElMessage.success('操作成功')
        emits('close')
      }
    }
  })
}

const cancel = () => {
  emits('close')
}

const accountList = ref<AccountItem[] | null>(null)
const init = async () => {
  loading.value = true
  accountList.value = await aiTeamModel.getMerchantAccountList({}) as AccountItem[] || []
  loading.value = false
}
init()
/** watch开始 */
// 监听入参，更新选中数据和选项变化
watch(() => props.data?.id, () => {
  if (props.data?.id) {
    Object.assign(editData, new InspecteOrderItemOrigin(), props.data)
  } else {
    Object.assign(editData, new InspecteOrderItemOrigin())
  }
}, {immediate: true})

const clearAllData = () => {
  accountList.value = null
}

onUnmounted(() => {
  clearAllData()
})

onBeforeRouteLeave(() => {
  clearAllData()
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.left-content {
  color: #323233;
  text-align: left;
  word-break: break-all;
}
.label {
  color: #646566;
  text-align: right;
  min-width: 80px;
  display: inline-block;
  flex-grow: 0;
  flex-shrink: 0;
}
li {
  margin-top: 10px;
  line-height: 22px;
  display: flex;
  justify-content: flex-start;
  .span {
    word-wrap: break-word
  }
}
.name-box {
  display: flex;
  width: 100%;
  height: 36px;
  padding: 8px 12px;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  background: #F0F2F5;
  position: relative;
  line-height: 20px;
  margin-bottom: 12px;
  color: var(--primary-black-color-600);
}
.rectangle-left {
  border-radius: 0px 2px 2px 0px;
  width: 2px;
  height: 24px;
  position: absolute;
  left: 0;
  top: 6px;
  background-color: var(--primary-orange-color)
}
</style>
