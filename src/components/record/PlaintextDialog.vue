<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    align-center
    class="dialog-form"
    :close-on-click-modal="false"
    append-to-body
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">查看明文</div>
    </template>
    <div v-loading="loading" class="tw-flex tw-flex-col tw-w-full tw-items-start tw-p-[12px] tw-text-[13px]">
      <div>
        <span>手机号码：</span>
        <span class="info-title">{{ historyData.phone }}</span>
      </div>
      <div>
        <span>查看数量：</span>
        <span v-if="historyData.remainCount >= 5" class="info-title">今日剩余【<span class="tw-text-[var(--el-color-primary)]">{{historyData.remainCount}}</span>】次</span>
        <span v-else class="info-title">
          今日剩余【<span class="tw-text-[var(--el-color-danger)]">{{historyData.remainCount}}</span>】次
          <span v-if="accountType == 1">，请根据实际情况评估是否需要向系统管理员申请更多次数！</span>
        </span>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">关闭</el-button>
        <el-button v-if="historyData.phone" type="primary" :loading="loading" @click="confirm">复制并关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, h, } from 'vue'
import { aiOutboundTaskModel } from '@/api/ai-report'
import to from 'await-to-js'
import { useUserStore } from '@/store/user'
import { copyText } from '@/utils/utils'

const emits = defineEmits(['update:data'])
const loading = ref(false)
const props = defineProps<{
  data: string
}>();

const userInfo = useUserStore()
const accountType = userInfo.accountType
const dialogVisible = ref(!!props.data)
const historyData = reactive<{
  recordId: string,
  phone: string,
  remainCount: number,
}>({
  recordId: '',
  phone: '',
  remainCount: 0
})
const cancel = () => {
  dialogVisible.value = false
  emits('update:data', '')
}
const confirm = () => {
  if (historyData.phone) {
    copyText(historyData.phone)
    cancel()
  }
}
const checkPlaintext = async () => {
  if (!props.data) {
    cancel()
    return
  } 
  if (props.data == historyData.recordId) return
  loading.value = true
  const [err, res] = await to(aiOutboundTaskModel.convertPlainPhoneByRecordId({ recordId: props.data })) as [Error | null, {
    phone: string,
    remainCount: number,
  } | null]
  loading.value = false
  
  if (err) {
    // 如果获取失败，则立即关闭弹窗
    cancel()
  } else {
    historyData.recordId = props.data
    historyData.phone = res?.phone || ''
    historyData.remainCount = res?.remainCount || 0
  }
}

watch(() => props.data, n => {
  dialogVisible.value = !!n
  if (!!n) {
    checkPlaintext()
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.dialog-form {
  z-index: 9 !important;
}
</style>
