<template>
  <el-drawer
    v-model="visible"
    :before-close="closeDetails"
    :size="drawerWidth()"
    :with-header="false"
  >
    <div class="tw-bg-white tw-h-[50px] tw-px-[12px] tw-text-[16px] tw-font-semibold tw-text-left tw-text-[#313233] tw-flex tw-items-center tw-justify-between">
      <span>{{findValueInEnum(props?.recordType, RecordTypeEnum)??'通话'}}记录详情</span>
      <el-button link @click="closeDetails">
        <el-icon :size="20" color="var(--primary-black-color-400)"><CloseBold /></el-icon>
      </el-button>
    </div>
    <div class="record-detail-container" v-loading="loading">
      <div v-if="accountType==0 && inspectionWorkOrderVisible" class="left">
        <InspectionWorkOrderBox
          :recordData="rowNormalData"
          :recordType="props?.recordType"
          @close="inspectionWorkOrderVisible = false"
        />
      </div>
      <div class="left">
        <CallRecordInfoBox
          :record-data="rowNormalData!"
          :recordType="props?.recordType"
          @updateRecord="updateRecordLeftInfo"
        />
        <div class="tw-grow-0 tw-shrink-0 tw-w-full tw-flex tw-items-center tw-justify-center tw-pl-[50px] tw-relative">
          <el-button v-if="accountType==0"  type="primary" :link="!inspectionWorkOrderVisible" class="order-btn" @click="showInspectionWorkOrder">
            巡检工单</el-button>
          <el-button type="primary" link @click="handleAudioChange(-1)" :disabled="(searchForm.startPage || 0) * (searchForm.pageNum || 0) + currentIndex <= 0">上一条</el-button>
          <span class="tw-mx-1">{{ `${(searchForm.startPage || 0) * (searchForm.pageNum || 0) + currentIndex + 1} / ${total === 10000 ? '总数获取中…': total}` }}</span>
          <el-button type="primary" link @click="handleAudioChange(1)" :disabled="(searchForm.startPage || 0) * (searchForm.pageNum || 0) + currentIndex + 1 >= total">下一条</el-button>
          <el-tooltip content="刷新当前记录" placement="right" :show-after="500">
            <el-icon size="--el-font-size-base" class="tw-cursor-pointer tw-ml-0.5 tw-text-[#969799] hover:tw-text-[var(--el-color-primary)]" @click="updateRecordLeftInfo"><SvgIcon name="reset" color="inherit"/></el-icon>
          </el-tooltip>
        </div>
      </div>
      <div class="right" v-loading="dialogLoading">
        <div class="tw-flex tw-items-center tw-p-[16px] tw-pt-0">
          <span class="tw-mr-1">通话录音</span>
          <AudioMode
            v-if="rowNormalData && rowNormalData.callId && rowNormalData.wholeAudioFileUrl"
            :audioUrl="rowNormalData.wholeAudioFileUrl || ''"
            :audioName="rowNormalData.taskName || '未知任务'"
            v-model:audioStatus="audioStatus"
            v-model:audioVolume="audioVolume"
            @update:audio-status="inspecteRecordAudio"
          >
          </AudioMode>
        </div>
        <CallRecordDialogBoxNew
          v-model:clearAudio="clearAudio"
          v-model:needUpdate="needUpdate"
          :isTransfer="rowNormalData?.isTransToCallSeat || false"
          :client-name="rowNormalData?.name||''"
          :startEndInfo="startEndInfo"
          :info-query-map="infoQueryMap"
          :dataList="rowDialogData || []"
          @play="inspecteRecordAudio('play')"
        />
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, defineAsyncComponent, onUnmounted } from 'vue'
import { TaskCallRecordItem, HangupEnum, RecordTypeEnum, CallStatusEnum } from '@/type/task'
import { aiOutboundTaskModel, } from '@/api/ai-report'
import { RecordDialogueData, TaskCallSearchModal } from '@/type/task'
import { CloseBold, CaretLeft } from '@element-plus/icons-vue'
import { findValueInEnum, changeAudioUrlOrigin } from '@/utils/utils'
import { InfoQueryItem, } from '@/type/speech-craft'
import { ElMessage } from 'element-plus'
import { ResponseData } from '@/axios/request/types'
import { useGlobalStore } from '@/store/globalInfo'
import { storeToRefs } from 'pinia'
import { scriptInfoModel } from '@/api/speech-craft'
import to from 'await-to-js';
import { useUserStore } from '@/store/user'
import { useTaskStore } from '@/store/taskInfo'

const CallRecordDialogBoxNew = defineAsyncComponent({loader: () => import('./CallRecordDialogBoxNew.vue')})
const CallRecordInfoBox = defineAsyncComponent({loader: () => import('./CallRecordInfoBox.vue')})
const AudioMode = defineAsyncComponent({loader: () => import('../AudioMode.vue')})
const InspectionWorkOrderBox = defineAsyncComponent({loader: () => import('./InspectionWorkOrderBox.vue')})

const props = defineProps<{
  visible: boolean; // 抽屉的可见性
  recordType: RecordTypeEnum // 通话类型
  tableData: TaskCallRecordItem[]; // 当前页的通话记录
  currentIndex: number; // 当前通话记录在当前页的通话记录的index
  total: number; // 总通话记录数（不是当页）
  searchForm: TaskCallSearchModal; // 搜索条件，用于翻页
}>();
const emits = defineEmits(['close', 'update:visible', 'update:record'])

const userInfo = useUserStore()
const accountType = userInfo.accountType

// 读取props数据，并用监听器监听变化
const visible = ref(props.visible || false)

// 读取缓存，通话记录信息
const searchForm = reactive(JSON.parse(JSON.stringify(props.searchForm)))
const allRecordData = ref<TaskCallRecordItem[] | null>(props.tableData || [])
const currentIndex = ref<number>(props.currentIndex || 0)
const total = ref(props.total || 0)


// pinia数据读取
const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)

// 详情抽屉宽度
const drawerWidth = () => {
  return window.innerWidth > 1400 ? '80%' : '900px'
}
const clearAudio = ref(false) // 对于一些操作，需要是的对话组件的音频清空
/** 左侧 */
const rowNormalData = ref<TaskCallRecordItem | null>(allRecordData.value ? allRecordData.value[currentIndex.value] : null) // 左侧通话记录数据
const audioStatus = ref<'pause' | 'play' | 'none'>('pause') // 左侧通话记录音频播放状态
const audioVolume = ref<number>(70) // 左侧通话记录音频声音
/** 刷新通话记录列表 */
const updateRecordListData = async () => {
  loading.value = true
  let res: ResponseData
  switch(props?.recordType) {
    case RecordTypeEnum['AI外呼'] : {
      const result = await to(aiOutboundTaskModel.findCallRecordList(searchForm, accountType === 0)) as [any, ResponseData];
      res = result[1]
      break;
    };
    case RecordTypeEnum['人机协同'] : {
      const result = await to(aiOutboundTaskModel.findCallRecordMixList(searchForm, accountType === 0)) as [any, ResponseData];
      res = result[1]
      break;
    };
    case RecordTypeEnum['人工直呼'] : {
      const result = await to(aiOutboundTaskModel.findCallRecordManualList(searchForm, accountType === 0)) as [any, ResponseData];
      res = result[1]
      break;
    };
  }
  loading.value = false
  return res?.data as TaskCallRecordItem[] || []

}
/** 点击左侧刷新按钮、转为线索后触发刷新操作 */
const updateRecordLeftInfo = async () => {
  const id = rowNormalData.value?.id
  rowNormalData.value = {}
  allRecordData.value = await updateRecordListData()
  const index = allRecordData.value.findIndex(item => item.id === id)
  currentIndex.value = index > -1 ? index : currentIndex.value
  rowNormalData.value = allRecordData.value[currentIndex.value]
  emits('update:record', searchForm, currentIndex.value, total.value, allRecordData.value)
}

// 左侧通话记录切换
const preAudioStatus = ref<'pause' | 'play' | null>(null) // 上一条录音的状态
const handleAudioChange = async (flag: -1 | 1) => {
  if (!allRecordData.value) return
  // 如果上一个是播放状态，那么下一个也应该是播放
  if (!preAudioStatus.value && audioStatus.value === 'play') {
    preAudioStatus.value = 'play'
  }
  audioStatus.value = 'pause'
  clearAudio.value = true
  const index2 = currentIndex.value + flag
  if (allRecordData.value[index2] && allRecordData.value[index2]?.callStatus == CallStatusEnum['呼叫成功']) {
    currentIndex.value = index2
    if (preAudioStatus.value === 'play') {
      audioStatus.value = preAudioStatus.value
      preAudioStatus.value = null
    }
    emits('update:record', searchForm, currentIndex.value, total.value, allRecordData.value)
  } else if (allRecordData.value[index2]?.id) {
    currentIndex.value = index2
    handleAudioChange(flag)
  } else {
    if ((flag > 0 && searchForm.pageNum > allRecordData.value?.length) || (flag < 0 && searchForm.startPage <= 0)) {
      currentIndex.value = index2 as number
      preAudioStatus.value = null
      ElMessage.warning( `未找到新的可播放的录音`)
      if (currentIndex.value <= 0 && flag === -1) {
        handleAudioChange(1)
      }
      if (currentIndex.value >= allRecordData.value?.length && flag === 1) {
        handleAudioChange(-1)
      }
    } else {
      loading.value = true
      currentIndex.value = (flag > 0) ? 0 : ((searchForm.pageNum || 20) - 1)
      searchForm.startPage = (searchForm.startPage || 0) + flag
      allRecordData.value = await updateRecordListData()
      if (allRecordData.value.length <= 0) {
        (flag > 0) ? handleAudioChange(-1) : handleAudioChange(1)
        ElMessage.warning(`未找到新的可播放的录音`)
        preAudioStatus.value = null
      } else {
        if (allRecordData.value[currentIndex.value]?.callStatus != CallStatusEnum['呼叫成功']) {
          handleAudioChange(flag)
        } else {
          if (preAudioStatus.value === 'play') {
            audioStatus.value = preAudioStatus.value
            preAudioStatus.value = null
          }
          emits('update:record', searchForm, currentIndex.value, total.value, allRecordData.value)
          loading.value = false
        }
      }
      loading.value = false
    }
  }
}
// 快捷键触发通话记录切换
const handleKeyup = (e: {code: string, preventDefault: Function}) => {
  if (['ArrowRight',].includes(e.code)) {
    handleAudioChange(1)
    e.preventDefault();
  }
  if (['ArrowLeft',].includes(e.code)) {
    handleAudioChange(-1)
    e.preventDefault();
  }
  if (['ArrowDown'].includes(e.code)) {
    audioVolume.value = audioVolume.value - 10 < 0 ? 0 : audioVolume.value - 10
    e.preventDefault();
  }
  if (['ArrowUp'].includes(e.code)) {
    audioVolume.value = audioVolume.value + 10 > 100 ? 100 : audioVolume.value + 10
    e.preventDefault();
  }
  if (['Space',].includes(e.code)) {
    audioStatus.value =  audioStatus.value === 'play' ? 'pause' : 'play'
    e.preventDefault();
  }
}

// 右侧机器人用户对话
const rowDialogData = ref<RecordDialogueData[] | null>([]) // 数据列表
const dialogLoading = ref(false) // 针对右侧对话部分的的loading
const infoQueryMap = ref<Map<string, InfoQueryItem>>(new Map([]))
const needUpdate = ref(false) // 通话记录对话详情是否需要更新
const startEndInfo = computed(() => {
  const res = [] as {dialogTime: string, content: string}[]
  if (rowNormalData.value?.talkTimeStart) {
    res.push({ dialogTime: rowNormalData.value?.talkTimeStart, content: '电话接通'})
  }
  // if (rowNormalData.value?.triggerMonitorTime) {
  //   res.push({ dialogTime: rowNormalData.value?.triggerMonitorTime, content: '触发转人工监听'})
  // }
  if (rowNormalData.value?.startPopWinTime) {
    res.push({ dialogTime: rowNormalData.value?.startPopWinTime, content: '触发转人工'})
  }
  if (rowNormalData.value?.startMonitorTime) {
    res.push({ dialogTime: rowNormalData.value?.startMonitorTime, content: '坐席开始监听'})
  }
  if (rowNormalData.value?.endMonitorTime) {
    res.push({ dialogTime: rowNormalData.value?.endMonitorTime, content: '坐席结束监听'})
  }
  if (rowNormalData.value?.startAnswerTime) {
    res.push({ dialogTime: rowNormalData.value?.startAnswerTime, content: '坐席开始接听'})
  }
  if (rowNormalData.value?.endAnswerTime) {
    res.push({ dialogTime: rowNormalData.value?.endAnswerTime, content: '坐席结束接听'})
  }
  if (rowNormalData.value?.talkTimeEnd) {
    res.push({ dialogTime: rowNormalData.value?.talkTimeEnd, content: (findValueInEnum(rowNormalData.value?.whoHangup, HangupEnum) || '')+ '挂断通话'})
  }
  return res
})

/** 更新机器人用户对话数据 */
const updateRecordDialogData = async () => {
  if ((currentIndex.value??-1) !== -1 && allRecordData.value && allRecordData.value[currentIndex.value]) {
    rowNormalData.value = JSON.parse(JSON.stringify(allRecordData.value[currentIndex.value]))
    const callId = rowNormalData.value?.callId || ''
    if (!callId) {
      rowDialogData.value = []
      needUpdate.value = true
      return ElMessage.warning('无法获取到callId！')
    }
    dialogLoading.value = true
    switch(props?.recordType) {
      case RecordTypeEnum['AI外呼']: {
        const [_, data] = await to(aiOutboundTaskModel.getAiDialogueDataList({
          callId: callId,
          recordId: rowNormalData.value?.recordId || '',
        }))
        rowDialogData.value = data || []
        break;
      }
      case RecordTypeEnum['人工直呼']: {
        const [_, data] = await to(aiOutboundTaskModel.getManualDialogueDataList({
          callId: callId,
          recordId: rowNormalData.value?.recordId || '',
        }))
        rowDialogData.value = data || []
        break;
      }
      case RecordTypeEnum['人机协同']: {
        const [_, data] = await to(aiOutboundTaskModel.getMixDialogueDataList({
          callId: callId,
          recordId: rowNormalData.value?.recordId || '',
        }))
        rowDialogData.value = data || []
        break;
      }
    }
    if (props?.recordType && [RecordTypeEnum['AI外呼'], RecordTypeEnum['人机协同']].includes(props?.recordType)) {
      const [err, res] = await to(scriptInfoModel.findInfoQueryList({id: rowNormalData.value?.speechCraftId!})) as [any, InfoQueryItem[]]
      infoQueryMap.value = new Map([])
      res?.map(item => {
        infoQueryMap.value.set(item.infoFieldName, item)
      })
    }
    needUpdate.value = true
    dialogLoading.value = false
  }
}

const taskInfo = useTaskStore()
/** 巡检通话记录 */
const inspecteRecordDetail = async () => {
  taskInfo.inspecteRecordDetail(rowNormalData.value?.recordId || '', props.recordType)
}
/** 巡检通话音频 */
const inspecteRecordAudio = async (status: string) => {
  taskInfo.inspecteRecordAudio(status, rowNormalData.value?.recordId || '', props.recordType)
}

/** 查看巡检工单 */
const inspectionWorkOrderVisible = ref(false)
const showInspectionWorkOrder = () => {
  inspectionWorkOrderVisible.value = !inspectionWorkOrderVisible.value
}

// 关闭抽屉
const closeDetails = () => {
  document.removeEventListener('keyup', handleKeyup);
  emits('update:visible', false)
}

// watch
watch(props, async () => {
  visible.value = props.visible
  if (props.visible) {
    allRecordData.value = props.tableData || []
    currentIndex.value = props.currentIndex
    total.value = props.total
    inspectionWorkOrderVisible.value = false
    Object.assign(searchForm, props.searchForm)
    rowNormalData.value = allRecordData.value[currentIndex.value]
    updateRecordDialogData()
    audioStatus.value !== 'play' && inspecteRecordDetail()
  } else {
    clearAudio.value = true
    audioStatus.value = 'pause'
  }
}, {deep: true})
watch(visible, n => {
  if (n) {
    document.addEventListener('keyup', handleKeyup)
  }
}, {deep: true})

const clearAllData = () => {
  document.removeEventListener('keyup', handleKeyup);
  allRecordData.value = null
  rowNormalData.value = null
  rowDialogData.value = null
}

onUnmounted(() => {
  clearAllData()
})
</script>

<style lang="postcss" scoped type="text/postcss">
.record-detail-container {
  height: calc(100% - 50px);
  background-color: #fff;
  box-sizing: border-box;
  font-size: 13px;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: row;
  .order-btn {
    position: absolute;
    left: 8px;
    top: 2px;
    width: 60px;
    height: 20px;
    font-size: 12px;
    margin-right: 12px;
  }
  .label {
    color: #646566;
    text-align: right;
    min-width: 75px;
    display: inline-block;
    margin-bottom: 2px;
    flex-grow: 0;
    flex-shrink: 0;
  }
  .left {
    padding: 0 0 12px 12px;
    width: 284px;
    overflow: hidden;
    height: 100%;
    flex-shrink: 0;
    flex-grow: 1;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }
  .right {
    flex-grow: 4;
    flex-shrink: 1;
    width: 900px;
    text-align: center;
    display: flex;
    flex-direction: column;

  }
}

</style>
