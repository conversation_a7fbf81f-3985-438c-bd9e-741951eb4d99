<template>
  <div class="tw-w-[56px] tw-h-[28px] tw-rounded-[2px] tw-bg-[#E1E3E6] tw-inline-flex tw-items-center tw-justify-between tw-p-[2px]">
    <span class="span-btn" :class="{'active-btn': activeType === 'card'}" @click="handleClick('card')">
      <span class="inner-span-btn"><el-icon size="15" color="#fff"><SvgIcon name="card"/></el-icon></span>
    </span>
    <span class="span-btn" :class="{'active-btn': activeType === 'list'}" @click="handleClick('list')">
      <span class="inner-span-btn"><el-icon size="15" color="#fff"><SvgIcon name="list" /></el-icon></span>
    </span>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, watch } from 'vue'
const props = defineProps<{
  active: string,
}>()
const activeType = ref(props.active)
watch(() => props.active, () => {
  activeType.value = props.active
}, { deep: true })
const emits = defineEmits([
  'update:active',
])
const handleClick = (val: string) => {
  emits('update:active', val)
}
</script>

<style lang="postcss" type="text/postcss" scoped>
.span-btn {
  width: 50%;
  height: 22px;
  background-color: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 2px;
  .inner-span-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
  }
}
.active-btn {
  background-color: #fff;
  .inner-span-btn {
    background-color: var(--el-color-primary);
    width: 18px;
    height: 18px;
    
  }
}
</style>
