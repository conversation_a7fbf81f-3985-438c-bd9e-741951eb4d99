<template>
  <div class="tw-flex tw-items-center w-w-full tw-min-w-[1080px] sm:tw-min-w-full tw-h-[56px] tw-px-[16px] tw-border-b-[1px] tw-bg-white tw-shrink-0">
    <div
      v-if="typeof props.title === 'string'"
      class="tw-text-[16px] tw-font-[600] tw-text-[var(--primary-black-color-600)]"
    >
      <span v-if="props.canGoBack"><el-icon class="tw-mr-1 tw-cursor-pointer" @click="goBack"><ArrowLeftBold /></el-icon></span>
      <span
        :class="props.canRefresh ? 'tw-cursor-pointer' : ''"
        @click="handleRefresh"
      >{{ props.title }}</span>
    </div>
    <div v-else>
      <span class="tw-text-[16px] tw-font-[600] tw-text-[var(--primary-black-color-600)] tw-mr-[16px]">{{ props.title.at(-1)?.title || '' }}</span>
      <template v-for="(item, index) in props.title" :key="item.title">
        <span
          v-if="index >= props.title.length - 1"
          class="tw-font-[400] tw-text-[12px] tw-text-[var(--primary-black-color-600)]"
          :class="props.canRefresh ? 'tw-cursor-pointer' : ''"
          @click="handleRefresh"
        >{{ item.title }}</span>
        <span v-else class="tw-font-[400] tw-text-[12px] tw-text-[var(--primary-black-color-500)]">
          <span
            @click="handleGoRouter(item)"
            class="pre-route"
          >{{ item.title }} </span>
          <span class="tw-mx-[4px]">{{ props.separator }}</span>
        </span>
      </template>
    </div>
    <el-tooltip v-if="props.canRefresh" content="刷新当前页面数据" placement="right" :show-after="500">
      <el-icon size="12" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="handleRefresh"><SvgIcon name="reset" color="inherit"/></el-icon>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, onActivated, onDeactivated, onBeforeUnmount } from 'vue'
import { ArrowLeftBold, } from '@element-plus/icons-vue'
import { onBeforeRouteLeave, useRouter  } from 'vue-router'
const props = withDefaults(defineProps<{
  title: string | {
    name?: string, title: string, query?: any, fn?: Function
  }[],
  separator?: string,
  canGoBack?: boolean,
  canRefresh?: boolean, // 是否显示刷新
  canLoopRefresh?: boolean, // 是否开启定时轮询
  loopInterval?: number, // 定时轮询时间间隔
}>(), {
  separator: '/',
  canGoBack: false,
  canRefresh: false,
  canLoopRefresh: false,
  loopInterval: 5 * 60 * 1000,
})
const emits = defineEmits(['refresh',])
const router = useRouter()
const goBack = () => {
  router.back()
}
const handleGoRouter = (item: {
  name?: string, title: string, query?: any,  fn?: Function
}) => {
  if (item.name) {
    router.push({ name: item.name, query: item.query })
  } else {
    router.back()
  }
  if (item.fn) item.fn()
}
const handleRefresh = () => {
  props.canRefresh && emits('refresh')
}
/** 定时轮询 开始 */
// 【定时更新】每隔默认5分钟触发刷新，【进入刷新】包括tab切换和浏览器标签切换，触发刷新
const timer = ref()
// 浏览器标签切换，触发刷新
const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible') {
    emits('refresh')
  }
}
// 添加定时器和监听器
const addLoop = () => {
  if (props.canLoopRefresh) {
    timer.value && clearLoop()
    document.addEventListener('visibilitychange', handleVisibilityChange)
    timer.value = setInterval(() => {
      emits('refresh')
    }, props.loopInterval || 5 * 60 * 1000)
  }
}
// 移除定时器和监听器
const clearLoop = () => {
  if (timer.value) {
    clearInterval(timer.value)
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    timer.value = null
  }
}
watch(() => props.canLoopRefresh, n => {
  n ? addLoop() : clearLoop()
})
onMounted(() => {
  addLoop()
})
onBeforeUnmount(() => {
  clearLoop()
})
onActivated(() => {
  addLoop()
})
onDeactivated(() => {
  clearLoop()
})
onBeforeRouteLeave(() => {
  clearLoop()
})
/** 定时轮询 结束 */
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-breadcrumb__item {
  font-size: 16px;
  font-weight: 600;
}
:deep(.el-breadcrumb__separator) {
  margin: 0 3px;
  font-size: 16px;
  font-weight: 500;
}
.pre-route {
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 2px;
  &:hover {
    color: var(--primary-black-color-600);
    background-color: var(--primary-black-color-100);
  }
}
</style>