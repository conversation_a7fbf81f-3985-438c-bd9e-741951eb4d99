<template>
   <div v-infinite-scroll="() => count=count+2" :infinite-scroll-immediate="false" :infinite-scroll-distance="1" class="timeline-item">
    <template v-for="dateItem in (timelineList||[]).slice(0, count)" :key="dateItem">
      <div class="tw-mt-[16px] tw-mb-[8px]  tw-h-[24px] tw-w-[60px]
        tw-flex tw-items-center tw-justify-center tw-bg-[#e8e7f4] tw-text-[#8D89C7]
        tw-rounded-[2px] tw-text-[12px]">{{ dayjs(dateItem).format('MM-DD') === dayjs().format('MM-DD') ? '今日' : dayjs(dateItem).format('MM-DD') }}
      </div>
      <div v-for="(item, index) in (dateMap?.get(dateItem)||[])" class="tw-flex" :class="`tw-mb-[${props.gap}px]`" :key="item[activeProps] || (dateItem + index)">
        <div class="tw-flex tw-flex-col tw-items-center tw-mr-[12px]">
          <div
            class="tw-rounded tw-border-[var(--primary-blue-color)] tw-w-[8px] tw-h-[8px] tw-border-[2px] tw-mt-[6px]"
            :class="active===item[activeProps] ? 'tw-bg-[var(--primary-blue-color)]' : ''"
          ></div>
          <div v-if="!(index===(dateMap?.get(dateItem)||[])?.length -1 && !props.needLastTimeLine)" class="tw-w-[1px] tw-flex-grow tw-bg-[var(--primary-black-color-300)]" :class="`tw-mt-[${props.gap}px]`"></div>
        </div>
        <!--整个跟进信息板块监听点击事件-->
        <template v-if="enableClick">
          <div
            @click="handleActive(item)"
            :class="{
              'tw-cursor-pointer': !!activeProps,
            }"
          >
            <slot name="content" :row="item"></slot>
        </div>
        </template>
        <!--仅展示内容，不响应点击事件-->
        <template v-else>
          <slot name="content" :row="item"></slot>
        </template>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { onUnmounted, ref, computed, watch } from 'vue'
import dayjs from 'dayjs'
import { onBeforeRouteLeave } from 'vue-router';
const props = withDefaults(
  defineProps<{
    list: any[], // 全部数据列表
    active?: number | string, // 当前激活数据的标识，可以使用id， 但无法使用index。
    activeProps: string, // 激活该数据的属性名， 激活该条数据，左侧圆点会变成实心，不传则不支持激活。
    sortProps: string, // 根据list中该时间属性排序
    gap?: number, // 间距
    needLastTimeLine?: boolean, // 最后一个时间节点是否需要连接线
    enableClick?: boolean, // 是否触发点击事件
  }>(), {
    gap: 4,
    needLastTimeLine: true,
    enableClick: true,
  }
)
// emit
const emits = defineEmits([
  'update:active',
])
const count = ref(6)
const active = computed(() => props.active??undefined)
const activeProps = computed(() => props.activeProps || false)
const handleActive = (item: any) => {
  if (activeProps.value && item[activeProps.value]) {
    emits('update:active', item)
  }
}
const timelineList = ref<string[] | null>([])
const dateMap = ref<Map<string, any[]> | null>(new Map([]))
watch(() => props.list, () => {
  timelineList.value = []
  dateMap.value = new Map([])
  props.list?.sort((a,b) => dayjs(a[props.sortProps]).isAfter(dayjs(b[props.sortProps])) ? -1 : 1)?.map(item => {
    if (item[props.sortProps]) {
      const dateStr = dayjs(item[props.sortProps])?.format('YYYY-MM-DD')
      if (timelineList.value?.includes(dateStr)) {
        if (!dateMap.value?.has(dateStr)) {
          dateMap.value?.set(dateStr, [])
        }
        const arr =  dateMap.value?.get(dateStr) || []
        arr.push(item)
        dateMap.value?.set(dateStr, arr)
      } else {
        timelineList.value?.push(dateStr)
        dateMap.value?.set(dateStr, [item])
      }
    }
  })
  timelineList.value?.sort((a,b) => dayjs(a).isAfter(dayjs(b)) ? -1 : 1)
  count.value = window.innerWidth > 1400 ? 6 : 4
}, {deep: true, immediate: true})
onUnmounted(() => {
  timelineList.value = null
  dateMap.value = null
})
onBeforeRouteLeave(() => {
  timelineList.value = null
  dateMap.value = null
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.tab-box {
  height: 41px;
  padding: 5px 0 0 8px;
  box-sizing: border-box;
  background: #f7f8fa;
  border-bottom: 1px solid var(--primary-black-color-200);
  display: flex;
  .normal-tab {
    width: 88px;
    height: 36px;
    box-sizing: border-box;
    text-align: center;
    line-height: 36px;
    margin-right: 4px;
    border-radius: 4px 4px 0px 0px;
    border: 1px solid var(--primary-black-color-200);
    background: #F0F2F5;
    cursor: pointer;
    font-size: 16px;
    color: var(--primary-black-color-400);
  }
  .active-tab {
    border-bottom-color: #fff;
    color: var(--el-color-primary);
    background: #fff;
  }
}
</style>
