<template>
  <el-time-picker
    v-if="type === 'timerange'"
    v-model="valTime"
    :disabled="props.disabled"
    class="date-box"
    :prefix-icon="prefix"
    is-range
    :range-separator="props.separator"
    :clearable="props.clearable"
    :start-placeholder="props.placeholder ? props.placeholder + '（开始）' : '开始时间'"
    :end-placeholder="props.placeholder ? props.placeholder + '（结束）': '结束时间'"
    @change="handleTimeChange()"
  />
  <el-date-picker
    v-else
    v-model="val"
    class="date-box"
    popper-class="popper-dom"
    :type="props.type"
    :placeholder="props.placeholder"
    :start-placeholder="props.placeholder ? props.placeholder + '（开始）' : `开始${props.type.includes('time') ? '时间' : '日期'}`"
    :end-placeholder="props.placeholder ? props.placeholder + '（结束）': `结束${props.type.includes('time') ? '时间' : '日期'}`"
    :shortcuts="props.showShortcuts ? shortcuts : []"
    :range-separator="props.separator"
    :format="props.format"
    :value-format="props.format"
    :prefix-icon="prefix"
    :disabled="props.disabled"
    :clearable="props.clearable"
    :disabled-date="props.disabledDate"
    @calendar-change="handleCalendarChange"
    @change="handleTimeChange()"
  />
  
</template>

<script lang="tsx" setup>
import { shallowRef, ref, computed, watch, onMounted, onUnmounted, onActivated } from 'vue'
import { Search } from '@element-plus/icons-vue'
import SvgIcon from '@/components/SvgIcon.vue'
import dayjs from 'dayjs'
import { ElMessage, } from 'element-plus'
// 组件入参props
const props = withDefaults(defineProps<{
  start?: string,
  end?: string,
  placeholder?: string, // 选择框占位符
  disabled?: boolean,
  format?: string,
  type?: string,
  showShortcuts?: boolean,
  separator?: string,
  clearable?: boolean,
  disabledDate?: Function,
  splitToday?: boolean, // 是否允许跨今日查询(当前仅针对type === 'datetimerange'生效), 主要用于通话记录分表
  maxRange?: number, // 允许最长可以选择的时间范围，单位ms，暂用于任务列表查询
}>(), {
  format: 'YYYY-MM-DD HH:mm:ss',
  showShortcuts: true,
  type: 'datetimerange',
  separator: '至',
  disabled: false,
  splitToday: false,
  clearable: true
})
// emit
const emits = defineEmits([
  'update:start', 'update:end', 'change'
])
const prefix = shallowRef({render() {
    return (props.type === 'timerange' ? <SvgIcon name="time"/> : <SvgIcon name="date"/>)
  }
})
const shortcuts = computed(() => {
  const res = [
    { text: '今日', value: [dayjs().startOf('day').format(props.format), dayjs().endOf('day').format(props.format)] },
    { text: '昨日', value: [dayjs().subtract(1, 'day').startOf('day').format(props.format), dayjs().subtract(1, 'day').endOf('day').format(props.format)] },
    
  ]
  if (!props.maxRange || props.maxRange >= 1000 * 60 * 60 * 24 * 7) {
    res.push({
      text: props.type === 'datetimerange' && props.splitToday ? '近一周(除今日)' : '近一周', 
      value: [
      dayjs().subtract(7, 'day').startOf('day').format(props.format), 
      props.type === 'datetimerange' && props.splitToday ? dayjs().add(-1, 'd').endOf('day').format(props.format) : dayjs().endOf('day').format(props.format)
      ]
    })
  }
  if (!props.maxRange || props.maxRange >= 1000 * 60 * 60 * 24 * 31) {
    res.push({
      text: props.type === 'datetimerange' && props.splitToday ? '近一月(除今日)' : '近一月', 
      value: [
        dayjs().subtract(1, 'month').startOf('day').format(props.format),
        props.type === 'datetimerange' && props.splitToday ? dayjs().add(-1, 'd').endOf('day').format(props.format) : dayjs().endOf('day').format(props.format)
      ]
    })
  }
  if (!props.maxRange || props.maxRange > 1000 * 60 * 60 * 24 * 180) {
    res.push({
      text: props.type === 'datetimerange' && props.splitToday ? '近半年(除今日)' : '近半年', 
      value: [
        dayjs().subtract(6, 'month').startOf('day').format(props.format), 
        props.type === 'datetimerange' && props.splitToday ? dayjs().add(-1, 'd').endOf('day').format(props.format) : dayjs().endOf('day').format(props.format)
      ]
    })
  }
  return res
}) 

const val = ref<undefined | string[]>([props.start || dayjs().startOf('day').format(props.format), props.end || dayjs().endOf('day').format(props.format)]) // 选中的数据
const valTime = ref([dayjs(), dayjs()])
// 时间变化触发emit更新
const handleTimeChange = () => {
  if (props.type === 'timerange') {
    if (valTime.value && valTime.value[1]) {
      emits('update:start', dayjs(valTime.value[0]).format(props.format) || undefined)
      emits('update:end', dayjs(valTime.value[1]).format(props.format) || undefined)
    } else {
      emits('update:start', undefined)
      emits('update:end', undefined)
    }
  } else {
    if (val.value && val.value[1]) {
      if (props.type.includes('range') && val.value && val.value[1] && props.maxRange &&  dayjs(val.value[1]).diff(dayjs(val.value[0])) > props.maxRange) {
        ElMessage({
          message: '选取时间范围过长，已为您自动调整',
          type: 'warning',
        })
        val.value[1] = dayjs(val.value[0]).add(props.maxRange, 'ms').endOf('day').format(props.format)
      }
      if (props.type === 'datetimerange' && props.splitToday) {
        const b1 = dayjs(val.value[0]).isBefore(dayjs().startOf('d'))
        const b2 = dayjs(val.value[1]).isBefore(dayjs().startOf('d'))
        if(!(b1 && b2 || (!b1 && !b2))) {
          ElMessage({
            message: '今日数据和历史数据需分开查询，已自动为您去除今日',
            type: 'warning',
          })
          val.value[1] = dayjs().add(-1, 'd').endOf('day').format(props.format)
        }
      }
      emits('update:start', val.value[0] || undefined)
      emits('update:end', val.value[1] || undefined)
    } else {
      emits('update:start', undefined)
      emits('update:end', undefined)
    }
  }
  emits('change')
}
// 针对YYYY-MM-DD HH:mm:ss，日期-时间选择器，修改时间后立即触发数据更新而不需要点确认
const handleCalendarChange = (params: [Date, Date]) => {
  if (params[0] && params[1] && props.format === 'YYYY-MM-DD HH:mm:ss' && props.type === 'datetimerange') {
    val.value = [dayjs(params[0]).startOf('day').format(props.format), dayjs(params[1]).endOf('day').format(props.format)]
  }
}
/** watch开始 */
// 监听入参，更新选中数据和选项变化
watch(props, () => {
  if (!props.start && !props.end) {
    val.value = undefined
    // @ts-ignore
    valTime.value = undefined
  } else {
    if (props.type === 'timerange') {
      if (typeof props.start === 'string' && props.start.length === 8) {
        const today = dayjs().format('YYYY-MM-DD');
        valTime.value = [dayjs(`${today} ${props.start}`), dayjs(`${today} ${props.end}`)]
        
      } else {
        valTime.value = [dayjs(props.start), dayjs(props.end)]
      }
    } else {
      val.value = [props.start || dayjs().startOf('day').format(props.format), props.end || dayjs().endOf('day').format(props.format)]
    }
  }
}, {deep: true, immediate: true})
</script>

<style lang="postcss" type="text/postcss">
.date-box {
  flex-grow: 1;
  flex-shrink: 1;
  
}
.popper-dom {
  .el-picker-panel {
    line-height: 22px;
    .el-picker-panel__sidebar {
      width: 100px;
      
    }
    .el-picker-panel__shortcut {
      font-size: 13px;
    }
    .el-picker-panel__body {
      margin-left: 100px;
      
    }
  }
  .el-date-table th {
    padding: 3px;
  }
  .el-date-table td {
    padding: 0;
  }
  .el-date-range-picker__content {
    padding: 8px;
    .el-picker-panel__icon-btn {
      margin-top: 4px;
    }
    .el-date-range-picker__header div {
      font-size: 14px;
    }
  }
}
.date-box.el-input__wrapper {
  justify-content: space-between;
  overflow: hidden;
  .el-range-input {
    padding-left: 2%;
    text-align: left;
  }
  .el-range__icon{
    margin: 1px -10px 1px 0;
    box-shadow: 0 0 0 1px var(--el-input-border-color,var(--el-border-color)) inset;
    border-radius: 0 4px 4px 0;
    background-color: #f5f7fa;
    padding: 1px 4px;
    order: 8;
    width: 30px;
  }
  .el-range-separator {
    flex-grow: 0;
    flex-shrink: 0;
    width: 24px;
    padding-right: 8px;
  }
}
.date-box.is-active {
  .el-range__icon {
    color: #fff;
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    background-color: var(--el-color-primary);
  }
}
</style>
