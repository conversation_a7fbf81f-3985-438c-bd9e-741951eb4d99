<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    align-center
    class="dialog-form"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ props.title }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-py-[12px] tw-px-[2px]"
    >
      <div class="tw-grid tw-gap-x-[8px] tw-gap-y-[12px]" :style="gridStyle">
        <div v-for="(item, index) in props.list" :key="index" class="name-box">
          <span class="rectangle-left"></span>
          <span class="tw-truncate">{{ item||'' }}</span>
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed,} from 'vue'
const emits = defineEmits(['update:visible', 'confirm'])
const props = withDefaults(defineProps<{
  title: string
  list: string[],
  visible: boolean,
  gridNum?: number
}>(), {
  gridNum: 1
})

const dialogVisible = ref(props.visible)
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

const gridStyle = computed(() => {
  return {
    'grid-template-columns': `repeat(${props.gridNum||1}, minmax(0, 1fr))`
  }
})
watch(() => props.visible, n => {
  dialogVisible.value = props.visible
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.info-data-box-inner {
  padding: 4px 8px;
  gap: 0;
}
.name-box {
  display: flex;
  height: 36px;
  padding: 8px 12px;
  align-items: center;
  gap: 10px;
  font-size: 13px;
  border-radius: 4px;
  background: #F0F2F5;
  position: relative;
  line-height: 20px;
  color: var(--primary-black-color-600);
}
.rectangle-left {
  border-radius: 0px 2px 2px 0px;
  width: 2px;
  height: 24px;
  position: absolute;
  left: 0;
  top: 6px;
  background-color: var(--primary-orange-color)
}
</style>
