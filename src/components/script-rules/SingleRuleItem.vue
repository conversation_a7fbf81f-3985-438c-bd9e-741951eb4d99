<template>
  <div class="tw-w-full tw-text-[13px] tw-flex tw-gap-x-[6px] tw-items-center">
    <!-- 选项类型 -->
    <el-select
      class="tw-mr-[4px] tw-shrink-0 tw-grow-0 tw-w-[180px] tw-self-start"
      :disabled="!!props.data?.id"
      v-model="editData[props.typeName]"
      filterable
      @change="handleTypeChange"
    >
      <el-option
        v-for="item in props.options"
        :key="item.value"
        :label="item.name"
        :value="item.value"
      ></el-option>
    </el-select>
    <!-- 多选,字符串 -->
    <SelectBox 
      v-if="propertyInfo && propertyInfo.options?.type && [ITypeEnum['mult-select-string'], ITypeEnum['mult-select-list']].includes(propertyInfo.options?.type)"
      v-model:selectVal="multSelectValue"
      :options="options"
      :canCopy="!props.readonly && !!propertyInfo?.options?.copy"
      :canSelectAll="!props.readonly"
      :copyName="propertyInfo?.options?.copy"
      :name="propertyInfo?.options?.name || 'name'"
      :val="propertyInfo?.options?.id || 'id'"
      :suffix="propertyInfo?.options?.suffix || undefined"
      :placeholder="`请选择${propertyInfo?.name}`"
      class="tw-grow"
      @update:select-val="handleMultValueChange"
      multiple filterable
    >
    </SelectBox>
    <!-- 单选，使用enum或者接口单选 -->
    <el-select
      v-if="propertyInfo && propertyInfo.options?.type===ITypeEnum['single-select']"
      v-model="editData[propertyInfo.property[0]!]"
      filterable
      class="tw-grow"
      :placeholder="`请选择${propertyInfo?.name}`"
      @change="handleValueChange"
    >
      <el-option
        v-for="item in options"
        :label="item[propertyInfo.options?.name || 'name']"
        :key="item[propertyInfo.options?.id || 'id']"
        :value="item[propertyInfo.options?.id || 'id']+''"
      />
    </el-select>

    <!-- 级联，存在路径，并且仅支持单选 -->
    <el-cascader
      v-if="propertyInfo
        && propertyInfo.options?.type===ITypeEnum['cascader']
        && propertyInfo.options?.emitPath"
      v-model="cascaderValue"
      filterable
      :options="options"
      :props="cascaderProps"
      clearable
      class="tw-grow"
      :placeholder="`请选择${propertyInfo?.name}`"
      @change="handleCascaderValueChange"
    />
    <!-- 级联，不包括路径，仅支持单选 -->
    <el-cascader
      v-if="propertyInfo
        && propertyInfo.options?.type===ITypeEnum['cascader']
        && !propertyInfo.options?.emitPath"
      v-model="editData[propertyInfo.property[0]!]"
      filterable
      :options="options"
      :props="cascaderProps"
      clearable
      class="tw-grow"
      :placeholder="`请选择${propertyInfo?.name}`"
      @change="handleValueChange"
    />
    <!-- 命中客户回复 -->
    <!-- 文本输入，通过inputLimit限制文本长度 -->
    <el-input
      v-if="propertyInfo && propertyInfo.options?.type===ITypeEnum['input']"
      v-model.trim="editData[propertyInfo.property[0]!]"
      class="tw-grow"
      type="textarea"
      show-word-limit
      :autosize="{minRows: 3, maxRows: 6}"
      :maxlength="propertyInfo.options?.limit || 1000"
      clearable
      :placeholder="`请输入${propertyInfo?.name ?? ''}`"
      @blur="handleValueChange"
    />
    <!--文本输入，输入将转换为string的list -->
    <InputListBox
      v-if="propertyInfo && propertyInfo.options?.type===ITypeEnum['mult-input']"
      v-model:value="(multSelectValue as string[])"
      :placeholder="`请输入${propertyInfo?.name ?? ''}`"
      :disabled="props.readonly"
      clearable
      canCopy
      :copyName="propertyInfo?.options?.copy"
      @update:value="handleMultValueChange"
    />

    <!-- 命中是/否 -->
    <template v-if="propertyInfo?.property.includes('yn')">
      <el-select
        class="tw-grow"
        :placeholder="`请选择是否${propertyInfo?.name ?? ''}`"
        v-model="editData.yn"
        @change="handleValueChange"
      >
        <el-option label="是" :value="true" />
        <el-option label="否" :value="false" />
      </el-select>
    </template>
    <!-- 命中全部/部分，个数， 0代表不命中 -->
    <template v-if="propertyInfo?.property.includes('num')">
      <span class="tw-shrink-0">的</span>
      <el-select v-if="propertyInfo?.numText!=='倍'" v-model="selectAll" @change="handleValueChange">
        <el-option label="全部" :value="1" />
        <el-option label="部分" :value="2" />
      </el-select>
      <InputNumberBox
        v-model:value="editData.num"
        :clearable="false"
        placeholder=""
        :disabled="propertyInfo?.numText!=='倍' && selectAll === 1"
        style="width:90px"
        :min="0"
        :max="propertyInfo?.numText === '次' ? 10 : (multSelectValue?.length || Number.MAX_VALUE)"
        :append="propertyInfo?.numText || '个'"
        @update:value="handleValueChange"
      />
    </template>
    <!-- 命中最小、最大数量 -->
    <template v-if="propertyInfo?.property.includes('minNum')">
      <InputNumberBox
        v-model:value="editData.minNum"
        placeholder="最低"
        :min="0"
        :max="propertyInfo?.numText === '次' ? editData.maxNum || 10 : editData.maxNum || multSelectValue?.length || Number.MAX_VALUE"
        style="width: 90px"
        :append="propertyInfo?.numText || '个'"
        @update:value="handleValueChange"
      />
      <span>&nbsp;至&nbsp;</span>
      <InputNumberBox
        v-model:value="editData.maxNum"
        placeholder="最高"
        :min="editData.minNum"
        :max="propertyInfo?.numText === '次' ? 10 : (multSelectValue?.length || Number.MAX_VALUE)"
        style="width: 90px"
        :append="propertyInfo?.numText || '个'"
        @update:value="handleValueChange"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, nextTick, onActivated, onMounted, onUpdated } from 'vue';
import { RuleItem, RuleTypeEnum, IRuleSetting, ITypeEnum, RuleItemOrigin, ruleTypePropteryMap, } from '@/type/IntentionType'
import { enum2Options, findValueInEnum, pickAttrFromObj } from '@/utils/utils'
import { useScriptStore } from '@/store/script'
import InputNumberBox from '@/components/InputNumberBox.vue'
import SelectBox from '@/components/SelectBox.vue'
import InputListBox from '@/components/InputListBox.vue'

// props & emits
const emits = defineEmits(['update:data',])
const props = defineProps<{
  data: RuleItem; // 当前规则，无法使用v-model，请用update:data手动更新
  options: {name: string, value: string}[]; // 可以下来选择的规则
  readonly?: boolean; // 是否只读
  typeName: 'conditionType';
  idName: 'conditionUniqueId';
}>();

const editData = reactive<RuleItem>({})
const multSelectValue = ref<(string | number)[]>([]) // 多选的数值
const cascaderValue = ref<(string| number)[]>([]) // 级联的数值，包含路径，需要手动赋值到editData的多个参数
const selectAll = ref(1) // 1: 全部 2: 部分 // 用于命中xx的全部/部分
const options = ref<null | any[]>([]) // 选项
const cascaderProps = computed(() => ({
  emitPath: propertyInfo.value?.options?.emitPath ?? true,
  multiple: false
}))

const propertyInfo = ref<null | IRuleSetting>(null)

/** 常量 */
const scriptStore = useScriptStore() // 话术信息
const handleMultValueChange = () => {
  if (propertyInfo.value && propertyInfo.value?.options?.type === ITypeEnum['mult-select-string']) {
    // @ts-ignore
    editData[propertyInfo.value.property[0]] = multSelectValue.value?.join(',') || undefined
  }
  if (propertyInfo.value && propertyInfo.value?.options?.type && [ITypeEnum['mult-select-list'], ITypeEnum['mult-input']].includes(propertyInfo.value?.options?.type)) {
    // @ts-ignore
    editData[propertyInfo.value.property[0]] = multSelectValue.value || undefined
  }
  handleValueChange()
}

const handleCascaderValueChange = () => {
  if (propertyInfo.value && propertyInfo.value?.options?.type === ITypeEnum['cascader'] ) {
    cascaderValue.value.forEach((item, index) => {
      // @ts-ignore
      editData[propertyInfo.value.property[index]] = item
    })
  }
  handleValueChange()
}

const handleSelectAll = () => {
  if (selectAll.value === 1) {
    editData.num = multSelectValue.value?.length || 1
  }
}

// 数据变化，更新父组件数据
const handleValueChange = () => {
  if (selectAll.value === 1) {
    editData.num = multSelectValue.value?.length || 1
  }
  emits('update:data', pickAttrFromObj(editData, 
    [
      'id', 'ifDelete',
      props.typeName, props.idName,
      ...(propertyInfo.value?.property || [])
    ]
  ))
}

// 切换规则类型，需要更新选项信息、数据信息、外部数据等
const handleTypeChange = () => {
  options.value = []
  multSelectValue.value = []
  cascaderValue.value = []
  // propertyInfo.value?.property.forEach(item => {
  //   editData[item] = undefined
  // })
  editData.ifDelete = editData.ifDelete ?? false
  propertyInfo.value = ruleTypePropteryMap.get(editData[props.typeName] || RuleTypeEnum['命中问答个数']) || null
  emits('update:data', pickAttrFromObj(editData, 
    [
      'id', 'ifDelete',
      props.typeName, props.idName,
      ...(propertyInfo.value?.property || [])
    ]
  ))

  if (!propertyInfo.value || !propertyInfo.value.options) return
  if (propertyInfo.value.options.optionName) {
    options.value = scriptStore[propertyInfo.value.options.optionName as keyof typeof scriptStore] as any[] || []
  } else if (propertyInfo.value.options?.type === ITypeEnum['single-select'] && propertyInfo.value.options.enum) {
    options.value =  enum2Options(propertyInfo.value.options.enum)
  }
}

/**
 * 初始化
 * 从ruleTypePropteryMap中获取该规则的配置，新增规则理论上只需要增加map对应选项，并更新组件入参的option即可
 * 详见ruleTypePropteryMap
 */
const init = () => {
  // 步骤1：从ruleTypePropteryMap中获取该规则有哪些字段
  propertyInfo.value = ruleTypePropteryMap.get(props.data[props.typeName] || RuleTypeEnum['命中问答个数']) || null
  // 步骤2：更新editData，去除多余的不需要的字段，保持一致性
  Object.assign(editData, new RuleItemOrigin(), {
    ...pickAttrFromObj(props.data, [
      'id', 'ifDelete',
      props.typeName, props.idName,
      ...(propertyInfo.value?.property || []),
    ]),
    ifDelete: props.data.ifDelete ?? false
  })
  // 步骤3：更新外部的editData
  emits('update:data', editData)

  // 步骤4：获取规则的下拉选项（scriptStore缓存中取、固定枚举）
  if (!propertyInfo.value || !propertyInfo.value.options) return
  if (propertyInfo.value.options.optionName) {
    options.value = scriptStore[propertyInfo.value.options.optionName as keyof typeof scriptStore] as any[] || []
  } else if (propertyInfo.value.options?.type === ITypeEnum['single-select'] && propertyInfo.value.options.enum) {
    options.value =  enum2Options(propertyInfo.value.options.enum)
  }
  
  // 步骤4：获取规则的数据，转换为当前页面使用的ref字段（cascaderValue、multSelectValue、selectAll等）
  if (propertyInfo.value?.options?.type === ITypeEnum['cascader'] && !!propertyInfo.value.options.emitPath) {
     // 级联（单选，包含路径）数据读取，由于后端数据存储为多个字段，需要手动赋值，拼接成一个list组成完整的级联单选数据
    propertyInfo.value.property.forEach((item, index) => {
      if (['num', 'yn', 'minNum', 'maxNum'].includes(item)) return
      cascaderValue.value[index] = +(editData[item] as string)
    })
  } else if (propertyInfo.value?.options?.type === ITypeEnum['mult-select-string'] ) {
    // 多选数据读取，由于后端数据存储的为字符串逗号分隔，需要转换为数组
    multSelectValue.value = (editData[propertyInfo.value.property[0]] as string)?.split(',')?.map(Number) || []
  } else if ([ITypeEnum['mult-select-list'], ITypeEnum['mult-input']].includes(propertyInfo.value?.options?.type)) {
    // 多选数据读取，由于后端数据转换为数组
    // @ts-ignore
    multSelectValue.value = editData[propertyInfo.value.property[0]] as any[] || []
  }
  // 根据num判断是否全选
  if (propertyInfo.value.property.includes('num') && multSelectValue.value) {
    selectAll.value = (editData.num && editData.num >= multSelectValue.value?.length) ? 1 : 2
  }
}

init()

/** watch结束 */ 
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-form .el-form-item:first-child {
  margin-top: 0;
}
.el-input-number .el-input__inner {
  text-align: left;
}
:deep(.el-form-item__label) {
  padding-right: 0;
}
:deep(.el-form-item__content) {
  font-size: var(--el-font-size-base);
}
</style>