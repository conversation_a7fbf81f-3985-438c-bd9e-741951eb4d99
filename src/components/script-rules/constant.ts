import { MutiRuleItem, IRuleSetting, RuleItem, ruleTypePropteryMap, ITypeEnum, RuleTypeEnum } from '@/type/IntentionType'
import { useScriptStore } from '@/store/script'
import { enum2Options, findValueInEnum, pickAttrFromObj, isEmpty } from '@/utils/utils'

/**
 * Translate the value of a cascader into its corresponding label path.
 * @param {object[]} cascaderData - The cascader data source.
 * @param {number|string[]} values - The value(s) to translate.
 * @returns {string} The translated label path.
 */
const translateCascade = (cascaderData: { label: string, value: number, children?: { label: string, value: number }[] }[], values: (number | string) []) => {
  let result: (number | string) [] = [];

  function findLabel(data: { label: string, value: number, children?: { label: string, value: number }[] }[], value: number | string) {
    for (let item of data) {
      if (item.value == value) {
        result.push(item.label);
        if (item.children && values.length > result.length) {
          findLabel(item.children, values[result.length]);
        }
        break;
      }
    }
  }

  findLabel(cascaderData, values[0]);
  return result.join('/');
}


/**
 * Translate rule items into readable strings.
 * @param {RuleItem[]} data - Rule items to be translated.
 * @returns {string[]} Translated strings.
 */
export const translateRules = (data: RuleItem[]) => {
  if (!data || data.length < 1) return []
  const res: string[] = []
  data?.map(item => {
    if (item.ifDelete || !item.conditionType) return
    const propertyInfo: IRuleSetting | null = ruleTypePropteryMap.get(item.conditionType) || null
    if (!propertyInfo) return
    let selectInfoStr = ''
    
    const scriptStore = useScriptStore() // 话术信息
    if (!propertyInfo.options) {

    } else if ([ITypeEnum['single-select'], ITypeEnum['mult-select-string'], ITypeEnum['mult-select-list']].includes(propertyInfo.options.type) && propertyInfo.options.optionName) {
      // 单选/多选，根据选项字段，从scriptStore缓存中获取选项
      // @ts-ignore
      const option = scriptStore[propertyInfo.options.optionName] || []
      // @ts-ignore
      const selectVals: any[] = [ITypeEnum['mult-select-list']].includes(propertyInfo.options.type) ? item[propertyInfo.property[0]] || [] : ((item[propertyInfo.property[0]] as string)?.split(',') || []) as any[]
      // @ts-ignore
      const selectInfo = option?.reduce((acc, cur) => {
        const idName = propertyInfo.options?.id || 'id'
        const labelName = propertyInfo.options?.name || 'name'
        if (cur[idName] && selectVals?.includes([ITypeEnum['mult-select-list']].includes(propertyInfo.options?.type!) ? cur[idName] : String(cur[idName]))) {
          return [...acc, `${cur[labelName]}`]
        }
        return acc 
      }, [])
      selectInfoStr = selectInfo?.length ? `【${selectInfo?.join('、')}】` : ''
    } else if(propertyInfo.options.type === ITypeEnum['single-select'] && propertyInfo.options.enum) {
      // 单选，根据选项信息翻译
      const option = enum2Options(propertyInfo.options.enum) || []
      const row = option.find(v => v.value == item[propertyInfo.property[0]])
      row && (selectInfoStr = row.name ? `【${row.name}】` : '')
    } else if (propertyInfo.options.type === ITypeEnum['cascader']
      && propertyInfo.options.emitPath
      && propertyInfo.options.optionName
      && propertyInfo.options.depth) {
      // 级联，根据选项字段，从scriptStore缓存中获取选项，有路径信息
      // @ts-ignore
      const option = scriptStore[propertyInfo.options.optionName] || []
      selectInfoStr = `【${translateCascade(option, propertyInfo.property.slice(0, propertyInfo.options.depth).map(v => item[v] as number | string))}】`
    } else if (propertyInfo.options.type === ITypeEnum['input']) {
      // @ts-ignore
      selectInfoStr = item[propertyInfo.property[0]]
    } else if(propertyInfo.options.type === ITypeEnum['mult-input']) {
      // @ts-ignore
      selectInfoStr = `【${item[propertyInfo.property[0]]?.join('、')}】`
    }
    //  else if (propertyInfo.options.type === ITypeEnum['cascader']
    //   && !propertyInfo.options.emitPath
    //   && propertyInfo.options.optionName
    //   && propertyInfo.options.depth) {
    //   // 级联，根据选项字段，从scriptStore缓存中获取选项，无路径信息
    //   // @ts-ignore
    //   const option = scriptStore[propertyInfo.options.optionName] || []
    //   selectInfoStr = 
    // }
    if (propertyInfo.property?.includes('num')) {
       res.push(`${propertyInfo.name || ''}：${selectInfoStr ? selectInfoStr + '中的' : selectInfoStr}${item.num || ''}${propertyInfo.numText || '个'}`)
    } else if (propertyInfo.property?.includes('minNum')) {
      const minMaxStr = (typeof item.minNum == 'number' && typeof item.maxNum == 'number')
        ? `${item.minNum}~${item.maxNum}`
        : typeof item.minNum == 'number' ? `≥${item.minNum}` : typeof item.maxNum == 'number' ? `≤${item.maxNum}` : ''
       res.push(`${propertyInfo.name || ''}：命中${selectInfoStr}${minMaxStr}${propertyInfo.numText || '个'}`)
    } else if (propertyInfo.property?.includes('yn')) {
       res.push(`${propertyInfo.name || ''}：${item.yn ? '是' : '否'}`)
    } else {
       res.push(`${propertyInfo.name || ''}：命中${selectInfoStr}`)
    }
    return
  })
  return res
}

/** 翻译话术高级规则、最终意向的条件至列表 */
export const translateMultRules = (data: MutiRuleItem[]) => {
  if (!data || data.length < 1) return [];
  const res: string[][] = []
  data.map(item => {
    if (item.ifDelete) return;
    const strArr: string[] = translateRules(item.advancedRuleConditionDTOList) || []
    res.push(strArr)
    return
  })
  return res
}

/**
 * 校验高级规则中的满足/排除条件
 * @param data 条件数组
 * @param required 是否必要
 * @returns string | null, string表示校验不通过的错误内容，返回null表示校验通过，
 */
export const checkMultRules = (data?: MutiRuleItem[], required: boolean = false) => {
  const exsitLen  = data?.filter(item => !item.ifDelete)?.length || 0
  if (!data || exsitLen < 1) {
    return required ? '请至少添加一个规则' : null;
  }
  let errMsg = ''
  data.some(item => {
    if (item.ifDelete) return false
    if (!item.advancedRuleConditionDTOList || item.advancedRuleConditionDTOList?.length < 1) {
      errMsg = '请至少添加一个规则';
      return true;
    }
    return item.advancedRuleConditionDTOList.some(subItem => {
      if (subItem.ifDelete) return false
      if (!subItem.conditionType) {
        errMsg = '请选择规则类型';
        return true
      }
      const propertyInfo: IRuleSetting | null = ruleTypePropteryMap.get(subItem.conditionType) || null
      const emptyItem = propertyInfo?.property.find(v => (subItem[v]??-1) === -1)
      // 当为命中次数可以仅填写最小或者最大值
      if (!!emptyItem && !(['minNum', 'maxNum'].includes(emptyItem) && (subItem.maxNum ?? subItem.minNum ?? undefined) !== undefined)) {
        errMsg = `请设置${findValueInEnum(subItem.conditionType, RuleTypeEnum) || ''}`;
        return true
      }
      return false
    })
  })
  return errMsg || null
}
