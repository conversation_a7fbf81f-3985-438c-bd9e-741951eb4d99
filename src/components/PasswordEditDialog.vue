<template>
  <el-dialog
    v-model="visible"
    width="480px"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">修改密码</div>
    </template>
    <el-form
      :model="editData"
      :rules="rules"
      label-width="80px"
      class="tw-px-[12px]"
      ref="editRef"
    >
      <el-form-item label="账号名称：" prop="account">
        <span>{{ editData.account }}</span>
      </el-form-item>
      <el-form-item v-if="props.isSelf" label="旧密码：" prop="oldPassword">
        <el-input v-model="editData.oldPassword" type="password" show-password clearable placeholder="请填写旧密码"></el-input>
      </el-form-item>
      <el-form-item label="新密码：" prop="newPassword">
        <el-input v-model="editData.newPassword" type="password" show-password clearable placeholder="请填写新密码"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button type="primary" @click="confirm" :icon="Select">修改</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, watch, reactive,} from 'vue'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { passwordReg, } from '@/utils/constant'
import { aiTeamModel } from '@/api/user'
import type { FormInstance, } from 'element-plus'
const emits = defineEmits(['confirm', 'update:editPassWordVisible'])
const props = defineProps<{
  editPassWordVisible: boolean,
  isSelf?: boolean,
  passwordData: {
    id: number,
    account: string,
    oldPassword?: string,
    newPassword: string
  }
}>();
const visible = ref(props.editPassWordVisible)
watch(() => props.editPassWordVisible, n => {
  visible.value = n
})
const editData = reactive<{
  id: number,
  account: string,
  oldPassword?: string,
  newPassword: string
}>(props.passwordData || {
  id: undefined,
  account: '',
  oldPassword: '',
  newPassword: ''
})
const editRef = ref<FormInstance  | null>(null)
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  visible.value = false
  emits('update:editPassWordVisible', false)
}
const validatePassword = (rule: any, value: any, callback: any) => {
  if (!value.match(passwordReg)) {
    callback(new Error('密码必须为包含数字和字母，6~16位'))
  } else {
    callback()
  }
}
const rules = {
  oldPassword: [
    { required: true, message: '请填写旧密码', trigger: 'blur' },
  ],
  newPassword: [
    { required: true, message: '请填写密码', trigger: 'blur' },
    { validator: validatePassword, trigger: 'blur' },
  ],
}

const confirm = () => {  
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      const { id, newPassword } = editData
      emits('confirm', {
        id: id as number, 
        oldPassword: editData.oldPassword || undefined,
        newPassword
      })
      cancel()
    }
  })
}
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {

  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>
