<template>
  <el-select
    v-model="selectVal"
    class="select-dom"
    clearable
    :multiple="!!props.multiple"
    :collapse-tags="true"
    :collapse-tags-tooltip="true"
    :placeholder="props.placeholder || '请选择'"
    popper-class="popper-dom"
    :style="props.styleStr"
    @change="updateSelect"
    @visible-change="handleVisibleChange"
  >
    <template v-if="props.selectList?.length > 0" #prefix>
      <el-select v-model="searchType" class="tw-w-[92px]" @change="handlePrefixChange">
        <el-option v-for="item in props.selectList" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>
    </template>
    <template #header>
      <div class="tw-flex tw-flex-col">
        <el-input
          v-if="props.filterable" 
          v-model.trim="searchVal"
          class="tw-mb-[8px] tw-h-[32px]"
          ref="inputRef"
          placeholder="请输入搜索内容"
          size="small"
          clearable
          @clear="filterOption('')"
          @input="(val: string) => filterOption(val)"
        >
          <template #suffix>
            <el-icon><SvgIcon name="search" /></el-icon>
          </template>
        </el-input>
        <div v-if="isEmpty" class="tw-pl-[1px] tw-pr-[3px] tw-text-[13px] tw-mb-[8px]">
          <div class="tw-text-left tw-font-[600] tw-mb-[6px]">过滤选项列表</div>
          <div class="tw-text-center tw-text-[#969799]">暂无数据</div>
        </div>
        <div class="tw-flex tw-justify-between tw-items-center tw-pl-[1px] tw-pr-[3px] tw-text-[13px]">
          <span class="tw-text-left tw-font-[600]">{{ title }}</span>
          <div v-if="options && options?.length>0" class="tw-flex-grow tw-flex">
            <template v-if="props.canCopy">
              <el-button type="primary" link @click="handleCopy" style="margin-left: 8px;">
                复制
              </el-button>
              <el-tooltip>
                <template #content>
                  <div v-if="copyIds&&copyIds.length>=1" class="tw-w-[30vw]">
                    <div class="tw-border-b tw-border-gray-400 tw-pb-[4px]">
                      <div class="tw-text-left tw-truncate">复制内容</div>
                    </div>
                    <p class="tw-mt-[4px] tw-grid tw-grid-cols-3 tw-text-left tw-gap-[5px]">
                      <span v-for="item in copyIds" class="tw-truncate">
                      {{ filterId(item)}}
                      </span>
                    </p>
                  </div>
                  <div v-else>暂无复制内容</div>
                </template>
                <el-button
                  :disabled="!copyIds||copyIds.length<1"
                  style="margin-left: 8px;"
                  :type="!copyIds||copyIds.length<1 ? 'default':'primary'"
                  link
                  @click="pasteIds"
                >粘贴</el-button>
              </el-tooltip>
            </template>
            <template v-if="props.canSelectAll">
              <el-button
                style="margin-left: 8px;"
                link
                type="primary"
                @click="selectAll"
              >全选</el-button>
            </template>
            <el-button v-if="!!selectVal?.length" type="danger" link @click="delAll" style="margin-left: 8px;">
              清空
            </el-button>
          </div>
          <span class="tw-max-w-[120px] tw-text-[#969799] tw-text-right tw-ml-[30px]">{{ `已选  ${selectVal?.length || 0}/${props.total || props.options.length || 0}` }}</span>
        </div>
      </div>
    </template>
      <div v-loading="props.loading || false" class="tw-absolute tw-w-full tw-h-full tw-top-0 tw-left-0">
      </div>
      <el-option
        v-for="item in options"
        :key="props.key ? (item[props.key] || item): (props.val ? item[props.val] : item)"
        :label="props.name ? (item[props.name] || item) : item"
        :value="props.val ? (item[props.val] || item) : item"
        style="font-size: 13px"
      >
        <div class="tw-flex tw-items-center">
          <span class="option-checkbox">
            <el-icon :size="12" color="#fff"><SvgIcon name="gou"/>  </el-icon>
          </span>
          <span class="tw-truncate">{{ props.name ? (item[props.name] || item) : item }}</span>
        </div>
        <slot name="option-tips" :option="item"></slot>
      </el-option>  
      <template v-if="(props.total && props.total>pageSize) || props.options?.length>pageSize" #footer>
        <PaginationBox
          class="tw-m-[-10px]"
          :pageSize="pageSize"
          :currentPage="currentPage"
          :total="props.total || optionsOrigin?.length"
          mini
          @search="updateListData(currentPage, pageSize)"
          @update="updateListData"
        >
        </PaginationBox>
      </template>
  </el-select>
</template>

<script lang="ts" setup>
import { onUnmounted, ref, computed, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import PaginationBox from '@/components/PaginationBox.vue'
import { onBeforeRouteLeave } from 'vue-router';
import { useGlobalStore } from '@/store/globalInfo'

// 组件入参props
const props = withDefaults(defineProps<{
  selectVal?: any[], // 当前选中数据
  selectValStr?: string, // 当前选中数据，字符串拼接形式
  options: any[], // 选项列表
  name?: string, // 选项列表中代表name的属性
  val?: string, // 选项列表中代表value的属性
  key?: string
  isString?: boolean,
  placeholder?: string, // 选择框占位符
  filterable?: boolean, // 是否支持搜索，搜索框显示
  styleStr?: string, // 当前dom的style
  canCopy?: boolean, // 是否支持复制、粘贴，并控制复制粘贴按钮显示。
  copyName?: string, // 使用缓存种复制粘贴的名称
  canSelectAll?: boolean, // 是否支持全选，若控制最多选择数量，则按顺序选择至上限，并控制复制粘贴按钮显示。
  isRemote?: boolean, // 是否远程搜索（后端接口）
  multiple: boolean,
  loading?: boolean,
  total?: number, // 若传total，则代表列表总数total和入参的options长度不一致，则默认加入分页器；
  selectList?: {name: string, value: string}[] // 前部是否需要多选，例如包含排除
  prefix?: string,
  pageSize?: number,
}>(), {
  multiple: false,
  filterable: false,
  selectList: () => [],
})
// emit
const emits = defineEmits([
  'update:selectVal', 'update:options', 'update:prefix', 'update:selectValStr'
])

const globalStore = useGlobalStore()
const selectVal = ref<null | any[]>(props.isString ? props.selectValStr?.split(',')||[] : props.selectVal||[]) // 选中的数据
const options = ref<any[] | null>([])
const optionsOrigin = computed(() => {
  return props.options?.filter(item => !searchVal.value || item[props.name as keyof typeof item].includes(searchVal.value)) || []
}) // 本地过滤后的选项列表
const title = computed(() => {
  if (!!searchVal.value && !isEmpty.value) {
    return '过滤选项列表'
  } else {
    return props.placeholder ? props.placeholder.replace('请选择', '') + '列表' : '选项列表'
  }
})
const copyIds = computed(() => {
  if (!props.canCopy || !props.copyName) return []
  return globalStore.findCopyInfoByName(props.copyName) || []
}) // 复制的数据
const searchVal = ref('') // 搜索输入框内容
const inputRef = ref() // 搜索输入框ref
const needFocus = ref(true)

/** 对于数据过长的列表进行分页 */
const pageSize = ref(props.pageSize || 50) // 默认下拉显示的选项，减少渲染压力
const currentPage = ref(1)
const updateListData = async (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  if (props.isRemote) {
    emits('update:options', searchVal.value, p-1, s)
  } else {
    options.value = optionsOrigin.value.slice((p - 1) * s, p * s)
  }
}

// 下拉框显示、消失触发函数
const handleVisibleChange = (v: boolean) => {
  // 保证首次进入是最顶部的
  if (v) {
    const timer: any = setTimeout(() => {
      inputRef.value?.focus()
      timer && clearTimeout(timer)
    }, 500)
  } else {
    // 失焦后将搜索内容清空，并重置搜索触发的定时器
    if (searchVal.value) {
      searchVal.value = ''
      updateListData(1, props.pageSize || 50)
    }
  }
}

/** 搜索功能，过滤选项 */
// 防止触发太多次，使用定时器触发防抖功能
const timer = ref<ReturnType<typeof setTimeout> | null>(null)
const isEmpty = ref(false)
const filterOption = (val: string,) => {
  timer.value && clearTimeout(timer.value)
  timer.value = setTimeout(async () => {
    currentPage.value = 1
    props.isRemote ? emits('update:options', val, currentPage.value-1, pageSize.value) : 
    (options.value = optionsOrigin.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value))
    if ((!options.value || options.value?.length < 1) && props.options?.length > 0) {
      options.value = props.options.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
      isEmpty.value = true
    } else {
      isEmpty.value = false
    }
    timer.value && clearTimeout(timer.value)
    timer.value = null
  }, props.isRemote ? 1000 : 200)
}

// 翻译粘贴板（sessionStorage缓存）存在的信息
const filterId = <T>(id: T) => {
  const vv = props.options.find(v=> {
    return (props.val ? v[props.val] : v)==id
  })
  if (vv) {
    return props.name ? (vv[props.name] || '') : (vv || '')
  } else {
    return ''
  }
  
}
// 清空选择数据
const delAll = () => {
  selectVal.value = []
  updateSelect()
}
// 复制功能
const handleCopy = () => {
  if (!selectVal.value || selectVal.value?.length<1 || !props.copyName || !props.canCopy) {
    return ElMessage.warning('复制内容不可为空')
  } else {
    globalStore.addCopyInfo({
      name: props.copyName,
      value: selectVal.value || [],
    })
    ElMessage.success('复制成功')
  }
}
// 粘贴功能
const pasteIds = () => {
  if (!props.canCopy || !props.copyName || !copyIds.value || copyIds.value?.length < 0) return
  const optionIds = props.options?.map(item => props.val ? item[props.val] : item)
  const copyFilterIds = copyIds.value.filter(v => optionIds?.includes(v))
  selectVal.value = [...new Set([...(selectVal.value || []), ...(copyFilterIds||[])])]
  ElMessage.success('粘贴完成')
  globalStore.deleteCopyeInfoByName(props.copyName)
  props.isString ? emits('update:selectValStr', selectVal.value.join(','))
  : emits('update:selectVal', selectVal.value||[])
}
// 全选功能
const selectAll = () => {
  selectVal.value = options.value?.map(item => props.val ? (item[props.val] || item) : item) || []
  props.isString ? emits('update:selectValStr', selectVal.value.join(','))
  : emits('update:selectVal', selectVal.value||[])
}

// 更新选中数据,并判断是否需要继续聚焦搜索框，暴露给父组件
const updateSelect = () => {
  const newItem = selectVal.value?.at(-1) || undefined
  if (newItem) {
    const index = (options.value ||[]).findIndex(item => (props.val ? item[props.val] || item : item) === newItem)
    if(index >= 6 || !searchVal.value) {
      needFocus.value = false
    } else {
      needFocus.value = true
    }
  }
  props.isString ? emits('update:selectValStr', selectVal.value?.join(',') || '')
  : emits('update:selectVal', selectVal.value||[])
}

// prefix多选
const searchType = ref(props.prefix || props.selectList[0] || '')
const handlePrefixChange = () => {
  props.selectList?.length > 0 && emits('update:prefix', searchType.value || '')
}

/** watch开始 */
// 监听入参，更新选中数据和选项变化
watch(() => props.options, () => {
  if (!!props.isRemote) {
    options.value = [...(props.options || [])]
  }
  if (!props.isRemote && !searchVal.value) {
    options.value = (props.options || []).slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
  }
}, {deep: true, immediate: true})

watch([() => props.selectVal, () => props.selectValStr],  () => {
  selectVal.value = props.isString ? (!!props.selectValStr ? props.selectValStr.split(',') : []) : props.selectVal || []
}, {deep: true})

const clearAllData = () => {
  timer.value && clearTimeout(timer.value)
  timer.value = null
  // @ts-ignore
  searchType.value = null
  selectVal.value = null
  inputRef.value = null
  options.value = null
}
onUnmounted(() => {
  clearAllData()
})
onBeforeRouteLeave(() => {
  clearAllData()
})
</script>

<style lang="postcss" type="text/postcss" scoped>

.select-dom {
  :deep(.el-select-tags-wrapper) {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    overflow-x: hidden;
    .el-tag {
      padding: 0 2px;
    }
    .el-tag__content {
      display: flex;
      align-items: center;
    }
  }
  :deep(.el-select__tags:has(+.el-input--prefix)) {
    left: 92px;
    width: calc(100% - 120px) !important;
    flex-wrap: nowrap;
    overflow-x: hidden;
    .el-select-tags-wrapper {
      width: 100%;
    }
  }
  :deep(.el-input__prefix) {
    margin: -1px 0 -1px -11px;
    z-index: 11;
    .el-select .el-input__wrapper {
      background-color: #f5f7fa;
      border-radius: 4px 0 0 4px;
    }
  }
}
.popper-dom {
  .el-button {
    font-size: 13px;
  }
  .el-select-dropdown__item {
    padding: 8px;
  }
  .el-select-dropdown.is-multiple .el-select-dropdown__item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .option-checkbox {
      width: 14px;
      height: 14px;
      border-radius: 2px;
      border-width: 1px;
      position: relative;
      margin-right: 8px;
      .el-icon {
        display: none;
        position: absolute;
        top: 1px;
        left: 0px;
      }
    }
    &.selected {
      font-weight: normal;
      color: #165DFF;
      .option-checkbox {
        background-color: #165DFF;
        border-color: #165DFF;
        .el-icon {
          display:inline-block;
        }
      }
    }
    &.selected::after {
      display: none;
      content: '';
    }
  }
}
</style>
