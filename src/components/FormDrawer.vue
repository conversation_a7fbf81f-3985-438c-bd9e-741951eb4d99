<template>
  <el-drawer
    v-model="dialogVisible"
    title="线路详情"
    :size="props.dialogStyle.drawerSize"
    align-center
    :close-on-click-modal="true"
    @opened="handleOpened"
    @closed="handleClosed"
  >
    <template #header>
      <div class="drawer-header" :style="`width: ${props.dialogStyle.formWidth};`">
        线路详情
      </div>
    </template>

    <el-scrollbar wrap-class="drawer-main">
      <div class="drawer-main-inner" :style="`width: ${props.dialogStyle.formWidth};`">
        <el-form
          inline
          :label-width="props.dialogStyle.labelWidth"
          label-position="right"
          :model="form"
          :disabled="props.readonly"
        >
          <slot></slot>
        </el-form>
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="drawer-footer" :style="`width: ${props.dialogStyle.bottomButtonBoxWidth};`">
        <el-button :icon="CloseBold" @click="handleCancel">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="handleConfirm">
          修改
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'

interface propType {
  // 弹窗显示隐藏
  visible: boolean,
  // 弹窗样式
  dialogStyle?: {
    // 表单项标签宽度
    labelWidth: string,
    // 抽屉宽度
    drawerSize: string,
    // 表单宽度
    formWidth: string,
    // 底部按钮容器宽度
    bottomButtonBoxWidth: string,
  },
  // 表单只读
  readonly: boolean,
  // 实体信息，通常也会包含实体ID
  content: { [keyName: string]: any },
  // 表单默认值，在成功提交或者清空表单后会使用此默认值
  contentDefault: { [keyName: string]: any },
}

const props = withDefaults(
  defineProps<propType>(),
  {
    visible: false,
    dialogStyle: () => {
      return {
        labelWidth: '80px',
        drawerSize: '82%',
        formWidth: '970px',
        bottomButtonBoxWidth: '930px',
      }
    },
    readonly: true,
  }
)
const emits = defineEmits([
  // 弹窗已打开
  'opened',
  // 关闭弹窗
  'close',
  // 通知父组件需要修改
  'modify'
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)

watch(
  // 不能只监听props.visible，因为这个是组件内的组件，需要监听上层组件的整个表单数据
  props,
  (val) => {
    dialogVisible.value = val.visible
    // 每次显示弹窗时
    if (val.visible) {
      Object.assign(form, JSON.parse(JSON.stringify(val.content)))
    }
  },
)

// 表单默认值
const formDefault = props.contentDefault
// 表单内容
const form = reactive({ ...formDefault })

/**
 * 弹窗显示动画结束后
 */
const handleOpened = () => {
  // 弹窗的显示动画播放结束，通知父组件同步状态
  emits('opened')
}

/**
 * 关闭弹窗
 */
const closeDialog = () => {
  dialogVisible.value = false
}

/**
 * 弹窗关闭动画结束后
 */
const handleClosed = () => {
  // 弹窗已经关闭，通知父组件同步状态
  emits('close')
}

/**
 * 点击确定按钮
 */
const handleConfirm = async () => {
  // 通知父组件需要修改
  emits('modify')
  // 关闭弹窗
  closeDialog()
}

/**
 * 点击取消按钮
 */
const handleCancel = () => {
  // 关闭弹窗
  closeDialog()
}
</script>

<style lang="postcss" scoped>
</style>
