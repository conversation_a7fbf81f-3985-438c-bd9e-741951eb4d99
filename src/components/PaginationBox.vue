<template>
  <el-scrollbar class="pagination-scrollbar">
    <el-pagination
      class="pagination-dox"
      :class="padding?'':'no-padding'"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="pageSizeList"
      :pager-count="pagerCount"
      :layout="layout"
      small
      :total="total || 0"
      @current-change="update"
    >
      <div class="tw-flex tw-justify-between tw-items-center tw-w-[80%] tw-grow">
        <span class="tw-flex tw-items-center">
          {{ `${total >= (currentPage - 1) * pageSize + 1 ? (currentPage - 1) * pageSize + 1 : total}-${total >= currentPage * pageSize ? currentPage * pageSize : total}，` }}
          <span v-if="!props.dynamicTotal">{{`共${total}个`}}</span>
          <span v-else>{{`总数获取中…`}}</span>
          <el-icon size="--el-font-size-base" class="tw-cursor-pointer tw-ml-0.5 tw-text-[#969799] hover:tw-text-[var(--el-color-primary)]" @click="search"><SvgIcon name="reset" color="inherit"/></el-icon>
        </span>
        <span v-if="!props.mini" class="tw-border-r-[1px] tw-border-r-[#E6E6E6] tw-pr-1">
          每页显示行数
          <el-select v-model="pageSize" @change="update">
            <el-option v-for="item in pageSizeList" :key="item" :label="item" :value="item" />
          </el-select>
        </span>
      </div>
      <template v-if="props.dynamicTotal">
        <el-button link @click="handlePageChange(-1)" :disabled="currentPage<=1"><el-icon class="tw-mx-[2px]" :size="12"><ArrowLeft /></el-icon></el-button>
        <span class="tw-text-[12px]">{{ currentPage }}</span>
        <el-button link @click="handlePageChange(1)" :disabled="total<pageSize"><el-icon class="tw-mx-[2px]" :size="12"><ArrowRight /></el-icon></el-button>
      </template>
    </el-pagination>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

const props = withDefaults(defineProps<{
  pageSize: number,
  currentPage: number,
  pageSizeList?: number[],
  pagerCount?: 5 | 7 | 9 | 11 | 13 | 15 | 17 | 19 | 21,
  total: number,
  dynamicTotal?: boolean
  mini?: boolean,
  padding?: boolean,
}>(), {
  pageSize: 20,
  currentPage: 1,
  dynamicTotal: false,
  pageSizeList: () => [20, 100, 500, 1000],
  pagerCount: 5,
  mini: false,
  padding: true,
})
const total = computed(() => props.total)
const pageSize = ref(props.pageSize)
const currentPage = ref(props.currentPage)
const layout = computed(() => {
  return props.dynamicTotal ? 'slot' : 'slot, prev, pager, next'
})
watch([() => props.currentPage, () => props.pageSize], () => {
  currentPage.value = props.currentPage
  pageSize.value = props.pageSize
}, {
  deep: true
})
const emits = defineEmits([
  'search', 'update',
])
const search = () => {
  emits('search')
}
const handlePageChange = (flag: number) => {
  currentPage.value = currentPage.value + flag
  emits('update', currentPage.value, pageSize.value)
}
const update = () => {
  emits('update', currentPage.value, pageSize.value)
}
</script>

<style lang="postcss" type="text/postcss">
.pagination-scrollbar {
  height: 40px;
  width: 100%;
  flex-shrink: 0;
  flex-grow: 0;
}

.pagination-dox {
  padding: 8px 16px;
  width: 100%;
  display: flex;
  background-color: #fff;
  .el-select {
    width: 70px;
    height: 24px;
    .el-input__wrapper {
      padding: 1px 8px;
    }
  }
  .btn-prev {
    margin-left: 0;
  }
}
.no-padding {
  padding: 0;
}
.el-pagination--small .el-select .el-input {
  width: 70px !important;
  height: 24px !important;
}
.el-pagination--small .btn-next, .el-pagination--small .btn-prev, .el-pagination--small .el-pager li {
  font-size: 12px;
  min-width: 18px;
  height: 18px;
  line-height: 18px;
  padding: 0;
  box-sizing: border-box;
  flex-grow: 0;
  &:nth-child(n-1) {
    margin-right: 4px;
  }
  &.is-active {
    border: 1px solid var(--el-color-primary);
    border-radius: 2px;
  }
}
</style>
