<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    class="restriction-dialog"
    align-center
    :close-on-click-modal="false"
    @close="onClose"
  >
    <template #header>
      <div class="form-dialog-header">
        频率限制
      </div>
    </template>

    <el-scrollbar class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          label-width="80px"
          label-position="right"
          :model="form"
          :rules="rules"
        >
          <!--行业选择-->
          <el-form-item v-if="props.type===FrequencyRestrictionTypeEnum.INDUSTRY" prop="productIndustryId" label="选择行业：" required>
            <el-cascader
              v-model="industrySearchVal"
              :disabled="props.isEdit"
              :options="industryOptions"
              :props="industryProps"
              clearable
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="2"
              :show-all-levels="false"
              placeholder="下拉选择"
              style="width: 100%;"
              @change="onSelectChange"
            />
          </el-form-item>

          <!--产品选择-->
          <el-form-item v-else-if="props.type===FrequencyRestrictionTypeEnum.PRODUCT" prop="productIndustryId" label="选择产品：" required>
            <el-select
              v-model="productSearchVal"
              :disabled="props.isEdit"
              clearable
              filterable
              placeholder="下拉选择"
              style="width: 100%;"
              @change="onSelectChange"
            >
              <el-option
                v-for="productItem in productOptions"
                :key="productItem.id"
                :label="productItem.name"
                :value="productItem.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item ref="callingRestrictionsRef" label="拨打限制：" prop="callingRestrictions" class="muti-items">
            <div class="tw-flex tw-flex-col tw-items-left">
              <div>
                <el-button type="primary" link :icon="Plus" @click="addRestriction(1)">添加拨打限制</el-button>
              </div>
              <div v-for="(item, index) in callingRestrictions" :key="index" class="tw-flex tw-justify-between tw-mt-[8px]">
                <InputNumberBox v-model:value="item.num" placeholder="填写" style="width: 209px;" :min="1" append="次" @update:value="handleRestrictionsChange(1)" />
                <InputNumberBox v-model:value="item.hours" placeholder="填写" style="width: 209px;" :min="1" append="天" @update:value="handleRestrictionsChange(1)" />
                <el-button type="danger" class="tw-flex-grow-0 tw-flex-shrink-0" link @click="delRestriction(index, 1)">
                  删除
                </el-button>
              </div>
            </div>
          </el-form-item>
          <el-form-item ref="dialingRestrictionsRef" label="拨通限制：" prop="dialingRestrictions" class="muti-items">
            <div class="tw-flex tw-flex-col tw-items-left">
              <div>
                <el-button type="primary" link :icon="Plus" @click="addRestriction(2)">添加拨通限制</el-button>
              </div>
              <div v-for="(item, index) in dialingRestrictions" :key="index" class="tw-flex tw-justify-between tw-mt-[8px]">
                <InputNumberBox v-model:value="item.num" placeholder="填写" style="width: 209px;" :min="1" append="次" @update:value="handleRestrictionsChange(2)" />
                <InputNumberBox v-model:value="item.hours" placeholder="填写" style="width: 209px;" :min="1" append="天" @update:value="handleRestrictionsChange(2)" />
                <el-button type="danger" class="tw-flex-grow-0 tw-flex-shrink-0" link @click="delRestriction(index, 2)">
                  删除
                </el-button>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="handleCancel">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, watch } from 'vue'
import { Throttle } from '@/utils/utils'
import { CascaderProps, ElMessage, FormRules } from 'element-plus'
import { CloseBold, Plus, Select } from '@element-plus/icons-vue'
import { CallLimit, FrequencyRestrictionInfo, FrequencyRestrictionTypeEnum, PutThroughLimit } from '@/type/dataFilter'
import { useGlobalStore } from '@/store/globalInfo'
import InputNumberBox from '@/components/InputNumberBox.vue'
import { IndustryOption, ProductItem } from '@/type/industry'
import { frequencyRestrictionModel } from '@/api/data-filter'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
  isEdit: boolean,
  type: FrequencyRestrictionTypeEnum,
  data: FrequencyRestrictionInfo,
  list: FrequencyRestrictionInfo[],
  options: IndustryOption[] | ProductItem[],
  mapList: { id: number, name: string }[],
}>(), {
  visible: false,
  isEdit: false,
  type: FrequencyRestrictionTypeEnum.GLOBAL,
  data: () => ({
    id: undefined,
    frequentType: '',
    productIndustryId: '',
    productIndustryName: '',
    callLimit: [],
    putThroughLimit: [],
  }),
  list: () => [],
  options: () => [],
  mapList: () => [],
})
const emits = defineEmits([
  'close',
  'update'
])

const globalStore = useGlobalStore()

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)

watch(() => props.visible, async (val) => {
  dialogVisible.value = val
  // 每次显示弹窗时
  if (val) {
    // 更新表单数据
    await nextTick()
    resetForm()
    Object.assign(form, JSON.parse(JSON.stringify(props.data ?? {})))
    covertRestrictionsFormat()

    if (props.type === FrequencyRestrictionTypeEnum.GLOBAL) {
      form.frequentType = '0'
    } else if (props.type === FrequencyRestrictionTypeEnum.INDUSTRY) {
      form.frequentType = '1'
      industrySearchVal.value = !!form.productIndustryId ? Number(form.productIndustryId) : null
      industryOptions.value = JSON.parse(JSON.stringify(props.options)) as IndustryOption[]
      industryMapList.value = JSON.parse(JSON.stringify(props.mapList)) as { id: number, name: string }[]
    } else if (props.type === FrequencyRestrictionTypeEnum.PRODUCT) {
      form.frequentType = '2'
      productSearchVal.value = !!form.productIndustryId ? Number(form.productIndustryId) : null
      productOptions.value = JSON.parse(JSON.stringify(props.options)) as ProductItem[]
      productMapList.value = JSON.parse(JSON.stringify(props.mapList)) as { id: number, name: string }[]
    } else {
      form.frequentType = undefined
      ElMessage({
        type: 'warning',
        message: '限制类型不正确，请重新打开编辑',
      })
    }
  }
})

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 搜索 开始 ----------------------------------------

// 搜索条件，行业，组件表单值
const industrySearchVal = ref<number | null>(null)
// 搜索条件，行业，级联选择器，数据内容
const industryOptions = ref<IndustryOption[]>([])
// 搜索条件，行业，级联选择器，配置信息
const industryProps: CascaderProps = {
  multiple: false,
  emitPath: false,
  value: 'id',
  label: 'name',
  children: 'secondaryIndustries',
}
// 搜索条件，行业，名称和ID映射表
const industryMapList = ref<{ id: number, name: string }[]>([])

// 搜索条件，产品，组件表单值
const productSearchVal = ref<number | null>(null)
// 搜索条件，产品，选择框，数据内容
const productOptions = ref<ProductItem[]>([])
// 搜索条件，产品，名称和ID映射表
const productMapList = ref<{ id: number, name: string }[]>([])

/**
 * 下拉框值变化
 * @param {number | null} val 新值
 */
const onSelectChange = (val: number | null) => {
  form.productIndustryId = val ? (val + '') : undefined
}

// ---------------------------------------- 搜索 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): FrequencyRestrictionInfo => {
  return {
    id: undefined,
    frequentType: '',
    productIndustryId: '',
    productIndustryName: '',
    callLimit: [],
    putThroughLimit: [],
  }
}
// 表单数据
const form: FrequencyRestrictionInfo = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  productIndustryId: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!dialogVisible.value) {
        // 弹窗关闭后不需要校验
        callback()
        return
      }
      if (!value) {
        if (props.type === FrequencyRestrictionTypeEnum.INDUSTRY) {
          callback(new Error('行业不能为空'))
        } else if (props.type === FrequencyRestrictionTypeEnum.PRODUCT) {
          callback(new Error('产品不能为空'))
        } else {
          callback(new Error('表单项不能为空'))
        }
      } else {
        callback()
      }
    }
  },
  callingRestrictions: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (callingRestrictions.value?.length) {
        const hours: number[] = []
        callingRestrictions.value.map(item => {
          if (!item.num || !item.hours) {
            callback(new Error('拨打限制规则存在空值'))
          } else if (isNaN(+item.num) || isNaN(+item.hours)) {
            callback(new Error('拨打限制规则必须为数字'))
          } else {
            hours.includes(item.hours) ? callback(new Error('拨打限制规则存在相同时间段')) : hours.push(item.hours)
          }
        })
      }

      if (props.type === FrequencyRestrictionTypeEnum.GLOBAL) {
        // 全局限制，天数应该在1-14天
        const check = !!form.callLimit?.every((item: CallLimit) => {
          return typeof item.callLimitTime === 'number'
            && <number>item.callLimitTime >= 1
            && <number>item.callLimitTime <= 14
        })
        console.log()
        if (!check) {
          callback(new Error('时间范围在1-14天'))
        } else {
          callback()
        }
      } else {
        // 行业限制，产品限制，没有天数范围
        callback()
      }
    }
  },
  dialingRestrictions: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (dialingRestrictions.value?.length) {
        const hours: number[] = []
        dialingRestrictions.value.map(item => {
          if (!item.num || !item.hours) {
            callback(new Error('拨打限制规则存在空值'))
          } else if (isNaN(+item.num) || isNaN(+item.hours)) {
            callback(new Error('拨打限制规则必须为数字'))
          } else {
            hours.includes(item.hours) ? callback(new Error('拨打限制规则存在相同时间段')) : hours.push(item.hours)
          }
        })
      }

      if (props.type === FrequencyRestrictionTypeEnum.GLOBAL) {
        // 全局限制，天数应该在1-14天
        const check = !!form.putThroughLimit?.every((item: PutThroughLimit) => {
          return typeof item.putThroughLimitTime === 'number'
            && <number>item.putThroughLimitTime >= 1
            && <number>item.putThroughLimitTime <= 14
        })
        if (!check) {
          callback(new Error('拨通限制规则时间范围在1-14天'))
        } else {
          callback()
        }
      } else {
        // 行业限制，产品限制，没有天数范围
        callback()
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      // ElMessage({
      //   message: '请按提示正确填写信息',
      //   type: 'warning',
      // })
    }
  })
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  try {
    // 处理参数
    let productIndustryId = ''
    let productIndustryName = ''
    if (form.frequentType === '1') {
      productIndustryId = industrySearchVal.value + ''
      const result = industryMapList.value.find((item) => {
        return item.id + '' === productIndustryId
      })
      productIndustryName = result?.name ?? ''
    } else if (form.frequentType === '2') {
      productIndustryId = productSearchVal.value + ''
      const result = productMapList.value.find((item) => {
        return item.id + '' === productIndustryId
      })
      productIndustryName = result?.name ?? ''
    }
    const params: FrequencyRestrictionInfo = {
      id: form.id ?? undefined,
      frequentType: form.frequentType ?? undefined,
      productIndustryId: productIndustryId,
      productIndustryName: productIndustryName,
      callLimit: form.callLimit ?? [],
      putThroughLimit: form.putThroughLimit ?? [],
    }
    // 请求接口
    if (form.id !== undefined && form.id !== null) {
      // 编辑
      await frequencyRestrictionModel.edit(params)
    } else {
      // 新建
      await frequencyRestrictionModel.add(params)
    }
    ElMessage({
      message: '保存成功',
      type: 'success',
    })
    emits('update', params)
    // 关闭弹窗
    closeDialog()
  } catch (e) {
  } finally {
    // 节流锁解锁
    throttleConfirm.unlock()
  }
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其它内容
  industrySearchVal.value = null
  productSearchVal.value = null
}
/**
 * 关闭弹窗，组件回调
 */
const onClose = () => {
  emits('close')
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 关闭弹窗，主动触发
 */
const closeDialog = () => {
  dialogVisible.value = false
}
/**
 * 点击确定按钮
 */
const handleConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const handleCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 频率限制 开始 ----------------------------------------

// 拨打限制DOM
const callingRestrictionsRef = ref()
// 拨通限制DOM
const dialingRestrictionsRef = ref()

// 拨打限制，组件数据
const callingRestrictions = ref<{ num?: number, hours?: number }[]>([])
// 拨通限制，组件数据
const dialingRestrictions = ref<{ num?: number, hours?: number }[]>([])

/**
 * 添加频率限制规则
 * @param {number} type 类型，1: 拨打限制, 2: 拨通限制
 */
const addRestriction = (type: number) => {
  if (type === 1) {
    callingRestrictions.value.push({
      num: undefined, hours: undefined
    })
  }
  if (type === 2) {
    dialingRestrictions.value.push({
      num: undefined, hours: undefined
    })
  }
  handleRestrictionsChange(type)
}
/**
 * 更新频率限制规则，接口数据转换成组件数据
 */
const covertRestrictionsFormat = () => {
  callingRestrictions.value = []
  dialingRestrictions.value = []
  form.callLimit?.forEach(item => {
    callingRestrictions.value.push({ num: +(item.callLimit ?? 0), hours: +(item.callLimitTime ?? 0) })
  })
  form.putThroughLimit?.map(item => {
    dialingRestrictions.value.push({ num: +(item.putThroughLimit ?? 0), hours: +(item.putThroughLimitTime ?? 0) })
  })
}
/**
 * 更新频率限制规则，组件数据转换成接口数据
 * @param {number} type 类型，1: 拨打限制, 2: 拨通限制
 */
const handleRestrictionsChange = (type?: number) => {
  if (!type || type === 1) {
    form.callLimit = []
    callingRestrictions.value?.map(item => {
      if (item.num && !isNaN(+item.num) && item.hours && !isNaN(+item.hours)) {
        form.callLimit!.push({
          callLimit: item.num ?? '',
          callLimitTime: item.hours ?? '',
        })
      }
    })
  }
  if (!type || type === 2) {
    form.putThroughLimit = []
    dialingRestrictions.value?.map(item => {
      if (item.num && !isNaN(+item.num) && item.hours && !isNaN(+item.hours)) {
        form.putThroughLimit!.push({
          putThroughLimit: item.num ?? '',
          putThroughLimitTime: item.hours ?? '',
        })
      }
    })
  }
}
/**
 * 删除频率限制规则
 * @param {number} index 选中规则在列表里的索引
 * @param {number} type 类型，1: 拨打限制, 2: 拨通限制
 */
const delRestriction = (index: number, type: number) => {
  if (type === 1) {
    if (index >= 0 && callingRestrictions.value.length > index) {
      callingRestrictions.value.splice(index, 1)
    }
  }
  if (type === 2) {
    if (index >= 0 && dialingRestrictions.value.length > index) {
      dialingRestrictions.value.splice(index, 1)
    }
  }
  handleRestrictionsChange(type)
}

// ---------------------------------------- 频率限制 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style lang="postcss" scoped>
/* 文本框内的标签 */
:deep(.el-cascader__tags .el-tag) {
  margin: 0 0 0 4px;
}
</style>
