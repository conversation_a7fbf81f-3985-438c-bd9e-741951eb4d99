<template>
  <el-dialog
    v-model="visible"
    width="480px"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">收集接口</div>
    </template>
    <el-form
      :model="editData"
      :rules="rules"
      label-width="80px"
      class="tw-px-[12px]"
      ref="editRef"
    >
      <el-form-item label="收集方案：" prop="type">
        <el-radio-group v-model="editData.type" class="tw-ml-[6px]">
          <el-radio :label="0">缓存获取</el-radio>
          <el-radio :label="1">数据导入</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="editData.type===1">
        <!-- <el-form-item label="菜单名称：" prop="menuName">
          <el-input v-model.trim="editData.menuName" maxlength="30" clearable placeholder="请输入菜单名称，30个字以内"></el-input>
        </el-form-item> -->
        <el-form-item label="菜单编码：" prop="menuCode">
          <el-select v-model="editData.menuCode" filterable maxlength="10" clearable placeholder="请选择菜单编码">
            <el-option v-for="item in menuList" :key="item.menuCode" :label="`${item.menuName}（${item.menuCode}）`" :value="item.menuCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="全部接口：" prop="apiStr">
          <el-input v-model.trim="editData.apiStr" type="textarea" :autosize="{ minRows: 2 }" clearable placeholder="请输入菜单编码"></el-input>
        </el-form-item>
      </template>
      
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button type="primary" @click="confirm" :icon="Select">修改</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, watch, reactive,} from 'vue'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import type { FormInstance, } from 'element-plus'
import { generateExcelByAoa } from '@/utils/export'
import { useUserStore } from '@/store/user'
import to from 'await-to-js';
import { authorizationModel } from '@/api/authorization'
import { MenuItem } from '@/type/authorization'

const emits = defineEmits(['update:visible'])
const props = defineProps<{
  visible: boolean,
}>();
const visible = ref(props.visible)
const menuList = ref<MenuItem[] | null>(null)
watch(() => props.visible, async n => {
  visible.value = n
  if (n) {
    const res = await to(authorizationModel.findMenuList())
    menuList.value = res[1] || []
  } else {
    editData.type = 0
    editData.apiStr = undefined
  }
})
const editData = reactive<{
  type?: number,
  menuCode?: string;
  apiStr?: string;
}>({
  type: 0,
  menuCode: undefined,
  apiStr: undefined,
})
const editRef = ref<FormInstance  | null>(null)
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  visible.value = false
  emits('update:visible', false)
}
const rules = {
  oldPassword: [
    { required: true, message: '', trigger: 'blur' },
  ],
}

const userStore = useUserStore()
const commonApis = [
  '/admin/login',
  '/adminRole/findOneAdminRoleById',
  '/traceLog/addTraceInfo',
  '/areaCode/findList',
  '/industryField/findAllIndustryField'
]
const expotCollectInterfaceByString = async () => {
  const menuCode = editData.menuCode
  const menuName = menuList.value?.find(v => v.menuCode === menuCode)?.menuName || ''
  if (!editData.apiStr || !menuCode) return ElMessage.warning('请输入菜单编码和接口字符串')
  const data = editData.apiStr?.split(',')?.flatMap(v => {
    const arr = v.split('/')
    const api = '/' + arr.slice(2).join('/')
    return commonApis.includes(api) ? [] : [[menuName, menuCode, arr[1], api]]
  }) || []
  generateExcelByAoa([['菜单名称', '菜单编码', '服务名', '接口地址', '接口名称'], ...data], '权限列表.xlsx')
  cancel()
}

const expotCollectInterfaceByStore = async () => {
  const data = Object.values(userStore.pageApiMap).map(item => {
    return item.flatMap(v => commonApis.includes((v.api)) ? [] : [[v.route, '', v.service, v.api, '']])
  })
  userStore.pageApiMap = {}
  generateExcelByAoa([['菜单名称', '菜单编码', '服务名', '接口地址', '接口名称'], ...data.flat(1)], '权限列表.xlsx')
  cancel()
}

const confirm = () => {  
  if (editData.type === 0) {
    expotCollectInterfaceByStore()
  } else {
    expotCollectInterfaceByString()
  }
}
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {

  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>
