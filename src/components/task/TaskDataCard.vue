<template>
  <div class="tw-flex">
    <div class="task-card task-card-main" :class="{'task-card-extend': isExtend}">
      <div class="tw-grid tw-grid-cols-2 tw-gap-x-[12px] tw-gap-y-[4px] tw-bg-white tw-rounded-[4px] tw-p-[12px] sm:tw-p-[4px]">
        <div class="tw-w-full tw-mb-[8px] tw-col-span-2 tw-flex tw-justify-between">
          <div class="tw-flex tw-items-center tw-w-full tw-overflow-hidden">
            <span class="name tw-truncate tw-cursor-pointer" @click="handleAddCache()" :style="{'color': `var(${colorMap[currentTask.callStatus || TaskStatusEnum['待执行']]})`}">
              {{ currentTask.taskName || '' }}
            </span>
            <el-icon v-if="currentTask.smsTemplateAbnormal == 1 && currentTask.callStatus === '未完成'" class="tw-ml-[8px] tw-shrink-0" color="#E64B17" :size="20"><SvgIcon name="sms-fill"/></el-icon>
            <div class="status-box-mini tw-ml-[8px] tw-shrink-0" :class="filterStatusClass(currentTask.callStatus || TaskStatusEnum['待执行'])">{{ currentTask.callStatus }}</div>
            <el-icon v-if="!!currentTask.nextDayCall" class="tw-ml-[4px]" :size="16" :color="`var(${colorMap[currentTask.callStatus || TaskStatusEnum['待执行']]})`"><SvgIcon name="nextday"/></el-icon>
            <el-button v-if="!!currentTask.ifLock && !props.readonly" type="primary" link @click="unlockTask">
              <el-icon :size="16" >
                <SvgIcon name="lock" color="#165DFF"/>
              </el-icon>
            </el-button>
          </div>
          <div>
            <slot name="btn"></slot>
          </div>
        </div>
        <div class="task-content">
          <span class="label">话术名称：</span>
          <span class="value">
            <span>{{ currentTask.speechCraftName || '-' }}</span>
            <el-button v-if="currentTask.speechCraftName" link type="primary" @click="copyText(currentTask.speechCraftName || '')">复制</el-button>
          </span>
        </div>
        <div class="task-content">
          <span class="label">外呼线路：</span>
          <span class="value">
            <span>{{ lineInfo || '-' }}</span>
            <el-button v-if="currentTask.lineName" link type="primary" @click="copyText(currentTask.lineName || '')">复制</el-button>
          </span>
        </div>
        <div class="task-content">
          <span class="label">屏蔽地区：</span>
          <span class="value">
            <el-button link type="primary" @click="showBlock">{{ blockInfo || '无' }}</el-button>
          </span>
        </div>
        <div class="task-content">
          <span class="label">拨打时段：</span>
          <span class="value">
            <TagsBox v-if="currentTask.startWorkTimeList && currentTask.endWorkTimeList" :key="currentTask.id" :tagsArr="concatTimeList(currentTask.startWorkTimeList, currentTask.endWorkTimeList)" tagsName="拨打时段" :tagsNum="2"></TagsBox>
          </span>
        </div>
        <div class="task-content">
          <span class="label">是否止损：</span>
          <span class="value">{{ currentTask.isAutoStop ? `是` : '否' }}</span>
        </div>
        <div v-if="currentTask.isAutoStop" class="task-content">
          <span class="label">止损时间：</span>
          <span class="value">{{ currentTask.taskEndTime ? dayjs(currentTask.taskEndTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}</span>
        </div>
        <div class="task-content">
          <span class="label">是否补呼：</span>
          <span class="value">
            {{ currentTask.autoReCall ? '是' : '否' }}
          </span>
        </div>
        <template v-if="isExtend">
          <template v-if="currentTask.autoReCall">
            <div class="task-content">
              <span class="label">分配方式：</span>
              <span class="value">
                {{ currentTask.autoReCall ? (currentTask.callRatioType == 1 ? '首呼优先' : '多轮次呼叫按比例') : '-' }}
              </span>
            </div>
            <div class="task-content">
              <span class="label">补呼间隔：</span>
              <span class="value">
                {{ currentTask.autoReCall && currentTask.firstRecallTime ? (currentTask.firstRecallTime ? currentTask.firstRecallTime + 'min' : '-') + (currentTask.secondRecallTime ? '；' + currentTask.secondRecallTime + 'min' : '') : '-' }}
              </span>
            </div>
          </template>
          <template v-if="currentTask.taskType === TaskTypeEnum['人机协同']">
            <div class="task-content">
              <span class="label">坐席组：</span>
              <span class="value">
                <TagsBox v-if="currentTask.callTeamIds && currentTask.callTeamIds?.length>0" :key="currentTask.id" :tagsArr="callTeamList.flatMap(item=>currentTask.callTeamIds?.includes(item.id!) ? [item.callTeamName!]:[])" tagsName="坐席组" :tagsNum="2"></TagsBox>
              </span>
            </div>
            <div class="task-content">
              <span class="label">坐席推送方式：</span>
              <span class="value">
                {{ findValueInEnum(currentTask.callTeamPushType, CallTeamPushEnum)??'-' }}
              </span>
            </div>
            <div class="task-content">
              <span class="label">坐席处理方式：</span>
              <span class="value">
                {{ findValueInEnum(currentTask.callTeamHandleType, CallTeamHandleEnum)??'-' }}
              </span>
            </div>
            <div class="task-content">
              <span class="label">坐席占用等级：</span>
              <span class="value">
                {{ findValueInEnum(currentTask.occupyRate, OccupyRateEnum)??'-' }}
              </span>
            </div>
            <div class="task-content">
              <span class="label">集线比：</span>
              <span class="value">
                {{ currentTask.lineRatio||'-' }}
              </span>
            </div>
            <div class="task-content">
              <span class="label">虚拟坐席系数：</span>
              <span class="value">
                {{ currentTask.virtualSeatRatio??'-' }}
              </span>
            </div>
          </template>
          
          <div class="task-content">
            <span class="label">预期完成时间：</span>
            <span class="value">{{ currentTask.expectedFinishTime || '-' }}</span>
          </div>
          <div class="task-content">
            <span class="label">触发短信：</span>
            <span class="value">
              <el-button v-if="currentTask.scriptSms && currentTask.scriptSms?.length > 0" link type="primary" @click="showSmsDialog(0)">{{ currentTask.scriptSms?.length }}</el-button>
              <span v-else>-</span>
            </span>
          </div>
          <div class="task-content">
            <span class="label">挂机短信：</span>
            <span class="value">
              <el-button v-if="currentTask.hangUpSms && currentTask.hangUpSms?.length > 0" link type="primary" @click="showSmsDialog(1)">{{ currentTask.hangUpSms?.length }}</el-button>
              <span v-else>-</span>
            </span>
          </div>
          <div class="task-content">
            <span class="label">变量信息：</span>
            <span class="value">
              <el-button v-if="currentTask.variableSms && currentTask.variableSms?.length > 0" link type="primary" @click="variableVisible = true">{{ currentTask.variableSms?.length }}</el-button>
              <span v-else>-</span>
            </span>
          </div>
          <div class="task-content">
            <span class="label">模板编号：</span>
            <span class="value">
              {{ currentTask.templateId || '-' }}
            </span>
          </div>
          <div class="task-content">
            <span class="label">黑名单：</span>
            <span class="value">
              <el-button
                v-if="currentTask.tenantBlackList && currentTask.tenantBlackList?.length > 0"
                link
                type="primary"
                @click="checkblackList()"
              >
                {{ currentTask.tenantBlackList?.length }}
              </el-button>
              <span v-else>-</span>
            </span>
          </div>
        </template>
      </div>
      <div class="tw-flex tw-justify-center">
        <div class="trapezoid" @click="isExtend=!isExtend">
          <el-icon v-if="isExtend" :size="14"><ArrowUpBold/>  </el-icon>
          <el-icon v-else :size="14"><ArrowDownBold/>  </el-icon>
        </div>
      </div>
    </div>
    <div class="tw-grid tw-grid-cols-2 tw-bg-white tw-rounded-[4px] tw-gap-x-[8px] tw-px-[8px] tw-py-[12px] tw-shrink-0 tw-w-[160px] tw-mb-[18px] tw-ml-[8px]">
      <div class="info-data-box-inner">
        <span class="info-title tw-mb-[2px]">
          导入名单
        </span>
        <span class="info-data-content">
          {{ formatNumber1(currentTask.phoneNum) ?? '-' }}
        </span>
      </div>
      <div class="info-data-box-inner">
        <span class="info-title tw-mb-[2px]">
          完成名单
        </span>
        <span class="info-data-content">
          {{ formatNumber1(currentTask.finishedPhoneNum) ?? '-' }}
        </span>
      </div>
      <div class="info-data-box-inner">
        <span class="info-title tw-mb-[2px]">
          首呼剩余
        </span>
        <span class="info-data-content">
          {{ (currentTask.callingPhoneNum && currentTask.callingPhoneNum >= 0 || isMasterAccount) ? formatNumber1(currentTask.callingPhoneNum) ?? '-' : '-' }}
        </span>
      </div>
      <div class="info-data-box-inner">
        <span class="info-title tw-mb-[2px]">
          补呼剩余
        </span>
        <span class="info-data-content">
          {{ (currentTask.recallingPhoneNum && currentTask.recallingPhoneNum >= 0 || isMasterAccount) ? formatNumber1(currentTask.recallingPhoneNum) ?? '-' : '-' }}
        </span>
      </div>
    </div>
  </div>
  
  <!-- 完成、触达、接通、意向 百分比 -->
  <div v-loading="props.loading" class="tw-grid tw-grid-cols-6 sm:tw-grid-cols-2 tw-gap-[8px] sm:tw-gap-[4px] tw-w-full tw-mt-[12px]">
    <div v-for="item in props.taskStatics" class="task-card tw-items-center tw-h-[112px] sm:tw-h-[96px]">
      <div class="tw-flex tw-justify-between tw-w-full tw-pb-[8px]">
        <div class="tw-flex tw-flex-col tw-justify-around">
          <div class="tw-flex tw-items-center">
            <span class="info-title">{{ item.name }}</span>
            <TooltipBox v-if="item.tooltip && item.tooltip?.length>0 && !isMobile" :title="item.name" :num="1" :content="item.tooltip" :useSlot="true">
              <el-icon :size="12" class="tw-cursor-pointer tw-ml-[2px]" color="var(--primary-black-color-400)"><SvgIcon name="warning" /></el-icon>
            </TooltipBox>
          </div>
          <div class="info-data-content">{{ formatNumberPercent(item.percent, 2, isMasterAccount)}}</div>
        </div>
        <el-progress v-show="!isMobile" class="tw-flex-grow-0 tw-flex-shrink-0" type="circle" :color="customColors" :percentage="item.percent > 100 ? 100 : item.percent" stroke-linecap="square" :width="50" :stroke-width="10">
          <span class="tw-text-[15px] 3xl:tw-text-[16px]" :style="{color: filterColor(item.percent)}"></span>
        </el-progress>
      </div>

      <div class="tw-flex tw-justify-between tw-w-full tw-border-t-[1px] tw-pt-[8px]">
        <span class="info-title">{{ item.text }}</span>
        <span class="info-data-content">{{ formatNumber1(item.number??'')??'-'  }}</span>
      </div>
    </div>
  </div>
  <!-- 人机协同，实时状态模块 -->
  <div v-if="currentTask.taskType === TaskTypeEnum['人机协同']" v-loading="props.loading" class="card-box tw-flex-col tw-mt-[12px]">
    <div class="tw-flex tw-items-center tw-self-start">
      <span class="tw-font-semibold tw-text-[var(--primary-black-color-600)] tw-text-[14px]">实时状态</span>
      <el-tooltip content="刷新实时状态" placement="right" :show-after="500">
        <el-icon size="13" class="tw-cursor-pointer tw-ml-0.5" color="#165DFF" @click="refreshRealData()"><SvgIcon name="reset" color="inherit"/></el-icon>
      </el-tooltip>
    </div>

    <div class="tw-w-full tw-grid tw-gap-[12px] tw-mt-[8px]" :class="`tw-grid-cols-${gridNum}`">
      <div class="info-data-box tw-col-span-2">
        <span class="info-data-box-inner">
          <span class="info-title">实际并发</span>
          <span class="info-data-content">{{ formatNumber1(props.taskRealtimeData?.realConcurrent) }}</span>
        </span>
        <span class="info-data-box-inner">
          <span class="info-title">理论并发</span>
          <span class="info-data-content">{{ formatNumber1(props.taskRealtimeData?.principleConcurrency) }}</span>
        </span>
      </div>
      <div class="info-data-box tw-col-span-3">
        <span class="info-data-box-inner">
          <span class="info-title">近10分钟转接率</span>
          <span class="info-data-content">{{ props.taskRealtimeData?.transferRate ? formatNumber(props.taskRealtimeData?.transferRate * 100, 2) + '%' : 0}}</span>
        </span>
        <span class="info-data-box-inner">
          <span class="info-title">近10分钟接通率</span>
          <span class="info-data-content">{{ props.taskRealtimeData?.putThroughPhoneRate ? formatNumber(props.taskRealtimeData?.putThroughPhoneRate * 100, 2) + '%' : 0 }}</span>
        </span>
      </div>
      <div class="info-data-box tw-col-span-5 tw-gap-[4px]">
        <span class="info-data-box-inner hover:tw-bg-white">
          <span class="info-title">集线比</span>
          <div class="tw-flex tw-items-center">
            <span class="info-data-content tw-min-w-[45px]">{{ props.taskRealtimeData?.lineRatio ?? '-' }}</span>
            <el-button v-if="!props.readonly && editPermission" class="tw-ml-[4px]" link type="primary" @click="editRatio(props.taskRealtimeData?.lineRatio??1)">修改</el-button>
          </div>
        </span>
        <span class="info-data-box-inner hover:tw-bg-white">
          <span class="info-title">坐席占用等级</span>
          <div class="tw-flex tw-items-center">
            <span class="info-data-content tw-min-w-[45px]">{{ findValueInEnum(props.taskRealtimeData?.occupyRate, OccupyRateEnum)|| '-' }}</span>
            <el-button v-if="!props.readonly && editPermission" class="tw-ml-[4px]" link type="primary" @click="editOccupyRate(props.taskRealtimeData?.occupyRate??OccupyRateEnum['高'])">修改</el-button>
          </div>
        </span>
        <span class="info-data-box-inner">
          <span class="info-title">签入座席</span>
          <span class="info-data-content">{{ props.taskRealtimeData?.signedSeats??'-' }}</span>
        </span>
        <span class="info-data-box-inner hover:tw-bg-white">
          <span class="info-title">虚拟坐席系数</span>
          <div class="tw-flex tw-items-center">
            <span class="info-data-content tw-min-w-[45px]">{{ props.taskRealtimeData?.virtualSeatRatio ?? '-' }}</span>
            <el-button v-if="!props.readonly && editPermission" class="tw-ml-[4px]" link type="primary" @click="editVirtualSeatRatio(taskRealtimeData?.virtualSeatRatio??1)">修改</el-button>
          </div>
        </span>
        <span class="info-data-box-inner hover:tw-bg-white group">
          <span class="info-title">空闲座席</span>
          <div class="tw-flex tw-items-center">
            <span class="info-data-content tw-min-w-[45px]">{{ props.taskRealtimeData?.seatDetails?.length ??'-' }}</span>
            <el-button class="tw-ml-[4px]" link type="primary" @click="checkRestSeat">查看</el-button>
          </div>
        </span>
      </div>

      <!-- <div v-for="realtimeItem in taskRealtimeData" class="tw-flex tw-flex-col tw-items-center tw-justify-between tw-h-[50px] tw-my-[6px]">
        <span class="tw-text-[18px] tw-text-[var(--el-color-primary)] tw-font-[600]">
          <span>
            {{ realtimeItem.text.includes('率') ? formatNumber((realtimeItem.number||0) *100, 2)+'%' : formatNumber1(realtimeItem.number) }}
          </span>
          <el-icon v-if="realtimeItem.text==='集线比'" class="tw-cursor-pointer tw-ml-0.5" :size="16" color="var(--el-color-primary)" @click="editRatio(realtimeItem.number??1)">
            <SvgIcon name="edit2"></SvgIcon>
          </el-icon>
        </span>
        <span class="tw-text-[12px] tw-text-[var(--primary-black-color-500)] tw-self-start">{{ realtimeItem.text }}</span>
      </div> -->
    </div>
  </div>
  <el-dialog
    v-model="editVisible"
    width="480px"
    class="dialog-form"
    align-center
    @closed="editVisible=false"
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">修改{{editTypeList[editType]}}</div>
    </template>
    <el-form  :model="editData">
      <el-form-item v-if="editType===1" :label-width="80" prop="lineRatio">
        <template #label>
          <div class="tw-w-full tw-flex tw-items-center tw-justify-end">
            <span>集线比</span>
            <el-tooltip content="任务外呼并发数与签入坐席数的比率，在外呼时，任务外呼并发数=签入坐席数*集线比，但≤任务锁定并发">
              <el-icon :size="'var(--el-font-size-base)'" color="var(--primary-black-color-400)" class="tw-ml-[2px]"><SvgIcon name="warning"/></el-icon>
            </el-tooltip>
            <span>：</span>
          </div>
        </template>
        <el-input-number v-model="editData.lineRatio" style="width: 100%" :controls="false" :precision="2" :min="1" :max="20000" placeholder="请输入集线比，1-20000"/>
      </el-form-item>
      <el-form-item v-if="editType===2" label="坐席占用等级：" :label-width="100" prop="occupyRate">
        <el-select v-model="editData.occupyRate" class="tw-w-full" placeholder="请选择坐席占用等级">
          <el-option v-for="item in occupyRateList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="editType===3" :label-width="120" prop="virtualSeatRatio">
        <template #label>
          <div class="tw-w-full tw-flex tw-items-center tw-justify-end">
            <span>虚拟坐席系数</span>
            <el-tooltip content="签入坐席全忙时，任务将以额外并发进行外呼。额外并发=集线比“签入坐席数“虚拟坐席系数">
              <el-icon :size="'var(--el-font-size-base)'" color="var(--primary-black-color-400)" class="tw-ml-[2px]"><SvgIcon name="warning"/></el-icon>
            </el-tooltip>
            <span>：</span>
          </div>
        </template>
        <el-input-number v-model="editData.virtualSeatRatio" style="width: 100%" :controls="false" :precision="2" :min="0" :max="100" placeholder="请输入虚拟坐席系数，0-100"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="editVisible=false" :icon="CloseBold">取消</el-button>
        <el-button :loading="editLoading" type="primary" @click="confirmEdit" :icon="Select">修改</el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog
    v-model="blockVisible"
    width="960px"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{taskRestrictReadonly ? '查看' : '编辑'}}屏蔽地区</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
    >
    <CitySettingBox
      :taskRestrictData="taskRestrictData"
      :selectedOperatorList="selectedOperatorList"
      :readonly="taskRestrictReadonly"
      loadByCity
      @update:data="handleCityUpdate"
    />
    </el-scrollbar>
    <template v-if="editPermission" #footer>
      <span class="dialog-footer">
        <el-button @click="blockVisible=false" :icon="CloseBold">取消</el-button>
        <el-button v-if="!taskRestrictReadonly" :loading="loading" type="primary" @click="confirmBlock" :icon="Select">确认</el-button>
        <el-button v-else type="primary" @click="taskRestrictReadonly=false">
          <el-icon :size="16" class="tw-mr-1"><SvgIcon name="edit" color="#fff"></SvgIcon></el-icon>
          编辑
        </el-button>
      </span>
    </template>
  </el-dialog>
  <RestSeatDialog
    v-model:visible="seatVisible"
    :list="seatRestList"
  />
  <TaskSmsDialog
    v-model:visible="smsVisible"
    :type="smsShowType"
    :data="currentTask"
  />
  <TaskVariableDialog
    v-model:visible="variableVisible"
    :data="currentTask"
  ></TaskVariableDialog>
  <RelatedListDialog
     title="挂载黑名单"
    :list="blackList||[]"
    :gridNum="1"
    v-model:visible="blackListVisible"
  />
</template>

<script lang="ts" setup>
import { reactive, computed, ref, watch, onUnmounted, defineAsyncComponent, shallowRef } from 'vue';
import TagsBox from '@/components/TagsBox.vue'
import { TaskManageModel, TaskManageItem, TaskStatusEnum, TaskTypeEnum, CallTeamHandleEnum, CallTeamPushEnum, ManualTaskManageStatics, ManualTaskManageStaticsOrigin, OccupyRateEnum } from '@/type/task'
import { formatNumber1, formatNumber, findValueInEnum, enum2Options, formatNumberPercent, copyText } from '@/utils/utils'
import { CloseBold, Select, ArrowUpBold, ArrowDownBold, } from '@element-plus/icons-vue'
import { useTaskStore } from '@/store/taskInfo'
import { SeatTeam } from '@/type/seat'
import { RestrictModal, OperatorEnum, RestrictModalOrigin } from '@/type/common'
import { useGlobalStore } from '@/store/globalInfo'
import TooltipBox from '@/components/TooltipBox.vue'
import { aiOutboundTaskModel } from '@/api/ai-report'
import { ElMessage } from 'element-plus'
import routeMap from '@/router/asyncRoute/route-map'
import { useUserStore } from "@/store/user";
import RestSeatDialog from './RestSeatDialog.vue'
import Confirm from '@/components/message-box'
import to from 'await-to-js'
import dayjs from 'dayjs'
import TaskSmsDialog from './TaskSmsDialog.vue'
import TaskVariableDialog from './TaskVariableDialog.vue'
import RelatedListDialog from '@/components/RelatedListDialog.vue'
import { traceApi } from '@/utils/trace';
import { BlackListGroupItem, } from '@/type/dataFilter'
import { merchantBlacklistModel } from '@/api/data-filter'

const CitySettingBox = defineAsyncComponent({ loader:() => { return import('@/components/CitySettingBox.vue')}})

const props = defineProps<{
  loading: boolean
  currentTask: TaskManageItem;
  taskRealtimeData?: ManualTaskManageStatics,
  readonly?: boolean,
  taskStatics:{
    text: string, percent: number, number?: number, name: string, tooltip?: string[]
  }[]
}>();
const emits = defineEmits(['update-task', 'refresh-realtime-data',])

const currentTask = reactive<TaskManageItem>(props.currentTask)
// 当前任务的类型
const taskTypeStr = computed(() => {
  return currentTask?.taskType === TaskTypeEnum['人机协同'] ? '人机协同任务' : 'AI外呼任务'
})
// 用户权限获取
const userStore = useUserStore();
const editLoading = ref(false) // 执行操作的时才使用，否则使用props.loading
const isMasterAccount = userStore.isMasterAccount || userStore.accountType !== 1
const permissions = userStore.permissions[routeMap[taskTypeStr.value].id]
const editPermission =  computed(() => permissions?.includes(routeMap[taskTypeStr.value].permissions['编辑任务']))

const globalInfo = useGlobalStore()
const isMobile = globalInfo.isMobile

const gridNum = ref(window.innerWidth >= 1600 ? 10 : 10)

const customColors = [
  { color: '#E59000', percentage: 30 },
  { color: '#165DFF', percentage: 70 },
  { color: '#13BF77', percentage: 100 },
]
const filterColor = (num: number) => {
  if (num >= 0 && num < 30) {
    return '#E59000'
  } else if (num >= 70) {
    return '#13BF77'
  } else {
    return '#165DFF'
  }
}


const isExtend = ref(false)
const filterStatusClass = (val: TaskStatusEnum) => {
  switch(val) {
    case TaskStatusEnum['进行中']: return 'green-status';
    case TaskStatusEnum['未完成']: return 'red-status';
    case TaskStatusEnum['待执行']: return 'blue-status';
    case TaskStatusEnum['已停止']: return 'orange-status';
    default: return 'blue-status'
  }
}
const  colorMap = {
  [TaskStatusEnum['进行中']]: '--primary-green-color',
  [TaskStatusEnum['未完成']]: '--primary-red-color',
  [TaskStatusEnum['待执行']]: '--primary-blue-color',
  [TaskStatusEnum['已停止']]: '--primary-orange-color',
  [TaskStatusEnum['全部']]: '--primary-blue-color',
}
const concatTimeList = (staArr: string[], endArr: string[]) => {
  if (!staArr || staArr.length < 1 || staArr.length !== endArr.length ) return []
  return staArr.map((item, index) => {
    return item + '-' + endArr[index]
  })
}

// 将任务添加缓存
const handleAddCache = () => {
  // 只读模式下不添加缓存(运营端)
  if (!!props.readonly) return
  if (!currentTask?.id) return ElMessage.warning('获取任务id失败')
  if (taskStore.taskCacheList?.find(item => item.id === currentTask.id)) return ElMessage.warning(`【${currentTask.taskName || ''}】已存在缓存队列`)
  taskStore.taskCacheList?.push({
    taskName: currentTask.taskName || '',
    taskType: currentTask.taskType || TaskTypeEnum['AI外呼'],
    id: currentTask.id
  })
  ElMessage.success(`【${currentTask.taskName || ''}】添加成功，当前共计${taskStore.taskCacheList?.length || 0}个任务`)
}

// 线路信息， 针对为负数的并发，区分主账户和非主账号展示
const lineInfo = computed(() => {
  if (!currentTask || !currentTask.lineName) return '-'
  if (currentTask.taskType !== TaskTypeEnum['AI外呼'] || !currentTask.aiAnswerNum) return currentTask.lineName
  return currentTask.lineName + ((!isMasterAccount && currentTask.aiAnswerNum < 0 ) ? '' : (' * ' + currentTask.aiAnswerNum))
})
// 屏蔽地区信息
const blockInfo = computed(() => {
  const res: string[] = []
  // 全部
  if (currentTask.allRestrictProvince && currentTask.allRestrictCity) {
    const num1 = currentTask.allRestrictProvince.split(',')?.length || 0
    const num2 = currentTask.allRestrictCity.split(',')?.length || 0
    res.push(`全部：${num1}省${num2}市`)
  }
  // 移动
  if (currentTask.ydRestrictProvince && currentTask.ydRestrictCity) {
    const num1 = currentTask.ydRestrictProvince.split(',')?.length || 0
    const num2 = currentTask.ydRestrictCity.split(',')?.length || 0
    res.push(`移动：${num1}省${num2}市`)
  }
  // 联通
  if (currentTask.ltRestrictProvince && currentTask.ltRestrictCity) {
    const num1 = currentTask.ltRestrictProvince.split(',')?.length || 0
    const num2 = currentTask.ltRestrictCity.split(',')?.length || 0
    res.push(`联通：${num1}省${num2}市`)
  }
  // 电信
  if (currentTask.dxRestrictProvince && currentTask.dxRestrictCity) {
    const num1 = currentTask.dxRestrictProvince.split(',')?.length || 0
    const num2 = currentTask.dxRestrictCity.split(',')?.length || 0
    res.push(`电信：${num1}省${num2}市`)
  }
  // 未知
  if (currentTask.unknownRestrictProvince && currentTask.unknownRestrictCity) {
    const num1 = currentTask.unknownRestrictProvince.split(',')?.length || 0
    const num2 = currentTask.unknownRestrictCity.split(',')?.length || 0
    res.push(`未知：${num1}省${num2}市`)
  }
  return  res.join('，')
})
/** 屏蔽城市 */
const blockVisible = ref(false)
const taskRestrictReadonly = ref(true)
const selectedOperatorList = ref<("全部" | OperatorEnum)[]>([])
const taskRestrictData = reactive<RestrictModal>(new RestrictModalOrigin())
// 进入屏蔽城市查看窗口
const showBlock = () => {
  const { allRestrictProvince, allRestrictCity, ydRestrictProvince, ydRestrictCity, ltRestrictProvince, ltRestrictCity, dxRestrictCity, dxRestrictProvince, virtualRestrictCity, virtualRestrictProvince, unknownRestrictCity, unknownRestrictProvince } = currentTask
  Object.assign(taskRestrictData, {
    allRestrictProvince, allRestrictCity, ydRestrictProvince, ydRestrictCity, ltRestrictProvince, ltRestrictCity, dxRestrictCity, dxRestrictProvince, virtualRestrictCity, virtualRestrictProvince, unknownRestrictCity, unknownRestrictProvince
  })
  selectedOperatorList.value = []
  taskRestrictReadonly.value = true
  blockVisible.value = true
}
// 编辑屏蔽城市，数据双向绑定更新
const handleCityUpdate = (data: RestrictModal , operators: ('全部' | OperatorEnum)[]) => {
  Object.assign(taskRestrictData, data)
  selectedOperatorList.value = operators
}
// 屏蔽城市确认执行函数
const confirmBlock = async () => {
  const params = {
    id: currentTask.id as number,
    ...taskRestrictData
  }
  editLoading.value = true
  const err = await traceApi(
    `${findValueInEnum(props.currentTask?.taskType, TaskTypeEnum)}任务-屏蔽地区`,
    params,
    aiOutboundTaskModel.setRestrictArea
  )
  editLoading.value = false
  if (!err) {
    ElMessage.success('操作成功')
    blockVisible.value = false
    refreshTaskData()
  }
}

/** 触发短信\挂机短信 */
const smsVisible = ref(false)
const smsShowType = ref(0) //  [0] 触发短信弹窗 [1] 挂机短信弹窗
const showSmsDialog = (type: number) => {
  smsVisible.value = true
  smsShowType.value = type
}

/** 短信变量 */
const variableVisible = ref(false)


/** 任务上锁 */
const unlockTask = async () => {
  if (!currentTask.id) {
    return ElMessage.warning('获取任务id失败')
  }
  const [err1] = await to(Confirm({
    text: `您确定要【解锁】任务【${currentTask.taskName}】吗?`,
    type: 'warning',
    title: '确认'
  })) as [Error | undefined, any]
  if (err1) return
  const params:{
    taskIds: string,
    groupId: string,
    taskType: TaskTypeEnum,
  } = {
    taskIds: currentTask.id + '',
    groupId: currentTask.groupId || userStore.groupId,
    taskType:currentTask.taskType!,
  }
  const err2 = await traceApi(
    `${findValueInEnum(props.currentTask?.taskType, TaskTypeEnum)}任务-解锁任务`,
    params,
    aiOutboundTaskModel.unlockTask
  )
  if (!err2) {
    ElMessage.success('操作成功')
    refreshTaskData()
  }
}

/** 集线比编辑 */
const editType = ref(0) // 0:关闭；1：集线比；2：坐席占比
const editTypeList = ['', '集线比', '坐席占比', '虚拟坐席系数']
const editVisible = ref(false)
const occupyRateList = enum2Options(OccupyRateEnum)
const editData = reactive<{
  lineRatio?: number,
  occupyRate?: OccupyRateEnum,
  virtualSeatRatio?: number,
}>({
  lineRatio: undefined,
  occupyRate: OccupyRateEnum['高'],
  virtualSeatRatio: undefined,
})
const editRatio = (val: number) => {
  editVisible.value = true
  editData.lineRatio = val || 1
  editType.value = 1
}
const editOccupyRate = (val: number) => {
  editVisible.value = true
  editData.occupyRate = val || OccupyRateEnum['高']
  editType.value = 2
}
const editVirtualSeatRatio = (val: number) => {
  editVisible.value = true
  editData.virtualSeatRatio = val || 0
  editType.value = 3
}
const confirmEdit = async () => {
  editLoading.value = true
  switch(editType.value) {
    case 1: {
      const params = {
        taskId: currentTask.id as number,
        groupId : currentTask.groupId || userStore.groupId,
        lineRatio: editData.lineRatio!,
      }
      await traceApi(
        `${findValueInEnum(props.currentTask?.taskType, TaskTypeEnum)}任务-修改集线比`,
        params,
        aiOutboundTaskModel.updateLineRatio
      )
      break;
    };
    case 2: {
      const params = {
        taskId: currentTask.id as number,
        groupId : currentTask.groupId || userStore.groupId,
        occupyRate: editData.occupyRate!,
      }
      await traceApi(
        `${findValueInEnum(props.currentTask?.taskType, TaskTypeEnum)}任务-坐席占用等级`,
        params,
        aiOutboundTaskModel.updateOccupyRate
      )
      break;
    };
    case 3: {
      const params ={
        taskId: currentTask.id as number,
        groupId : currentTask.groupId || userStore.groupId,
        virtualSeatRatio: editData.virtualSeatRatio!,
      }
      await traceApi(
        `${findValueInEnum(props.currentTask?.taskType, TaskTypeEnum)}任务-修改虚拟坐席占比`,
        params,
        aiOutboundTaskModel.updateVirtualSeatRatio
      )
      break;
    };
  }
  editLoading.value = false
  refreshTaskData()
  editVisible.value = false
  editType.value = 0
  editData.lineRatio = undefined
}

const refreshTaskData = () => {
  emits('update-task', currentTask)
}
const refreshRealData = () => {
  emits('refresh-realtime-data', currentTask)
}

const seatVisible = ref(false)
const seatRestList = ref<{
  id: number, account?: string, callSeatRestTimeStart?: string
}[]>([])
const checkRestSeat = () => {
  const data = props.taskRealtimeData?.seatDetails
  if (data && data.length > 0) {
    seatRestList.value = data
    seatVisible.value = true
  } else {
    ElMessage.warning('暂无空闲坐席')
  }
}

const blackListVisible = ref(false)
const blackList = shallowRef<string[] | null>([])
const checkblackList = () => {
  const data = currentTask?.tenantBlackList || []
  blackList.value = []
  if (data && data.length > 0) {
    blacklistOptions.value?.forEach(item => {
      if (item.id && data.includes(item.id) && item.groupName) {
        blackList.value?.push(item.groupName)
      }
    })
    blackListVisible.value = true
  } else {
    ElMessage.warning('暂无挂载黑名单')
  }
}

const callTeamList = ref<SeatTeam[]>([])
const blacklistOptions = ref<BlackListGroupItem[] | null>([])
const taskStore = useTaskStore()
watch(() => props.currentTask.id, async () => {
  Object.assign(currentTask, props.currentTask)
  if (props.currentTask.id) {
    editType.value = 0
    if (currentTask.taskType === TaskTypeEnum['人机协同']) {
      callTeamList.value = await taskStore.getCallTeamListOptions(props.currentTask?.groupId)
    }
    const groupId = props.currentTask.groupId!
    const [err1, data1]= await to(
      userStore.accountType !== 1
      ? merchantBlacklistModel.getGroupListByGroupId({groupId: groupId})
      : merchantBlacklistModel.getGroupList({}))
    blacklistOptions.value = data1 || []
  }
}, { immediate: true, })
</script>

<style scoped lang="postcss" type="text/postcss">
.task-card {
  border-radius: 4px;
  padding: 12px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  @media screen and (max-width: 600px) {
    font-padding: 6px;
  }
  &.is-row {
    flex-direction: row;
  }
  .status-box-mini {
    line-height: 15px;
    height: 19px;
    width: 41px;
  }
  .value {
    color: #646566;
    align-items: center;
    display: flex;
  }
  .trapezoid {
    -webkit-clip-path: polygon(0 0, 100% 0, 82% 100%, 18% 100%);
    clip-path: polygon(0 0, 100% 0, 82% 100%, 18% 100%);
    background-color: #fff;
    width: 92px;
    height: 17px;
    cursor: pointer;
  }
}
.task-card-main {
  width: 100%;
  padding: 0;
  background-color: #f2f3f5;
}
.task-content {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  height: 22px;
  line-height: 22px;
  width: 100%;
  overflow: hidden;
  font-size: 13px;
  @media screen and (max-width: 600px) {
    font-size: 10px;
  }
  .label {
    min-width: 48px;
    flex-grow: 0;
    flex-shrink: 0;
    text-align: justify;
    color: #969799
  }
  .value {
    flex-grow: 1;
    flex-shrink: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: justify;
    color: #969799
  }
}
.task-card-extend {
  width: 100%;
}
.name {
  font-size: 14px;
  flex-shrink: 1;
  font-weight: 600;
  text-align: justify;
  word-break: break-all;
}
.info-title {
  @media screen and (max-width: 600px) {
    font-size: 11px;
  }
}
.info-data-content {
  font-size: 20px;
  @media screen and (max-width: 600px) {
    font-size: 13px;
  }
}
.info-data-box-inner {
  padding: 4px 8px;
  height: 100%;
  justify-content: space-around;
}
</style>
