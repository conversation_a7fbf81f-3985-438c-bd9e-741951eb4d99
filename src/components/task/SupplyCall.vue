<template>
  <div class="call-supply-container">
    <div class="tw-w-full tw-bg-white tw-mb-[8px]">
      <div class="tw-grid tw-grid-cols-4 tw-gap-[8px] tw-border-b-[1px] tw-pb-[12px]">
        <div class="item">
          <el-input
            v-model.trim="searchForm.phone"
            placeholder="号码/名单编号"
            @keyup.enter="search"
            clearable
          >
          </el-input>
        </div>
        <div class="item">
          <el-select v-model="searchForm.operator" placeholder="运营商" clearable>
            <el-option v-for="item in operatorList" :key="item" :label="item" :value="item"/>
          </el-select>
        </div>
        <div class="item">
          <el-select v-model="province" placeholder="省" filterable clearable>
            <el-option v-for="item in provinceList" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item"/>
          </el-select>
        </div>
        <div class="item">
          <el-select v-model="city" placeholder="市" filterable clearable>
            <el-option v-for="item in cityList" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item"/>
          </el-select>
        </div>
        <template v-if="isExpand">
          <div class="item">
            <InputNumberBox v-model:value="searchForm.putThroughNumLeft" :max="searchForm.putThroughNumRight || Number.MAX_VALUE" placeholder="最低接通" style="width: 47%" append="次"/>
            <span>至</span>
            <InputNumberBox v-model:value="searchForm.putThroughNumRight" placeholder="最高接通" style="width: 47%" append="次" :min="searchForm.putThroughNumLeft || 0"/>
          </div>
          <div class="item">
            <InputNumberBox v-model:value="searchForm.calledNumLeft" :max="searchForm.calledNumRight || Number.MAX_VALUE" placeholder="最低拨打" style="width: 47%" append="轮"/>
            <span>至</span>
            <InputNumberBox v-model:value="searchForm.calledNumRight" placeholder="最高拨打" style="width: 47%" append="轮" :min="searchForm.calledNumLeft || 0"/>
          </div>
          <div class="item tw-col-span-2">
            <span class="tw-w-[66px] tw-shrink-0">加入时间：</span>
            <TimePickerBox
              v-model:start="searchForm.addStartTime"
              v-model:end="searchForm.addEndTime"
              placeholder="加入时间"
              :splitToday="true"
              :clearable="false"
            />
          </div>
        </template>
      </div>
      <div class="tw-mt-[12px] tw-h-[32px]">
        <div class="tw-float-left tw-flex tw-items-end">
          <el-button
            v-if="getPermission && props.currentTask?.callStatus!=TaskStatusEnum['进行中']"
            :loading="loadingCancel"
            @click="cancelCallQueue"
            class="tw-w-[110px]"
            plain
            type="primary"
          >
            <el-icon size="13px"><SvgIcon name="cancel" color="inherit"/></el-icon>
            <span>取消全部呼叫</span>
          </el-button>
          <!-- <div class="tw-ml-1 tw-font-[600] tw-text-[13px] tw-leading-[24px] tw-text-[var(--primary-black-color-600)]">
            补呼剩余：{{ total || 0 }}
          </div> -->
        </div>
        <div class="tw-float-right tw-leading-[32px]">
          <el-button type="primary" @click="clearSearchForm" link>
            <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
            <span>重置</span>
          </el-button>
          <el-button type="primary" @click="search" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
          <el-button type="primary" v-if="isExpand" @click="isExpand=false" link>收起<el-icon size="--el-font-size-base"><ArrowUp /></el-icon></el-button>
          <el-button type="primary" v-else @click="isExpand=true" link>展开<el-icon size="--el-font-size-base"><ArrowDown /></el-icon></el-button>
        </div>
      </div>
    </div>
    <el-table
      :data="tableData"
      v-loading="loading"
      :header-cell-style="tableHeaderStyle"
      :row-key="(row: TaskCallItem) => row.phoneRecordId??'' + row.callId??''"
      stripe
    >
      <el-table-column label="号码" align="center" min-width="180">
        <template #default="{ row }">
          <div class="phone-msg">
            <span>{{ filterPhone(row.phoneRecordId) + ' ' + (row.operator || '') }}</span>
            <el-tooltip content="复制" placement="right" :show-after="500">
              <el-icon :size="14" class="hover:tw-text-[var(--el-color-primary)] tw-cursor-pointer" @click="copy(row.phoneRecordId)"><SvgIcon name="copy" color="inherit"></SvgIcon></el-icon>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column property="name" label="姓名" align="center"></el-table-column> -->
      <el-table-column label="省市" align="left" min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.province || row.city ? (row.province || '') + ' ' + (row.city || '') : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="callStatus" label="呼叫状态" align="center" min-width="80">
        <template #default="{ row }">
          <span
            v-if="row?.callStatus"
            class="status-box-mini tw-mx-auto"
            :class="filterStatusStyle(row?.callStatus)"
          >
            {{row.callStatus}}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column property="calledNum" label="接通/呼叫" align="center" min-width="160">
        <template #default="{ row }">
          {{ row.calledNum ? `${row.putThroughNum || 0}/${row.calledNum || 0}` : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="addTime" label="加入时间" align="center" sortable min-width="160">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      class="stick-pagination"
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
</template>

<script lang="ts" setup>
import { TaskCallItem, TaskCallSearchModal, TaskManageItem, TaskTypeEnum, TaskStatusEnum } from '@/type/task'
import { reactive, computed, ref, watch, onActivated, } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { tableHeaderStyle } from '@/assets/js/constant'
import { aiOutboundTaskModel } from '@/api/ai-report'
import { copyText, filterPhone, findValueInEnum } from '@/utils/utils'
import { ArrowDown, ArrowUp, CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { OperatorEnum } from '@/type/common'
import PaginationBox from '@/components/PaginationBox.vue'
import { useGlobalStore } from '@/store/globalInfo'
import { ResponseData } from '@/axios/request/types'
import Confirm from '@/components/message-box'
import routeMap from '@/router/asyncRoute/route-map'
import { useUserStore } from "@/store/user";
import InputNumberBox from '@/components/InputNumberBox.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import to from 'await-to-js';
import { traceApi } from '@/utils/trace';

const props = defineProps<{
  currentTask: TaskManageItem;
  needRefresh: boolean;
}>();

const emits = defineEmits(['update:needRefresh'])
const globalStore = useGlobalStore()
const loading = ref(false)

// 用户权限获取
const userStore = useUserStore();
const permissions = userStore.permissions[routeMap[props.currentTask.taskType === TaskTypeEnum['人机协同'] ? '人机协同任务' : 'AI外呼任务'].id]
const getPermission = computed(() => {
  return permissions?.includes(routeMap[props.currentTask.taskType === TaskTypeEnum['人机协同'] ? '人机协同任务' : 'AI外呼任务'].permissions['编辑任务'])
})

const isExpand = ref(false)
// 表格和分页
const tableData = ref<TaskCallItem []>([])
const pageSizeList = [20, 50, 100, 200]
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(pageSizeList[0])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  search()
}
class SearchFormOrigin {
  taskId = props.currentTask.id
  phone = ''
  operator = undefined
  province = undefined
  city = undefined
  calledNumLeft = undefined
  calledNumRight = undefined
  putThroughNumLeft = undefined
  putThroughNumRight = undefined
  addStartTime = dayjs(props.currentTask.createTime).startOf('day').format('YYYY-MM-DD HH:mm:ss')
  addEndTime = dayjs(props.currentTask.createTime).endOf('day').format('YYYY-MM-DD HH:mm:ss')
}
const searchForm = reactive<TaskCallSearchModal>(new SearchFormOrigin())
const city = ref<string | undefined>(undefined)
const province = ref<string | undefined>(undefined)
watch(province, () => {
  searchForm.province = province.value?.split(',')[1] || undefined
  city.value = undefined
})
watch(city, () => {
  searchForm.city = city.value?.split(',')[1] || undefined
})
const provinceList = ref<string[]>([])
const provinceAllMap = ref<{ [key: string]: string[] }>({})
const cityList = computed(() => {
  return province.value ? (provinceAllMap.value[province.value] || []) : (Object.values(provinceAllMap.value).flat() || [])
})
const initData = async () => {
  await globalStore.getProvinceInfo()
  provinceAllMap.value = globalStore.provinceAllMap
  provinceList.value = globalStore.getProvinceList || []
}

const operatorList = OperatorEnum
const search = async () => {
  loading.value = true
  const [_, res] = await to(aiOutboundTaskModel.findCallSupplyList({
    ...searchForm,
    startPage: currentPage.value > 1 ? currentPage.value - 1 : 0,
    pageNum: pageSize.value
  })) as [any, ResponseData]
  tableData.value = (res?.data || []) as TaskCallItem[] || []
  total.value = res?.total || 0
  emits('update:needRefresh', false)
  loading.value = false
}
const filterStatusStyle = (status: string) => {
  switch (status) {
    case '呼叫中': return 'orange-status';
    case '呼叫成功': return 'green-status';
    case '呼叫完成': return 'green-status';
    default: return 'blue-status';
  }
}
watch(() => props.currentTask.id, () => {
  if (props.currentTask?.id && props.currentTask?.id > 0) {
    clearSearchForm()
  }
})
watch(() => props.needRefresh, n => {
  if (props.currentTask?.id && props.currentTask?.id > 0) {
    n && search()
  }
})
const clearSearchForm = () => {
  Object.assign(searchForm, new SearchFormOrigin())
  province.value = undefined
  city.value = undefined
}
// 表格区
const loadingCancel = ref(false)
const cancelCallQueue = () => {
  Confirm({ 
    text: `<p>当前不支持对筛选出的部分数据取消呼叫，将对全部数据取消呼叫，请确认后再执行！</p>
    <p class="tw-mt-1">部分号码已送呼，有可能取消呼叫失败，请知悉！</p>`,
    type: 'warning',
    title: '取消全部呼叫'
  }).then(async () => {
    const params = {
      ...searchForm,
      startPage: currentPage.value > 1 ? currentPage.value - 1 : 0,
      pageNum: pageSize.value
    }
    loadingCancel.value = true
    const err = await traceApi(
      `${findValueInEnum(props.currentTask?.taskType, TaskTypeEnum)}任务-补呼队列-取消呼叫`,
      params,
      aiOutboundTaskModel.cancelCallSupply
    )
    if (!err) {
      ElMessage.success('操作成功')
      search()
    }
    loadingCancel.value = false
  }).catch(() => {})
}
const copy =(val: string) => {
  copyText(val || '')
}


onActivated(() => {
  initData()
  search()
})

</script>

<style scoped lang="postcss" type="text/postcss">
.call-supply-container {
  padding: 16px 0 0;
  width: 100%;
  box-sizing: border-box;
  font-size: 13px;
  :deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) .el-input__wrapper {
    width: 100%;
  }
  .el-button {
    font-size: 13px;
  }
  .phone-msg {
    display: flex;
    align-items: center;
    span {
      width: 130px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-icon {
      display: none;
    }
    &:hover .el-icon {
      display: inline-block;
    }
  }
  .item {
    height: 32px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .label {
      flex-shrink: 0;
      flex-grow: 0;
      width: 30px;
    }
    :deep(.el-date-editor.el-input) {
      width: 47%;
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-table {
    font-size: 13px;
  }
  :deep(.el-table .caret-wrapper) {
    display: none;
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
  .stick-pagination {
    position: sticky;
    bottom: 0;
    left: 0;
    background-color: #fff;
    z-index: 10;
  }
}
</style>
