<template>
  <el-scrollbar v-if="currentTask && !!currentTask.id" v-loading="loading2" ref="taskDetailScrollbar" view-class="tw-bg-[#f2f3f5] tw-p-[16px] sm:tw-p-[6px]" class="tw-grow tw-overflow-y-auto">
    <div class="tw-flex tw-justify-between tw-mb-[12px] tw-items-center">
      <!-- 顶部，任务时间 -->
      <span class="tw-ml-3 tw-self-end tw-flex tw-items-center">
        <span class="info-content">{{ currentTaskStr }}</span>
        <el-tooltip content="刷新当前任务数据" placement="right" :show-after="500">
          <el-icon size="13" class="tw-cursor-pointer tw-ml-0.5" color="#165DFF" @click="handleTaskItem(currentTask)"><SvgIcon name="reset"/></el-icon>
        </el-tooltip>
      </span>
      <!-- 顶部，任务操作，启停，导入名单 -->
      <div v-if="!props.readonly" class="tw-flex tw-items-center">
        <template v-if="uploadPermission">
          <el-button link type="primary" @click="downloadTemplate" class="tw-underline tw-underline-offset-2">下载模板</el-button>
          <el-dropdown class="tw-mx-[12px]" @command="handleBatchOperator">
            <el-button>
              <el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="upload"></SvgIcon></el-icon>
              <span>导入名单</span>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="file">文件导入</el-dropdown-item>
                <el-dropdown-item command="single">单个导入</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        <el-upload
          v-show="false"
          ref="uploadFileBtn"
          :action="actionUrl"
          :headers="headerInfo"
          accept=".csv,.xls,.xlsx"
          :http-request="uploadFileAction"
        >
          <el-button class="upload-btn" >
            <el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="upload"></SvgIcon></el-icon>
            导入名单
          </el-button>
        </el-upload>
        <el-button v-if="currentTask.callStatus == TaskStatusEnum['进行中'] && startPermission" type="danger" class="normal-btn" :loading="loading3" @click="handleStop">
          <el-icon :size="16"><SvgIcon name="stop2"></SvgIcon></el-icon>
          <span>停止任务</span>
        </el-button>
        <el-button v-if="!currentTask.ifLock && currentTask.callStatus && ![TaskStatusEnum['进行中']].includes(currentTask.callStatus) && startPermission" class="normal-btn" type="primary" @click="callVisible = true">
          <el-icon :size="16" color="#fff"><SvgIcon name="play-fill" color="inherit"></SvgIcon></el-icon>
          <span>开始任务</span>
        </el-button>
      </div>
    </div>
    <!-- 任务详情卡片，任务完成情况，人机协同实时状态 -->
    <TaskDataCard
      :loading="loading1"
      :currentTask="currentTask"
      :readonly="props.readonly"
      :taskStatics="taskStatics"
      :taskRealtimeData="taskRealtimeData"
      @update-task="updateTask"
      @refresh-realtime-data="refreshRealtimeData(currentTask)"
    >
      <template v-slot:btn>
        <div v-if="!!props.isFromList" class="tw-flex tw-items-center">
          <el-button link type="primary" @click="handleNextTask(-1)" class="next-btn">上一个</el-button>
          <el-button link type="primary" @click="handleNextTask(1)" class="next-btn">下一个</el-button>
        </div>
      </template>
    </TaskDataCard>
    <div v-if="!props.readonly" class="tw-mt-[12px] tw-flex">
      <el-radio-group v-model="taskDataType" >
        <el-radio-button :label="1">计划</el-radio-button>
        <el-radio-button :label="2">数据</el-radio-button>
      </el-radio-group>
    </div>
    <div class="tw-rounded-[8px] tw-mt-[12px] tw-p-[16px] sm:tw-p-[6px] tw-bg-white tw-relative">
      <div v-if="taskDataType === 1" v-loading="loading2">
        <el-tabs v-model="active">
          <el-tab-pane label="首呼队列" :name="1"></el-tab-pane>
          <el-tab-pane label="补呼队列" :name="2"></el-tab-pane>
          <el-tab-pane label="名单管理" :name="3"></el-tab-pane>
          <el-tab-pane label="通话记录" :name="4"></el-tab-pane>
        </el-tabs>
        <keep-alive>
          <CallQueue v-if="active == 1" v-model:needRefresh="needRefresh" :currentTask="currentTask"/>
          <SupplyCall v-else-if="active == 2" v-model:needRefresh="needRefresh" :currentTask="currentTask"/>
          <NameList v-else-if="active == 3" v-model:needRefresh="needRefresh" :currentTask="currentTask"/>
          <CallLog v-else v-model:needRefresh="needRefresh" :currentTask="currentTask"/>
        </keep-alive>
      </div>
      <div v-else>
        <div class="tw-text-left tw-text-[16px] tw-font-[600] tw-text-[--primary-black-color-600] tw-h-[30px]">数据统计</div>
        <TaskChartBox v-model:needRefresh="needRefresh" :currentTask="currentTask"></TaskChartBox>
      </div>
    </div>
  </el-scrollbar>
  <CallTaskDialog
    v-model:visible="callVisible"
    :currentTask="currentTask"
    @confirm="confirmCallVisible"
  ></CallTaskDialog>
  <SingleUploadDialog
    v-model:visible="singleUploadVisible"
    :data="currentTask!"
    @confirm="confirmSingleUploadVisible"
  ></SingleUploadDialog>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, watch, defineAsyncComponent, } from 'vue'
import { TaskManageItem, TaskStatusEnum, TaskTypeEnum, ManualTaskManageStatics, TaskManageRates, ManualTaskManageStaticsOrigin, OccupyRateEnum } from '@/type/task'
import { findValueInEnum, getToken } from '@/utils/utils'
import { generateExcelByAoa } from '@/utils/export'
import routeMap from '@/router/asyncRoute/route-map'
import { useUserStore } from "@/store/user";
import { ElMessage, UploadFile, UploadRequestOptions } from 'element-plus'
import { aiOutboundTaskModel } from '@/api/ai-report'
import { useGlobalStore } from '@/store/globalInfo'
import dayjs from 'dayjs'
import Confirm from '@/components/message-box'
import CallTaskDialog from './CallTaskDialog.vue'
import TaskChartBox from './TaskChartBox.vue'
import TaskDataCard from './TaskDataCard.vue'
import SingleUploadDialog from './SingleUploadDialog.vue'
import to from 'await-to-js';
import { useTaskStore } from '@/store/taskInfo'
import { SystemVariableEnum } from '@/type/merchant'
import { trace, traceApi } from '@/utils/trace';

const CallQueue = defineAsyncComponent({loader: () => import('./CallQueue.vue')})
const SupplyCall = defineAsyncComponent({loader: () => import('./SupplyCall.vue')})
const NameList = defineAsyncComponent({loader: () => import('./NameList.vue')})
const CallLog = defineAsyncComponent({loader: () => import('./CallLog.vue')})

const props = defineProps<{
  currentTask: TaskManageItem;
  isFromList?: boolean; // 是否来自列表形式展示的任务详情，若是会显示：上一个下一个按钮
  needRefresh: boolean;
  readonly?: boolean; // 设为readonly后，仅展示统计部分
}>();
const emits = defineEmits(['update-task', 'update:needRefresh', 'update-change', 'close'])

// 正在加载
const globalStore = useGlobalStore()
const taskStore = useTaskStore()
const loading1 = ref(false) // 获取任务实时数据
const loading2 = ref(false) // 上传名单loading
const loading3 = ref(false) // 开始结束按钮loading

// 当前任务入参
const currentTask = reactive(props.currentTask)
// 当前任务的任务类型
const taskTypeStr = computed(() => {
  return currentTask?.taskType === TaskTypeEnum['人机协同'] ? '人机协同任务' : 'AI外呼任务'
})
// 用户权限获取
const userStore = useUserStore();
const permissions = userStore.permissions[routeMap[taskTypeStr.value].id]
const startPermission =  computed(() => permissions?.includes(routeMap[taskTypeStr.value].permissions['启停任务']))
const uploadPermission =  computed(() => permissions?.includes(routeMap[taskTypeStr.value].permissions['导入名单']))

const needRefresh = ref(props.needRefresh)
// 当前任务，顶部显示的时间
const currentTaskStr = computed(() => {
  if (!currentTask.callStatus || TaskStatusEnum['待执行'] === currentTask.callStatus) {
    return currentTask.createTime ? '于 ' + (dayjs(currentTask.createTime).format('YYYY-MM-DD HH:mm:ss') || '-') + ' 创建' : ''
  } else if (TaskStatusEnum['进行中'] === currentTask.callStatus) {
    return currentTask.taskStartTime ? '于 ' + (dayjs(currentTask.taskStartTime).format('YYYY-MM-DD HH:mm:ss') || '-') + ' 启动' : ''
  } else {
    return currentTask.taskEndTime ? '于 ' + (dayjs(currentTask.taskEndTime).format('YYYY-MM-DD HH:mm:ss') || '-') + ' 停止' : (dayjs(currentTask.updateTime).format('YYYY-MM-DD HH:mm:ss') || '-') + ' 更新'
  }
})

// 触达率等数据
const taskStatics = ref<{
  text: string, percent: number, number?: number, name: string, tooltip?: string[],
}[]>([])
const handleTaskItem = async (item: TaskManageItem) => {
  emits('update:needRefresh', false)
  if (!item?.id || item.id < 0) return Object.assign(currentTask, item)
  needRefresh.value = true
  // 获取任务的意向客户范围
  !props.readonly && taskStore.getIntentionClassScope(props.currentTask?.groupId, userStore.accountType) // 更新意向客户范围
  // 更新任务数据
  const [_, data1] = await to(aiOutboundTaskModel.findTaskByIds(item.id)) as [Error | null , TaskManageItem[]]
  (data1 && data1.length > 0) ? Object.assign(currentTask, data1[0]) : Object.assign(currentTask, item)
  if (!(currentTask && currentTask.id && currentTask.groupId)) return
  taskStatics.value = [
    { text:'已完成名单', percent: 0, name: '完成率', number: undefined},
    { text:'名单呼叫', percent: 0, name: '触达率', number: undefined},
    { text:'名单接通', percent: 0, name: '接通率', number: undefined},
    { text:'意向客户数', percent: 0, name: '意向率', number: undefined},
  ]
  let [err, data] = await to(aiOutboundTaskModel.getRates({
    taskId: currentTask.id as number,
    groupId: currentTask.groupId || '',
  })) as [Error | null, TaskManageRates]
  if (err)  {
    data = {
      calledNum: 0,
      reachRate: 0,
      finishedNum: 0,
      finishedRate: 0,
      putThroughNum: 0,
      putThroughRate: 0,
      phoneIntentionRate: 0,
      phoneIntentionNum: 0,
    }
  }
  taskStatics.value = [
    { text:'已完成名单', percent: data.finishedRate || 0, name: '完成率', number: data.finishedNum??undefined, tooltip: ['已完成名单：首呼+补呼完成的名单数', '完成率：已完成名单/导入名单'],},
    { text:'名单呼叫', percent: data.reachRate || 0, name: '触达率', number: data.calledNum??undefined, tooltip: ['名单呼叫：首呼完成的名单数', '触达率：名单呼叫/导入名单'],},
    { text:'名单接通', percent: data.putThroughRate || 0, name: '接通率', number: data.putThroughNum??undefined, tooltip: ['名单接通：接通次数>=1的名单数', '接通率：名单接通/名单呼叫'], },
    { text:'意向客户数', percent: data.phoneIntentionRate || 0, name: '意向率', number: data.phoneIntentionNum??undefined, tooltip: [`意向客户数：意向分类=${taskStore.intentionClassScope || ''}的名单数`, '意向率：意向客户数/名单接通'], },
    { text:'短信触发数', percent: item.triggerSmsNumber && item.putThroughPhoneNum ? +(item.triggerSmsNumber / item.putThroughPhoneNum * 100).toFixed(2) : 0, name: '触发率', number: item.triggerSmsNumber??undefined, 
      tooltip: ['短信触发数：触发短信+挂机短信的触发名单数', '短信触发率：短信触发数/名单接通数']
    },
    {
      text:'短信成功数',
      percent: item.triggerSmsNumber && item.sendSmsNumber ? +(item.sendSmsNumber / item.triggerSmsNumber * 100).toFixed(2) : 0,
      name: '成功率',
      number: item.sendSmsNumber??undefined,
      tooltip: ['短信发送成功数：短信状态为“发送成功”的数量', '短信成功率：短信发送成功数/短信触发次数']
    },
  ]
  currentTask.taskType === TaskTypeEnum['人机协同'] && refreshRealtimeData(item)
}
// 人机协同数据
const taskRealtimeData = reactive<ManualTaskManageStatics>(new ManualTaskManageStaticsOrigin())
const refreshRealtimeData = async (item: TaskManageItem) => {
  if (!item.id || !item.groupId) return
  loading1.value = true
  const [err1, data] = await to(aiOutboundTaskModel.getManualStatistic({
    taskId: item.id as number,
    groupId: item.groupId || '',
  }))
  const [err2, allManualConcurrency] = await to(aiOutboundTaskModel.getAllManualConcurrency())
  Object.assign(taskRealtimeData, new ManualTaskManageStaticsOrigin(), data, {
    realConcurrent: allManualConcurrency ? allManualConcurrency[currentTask.id!] || 0 : 0
  })
  loading1.value = false
}

const downloadTemplate = async () => {
  const arr: string[] = currentTask.variableSms?.flatMap(item => {
    if (item.variableName && !findValueInEnum(item.variableName, SystemVariableEnum)) {
      return [item.variableName]
    } else {
      return []
    }
  }) || []
  generateExcelByAoa([
    ['姓名', '手机号', '备注', ...arr]
  ], `${currentTask.taskName}导入模板.xlsx`, )
}

// 导入名单
const uploadFileBtn = ref()
const handleBatchOperator = (type?: string) => {
  if (type === 'single') {
    singleUploadVisible.value = true
  }
  if (type === 'file') {
    (document.querySelector('.upload-btn') as HTMLButtonElement)?.click();
  }
}
const headerInfo = { token: getToken(), }
const actionUrl = computed(() => {
  let baseURL = location.protocol === 'https:'
    ? import.meta.env.VITE_API_ORIGIN_HTTPS
    : import.meta.env.VITE_API_ORIGIN_HTTP
  return baseURL + `AiSpeech/aiOutboundTask/importPhonesFromExcel?id=${currentTask.id}`
})
// 批量上传接口调用和处理
const uploadFileAction = async (opt: UploadRequestOptions) => {
  loading2.value = true
  const data = new FormData()
  data.append('file', opt.file)
  await trace({
    page: `${findValueInEnum(props.currentTask.taskType, TaskTypeEnum)}任务-导入名单-文件上传: 开始`,
    params: { taskId: currentTask.id!, file: opt.file.name || '' },
  })
  const [err, _] = await to((aiOutboundTaskModel.importPhones(data, {
    id: currentTask.id!
  })))
  await trace({
    page: `${findValueInEnum(props.currentTask.taskType, TaskTypeEnum)}任务-导入名单-文件上传: 完成`,
    params: err || null,
  })
  !err && ElMessage({
    message: `${opt.file.name || ''}文件上传成功`,
    type: 'success',
  })
  needRefresh.value = true
  loading2.value = false
}

const singleUploadVisible = ref(false)
const confirmSingleUploadVisible = () => {
  needRefresh.value = true
}
// 开始停止任务
const callVisible = ref(false)
const confirmCallVisible = () => {
  callVisible.value = false
  !!props.isFromList && emits('close')
  emits('update-task', !!props.isFromList ? currentTask.callStatus : TaskStatusEnum['进行中'])
}
// 停止任务
const handleStop = async () => {
  const res = await aiOutboundTaskModel.findTaskByIds(currentTask.id!) as TaskManageItem[]
  if (res && res[0].callStatus !== TaskStatusEnum['进行中']) {
    !!props.isFromList && emits('close')
    emits('update-task', !!props.isFromList ? currentTask.callStatus : TaskStatusEnum['未完成'])
    return ElMessage.warning('任务已停止')
  }
  Confirm({
    text: `您确定要停止任务【${currentTask.taskName}】吗?`,
    type: 'warning',
    title: '停止确认'
  }).then(async () => {
    const params = {
      taskId: currentTask.id as number,
      callStatus: TaskStatusEnum['已停止']
    }
    loading3.value = true

    const err = await traceApi(
      `${findValueInEnum(props.currentTask.taskType, TaskTypeEnum)}任务-停止任务`,
      params,
      currentTask.taskType === TaskTypeEnum['人机协同']
        ? aiOutboundTaskModel.startMixTask
        : aiOutboundTaskModel.startAiTask
    )
    loading3.value = false
    if (!err) {
      ElMessage.success('操作成功')
      !!props.isFromList && emits('close')
      emits('update-task', !!props.isFromList ? currentTask.callStatus : TaskStatusEnum['未完成'])
    }
  }).catch(() => {})
}

const updateTask = () => {
  emits('update-task', currentTask.callStatus)
}

// 切换上一个下一个
const handleNextTask = (flag: number) => {
  emits('update-change', flag)
}

const active = ref(1) // 默认显示的队列
const taskDataType = ref(!props.readonly ? 1 : 2) // 任务详情，1：计划；2：数据；

const taskDetailScrollbar = ref()
watch(() => props.needRefresh, n => {
  if (n) {
    handleTaskItem(props.currentTask)
  }
}, {immediate: true})

</script>

<style scoped lang="postcss" type="text/postcss">

  :deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
    background-color: rgba(0, 0, 0, 0) !important;
  }

  .el-tabs :deep(.el-tabs__item){
    padding-bottom: 10px;
    font-size: 16px;
    height: 30px;
    line-height: 20px;
  }
  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
    border: none;
    background-color: #E6E6E6;
  }
  :deep(.el-tabs__nav-prev), :deep(.el-tabs__nav-next) {
    line-height: 60px;
    font-size: 16px;
  }
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
  .next-btn {
    padding: 2px 8px 6px;
  }
</style>
