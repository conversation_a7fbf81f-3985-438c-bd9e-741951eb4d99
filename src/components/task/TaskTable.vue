<template>
  <div class="tw-flex tw-flex-col tw-overflow-hidden tw-bg-[#fff] tw-flex-grow tw-flex-shrink">
    <div v-if="props.showSelectTask"class="tw-mb-[8px] tw-px-[12px] tw-flex tw-justify-between tw-items-center tw-text-[13px]">
      <div class="tw-flex tw-items-center">
        <el-checkbox
          v-model="isSelectAll"
          :indeterminate="selectData&&selectData?.length>0&&selectData?.length<total"
          @change="handleSelectAll"
        >全部数据</el-checkbox>
        <span class="tw-text-[var(--primary-black-color-300)] tw-ml-1">已选：</span>
        <span class="tw-text-[var(--primary-black-color-300)]">{{ selectData?.filter(item => item.id && item.id > 0)?.length || 0 }}</span>
        <span class="tw-text-[var(--primary-black-color-300)]">/</span>
        <span class="tw-text-[var(--primary-black-color-300)]">{{ props.showTotal && total ? (total - 1) || 0 : total || 0 }}</span>
      </div>
      <div>
        <slot name="btn" :rows="selectData?.filter(item => item.id && item.id > 0) || []"></slot>
      </div>
    </div>
    <div v-if="props.showSelectColumn" class="tw-mb-[6px] tw-px-[16px] tw-flex">
      <ColumnsSelection v-model:columnsSelected="selectColumns" :columnsAll="columnsAll"/>
    </div>
    <el-table
      :row-style="getRowStyle"
      :data="tableTempData"
      :header-cell-style="{background:'#F7F8FA', color: '#646566'}"
      class="tw-grow"
      ref="tableRef"
      border
      @sort-change="handleSortChange"
      row-key="id"
    >
      <el-table-column v-if="props.showSelectTask" width="36" align="left"  fixed="left">
        <template #header>
          <el-checkbox
            v-if="tableTempData.length>0"
            :model-value="selectIdsInCurrentPage?.length===tableTempData?.length"
            :indeterminate="selectIdsInCurrentPage?.length>0 && selectIdsInCurrentPage?.length<tableTempData?.length"
            @change="handleSelect()"
          ></el-checkbox>
        </template>
        <template #default="{ row }">
          <el-checkbox
            v-show="row.id && row.id > 0"
            :model-value="selectIdsInCurrentPage?.includes(row.id)"
            @change="handleSelect(row)"
          ></el-checkbox>
        </template>
      </el-table-column>
      <el-table-column property="taskName" label="任务名称" align="left" :width="isMobile ? 190 : 280" show-overflow-tooltip fixed="left" key="taskName">
        <template #default="{ row, $index }">
          <div class="tw-flex tw-items-center" :class="props.currentId===row.id ? 'tw-text-[#165DFF]':''" @click="handleTaskNameAction(row, $index)">
            <el-icon v-if="!!row.ifLock" :size="13" class="tw-mr-[3px]">
              <SvgIcon name="lock" color="inherit"/>
            </el-icon>
            <span class="tw-truncate">
              {{ row.taskName || '-' }}
            </span>
            <el-icon v-if="!!row.nextDayCall" class="tw-ml-[4px]" :size="16" color="#165DFF"><SvgIcon name="nextday"/></el-icon>
            <el-icon v-if="row.smsTemplateAbnormal == 1 && row.callStatus === '未完成'" class="tw-ml-[8px]" color="#E64B17" :size="20"><SvgIcon name="sms-fill"/></el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="accountType!=0" property="id" label="任务ID" align="left" :width="isMobile ? 60 : 90">
        <template #default="{ row }">
          {{ row.id && row.id > 0 ? row.id : '-' }}
        </template>
      </el-table-column>
      <template v-if="accountType===0">
        <el-table-column property="taskType" label="任务类型" align="left" min-width="80" :formatter="formatterEmptyData">
          <template #default="{ row }">
            {{ findValueInEnum(row.taskType, TaskTypeEnum)||'-' }}
          </template>
        </el-table-column>
      </template>
      <el-table-column property="speechCraftName" label="话术名称" align="left" :min-width="isMobile ? 200 : 240">
        <template #default="{ row }">
          <el-tooltip placement="top" trigger="click" :disabled="!row.speechCraftName">
            <template #content> <div class="tw-max-w-[20vw]">{{ row.speechCraftName }}</div></template>
            <div class="tw-line-clamp-1">
              {{ row.speechCraftName || '-' }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column v-if="props.showBatchStatus" property="secondaryIndustry" label="话术行业" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="callStatus" label="任务状态" align="center" min-width="80">
        <template #default="{ row }">
          <span v-if="row.callStatus" class="status-box-mini tw-mx-auto" :class="getStatusStyle(row.callStatus)">{{ TaskStatusEnum[row.callStatus as TaskStatusEnum] }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column property="lineName" label="外呼线路" align="left" :min-width="isMobile ? 200 : 240" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="aiAnswerNum" :label="props.taskType === TaskTypeEnum['AI外呼'] ? '锁定并发' : '理论并发'" align="left" sortable="custom" :min-width="isMobile ? 80 : 100" :formatter="(row: any, column : any, cellValue: any) => formatNumber(cellValue)">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <template v-if="accountType!==0">
        <el-table-column property="startWorkTimeList" label="拨打时段" align="left" min-width="240" :formatter="formatterEmptyData">
          <template #default="{ row }">
            <TagsBox v-if="row.startWorkTimeList && row.endWorkTimeList" :key="row.id" :tagsArr="concatTimeList(row.startWorkTimeList, row.endWorkTimeList)" tagsName="拨打时段" :tagsNum="2"></TagsBox>
          </template>
        </el-table-column>
        <el-table-column property="allRestrictProvince" label="屏蔽地区" align="left" min-width="160" :formatter="formatterEmptyData">
          <template #default="{ row }">
            <el-button v-if="row.id && row.id > 0" link type="primary" @click="showBlock(row)">
              {{ getBlockInfo(row) || '无' }}
            </el-button>
          </template>
        </el-table-column>
      </template>
      <el-table-column property="isAutoStop" label="是否止损" align="center" width="80" :formatter="formatterEmptyData">
        <template #default="{ row }">
          {{ findValueInStatus(row.isAutoStop) || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="taskEndTime" label="止损时间" align="center" min-width="160" :formatter="formatterEmptyData">
        <template #default="{ row }">
          {{ row.taskEndTime && row.isAutoStop ? row.taskEndTime : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="autoReCall" label="自动补呼" align="center" width="80" :formatter="formatterEmptyData">
        <template #default="{ row }">
          {{ findValueInStatus(row.autoReCall) || '-' }}
        </template>
      </el-table-column>
      <template v-if="accountType!==0">
        <el-table-column property="callRatioType" label="资源分配方式" align="center" width="120" :formatter="formatterEmptyData">
          <template #default="{ row }">
            {{ row.autoReCall ? (row.callRatioType === 1 ? '首呼优先分配' : '按比例分配') : '-' }}
          </template>
        </el-table-column>
        <el-table-column property="firstRecallTime" label="补呼间隔" align="left" width="120" :formatter="formatterEmptyData" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.autoReCall && row.firstRecallTime ? (row.firstRecallTime ? row.firstRecallTime + 'min' : '-') + (row.secondRecallTime ? '；' + row.secondRecallTime + 'min' : '') : '-' }}
          </template>
        </el-table-column>
        <template v-if="props.taskType!==TaskTypeEnum['AI外呼']">
          <el-table-column property="callTeamIds" label="坐席组" align="left" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip>
            <template #default="{ row }">
              {{ callTeamList?.flatMap(item=>row.callTeamIds?.includes(item.id!) ? [item.callTeamName!]:[]).join('、') || '-' }}
            </template>
          </el-table-column>
          <el-table-column property="callTeamPushType" label="坐席推送方式" align="center" width="120" :formatter="formatterEmptyData" show-overflow-tooltip>
            <template #default="{ row }">
              {{ findValueInEnum(row.callTeamPushType, CallTeamPushEnum)||'-' }}
            </template>
          </el-table-column>
          <el-table-column property="callTeamHandleType" label="坐席处理方式" align="center" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip>
            <template #default="{ row }">
              {{ findValueInEnum(row.callTeamHandleType, CallTeamHandleEnum)||'-' }}
            </template>
          </el-table-column>
          <el-table-column property="lineRatio" label="集线比" align="left" min-width="80" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
          <el-table-column property="occupyRate" label="坐席占用等级" align="center" width="120" :formatter="formatterEmptyData" show-overflow-tooltip>
            <template #default="{ row }">
              {{ findValueInEnum(row.occupyRate, OccupyRateEnum)||'-' }}
            </template>
          </el-table-column>
          <el-table-column property="virtualSeatRatio" label="虚拟坐席系数" align="left" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
        </template>
      </template>
      <el-table-column property="tenantBlackList" label="黑名单" align="center" min-width="80" :formatter="formatterEmptyData">
        <template #default="{ row }">
          <el-button v-if="row.tenantBlackList && row.tenantBlackList.length" link type="primary" @click="checkblackList(row)">
            {{ row.tenantBlackList?.length }}
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column property="phoneNum" label="导入名单数" align="left" sortable="custom" :min-width="isMobile ? 80 : 120" :formatter="(row: any, column : any, cellValue: any) => formatNumber(cellValue)">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="剩余名单" property="remainNum" sortable="custom" align="left" min-width="120">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber((row.callingPhoneNum??0) + (row.recallingPhoneNum??0)) }}
        </template>
      </el-table-column>
      <el-table-column property="callingPhoneNum" label="首呼剩余" sortable="custom" align="left" min-width="120" :formatter="(row: any, column : any, cellValue: any) => formatNumber(cellValue)">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="recallingPhoneNum" label="补呼剩余" sortable="custom" align="left" min-width="120" :formatter="(row: any, column : any, cellValue: any) => formatNumber(cellValue)">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="calledPhoneNum" label="已呼名单" align="left" sortable="custom" :min-width="isMobile ? 80 : 120" :formatter="(row: any, column : any, cellValue: any) => formatNumber(cellValue)">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="putThroughPhoneNum" label="接通数" align="left" sortable="custom" :min-width="isMobile ? 80 : 120" :formatter="(row: any, column : any, cellValue: any) => formatNumber(cellValue)">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="finishedPhoneRate" label="名单执行进度" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <div class="tw-flex tw-items-center">
            <span class="tw-w-[48px] sm:tw-w-[36px] tw-flex-grow-0 tw-flex-shrink-0">{{ formatNumberPercent(row.finishedPhoneRate, 1) || '-' }}</span>
            <span class="tw-flex-grow">{{formatNumber1(row.finishedPhoneNum) + '/' + formatNumber1(row.phoneNum)}}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="putThroughPhoneRate" label="名单接通率" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <div class="tw-flex tw-items-center">
            <span class="tw-w-[48px] sm:tw-w-[36px] tw-flex-grow-0 tw-flex-shrink-0">{{ formatNumberPercent(row.putThroughPhoneRate, 1) || '-' }}</span>
            <span class="tw-flex-grow">{{formatNumber1(row.putThroughPhoneNum) + '/' + formatNumber1(row.calledPhoneNum)}}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="triggerSmsRate" label="短信触发率" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <div class="tw-flex tw-items-center">
            <span class="tw-w-[48px] sm:tw-w-[36px] tw-flex-grow-0 tw-flex-shrink-0">{{ formatNumberPercent(row.triggerSmsRate, 1) || '-' }}</span>
            <span class="tw-flex-grow">{{formatNumber1(row.triggerSmsNumber) + '/' + formatNumber1(row.putThroughPhoneNum)}}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="sendSmsRate" label="短信成功率" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <div class="tw-flex tw-items-center">
            <span class="tw-w-[48px] sm:tw-w-[36px] tw-flex-grow-0 tw-flex-shrink-0">{{ formatNumberPercent(row.sendSmsRate, 1) || '-' }}</span>
            <span class="tw-flex-grow">{{formatNumber1(row.sendSmsNumber) + '/' + formatNumber1(row.triggerSmsNumber)}}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="createTime" label="创建时间" align="center" sortable="custom" :min-width="isMobile ? 120 : 160" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>
      <!-- <el-table-column v-if="accountType===0" property="account" label="创建人" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column> -->
      <el-table-column property="taskStartTime" label="开始时间" align="center" sortable="custom" :min-width="isMobile ? 120 : 160" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.taskStartTime ? dayjs(row.taskStartTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="taskEndTime" label="结束时间" align="center" sortable="custom" :min-width="isMobile ? 120 : 160" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.taskEndTime ? dayjs(row.taskEndTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="expectedFinishTime" label="预期完成时间" align="center" sortable="custom" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.expectedFinishTime ? dayjs(row.expectedFinishTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>

      <el-table-column v-if="!isMobile && accountType===0" label="操作" align="right" fixed="right" :width="accountType===0 ? 80 : 200">
        <template #default="{ row, $index }">
          <slot v-if="row.taskName !== '合计'" name="operate" :row="row" :index="$index"></slot>
        </template>
      </el-table-column>
      <el-table-column v-if="props.showBatchStatus" property="batchStatus" label="操作状态" align="center" fixed="right" width="80" key="batchStatus">
        <template #default="{ row }">
          <el-tooltip v-if="row.id && row.id > 0" placement="top" trigger="click">
            <template #content>
              <div class="tw-text-left">
                <p>最近一次操作：</p>
                <li>操作时间： {{ batchTaskInfo.expire }}</li>
                <li>操作类型： {{ batchTaskInfo.lastOperator }}</li>
              </div>
            </template>
            <span class="status-box-mini" :class="getBatchStatusStyle(row.batchStatus)">{{ row.batchStatus || '未知状态' }}</span>
          </el-tooltip>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="total < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      v-if="props.showPagination"
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total || 0"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>

  <!-- 黑名单查看弹窗 -->
  <RelatedListDialog
     title="挂载黑名单"
    :list="blackList||[]"
    :gridNum="1"
    v-model:visible="blackListVisible"
  />

</template>

<script lang="ts" setup>
import { computed, watch, ref, reactive, onActivated, onMounted, onUnmounted } from 'vue'
import dayjs from 'dayjs'
import TagsBox from '@/components/TagsBox.vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import PaginationBox from '@/components/PaginationBox.vue'
import ColumnsSelection from '@/components/ColumnsSelection.vue'
import { useGlobalStore } from '@/store/globalInfo'
import { TaskManageItem, TaskStatusEnum, TaskTypeEnum, CallTeamHandleEnum, CallTeamPushEnum, OccupyRateEnum } from '@/type/task'
import { formatNumber, formatNumber1, formatterEmptyData, findValueInEnum, handleTableSort, formatNumberPercent, findValueInStatus } from '@/utils/utils'
import { useUserStore } from '@/store/user'
import { SeatTeam } from '@/type/seat'
import { useTaskStore } from '@/store/taskInfo'
import { storeToRefs } from 'pinia'
import { BlackListGroupItem, } from '@/type/dataFilter'
import { merchantBlacklistModel } from '@/api/data-filter'
import to from 'await-to-js'
import { ElMessage } from 'element-plus'
import RelatedListDialog from '@/components/RelatedListDialog.vue'

const globalInfo = useGlobalStore()
const userInfo = useUserStore()
const accountType = userInfo.accountType
const taskStore = useTaskStore()
const { batchTaskInfo } = storeToRefs(taskStore)
const isMobile = globalInfo.isMobile

const getRowStyle = ({row}: {row: TaskManageItem}) => {
  if (row.taskName === '合计') {
    return {
      fontWeight: 600,
    }
  } else {
    return null
  }
}
// 表格-批量操作显示class
const getBatchStatusStyle = (batchStatus: string) => {
  switch(batchStatus) {
    case '等待执行': return 'orange-status';
    case '正在执行': return 'blue-status';
    case '执行成功': return 'green-status';
    case '执行失败': return 'red-status';
    default: return 'orange-status'
  }
}

// props和emits
const emits = defineEmits(['show-block', 'update:table', 'lock-task', 'go-detail',])
const props = withDefaults(defineProps<{
  tableData: TaskManageItem[]
  taskType?: TaskTypeEnum
  groupId: string // 必传，用于获取商户的坐席组信息（区分商户端当前用户和运营端中选中商户）
  showPagination?: boolean
  showSelectColumn?: boolean
  showTotal?: boolean // 是否显示合计
  showSelectTask?: boolean
  currentId?: number | string,
  showBatchStatus?: boolean
}>(), {
  showPagination: false,
  showSelectColumn: false,
  showSelectTask: false,
  showBatchStatus: false,
})

// 分页
const currentPage = ref(1)
const pageSize = ref(globalInfo.pageSize || 20)
const total = ref(0)
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  globalInfo.pageSize = s
}
const search = () => {
  emits('update:table')
}

// 列表-列名筛选
const columnsAll = computed(() => {
  const res = [
    {name: '线路名称',value: 'lineName', disabled: true},
  ]
  return res
})
const selectColumns = ref<string[]>(columnsAll.value?.map(item => item.value) || [])


// 任务已选择
const selectData = ref<TaskManageItem[] | null>(null)
const isSelectAll = ref(false) // 是否全选
const handleSelectAll = (val: boolean) => {
  if (val) {
    selectData.value = JSON.parse(JSON.stringify(tableData.value || []))
  } else {
    selectData.value = []
  }
}
const selectIdsInCurrentPage = computed(() => {
  const ids = [...tableTempData.value?.map(item => item.id) || []]
  const res: number[] = []
  selectData.value?.map(item => {
    item.id && ids.includes(item.id) && res.push(item.id)
  })
  return res
})
// 对列表进行选中操作
const handleSelect = (row?: TaskManageItem) => {
  if (!selectData.value) selectData.value = []
  if (row) {
    const index = selectData.value?.findIndex(item => item.id === row.id) ?? -1
    if ( index >= 0) {
      selectData.value?.splice(index, 1)
      isSelectAll.value = false
    } else {
      selectData.value?.push(row)
      if (selectData.value?.length === total.value) {
        isSelectAll.value = true
      }
    }
  } else {
    if (selectIdsInCurrentPage.value.length < tableTempData.value.length) {
      selectIdsInCurrentPage.value
      tableTempData.value.map(item => {
        if (item.id && !selectIdsInCurrentPage.value.includes(item.id)) {
          selectData.value?.push(item)
        }
      })
      if (selectData.value?.length === total.value) {
        isSelectAll.value = true
      }
    } else {
      const data = tableTempData.value.map(item => item.id)
      selectData.value = selectData.value?.filter(item => !data.includes(item.id)) || []
      isSelectAll.value = false
    }
  }
}

// 任务状态样式
const getStatusStyle = (val: TaskStatusEnum) => {
  switch(val) {
    case TaskStatusEnum['进行中']: return 'green-status';
    case TaskStatusEnum['未完成']: return 'red-status';
    case TaskStatusEnum['待执行']: return 'blue-status';
    case TaskStatusEnum['已停止']: return 'orange-status';
    default: return 'blue-status'
  }
}
// 屏蔽地区翻译
const getBlockInfo = (currentTask: TaskManageItem) => {
  const res: string[] = []
  // 全部
  if (currentTask.allRestrictProvince && currentTask.allRestrictCity) {
    const num1 = currentTask.allRestrictProvince.split(',')?.length || 0
    const num2 = currentTask.allRestrictCity.split(',')?.length || 0
    res.push(`全部：${num1}省${num2}市`)
  }
  // 移动
  if (currentTask.ydRestrictProvince && currentTask.ydRestrictCity) {
    const num1 = currentTask.ydRestrictProvince.split(',')?.length || 0
    const num2 = currentTask.ydRestrictCity.split(',')?.length || 0
    res.push(`移动：${num1}省${num2}市`)
  }
  // 联通
  if (currentTask.ltRestrictProvince && currentTask.ltRestrictCity) {
    const num1 = currentTask.ltRestrictProvince.split(',')?.length || 0
    const num2 = currentTask.ltRestrictCity.split(',')?.length || 0
    res.push(`联通：${num1}省${num2}市`)
  }
  // 电信
  if (currentTask.dxRestrictProvince && currentTask.dxRestrictCity) {
    const num1 = currentTask.dxRestrictProvince.split(',')?.length || 0
    const num2 = currentTask.dxRestrictCity.split(',')?.length || 0
    res.push(`电信：${num1}省${num2}市`)
  }
  // 未知
  if (currentTask.unknownRestrictProvince && currentTask.unknownRestrictCity) {
    const num1 = currentTask.unknownRestrictProvince.split(',')?.length || 0
    const num2 = currentTask.unknownRestrictCity.split(',')?.length || 0
    res.push(`未知：${num1}省${num2}市`)
  }
  return  res.join('，')
}
// 查看屏蔽地区详情
const showBlock = (currentTask: TaskManageItem) => {
  emits('show-block', currentTask, true)
}
// 点击【运营商及外呼支持范围】进入详情展示
// const lockTask = (currentTask: TaskManageItem) => {
//   currentTask.ifLock = 1- (currentTask.ifLock??0)
//   emits('lock-task', currentTask)
// }

// 查看任务挂载的黑名单
const blackListVisible = ref(false)
const blackList = ref<string[] | null>([])
const checkblackList = (currentTask: TaskManageItem) => {
  const data = currentTask?.tenantBlackList || []
  blackList.value = []
  if (data && data.length > 0) {
    blacklistOptions.value?.forEach(item => {
      if (item.id && data.includes(item.id) && item.groupName) {
        blackList.value?.push(item.groupName)
      }
    })
    blackListVisible.value = true
  } else {
    ElMessage.warning('暂无挂载黑名单')
  }
}

const handleTaskNameAction = (row: TaskManageItem, index: number) => {
  emits('go-detail', row, index)
}

// 拼接开始结束时间
const concatTimeList = (staArr: string[], endArr: string[]) => {
  if (!staArr || staArr.length < 1 || staArr.length !== endArr.length ) return []
  return staArr.map((item, index) => {
    return item + '-' + endArr[index]
  })
}

const callTeamList = ref<SeatTeam[] | null>([])
const blacklistOptions = ref<BlackListGroupItem[] | null>([])
const init = async () => {
  if (props.taskType !== TaskTypeEnum['AI外呼']) {
    callTeamList.value = await taskStore.getCallTeamListOptions(props.groupId)
  }
  const [err1, data1]= await to(
    accountType !== 1
    ? merchantBlacklistModel.getGroupListByGroupId({groupId: props.groupId})
    : merchantBlacklistModel.getGroupList({})
  )
  blacklistOptions.value = data1 || []
  pageSize.value = globalInfo.pageSize || 20
}

onMounted(() => {
  init()
})

onActivated(() => {
  init()
})

// 任务数据列表
const tableData = ref<TaskManageItem[] | null>(props.tableData || [])

// 用于对于keepalive，记录排序
const orderCol = ref('')
const orderType = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  orderCol.value = params.prop
  orderType.value = params.order
  // emits('update:sortInfo', params)
}
const tableTempData = computed(() => {
  const data = handleTableSort(tableData.value || [], orderCol.value, orderType.value)
  const res = props.showPagination ? data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value) : data
  return res
})
const tableRef = ref()
watch(() => props.tableData, () => {
  const totalRow: TaskManageItem = {
    id: -1,
    taskName: '合计',
    account: '', // 商户账号
    callingPhoneNum: 0,
    recallingPhoneNum: 0,
    calledPhoneNum: 0,
    finishedPhoneNum: 0,
    finishedPhoneRate: 0,
    aiAnswerNum: 0,
    phoneNum: 0,
    putThroughPhoneRate: 0,
    putThroughPhoneNum: 0,
    triggerSmsNumber: 0,
    sendSmsNumber: 0,
    triggerSmsRate: 0,
    sendSmsRate: 0
  }
  tableData.value = props.tableData?.map(item => {
    // 计算合计项
    totalRow.calledPhoneNum = (totalRow.calledPhoneNum || 0) + (item.calledPhoneNum || 0)
    totalRow.callingPhoneNum = (totalRow.callingPhoneNum || 0) + (item.callingPhoneNum || 0)
    totalRow.recallingPhoneNum = (totalRow.recallingPhoneNum || 0) + (item.recallingPhoneNum || 0)
    totalRow.finishedPhoneNum = (totalRow.finishedPhoneNum || 0) + (item.finishedPhoneNum || 0)
    totalRow.phoneNum = (totalRow.phoneNum || 0) + (item.phoneNum || 0)
    totalRow.aiAnswerNum = (totalRow.aiAnswerNum || 0) + (item.aiAnswerNum || 0)
    totalRow.putThroughPhoneNum = (totalRow.putThroughPhoneNum || 0) + (item.putThroughPhoneNum || 0)
    totalRow.triggerSmsNumber = (totalRow.triggerSmsNumber || 0) + (item.triggerSmsNumber || 0)
    totalRow.sendSmsNumber = (totalRow.sendSmsNumber || 0) + (item.sendSmsNumber || 0)
    // 返回并计算一些百分比
    return {
      ...item,
      remainNum: (item.callingPhoneNum??0) + (item.recallingPhoneNum??0),
      // 名单执行度，前端重新计算，后端取整了
      finishedPhoneRate: item.finishedPhoneNum && item.phoneNum ? Math.round(item.finishedPhoneNum / item.phoneNum * 1000) / 10 : undefined,
      // 名单接通率
      putThroughPhoneRate: item.putThroughPhoneNum && item.calledPhoneNum ? Math.round(item.putThroughPhoneNum / item.calledPhoneNum * 1000) / 10 : undefined,
      // 短信触发率，后端无此数据
      triggerSmsRate: item.triggerSmsNumber && item.putThroughPhoneNum ? Math.round(item.triggerSmsNumber / item.putThroughPhoneNum * 1000) / 10 : undefined,
      // 短信成功率，后端无此数据
      sendSmsRate: item.triggerSmsNumber && item.sendSmsNumber ? Math.round(item.sendSmsNumber / item.triggerSmsNumber * 1000) / 10 : undefined,
    }
  }) || []
  totalRow.finishedPhoneRate = (totalRow.finishedPhoneNum || 0) * 100 / (totalRow.phoneNum || 0)
  totalRow.putThroughPhoneRate = (totalRow.putThroughPhoneNum || 0) * 100 / (totalRow.calledPhoneNum || 0)
  totalRow.triggerSmsRate = (totalRow.triggerSmsNumber || 0) * 100 / (totalRow.putThroughPhoneNum || 0)
  totalRow.sendSmsRate = (totalRow.sendSmsNumber || 0) * 100 / (totalRow.triggerSmsNumber || 0)
  props.showTotal && props.tableData?.length && tableData.value.unshift(totalRow)
  
  total.value = tableData.value.length || 0
  isSelectAll.value = false

}, {deep: true, immediate: true})

// 监听groupId变化，重新获取商户黑名单
watch(() => props.groupId, () => {
  props.groupId && init()
})

onUnmounted(() => {
  tableData.value = null
  selectData.value = null
  isSelectAll.value = false
  callTeamList.value = null
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-table {
  font-size: var(--el-font-size-base);
  @media screen and (max-width: 600px) {
    font-size: 10px;
  }
  :deep(.cell) {
    padding: 0 8px;
    @media screen and (max-width: 600px) {
      padding: 0 2px;
    }
  }
  :deep(.caret-wrapper) {
    display: none;
  }
}
.status-box-mini {
  @media screen and (max-width: 600px) {
    width: 36px;
    font-size: 10px;
  }
}
:deep(.el-checkbox__label) {
  font-size: 13px;
}
</style>
