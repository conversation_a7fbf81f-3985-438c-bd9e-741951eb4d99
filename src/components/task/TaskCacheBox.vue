<template>
  <div v-if="taskStore.taskCacheList?.length > 0" class="task-cache-box" @click="handleShowDialog">
    <el-icon :size="22" >
      <Collection />
    </el-icon>
    <span class="dot">
      {{ taskStore.taskCacheList?.length || 0 }}
    </span>
  </div>

  <el-dialog
    v-model="dialogVisible"
    width="720px"
    align-center
    class="dialog-form"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">
        任务缓存
      </div>
    </template>
    <el-table
      :max-height="'calc(100vh - 200px)'"
      class="tw-mt-[12px]"
      :data="taskStore.taskCacheList"
      style="width: 100%"
      row-key="id"
      :header-cell-style="tableHeaderStyle"
    >
      <el-table-column align="left" label="序号" width="48">
        <template #default="{ $index }">
          {{ $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="left" property="id" label="ID" width="80"></el-table-column>
      <el-table-column align="left" property="taskName" label="任务名称" min-width="240"></el-table-column>
      <el-table-column align="left" property="taskType" label="任务类型" width="100">
        <template #default="{ row }">
          {{ findValueInEnum(row.taskType, TaskTypeEnum) || '-' }}
        </template>
      </el-table-column>
      <el-table-column align="right" property="remark" label="操作" width="80">
        <template #default="{ row }">
          <el-button type="danger" link @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="暂无缓存任务" />
      </template>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          v-if="!showLoadTask"
          class="tw-absolute tw-left-[16px] tw-bottom-[24px] tw-underline tw-underline-offset-[2px]"
          link
          type="danger"
          @click="clearAllTask"
        >
          清空任务缓存
        </el-button>
        <el-button v-else :icon="Select" type="primary" @click="loadTask" >
          导入任务
        </el-button>
        <el-button @click="cancel" :icon="CloseBold">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useTaskStore } from '@/store/taskInfo'
import { tableHeaderStyle } from '@/assets/js/constant'
import { CloseBold, Select, Collection  } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import { findValueInEnum } from '@/utils/utils'
import { TaskTypeEnum } from '@/type/task'

const taskStore = useTaskStore()
const dialogVisible = ref(false)
const emits = defineEmits(['loadTask'])
const route = useRoute();
const showLoadTask = computed(() => {
  return route.name === 'OperationTool'
})

const handleShowDialog = () => {
  dialogVisible.value = true
}
const cancel = () => {
  dialogVisible.value = false
}
const del = (item: {id?: number}) => {
  taskStore.taskCacheList = taskStore.taskCacheList.filter(v => v.id !== item.id)
}

const loadTask = () => {
  if (taskStore.taskCacheList.length === 0) return ElMessage.warning('暂无缓存任务')
  emits('loadTask', taskStore.taskCacheList?.map(v => v.id) || [], 0)
  dialogVisible.value = false
  taskStore.taskCacheList = []
}

const clearAllTask = () => {
  taskStore.taskCacheList = []
}

</script>

<style scoped lang="postcss" type="text/postcss">
.task-cache-box {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px 0 0 6px;
  background: linear-gradient(90deg, #0167FF 0%, #019FFF 100%);
  color: #fff;
  width: 44px;
  height: 44px;
  position: fixed;
  bottom: 100px;
  right: 0;
  z-index: 10;
  opacity: 0.8;
}
.dot {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: var(--primary-orange-color);
  color: #fff;
  border-radius: 50%;
  width: 15px;
  height: 15px;
  font-size: 10px;
  transition: all 0.3s ease-in-out;
}
</style>
