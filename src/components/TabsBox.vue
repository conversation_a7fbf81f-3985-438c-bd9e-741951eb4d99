<template>
   <div class="tab-box">
      <span
      v-for="(item, index) in props.tabList"
        :key="item"
        class="normal-tab"
        :class="{'active-tab': active === item}"
        @click="updateTab(item, index)"
      >
        {{ item }}
      </span>
      <slot></slot>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'
const props = defineProps<{
  tabList: string[],
  active: string | number,
  type?: string,
}>()
const active = ref(typeof props.active === 'number' ? props.tabList[props.active - 1] || props.tabList[0] : props.active || props.tabList[0])
watch(props, () => {
  active.value = typeof props.active === 'number' ? props.tabList[props.active - 1] || props.tabList[0] : props.active || props.tabList[0]
}, { deep: true })
const emits = defineEmits([
  'update:active',
])
const updateTab = (item: string, index: number) => {
  emits('update:active', props.type === 'number' ? index + 1 : item)
}

</script>

<style lang="postcss" type="text/postcss" scoped>
.tab-box {
  height: 41px;
  padding: 5px 0 0 8px;
  box-sizing: border-box;
  background: #f7f8fa;
  border-bottom: 1px solid var(--primary-black-color-200);
  display: flex;
  position: relative;
  .normal-tab {
    width: 88px;
    height: 36px;
    box-sizing: border-box;
    text-align: center;
    line-height: 36px;
    margin-right: 4px;
    border-radius: 4px 4px 0px 0px;
    border: 1px solid var(--primary-black-color-200);
    background: #F0F2F5;
    cursor: pointer;
    font-size: 16px;
    color: var(--primary-black-color-400);
  }
  .active-tab {
    border-bottom-color: #fff;
    font-weight: 550;
    color: var(--el-color-primary);
    background: #fff;
  }
}
</style>
