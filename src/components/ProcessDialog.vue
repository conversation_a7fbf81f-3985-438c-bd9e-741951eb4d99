<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    align-center
    width="600px"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ props.title || '执行结果' }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px] tw-my-[16px]"
      view-class="tw-flex tw-flex-col tw-items-center tw-leading-[24px]"
    >
      <div class="tw-flex tw-justify-between tw-w-[90%]">
        <div class="tw-text-[var(--el-color-primary)]">
          <span>成功：</span>
          <span>{{ successNumber || 0 }}</span>
        </div>
        <div class="tw-text-[var(--primary-red-color)]">
          <span>失败：</span>
          <span>{{ processInfo?.length || 0 }}</span>
        </div>
        
      </div>
      <div class="tw-w-3/4 tw-my-[8px] tw-flex">
        <span class="tw-flex-shrink-0 tw-mr-1">完成进度：</span>
        <el-progress
          :percentage="(successNumber+processInfo?.length)*totalNumber>0 ? Math.round(100*(successNumber+processInfo?.length)/totalNumber) : 0"
          class="tw-w-full"
        >
          <span>{{ successNumber+processInfo?.length }}</span>
          <span class="tw-mx-1">/</span>
          <span>{{ totalNumber }}</span>
        </el-progress>

      </div>
      <el-table
        v-if="processInfo?.length>0"
        class="tw-mt-[12px]"
        :data="processInfo"
        style="width: 100%"
        :header-cell-style="tableHeaderStyle"
      >
        <el-table-column align="left" label="序号" width="64">
          <template #default="{ $index }">
            {{ $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column align="left" property="name" label="名称" min-width="240">
          <template #default="{ row }">
            <el-tooltip placement="top" trigger="click">
              <template #content><div class="tw-w-[20vw]" v-html="row.name"></div></template>
              <div class="tw-line-clamp-2">
                {{ row.name }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="left" property="remark" label="失败原因" min-width="200">
          <template #default="{ row }">
            <el-tooltip placement="top" trigger="click">
              <template #content><div class="tw-w-[20vw]" v-html="row.remark"></div></template>
              <div class="tw-line-clamp-2">
                {{ row.remark }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <!-- 插槽，存放按钮，如导出失败 -->
        <slot name="btn"></slot>
        <el-button :loading="!isFinish" @click="visible=false" :icon="CloseBold">{{isFinish ? '关闭' : '上传中'}}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, } from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { tableHeaderStyle } from '@/assets/js/constant'
const emits = defineEmits(['update:visible'])
const props = defineProps<{
  processInfo: {name: string, remark?: string}[];
  successNumber: number;
  visible: boolean;
  totalNumber: number;
  title?: string
}>();
const visible = ref(false)
const processInfo = computed(() => props.processInfo || [])
const successNumber = computed(() => props.successNumber || 0)
const totalNumber = computed(() => props.totalNumber || 0)
watch(() => props.visible, () => {
  visible.value = props.visible
})
const isFinish = computed(() => {
  return successNumber.value + processInfo.value?.length === totalNumber.value
})
const cancel = () => {
  emits('update:visible', false)
}
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-scrollbar__bar.is-vertical) {
  top: 18px;
}
.el-table {
  font-size: 13px;
}
</style>