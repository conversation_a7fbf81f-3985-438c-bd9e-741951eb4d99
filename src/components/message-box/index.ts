import { createVNode, render } from 'vue'
import AiConfirm from './AiConfirm.vue'
const Confirm = ({ title, text, type, countdownTime, confirmText, cancelText, }: {
  title?: string,
  text: string,
  type?: string,
  countdownTime?: number,
  confirmText?: string,
  cancelText?: string,
}) => {
  const divNode = createVNode('div')
  render(divNode, document.body)
  const container = divNode.el as Element
  // 返回 Promise 对象
  return new Promise((resolve, reject) => {
    const confirmCallback = () => {
      render(null, container)
      resolve('点击确认')
    }
    const cancelCallback = () => {
      render(null, container)
      reject(new Error('点击取消'))
    }
    const VNode = createVNode(AiConfirm, { title, text, type, confirmText, cancelText, countdownTime, confirmCallback, cancelCallback })
    render(VNode, container)
  })
}
export default Confirm
