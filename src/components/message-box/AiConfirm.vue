<template>
  <div class="ai-confirm">
    <div class="wrapper" ref="target">
      <div class="wrapper-header tw-text-left tw-text-[16px] tw-font-semibold tw-h-[40px]">
        {{ title }}
      </div>
      <div class="wrapper-body">
        <el-icon v-if="type === 'warning'" :size="20" color="#E59000"><WarningFilled /></el-icon>
        <el-icon v-if="type === 'danger'" :size="20" color="#E54B17"><SvgIcon name="delete"></SvgIcon></el-icon>
        <span class="tw-ml-0.5 tw-text-justify tw-break-all" v-html="text"></span>
      </div>
      <div class="wrapper-footer tw-flex tw-justify-end tw-text-[13px]">
        <el-button @click="cancelCallback" :icon="CloseIcon" class="tw-h-[32px]">{{ cancelText }}</el-button>
        <el-button @click="confirmCallback" :disabled="remainingTime>0" :icon="SelectIcon" type="primary" class="tw-h-[32px]">{{ confirmText + (remainingTime > 0 ? `(${remainingTime})` : '') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { shallowRef } from 'vue'
import { onClickOutside } from '@vueuse/core'
import { CloseBold, Select, WarningFilled } from '@element-plus/icons-vue'
export default {
  name: 'XtxConfirm',
  components: {WarningFilled},
  props: {
    title: {
      type: String,
      default: '温馨提示'
    },
    text: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'warning'
    },
    confirmText: {
      type: String,
      default: '确认'
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    countdownTime: {
      type: Number,
      default: 0
    },
    // 确认按钮
    confirmCallback: {
      type: Function,
      default: () => {}
    },
    // 取消按钮
    cancelCallback: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      CloseBold, Select,
    }
  },
  setup (props) {
    // 点击 target 目标元素外部相当于点击了取消
    const target = shallowRef(null)
    const remainingTime = shallowRef(props.countdownTime)
    onClickOutside(target, () => {
      props.cancelCallback()
    })
    const CloseIcon = shallowRef(CloseBold)
    const SelectIcon = shallowRef(Select)
    if (props.countdownTime && props.countdownTime > 0) {
      const intervalId = setInterval(() => {
        remainingTime.value -= 1;
        if (remainingTime.value <= 0) {
          clearInterval(intervalId);
        }
      }, 1000);
    }
    
    return { target, CloseIcon, SelectIcon, remainingTime }
  }
}
</script>

<style scoped lang="postcss">
.ai-confirm {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.5);
  .wrapper {
    width: 480px;
    background: #fff;
    border-radius: 4px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    @media screen and (max-width: 600px) {
      width: 260px;
    }
    .wrapper-header,
    .wrapper-footer {
      padding: 12px 16px;
      width: 100%;
      line-height: 16px;
      .el-button {
        min-width: 66px;
        height: 32px;
        font-size: 13px;
      }
    }
    .wrapper-body {
      padding: 24px;
      font-size: 13px;
      display: flex;
      align-items: center;
      border-top: 1px solid #EBEDF0;
      border-bottom: 1px solid #EBEDF0;
    }
  }
}
</style>
