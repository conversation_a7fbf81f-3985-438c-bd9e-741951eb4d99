<template>
  <div class="tw-w-full">
    <div v-if="!props.disabled" class="tw-flex tw-items-center tw-w-full">
      <el-input
        v-model="newStr"
        clearable
        @keyup.enter="add()"
        :placeholder="`请输入${props.placeholder}，英文逗号隔开，回车或点击按钮完成新增`"
      ></el-input>
      <el-button type="primary" link @click="add()" class="tw-ml-[6px]">{{ props.addLabel }}</el-button>
      <template v-if="props.canCopy">
        <el-button type="primary" link @click="copy()">复制</el-button>
        <el-tooltip :disabled="!copyArr?.length">
          <template #content>
            <div  class="tw-w-[300px]">
              <div class="tw-border-b tw-border-gray-400 tw-pb-[4px]">
                <div class="tw-text-left tw-truncate">复制内容</div>
              </div>
              <p class="tw-mt-[4px] tw-grid tw-grid-cols-3 tw-text-left tw-gap-[5px]">
                <span v-for="item in copyArr" class="tw-truncate">
                {{ item }}
                </span>
              </p>
            </div>
          </template>
          <el-button
            :disabled="!copyArr||copyArr.length<1"
            :type="!copyArr?.length ? 'default':'primary'"
            link
            @click="paste()"
          >粘贴</el-button>
        </el-tooltip>
      </template>
      <el-button v-if="props.clearable" type="danger" link @click="delAll()" class="tw-ml-[6px]">清空</el-button>
    </div>
    <div class="tw-w-full tw-flex tw-items-center tw-justify-start tw-flex-wrap">
      <el-tag
        v-for="(tag, index) in val"
        :key="tag"
        class="tw-mx-0.5 tw-my-[2px]"
        :closable="!props.disabled"
        @close="del(index)"
      >
        {{ tag }}
      </el-tag>
      <span v-if="val && val.length>1 && props.canCopy && props.disabled" class="tw-cursor-pointer tw-text-[#165DFF] tw-ml-[6px]" @click="copy()">复制</span>
      <span v-if="val && val.length<1 && props.disabled">-</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, } from 'vue'
import { useGlobalStore } from '@/store/globalInfo'
import { ElMessage, } from 'element-plus'

// 组件入参props
const props = withDefaults(defineProps<{
  value?: string[], // 选项列表中代表value的属性
  addLabel?: string,  // 添加按钮名称
  placeholder?: string, // 选择框占位符
  disabled?: boolean,
  clearable?: boolean,
  canCopy?: boolean, // 是否支持复制、粘贴，并控制复制粘贴按钮显示。
  copyName?: string, // 粘贴列表，
}>(), {
  addLabel: '批量添加',
  disabled: false,
  clearable: false
})
// emit
const emits = defineEmits([
  'update:value',
])

const val = ref<string[]>(props.value || []) // 选中的数据

// 更新选中数据,并判断是否需要继续聚焦搜索框，暴露给父组件
const newStr = ref('')
const add = () => {
  if (!val.value) {
    val.value = []
  }
  const res = newStr.value ? (newStr.value?.split(',') || []).flatMap(item => item ? [item.trim()] : []) : []
  const oldLen = val.value.length + res.length
  val.value = [...new Set([...val.value, ...res])]
  if (oldLen !== val.value.length) ElMessage.warning(`${props.placeholder || ''}存在重复，已为您去重`)
  newStr.value = ''
  emits('update:value', val.value)
}

const globalStore = useGlobalStore()

const copy = () => {
  if (!val.value || val.value?.length<1 || !props.copyName || !props.canCopy) {
    return ElMessage.warning('复制内容不可为空')
  } else {
    globalStore.addCopyInfo({
      name: props.copyName,
      value: val.value
    })
    ElMessage.success('复制成功')
  }
}

const copyArr = computed(() => {
  if (!props.canCopy || !props.copyName) return []
  return globalStore.findCopyInfoByName(props.copyName) || []
}) // 复制的数据
const paste = () => {
  if (!props.canCopy || !props.copyName || !copyArr.value?.length) return
  const oldLen = val.value.length + copyArr.value.length
  val.value = [...new Set([...(val.value || []), ...(copyArr.value||[])])]
  if (oldLen !== val.value.length) ElMessage.warning(`${props.placeholder || ''}存在重复，已为您去重`)
  ElMessage.success('粘贴完成')
  globalStore.deleteCopyeInfoByName(props.copyName)
  emits('update:value', val.value)
}

const delAll = () => {
  val.value = []
  emits('update:value', val.value)
}
const del = (index: number) => {
  if (!val.value) {
    val.value = []
  } else {
    val.value?.splice(index, 1)
  }
  emits('update:value', val.value)
}

/** watch开始 */
// 监听入参，更新选中数据和选项变化
watch(() => props.value, () => {
  val.value = props.value || []
}, {deep: true})
</script>

<style lang="postcss" type="text/postcss" scoped>
.input-number-dom :deep(.el-input-group__append) {
  padding: 0 8px;
}
.el-button + .el-button {
  margin-left: 6px;
}
</style>
