import { ScriptCorpusItem, CorpusTypeEnum, ScriptInfo, EventValueItem, ScriptMultiContent, corpusTypeOption, ScriptBranch, ConnectTypeEnum, CanvasCorpus, CanvasBranch} from '@/type/speech-craft'
import { scriptCorpusModel, } from '@/api/speech-craft'
import { findValueInEnum, pickAttrFromObj } from '@/utils/utils'
import { Node  } from '@antv/x6'
import { EdgeItem, PortItem } from '@/type/common'
import { ElMessage } from 'element-plus'
import { useGlobalStore } from '@/store/globalInfo'
import { useScriptStore } from '@/store/script'
import to from 'await-to-js'
import { trace } from '@/utils/trace'

export const groups = {
  in: {
    position: 'top',
    attrs: {
      circle: {
        r: 4,
        magnet: true,
        stroke: '#1f8cec',
        strokeWidth: 2,
        fill: '#fff',
      },
    },
  },
  'in-selected': {
    position: 'top',
    attrs: {
      circle: {
        r: 4,
        magnet: true,
        stroke: '#1f8cec',
        strokeWidth: 2,
        fill: '#1f8cec',
      },
    },
  },
  'out': {
    position: 'bottom',
    markup: [
      {
        tagName: 'rect',
        selector: 'rect',
      },
      {
        tagName: 'circle',
        selector: 'circle',
      },
    ],
    attrs: {
      circle: {
        r: 4,
        magnet: true,
        stroke: '#1f8cec',
        strokeWidth: 2,
        fill: '#fff',
      },
      rect: {
        magnet: true,
        stroke: 'transparent',
        fill: 'transparent',
        strokeWidth: 1,
        width: 30,
        height: 27,
        x: -15,
        y: -8,
      },
    },
  },
}
export enum X6ColorEnum {
   'red' = '#E54B17',
   'orange' = '#E59000',
   'yellow' = '#FFCC00',
   'green' = '#13BF77',
   'purple' = '#CC0099',
   'olivedrab' = '#6b8e23',
   'crimson' = '#990000',
   'navy' = '#000080',
   'saddlebrown' = '#8b4513'
}
export const x6ColorList: {
  name: X6ColorEnum, value: string
}[] = [
  { name: X6ColorEnum['red'], value: 'red', },
  { name: X6ColorEnum['orange'], value: 'orange', },
  { name: X6ColorEnum['yellow'], value: 'yellow', },
  { name: X6ColorEnum['green'], value: 'green' , },
  { name: X6ColorEnum['purple'], value: 'purple' , },
  { name: X6ColorEnum['olivedrab'], value: 'olivedrab', },
  { name: X6ColorEnum['crimson'], value: 'crimson', },
  { name: X6ColorEnum['navy'], value: 'navy', },
  { name: X6ColorEnum['saddlebrown'], value: 'saddlebrown', },
]

/** 画布node 连接桩（即语料的分支） */
export const filterNextStr = (data : CanvasCorpus) => {
  const scriptStore = useScriptStore()
  const processList = scriptStore.masterProcessOptions
  const normalPorts : PortItem[] = []
  const specialPorts : PortItem[] = []
  const infoBranches: {
    [key: string]: string
  } = {}
  data?.branches && data?.branches.map((item: ScriptBranch) => {
    if (item.name && ['沉默','未命中'].includes(item.name)) {
      specialPorts.unshift({
        id: data.corpusId + 'port' + '--' + item.id,
        name: item.name,
        group: "out",
        attrs: { circle: { stroke: '#8f8f8f',} },
      })
    } else {
      const colorVal = X6ColorEnum[item.color as keyof typeof X6ColorEnum] || '#3A7CFF'
      normalPorts.push({
        id: data.corpusId + 'port' + '--' + item.id,
        name: item.name || '',
        group: "out",
        attrs: {
          circle: item.color ? {
            stroke: colorVal
          } : {}
        },
      })
      if (item.color && item.id) {
        infoBranches[item.id] = colorVal
      }
    }
  })
  const  ports: PortItem[] = [...normalPorts, ...specialPorts]
  if (data.corpusType.includes('_ORDINARY')) {
    let branchStr = ``
    ports && ports.map((item, index) => {
      const color = data.branches.find(v => String(v.id) === item.id.split('--')[1])?.color || undefined
      const boxClass = item.name && ['沉默', '未命中'].includes(item.name) ? 'gray-status' : (color ? color + '-status': 'blue-status')
      branchStr = branchStr + `<li class="${boxClass} tw-truncate tw-cursor-pointer tw-m-auto tw-box-border" style="height:24px;width:68px;line-height:24px;border-radius:2px">${item.name}</li>`
    })
    return `<div class="tw-list-none" style="display:grid;grid-auto-flow: column;grid-auto-columns: auto;gap: 12px;margin:4px auto 8px;">${branchStr}</div>`
  } else {
    const str1 = findValueInEnum(data.connectType, ConnectTypeEnum) || ''
    const str2 = processList.find(item => item.headCorpusId == data.connectCorpusId)?.name || ''
    let res = ''
    switch (data.connectType) {
      case ConnectTypeEnum['挂机']: return `<div class="tw-flex tw-items-center tw-justify-center" style="background: #E54B17;color:#fff;padding:10px 4px;margin-top:4px">${str1}</div>`;
      case ConnectTypeEnum['指定主动流程']: return `<div class="tw-flex tw-items-center tw-justify-center" style="background: #13BF77;color:#fff;padding:10px 4px;margin-top:4px">${str1}：${str2}</div>`;
      default: return `<div class="tw-flex tw-items-center tw-justify-center" style="background: #165DFF;color:#fff;padding:10px 4px;margin-top:4px">${str1}</div>`
    }
  }
}

/** 画布node 标题+短信/转人工触发+意向分类 */
export const filterNodeTitle = (data: CanvasCorpus, needPercent?: boolean) => {
  // 标题的模板字符串
  const titleStr = `<span class="tw-line-clamp-2 tw-grow tw-text-left">${data.name}</span>`

  // 触发转人工、短信
  const triggerStr = (data.smsTriggerName ? `
    <svg aria-hidden="true" style="width:16px;height:16px;font-size:16px;color:#165DFF">
      <use href="#icon-trigger-sms"/>
    </svg>
  ` : '') + (data.listenInOrTakeOver ? `
    <svg aria-hidden="true" style="width:16px;height:16px;font-size:16px;color:#165DFF;margin-left:4px">
      <use href="#icon-trigger-human"/>
    </svg>
  `: '')

  // 是否需要显示统计百分比
  // @ts-ignore
  const percentStr = needPercent ? (`<span class="tw-max-w-[36px] tw-text-right tw-text-[#165DFF] tw-grow-0" style="margin-left:8px">${data.percent|| ''}</span>`) : ''

  // 分类的模板字符串
  const aiIntentionMap = new Map([
    ['A', '#13BF77'], ['B', '#13BF77'],
    ['C', '#165DFF'], ['D', '#165DFF'], ['E', '#165DFF'],
    ['F', '#E54B17'], ['G', '#E54B17'],
  ])
  const colorStr = data.aiIntentionType?.intentionType ? aiIntentionMap.get(data.aiIntentionType?.intentionType) || '#999' : '#999'
  const aiIntentionStr = `<span class="tw-text-[${colorStr}] tw-grow-0 tw-shrink-0 tw-w-[30px]" style="margin-left:4px">${data.aiIntentionType?.intentionType || ''}</span>`
  return  `${titleStr}${triggerStr}${percentStr}${data.aiIntentionType?.intentionType ? aiIntentionStr : ''}`
}


/** 画布node 内容部分（内容+事件触发） */
export const filterNodeContent = (data: CanvasCorpus, eventValuesOptions: Record<string, EventValueItem>) => {
  const eventArr: string[] = []
  data.eventTriggerValueIds && data.eventTriggerValueIds?.length > 0 && data.eventTriggerValueIds?.map(item => {
    eventValuesOptions[item] && eventArr.push(eventValuesOptions[item].explanation || eventValuesOptions[item].name || '')
  })
  const eventValuesStr = eventArr.length > 0 ?`<div class="tw-text-justify tw-leading-[16px] tw-px-[16px] tw-line-clamp-2 tw-text-[#aaa]"><span class="tw-text-[#2c3e50]">触发事件：</span>${eventArr.join('、')}</div>` : ''
  return `<div class="tw-text-justify tw-px-[16px] tw-line-clamp-2 tw-text-[#626366] tw-break-all">${data.content}</div>${eventValuesStr}`
}

export const filterNodeDom = (nodeItem: Partial<CanvasCorpus> & {id?: number, branchList?: ScriptBranch[], scriptMultiContents?: ScriptMultiContent[]}, curCanvas: ScriptInfo, ports: PortItem[],) => {
  const { corX = 50, corY = 100, name, corpusType, aiIntentionType, connectCorpusId, connectType, eventTriggerValueIds, listenInOrTakeOver, percent, smsTriggerName } = nodeItem
  const corpusId = nodeItem.corpusId || nodeItem.id
  const branches = nodeItem.branches || nodeItem.branchList || []
  const content = nodeItem.content || ((nodeItem.scriptMultiContents && nodeItem.scriptMultiContents.length) ? nodeItem.scriptMultiContents[0].scriptUnitContents?.reduce((a, b) => a + b.content, '') || '' : '')
  return {
    id: corpusId ? 'corpus--' + corpusId : 'corpus--',
    x: corX,
    y: corY,
    shape: 'cu-data-node',
    width: corpusType?.includes('_CONNECT') ? 200 : (ports.length <= 1 ? 160 : ports.length * 80),
    height: (content ? 120 : 100) + (eventTriggerValueIds && eventTriggerValueIds.length > 0 ? 16 : 0),
    zIndex: 10 - ports.length,
    ports: {
      groups,
      items: curCanvas.headCorpusId === corpusId ? ports : [...ports,{
        id: corpusId + 'port' + '--' + 'in',
        group: 'in',
      }],
    },
    data: {
      corpusId,
      name: name,
      content,
      corpusType,
      branches,
      connectType,
      connectCorpusId,
      aiIntentionType, eventTriggerValueIds, listenInOrTakeOver, smsTriggerName,
      percent,
    },
  }
}

/**
 * 粘贴语料:①查询语料->②更新isHead和isTopHead->③文字内容改造->④分支信息改造->⑤如是深层沟通头节点改造命中条件信息->
 * ->⑥保存语料->⑦获取创建出来的分支id生产旧分支的映射表 -> ⑧保存优先级信息-> ⑨更新画布节点和连接线
 * @param obj ：复制的语料和分支信息
 * @param curCanvas ：当前画布
 */
export const pasteCorpus = async (obj: {
  nodes: {
    id: number, x?: number, y?: number, canvasId: number | null, corpusType: CorpusTypeEnum
  }[],
  edges: {
    id: number, pre?: number, next?: number,
  }[]
}, curCanvas: ScriptInfo, curPos: {x: number, y: number},) => {
  const globalStore = useGlobalStore()
  globalStore.loading = true
  globalStore.copyCellList.pasteIds = []
  const scriptStore = useScriptStore()
  const eventValuesOptions: {[key: string]: EventValueItem} = {}
  const res = await scriptStore.getEventOptions()
  if (res && res.length > 0) {
    res.map(item => {
      item.eventValuemap && Object.assign(eventValuesOptions, item.eventValuemap)
    })
  }
  const nodes: Node.Metadata[] = []
  const edges: EdgeItem[] = []
  const originPos = {
    x: 0,
    y: 0,
  }
  try {
    const nodeObj: {[key: number]: number} = {}
    const branchObj: {[key: number]: {
      id: number, color?: string | null
    }} = {}
    let headCorpusId: number | null = null
    trace({
      page: `话术编辑-复制语料(${scriptStore.id})`,
      params: obj.nodes
    })
    await Promise.all(
      obj.nodes?.map(async (item, index) => {
        // 将第一个语料作为定位点
        if (index === 0) {
          originPos.x = item.x || 0
          originPos.y = item.y || 0
        }
        if (item.x && item.y) {
          const dataOrigin = await scriptCorpusModel.findMasterCorpus({corpusId: item.id}) as ScriptCorpusItem
          const branchOriginList = dataOrigin.branchList || []
          // 复制的语料是否是头节点：原本是头节点+当前画布无头节点
          const isHeadCurrent = dataOrigin.isHead && !curCanvas.headCorpusId ? true : false
          const [err, resultData] = await to(scriptCorpusModel.copyCanvasCorpus({
            corpusId : dataOrigin.id!,
            isHead: isHeadCurrent,
            scriptId: curCanvas.scriptId!,
          }))
          if (err) return
          const { id, branchList, isHead, } = resultData
          // 获取新旧语料的映射表，用于优先级和分支连接线的匹配
          if (dataOrigin.id) {
            nodeObj[dataOrigin.id] = id || 0
          }
          // 获取新旧语料分支的映射表，用于优先级和分支连接线的匹配
          branchList && branchList.map((bb, index) => {
            if (branchOriginList && branchOriginList[index]?.name === bb.name && branchOriginList[index]?.id) {
              branchObj[branchOriginList[index].id] = {id: bb.id!, color: bb.color || undefined}
            }
          })
          
          const normalPorts : PortItem[] = []
          const specialPorts : PortItem[] = []
          const infoBranches: {
            [key: string]: string
          } = {}
          branchList && branchList.map((branch: ScriptBranch) => {
            if (branch.name && ['沉默','未命中'].includes(branch.name)) {
              specialPorts.unshift({
                id: id + 'port' + '--' + branch.id,
                name: branch.name,
                group: "out",
                attrs: { circle: { stroke: '#999',} },
              })
            } else {
              const colorVal = X6ColorEnum[branch.color as keyof typeof X6ColorEnum] || '#3A7CFF'
              normalPorts.push({
                id: id + 'port' + '--' + branch.id,
                name: branch.name || '',
                group: "out",
                attrs: {
                  circle: branch.color ? {
                    stroke: colorVal
                  } : {}
                },
              })
              if (branch.color && branch.id) {
                infoBranches[branch.id] = colorVal
              }
            }
          })
          id && globalStore.copyCellList.pasteIds?.push(id)
          const  ports: PortItem[] = [...normalPorts, ...specialPorts]
          // 更新画布头节点
          if (!curCanvas.headCorpusId && isHead) {
            curCanvas.headCorpusId = id
          }
          nodes.push(filterNodeDom({
            ...resultData,
            corX: curPos.x + item.x! - originPos.x,
            corY: curPos.y + item.y! - originPos.y,
          }, curCanvas, ports))
        }
      })
    )
    obj.edges?.map(item => {
      const id = item.id && branchObj[item.id]?.id || undefined
      const color = item.id && branchObj[item.id]?.color || undefined
      const preCorpusId = item.pre && nodeObj[item.pre] || undefined
      const nextCorpusId = item.next && nodeObj[item.next] || undefined
      if (preCorpusId && nextCorpusId && id) {
        edges.push({
          id: preCorpusId + 'branch' + '--' + id,
          shape: 'edge',
          source:  {
            cell: 'corpus--' + preCorpusId,
            port: preCorpusId + 'port' + '--' + id,
          },
          target: {
            cell: 'corpus--' + nextCorpusId,
            port: nextCorpusId + 'port' + '--in'
          },
          zIndex: 11,
          attrs: {
            line: {
              stroke: color ? X6ColorEnum[color  as keyof typeof X6ColorEnum] || '#8f8f8f' : '#8f8f8f',
              strokeWidth: 1,
            },
          },
        })
      }
    })
    nodes.map((item, index) => {
      nodes[index].data.nextCorpusId = item.data.nextCorpusId && nodeObj[item.data.nextCorpusId] || null
    })
    return {nodes: nodes, edges: edges, headCorpusId: headCorpusId || null}
  } catch (err) {
    ElMessage({
      type: 'error',
      message: '粘贴失败'
    })
    globalStore.loading = true
    setTimeout(async () => {
      nodes && nodes.length > 0 && await Promise.all(
        nodes.map(async node => {
          const corpusId = node.data.corpusId
          await scriptCorpusModel.deleteOneScriptCorpusById({corpusId, scriptId: scriptStore.id}) as ScriptCorpusItem
        })
      )
    }, 500)
    globalStore.loading = false
    return {nodes: [], edges: []}
  }
}

/**
 * 删除语料
 * @param corpusIds ：语料ids
 * @param curCanvas ：当前画布
 */
export const deleteNodes = async (corpusIds: number[]) => {
  const globalStore = useGlobalStore()
  const scriptStore = useScriptStore()
  globalStore.loading = true
  try {
    if ( corpusIds && corpusIds.length > 0) {
      trace({ page: `话术编辑-删除语料(${scriptStore.id})`, params: corpusIds })
      await Promise.all(
        corpusIds.map(async (corpusId: number) => {
          await scriptCorpusModel.deleteOneScriptCorpusById({corpusId, scriptId: scriptStore.id}) as ScriptCorpusItem
          if (scriptStore.currentCanvas && corpusId === scriptStore.currentCanvas.headCorpusId ) {
            scriptStore.currentCanvas.headCorpusId = null
          }
        })
      )
      ElMessage({
        message: '语料删除成功',
        type: 'success',
      })
      await scriptStore.saveGraph(true)
    }
  } catch(err) {
    ElMessage.error('语料删除失败')
    await scriptStore.saveGraph(true)
  }
  globalStore.loading = false
}
