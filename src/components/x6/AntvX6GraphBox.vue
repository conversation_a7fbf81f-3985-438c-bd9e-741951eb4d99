<template>
  <div class="graph-container" :class="{'graphClass': isFullScream}" ref="graphRef">
    <!-- 画布主体 -->
    <el-scrollbar class="tw-w-full tw-h-full">
      <div id="graph" class="tw-box-border tw-relative tw-w-full tw-h-full tw-overflow-hidden">
      </div>
    </el-scrollbar>

    <!-- 顶部左侧按钮： 普通语料 | 连接语料 -->
    <div class="tw-absolute tw-left-1 tw-top-1">
      <slot></slot>
      <div class="btn-box" v-if="!isChecked && props.curCanvas.id">
        <el-button type="primary" :icon="Plus" @click="addCorpus(1)" class="add-dom">普通语料</el-button>
        <el-button type="primary" v-if="(nodeList && nodeList.length > 0)" :icon="Plus" @click="addCorpus(2)">连接语料</el-button>
      </div>
      <svg viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="greenGradient" x1="0" y1="0" x2="100%" y2="100%">
            <stop offset="0" stop-color="rgb(0, 255, 127)" />
            <stop offset="100%" stop-color="rgba(26, 115, 232, 0.953)"/>
          </linearGradient>
        </defs>
        <defs>
          <marker id="triangle" viewBox="0 0 10 10"
            refX="8" refY="5"
            markerUnits="strokeWidth"
            markerWidth="4" markerHeight="4"
            orient="auto"
          >
            <path d="M 0 0 L 10 5 L 0 10 L 2 5 z" fill="inherit"/>
          </marker>
        </defs>
      </svg>
    </div>

    <!-- 顶部右侧按钮：链接线起止节点 | 保存画布 | 全屏画布 -->
    <div class="tw-absolute tw-right-1 tw-top-[13px] tw-text-[#999] tw-flex tw-flex-col tw-items-end tw-gap-y-1">
      <div class="tw-flex tw-items-center">
        <!-- 保存画布 -->
        <template v-if="!isChecked && nodeList && nodeList.length > 0">
          <el-button type="primary" @click="save">保存画布</el-button>
          <el-button type="primary" @click="checkGraph">检查画布</el-button>
        </template>

        <!-- 全屏画布 -->
        <div class="tw-ml-[6px] tw-flex tw-items-center" @click="goFullScreen">
          <el-icon v-if="!isFullScream" :size="26" ><FullScreen /></el-icon>
          <el-icon v-else :size="26"><Close /></el-icon>
        </div>
      </div>

       <!-- 顶部右侧按钮：链接线起止节点 -->
      <div v-if="!isChecked && (currentEdage?.start || currentEdage?.end)" class="tw-flex tw-flex-col tw-border-[#409eff] tw-border-[1px] tw-items-start tw-gap-[2px] tw-opacity-70 tw-bg-[#fff] tw-p-1">
        <span class="info-title">开始分支： </span>
        <el-input :model-value="currentEdage.start?.name" style="width: 180px" readonly placeholder="开始分支"></el-input>
        <span class="info-title">结束语料： </span>
        <el-input :model-value="currentEdage.end?.name" style="width: 180px" readonly placeholder="结束语料"></el-input>
        <el-button size="small" class="tw-w-full tw-mt-1" type="primary" @click="clearEdge">隐藏</el-button>
      </div>

      <!-- 顶部右侧：正在播放的音频 -->
      <div v-if="!!currentAudioName" class="info-title tw-bg-white tw-w-[250px] tw-p-[12px] tw-border-[#409eff] tw-border-[1px] tw-opacity-70">
        <div class="tw-flex tw-mb-[8px]">
          <span class="tw-shrink-0">正在播放：</span>
          <span class="tw-grow tw-text-justify">{{ currentAudioName || '-' }}</span>
        </div>
        <div class="tw-flex">
          <span class="tw-shrink-0">播放速度：</span>
          <el-select v-model="playbackRate" class="tw-grow" size="small" placeholder="请选择播放速度" @change="handlePlayRateChange">
            <el-option v-for="item in audioRateOption" :key="item.value" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </div>
        <el-button size="small" class="tw-w-full tw-mt-1" type="primary" @click="clearAudio">关闭</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, reactive, onDeactivated, onActivated } from 'vue'
import { Graph, Node, Edge, Cell, NodeView, Shape } from '@antv/x6'
import { Selection } from '@antv/x6-plugin-selection'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Clipboard } from '@antv/x6-plugin-clipboard'
import { History } from '@antv/x6-plugin-history'
import { FullScreen, Close, Plus } from '@element-plus/icons-vue'
import { useScriptStore } from '@/store/script'
import { CorpusTypeEnum, ScriptInfo, EventValueItem, ConnectTypeEnum, } from '@/type/speech-craft'
import { scriptCorpusModel } from '@/api/speech-craft'
import { ElMessage } from 'element-plus'
import { EdgeItem } from '@/type/common'
import { x6ColorList, X6ColorEnum, filterNextStr, filterNodeTitle, filterNodeContent } from './config'
import { useGlobalStore } from '@/store/globalInfo'
import { storeToRefs } from 'pinia'
import { onBeforeRouteLeave } from 'vue-router'
import to from 'await-to-js'
import { audioRateOption } from '@/assets/js/constant'

const globalStore = useGlobalStore()
const { copyCellList } = storeToRefs(globalStore)
const scriptStore = useScriptStore()
const isChecked = scriptStore.isChecked
const { x6History } = storeToRefs(scriptStore)
const props = defineProps<{
  edgeList: EdgeItem[];
  nodeList: Node.Metadata[];
  curCanvas: ScriptInfo,
  needRefresh: boolean, // 是否需要更新
}>();
const emits = defineEmits(['save', 'update', 'edit', 'delete', 'confirm', 'add-normal', 'add-connect', 'paste', 'update:needRefresh'])
const nodeList = ref<Node.Metadata[] | null>([])
const edgeList = ref<EdgeItem[] | null>([])
const isFullScream = ref(false)
const activeType = ref<'edit' | 'voice'>('edit') // edit: 编辑模式 | voice：听音模式
const graphRef = ref()
// 快速连线数据
const currentEdage = reactive<{
  start: { id: string; name: string, } | undefined,
  end: { id: string; name: string, } | undefined,
  edge: EdgeItem | undefined,
}>({
  start: undefined,
  end: undefined,
  edge: undefined
})
// 当前正在听音的语料
const currentAudioName = ref<string | null>(null)
const playbackRate = ref(1)
// 音频播放模块
const currentAudio = ref<null | HTMLAudioElement>(null)

const clearEdge = () => {
  currentEdage.start = undefined
  currentEdage.end = undefined
  currentEdage.edge = undefined
}

// 检测画布中是否有未连接的语料
const checkGraph = () => {
  const existSourceports: string[] = []
  const existTargetports: string[] = []
  edgeList.value?.map(item => {
    existSourceports.push(item.source.port)
    existTargetports.push(item.target.port)
  })
  // 全部的连接桩
  const existAbnormal = nodeList.value && nodeList.value.some(item => {
    // @ts-ignore
    const abnormalport = item.ports?.items.some(v => {
      if (!v.id) {
        return false
      }
      // 检测分支
      if (v.group === 'out' && existSourceports.includes(v.id)) {
        return false
      }
      // 检测节点
      if (v.group === 'in' && existTargetports.includes(v.id)) {
        return false
      }
      if (v.group === 'out') {
        ElMessage.warning(`【${item.data.name || ''}】存在未使用的分支【${v.name}】，已为你选择作为开始分支`)
        currentEdage.start = {
          id: v.id,
          name: item.data.name + ' / ' + v.name,
        }
        currentEdage.edge = {
          id: v.id.replace('port', 'branch'),
          shape: 'edge',
          source:  {
            cell: 'corpus--' + item.data.corpusId,
            port: v.id,
          },
          zIndex: 11,
          target: {
            cell: 'corpus--',
            port: 'port--in',
          },
          attrs: {
            line: {
              stroke: v.attrs?.circle?.stroke || '#8f8f8f',
              strokeWidth: 1,
            },
          },
        }
      } else if (v.group === 'in') {
        ElMessage.warning(`存在未使用的语料【${item.data.name || ''}】`)
      }
      
      // 定位到未连接语料，并选中
      const iCell = graph.value?.getCellById(item.id)
      if (!iCell) return true
      const { x, y } = iCell.position()
      // 将节点坐标防止在左上角（200，200）位置
      graph.value?.positionPoint({ x, y }, 200, 200)
      graph.value.resetSelection(iCell)
      return true
    })
    // 如存在异常连接分支直接返回
    if (abnormalport) return true
    // 如不存在异常连接分支，检查连接到的主动流程是否存在
    const masterProcessList = scriptStore.masterProcessOptions || []
    const abnormalConnect = item.data.connectType === ConnectTypeEnum['指定主动流程'] && !masterProcessList?.find(v => v.headCorpusId === item.data.connectCorpusId)
    if (abnormalConnect) {
      ElMessage.warning(`【${item.data.name || ''}】指定主动流程不存在`)
    }
    return abnormalConnect
  })
  if (!existAbnormal) {
    ElMessage.success('画布无异常')
    save()
  }
}

const save = () => {
  emits('save', nodeList.value, edgeList.value, props.curCanvas)
}
const graph = ref()
const addCorpus = (num: number) => {
  const posHtml = graphRef.value.querySelector('.add-dom') && document.querySelector('.add-dom')?.getBoundingClientRect()
  const posCavas = posHtml && posHtml.x && posHtml.y ? graph.value.clientToLocal({
    x: posHtml?.x,
    y: posHtml?.y + 200
  }) : {
    x: 50,
    y: 200
  }
  switch(num) {
    case 1: emits('add-normal', posCavas); break;
    case 2: emits('add-connect', posCavas); break;
  }
}
watch(() => isFullScream.value, (n) => {
  if (graph.value && n) {
    graph.value.width = window.innerWidth - 10
  }
})
watch(() => scriptStore.currentCanvas?.id, n => {
  x6History.value = []
  currentAudio.value?.pause()
  currentAudio.value = null
  currentAudioName.value = null
  clearEdge()
}, {
  immediate: true,
})
const portMap = reactive<Map<string, number>>(new Map([]))
const goFullScreen = () => {
  isFullScream.value = !isFullScream.value
  graph.value && (isFullScream.value ? graph.value.resize(5000, 10000) : graph.value.resize(3000, 6000))
}

Shape.HTML.register({
  shape: 'cu-data-node',
  effect: ['data'],
  width: 200,
  height: 120,
  html(cell) {
    // 获取节点传递过来的数据
    const data = cell.getData();
    return `
      <div class="dom-body tw-bg-white tw-rounded-[4px] tw-border-transparent tw-h-[100%] tw-w-[100%] tw-flex tw-flex-col tw-justify-between tw-text-[12px] tw-pt-[16px] tw-overflow-y-hidden tw-box-border">
        <div class="tw-flex tw-items-center tw-w-full tw-text-[14px] tw-px-[12px] tw-font-semibold tw-text-[#313233]">${filterNodeTitle(data)}</div>
        ${filterNodeContent(data, eventValuesOptions)}
        ${filterNextStr(data)}
      </div>
    `
  }
})

/**
 * 通过监听器捕获node新增/删除更新操作
 * 1. 画布新增node/删除node；√
 * 2. 用户撤销操作（涉及node）
 * 3. 用户使用复制粘贴功能；×
 */
watch([() => props.nodeList, () => props.curCanvas.id], (n, o) => {
  // 删除/新增node，撤销粘贴，撤销新增删除node
  if (n[0]?.length !== o[0]?.length || n[1] !== o[1]) {
    nodeList.value = props.nodeList
    edgeList.value = props.edgeList
    const cells: Cell[] = []
    let portsAll: any[] = []
    nodeList.value && nodeList.value.map(item => {
      // @ts-ignore
      portsAll = [...portsAll, ...(item.ports?.items ? item.ports?.items.map(v => v.id) : [])]
      cells.push(graph.value?.createNode(item))
    })
    edgeList.value = edgeList.value && edgeList.value.filter(item => portsAll?.includes(item.source.port) && portsAll?.includes(item.target.port))
    edgeList.value.map(item => cells.push(graph.value?.createEdge(item)))
    graph.value?.resetCells(cells, {silence: true})
    if (copyCellList.value.pasteIds && copyCellList.value.pasteIds.length > 0 && x6History.value.at(-1)?.type === '画布-复制') {
      graph.value?.resetSelection(copyCellList.value.pasteIds.map(item => 'corpus--' + item))
    }
  }
  if (n[1] !== o[1]) {
    graph.value?.positionContent('top-left')
  }
}, {
  deep: true
})
// 仅针对node编辑后触发、粘贴（包括直接编辑，撤销编辑）
watch(() => props.needRefresh, n => {
  if (n) {
    nodeList.value = props.nodeList
    edgeList.value = props.edgeList
    const cells: Cell[] = []
    let portsAll: any[] = []
    nodeList.value && nodeList.value.map(item => {
      // @ts-ignore
      portsAll = [...portsAll, ...(item.ports?.items ? item.ports?.items.map(v => v.id) : [])]
      cells.push(graph.value?.createNode(item))
    })
    edgeList.value = edgeList.value && edgeList.value.filter(item => portsAll?.includes(item.source.port) && portsAll?.includes(item.target.port))
    edgeList.value.map(item => cells.push(graph.value?.createEdge(item)))
    graph.value?.resetCells(cells, {silence: true})
    if (copyCellList.value.pasteIds && copyCellList.value.pasteIds.length > 0 && x6History.value.at(-1)?.type === '画布-复制') {
      graph.value?.resetSelection(copyCellList.value.pasteIds.map(item => 'corpus--' + item))
    }
    emits('update:needRefresh', false)
  }
})
const eventValuesOptions = reactive<{[key: string]: EventValueItem}>({})
const init = async () => {
  const res = await scriptStore.getEventOptions()
  if (res && res.length > 0) {
    res.map(item => {
      item.eventValuemap && Object.assign(eventValuesOptions, item.eventValuemap)
    })
  }
  if (graph.value) {
    graph.value?.dispose()
  }
  graph.value = new Graph({
    container: document.getElementById('graph') as HTMLElement,
    height: 1200,
    width: 800,
    autoResize: true,
    async: true,
    panning: true,
    mousewheel: true,
    scaling: {
      min: 0.3,
      max: 1.5,
    }, // 最大最小缩放尺寸
    interacting: !isChecked,
    background: {
      color: '#f2f2f2',
    },
    highlighting: {
      embedding: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#fff',
            stroke: '#47C769',
          },
        },
      },
      // 连线过程中，链接桩可以被链接时被使用
      magnetAvailable: { // 高亮
        name: 'stroke',
        args: {
          attrs: {
            fill: '#fff',
            stroke: '#47C769',
          },
        },
      },
      // 连线过程中，自动吸附到链接桩时被使用
      magnetAdsorbed: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#fff',
            stroke: '#31d0c6',
          },
        },
      },
    },
    connecting: {
      snap: {
        radius: 30,
      },
      // allowMulti: false,
      allowNode: false,
      anchor: 'center',
      allowLoop: false,
      connectionPoint: 'anchor',
      // anchor: 'top',
      router: {
        name: 'normal',
        args: {
          offset: 12,
          direction: 'T',
        },
      },
      allowBlank: false,
      connector: {
        name: 'smooth',
        args: {
          offset: 24,
          direction: 'V',
        },
      },
      validateConnection({ sourcePort, targetPort }) {
        return sourcePort?.split('--')[1] !== 'in' && targetPort?.split('--')[1] === 'in' && !edgeList.value?.find(item => item.source.port === sourcePort)
      },
    }
  })
  graph.value.use(
    new Selection({
      enabled: true,
      multiple: !isChecked,
      rubberband: true,
      rubberEdge : true,
      showNodeSelectionBox: false,
      movable: !isChecked,
      modifiers: 'ctrl',
    }),
  )
  graph.value.use(
    new Keyboard({
      enabled: !isChecked,
    }),
  )
  graph.value.use(
    new Clipboard({
      enabled: !isChecked,
    })
  )
  graph.value.use(
    new History({
      enabled: !isChecked,
      ignoreAdd: false,
      ignoreRemove: false,
      ignoreChange:  false,
    })
  )
  nodeList.value = props.nodeList
  edgeList.value = props.edgeList
  graph.value?.fromJSON({
    nodes: nodeList.value,
    edges: edgeList.value,
  })
  graph.value?.positionContent('top-left')

  // 实现点击分支名称选中分支：强行根据点击位置，判断位于分支名称的大概位置，进行edged的选中
  graph.value.on('node:click', async (params: {
    node: Node,
    view: NodeView,
    x: number, y: number,
  }) => {
    const { x, y } = params // 点击位置坐标
    const node = params.node // 点击节点（语料)
    // 首先判断听音模式还是编辑模式
    // --如果是听音模式，则直接查询语料得音频，弹出多语句音频弹窗，并播放；
    // --如果是编辑模式，则判断点击区域
    // ----------------如果在连接桩，则判断是否存在连接线，如存在则高亮连接线；
    // ----------------如不存在，则触发快捷连线，放入开始分支；
    if (activeType.value === 'edit') {
      const { x: node_x, y: node_y } = node.position() // 节点（语料)坐标（左上角）
      const {width, height} = node.size() // 节点（语料)尺寸
      const ports = node.getPorts()?.filter(item => item.group === 'out') // 节点多少分支
      if (ports.length > 0) {
        let portId: string = ''
        let portName: string = '' // 分支名称
        let portColor: string = '' // 分支颜色
        for (let i = 0; i < ports.length; i++) {
          if ( x >= node_x + width/ports.length * i && x < node_x + width/ports.length * (i + 1) && y > (node_y + height - 40)) {
            portId = ports[i].id || ''
            portName = ports[i].name || ''
            portColor = ports[i].attrs?.circle?.stroke as string || ''
            break
          }
        }
        const edge = edgeList.value?.find(item => item.source.port === portId)
        // if--如果选择的是连接桩，且存在连线，则不再选中节点，选择连线
        // else-if--如果选择的是连接桩，且不存在连线,且非查看模式，则进行点击连线，更新currentEdage
        // else--不是连接桩，不进行操作（默认）
        if (portId && edge) {
          graph.value.cleanSelection()
          graph.value.select(edge.id)
        } else if(portId && !edge && !isChecked) {
          currentEdage.start = {
            name: node.data.name + ' / ' + portName,
            id: portId
          }
          currentEdage.edge = {
            id: portId.replace('port', 'branch'),
            shape: 'edge',
            source:  {
              cell: 'corpus--' + node.data.corpusId,
              port: portId,
            },
            zIndex: 11,
            target: {
              cell: 'corpus--',
              port: 'port--in',
            },
            attrs: {
              line: {
                stroke: portColor || '#8f8f8f',
                strokeWidth: 1,
              },
            },
          }
        }
      }
      // 如点击语料，且存在开始节点行为，尝试进行链接匹配
      if (!!currentEdage.start?.id ) {
        const inPort = node.getPorts()?.filter(item => item.group !== 'out')
        // 没有in连接桩，不进行连接
        if (!inPort || inPort.length !== 1 || !inPort[0].id) return
        // 如果开始结束的是同一语料，则不进行连接
        if (currentEdage.edge && currentEdage.edge.source.cell === node.id) return
        currentEdage.end = {
          name: node.data.name,
          id: inPort[0].id,
        }
        if (currentEdage.edge) {
          // 更新当前链接信息
          currentEdage.edge.target = {
            cell: 'corpus--' + node.data.corpusId,
            port: inPort[0].id,
          }
          // 更新edgeList
          edgeList.value = [...(edgeList.value || []), currentEdage.edge]
          // 更新画布，连接
          graph.value?.addEdge(currentEdage.edge)
          // 同步外部组件的数据
          emits('update', nodeList.value, edgeList.value)
          // 更新缓存
          scriptStore.updateScript(nodeList.value!, edgeList.value!, props.curCanvas)

          // 更新操作历史记录
          x6History.value.push({
            type: '画布-新增连线',
            // @ts-ignore
            data: {source: currentEdage.start.id, target: currentEdage.end.id},
          })

          // 清空连接数据
          clearEdge()
        }
      }
    }
  })

  // 右击查看/听音频
  graph.value.on('node:contextmenu', async (params: { node: Node }) => {
    const node = params.node

    // 如语料不存在文字内容(如非挂机的连接语料），不进行操作
    if (!node.data?.content) return
    // 查询语料信息
    const [err, data] = await to(scriptCorpusModel.findMasterCorpus({corpusId: node.data.corpusId}))
    if (err || !data?.scriptMultiContents?.length) return ElMessage.error('获取语料音频失败')
    // 校验全部语句是否有未上传的音频
    let hasEmptyAudio = false
    const currentAudioList = (data?.scriptMultiContents[0]?.scriptUnitContents || []).map(item => {
      if (!item.audioPath) {
        hasEmptyAudio = true
      }
      return {
        content: item.content || '',
        contentName: item.contentName || '',
        id: item.id || undefined,
        audioPath: item.audioPath || '',
      }
    })
    if (hasEmptyAudio) {
      return ElMessage.warning('部分语句未上传音频！')
    }
    // 赋值右上侧当前播放的语料内容
    currentAudioName.value = node.data?.name || ''
    // 选中该语料
    graph.value?.cleanSelection()
    graph.value?.select(node.id)

    // 播放音频
    playList(currentAudioList?.map(item => item.audioPath || '') || [], 0)
  })

  // 双击事件触发编辑语料
  graph.value.on('node:dblclick', async ({ node, }: { node: Node, }) => {
    const { x, y } = nodeList.value?.find(item => item.id === node.id) || {};
    const [err, data] = await to(scriptCorpusModel.findMasterCorpus({corpusId: node.data.corpusId}))
    if (err || !data) return ElMessage.error('获取语料失败')
    data.corX = x
    data.corY = y
    // 清空连接数据，防止调整语料名或分支导致数据异常
    clearEdge()
    emits('edit', data)
  })
  /**
   * 监听edge选中：修改连接桩颜色，对普通分支，修改edge为蓝色
   */
  graph.value.on('edge:selected', ({ edge }: {edge: Edge, }) => {
    const {port: targetport} = edge.target as { cell:string, port: string }
    const {port: sourceport} = edge.source as { cell:string, port: string }
    const node1 = edge.getSourceNode()
    if (node1) {
       // 获取Source链接桩stroke颜色
      const fillColor: string = node1.getPortProp(
        sourceport, ['attrs', 'circle', 'stroke'],
      ) || '#1f8cec'
      // 修改Source链接桩fill颜色
      node1.setPortProp(
        sourceport, ['attrs', 'circle'], { fill: fillColor }
      )
      // 对普通分支，修改edge为蓝色
      edge.setAttrs({
        line: {
          stroke: fillColor || '#1f8cec'
        }
      })
    }
    // 修改target链接桩fill颜色
    const node2 = edge.getTargetNode()
    node2 && node2.setPortProp(
      targetport, '', { group: 'in-selected' }
    )
    portMap.set(targetport, (portMap.get(targetport) || 0) + 1)
  })
  /**
   * 监听edge选中失效：清空连接桩颜色，对普通分支，重置edge颜色
   */
  graph.value.on('edge:unselected', ({ edge }: {edge: Edge,}) => {
    const {cell: targetCell, port: targetport} = edge.target as { cell:string, port: string }
    const {cell: sourceCell, port: sourceport} = edge.source as { cell:string, port: string }
    const node1 = edge.getSourceNode()
    node1 && node1.setPortProp(
      sourceport, ['attrs', 'circle'], { fill: '#fff' }
    )
    // 对普通分支，重置edge颜色
    if (!x6ColorList.find(item => item.name === edge.getAttrs().line.stroke)) {
      edge.setAttrs({
        line: {
          stroke: '#8f8f8f'
        }
      })
    }
   
    const node2 = edge.getTargetNode()
    const num = portMap.get(targetport) || 0
    portMap.set(targetport, num >=1 ? num - 1 : 0)
    portMap.get(targetport) === 0 && node2 && node2.setPortProp(
      targetport, '', { group: 'in' }
    )
  })
  graph.value.on('edge:connected', ({ isNew, edge }: {
    isNew: boolean,
    edge: Edge,
  }) => {
    if (isNew) {
      const {source, target,} = edge
      const sourceNode = edge.getSourceCell()
      const index = nodeList.value?.findIndex(item => item.id == sourceNode?.id)
      let color = '#8f8f8f'
      // @ts-ignore
      nodeList.value[index] && (nodeList.value[index].data.branches = nodeList.value[index].data.branches.map(item => {
        // @ts-ignore
        if(item.id == parseInt(source.port.split('--')[1])) {
          // @ts-ignore
          item.nextCorpusId = target.port.split('port')[0]
          color = item.color ? X6ColorEnum[item.color as keyof typeof X6ColorEnum] || '#8f8f8f' : '#8f8f8f'
        }
        return item
      }))
      x6History.value.push({
        type: '画布-新增连线',
        // @ts-ignore
        data: {source: source?.port, target: target?.port},
      })
      edgeList.value?.push({
          // @ts-ignore
          id: source.port.replace('port', 'branch'),
          shape: 'edge',
          // @ts-ignore
          source, target,
          attrs: {
            line: {
              stroke: color || '#8f8f8f',
              strokeWidth: 1,
            },
          },
          zIndex: 11
      })
      edge.attr('line/stroke', color || '#8f8f8f')
      edge.attr('line/strokeWidth', 1)
      scriptStore.updateScript(nodeList.value!, edgeList.value!, props.curCanvas)
      emits('update', nodeList.value, edgeList.value)
    }
  })
  graph.value.on('node:moved', ({ node, }: {
    node: Node
  }) => {
    // isMoves.value = true
    const { x, y } = node.position()
    const index = nodeList.value!.findIndex(item => item.id === node.id)
    nodeList.value![index].x = x
    nodeList.value![index].y = y
    scriptStore.updateScript(nodeList.value!, edgeList.value!, props.curCanvas)
    emits('update', nodeList.value, edgeList.value)
  })
  graph.value.bindKey(['backspace', 'delete'], (event: Event) => {
    let cells = graph.value.getSelectedCells()
    const nodeIds: number[] = []
    const edgeIds: string[] = []
    cells.map((removeCell: Cell) => {
      if (removeCell.isNode() && removeCell.data.corpusId && removeCell.data.corpusId == props.curCanvas.headCorpusId) {
        ElMessage({
          type: 'error',
          message: '请勿删除头部语料'
        })
        
        cells = cells.filter((v: Cell) => !v.data || v.data.corpusId != removeCell.data.corpusId)
        return 
      }
      if (removeCell.isNode()) {
        nodeList.value = nodeList.value!.filter(item => item.id != removeCell.id)
        nodeIds.push(+removeCell.id.split('--')[1])
        // @ts-ignore
        edgeList.value = edgeList.value.filter(item => item.source?.cell != removeCell.id && item.target.cell != removeCell.id)
      } else if (removeCell.isEdge()) {
        edgeIds.push(removeCell.id)
        // @ts-ignore
        edgeList.value = edgeList.value.filter(item => !(item.source.port === removeCell.source.port && item.target.port === removeCell.target.port))
      }
    })
    
    if (cells.length) {
      graph.value.cleanSelection()
      graph.value.removeCells(cells)
    }
    scriptStore.updateScript(nodeList.value!, edgeList.value!, props.curCanvas)
    nodeIds.length && scriptStore.x6History.push({
      data: nodeIds,
      type: '画布-删除语料',
    })
    edgeIds.length && scriptStore.x6History.push({
      data: edgeIds,
      type: '画布-删除连线',
    })
    // 清空连接数据，防止删除语料后，仍选中该分支为开始节点，导致数据异常
    clearEdge()

    emits('delete', nodeIds)
    emits('update', nodeList.value, edgeList.value)
  })
  // @ts-ignore
  // graph.value.on('history:add', (args) => {
  //   // isMoves.value = false
  //   const val = args.cmds?.pop()
  //   if (val?.event == 'cell:added') {
  //     x6History.value.push({
  //       data: parseInt(val.data.id) || parseInt(val.data.id.split('--')[1]),
  //       type: '画布-新增语料', // 放到更新节点函数中
  //     })
  //   }
  // })
  // select all
  graph.value.bindKey(['meta+a', 'ctrl+a'], (event: Event) => {
    event.preventDefault();
    const nodes = graph.value.getNodes()
    const edges = graph.value.getEdges()
    if (nodes) {
      graph.value.resetSelection([...nodes, ...edges])
    }
  })
  graph.value.bindKey(['meta+s', 'ctrl+s'], () => {
    // isMoves.value = false
    window.event!.returnValue = false // 阻止默认网页保存事件
    save()
  })
  graph.value.bindKey(['meta+c', 'ctrl+c'], () => {
    // isMoves.value = false
    const cells = graph.value.getSelectedCells()
    const list1: {id: number, nextCorpusId?: number, x?: number,y?: number, canvasId: number | null, corpusType: CorpusTypeEnum}[] = []
    const list2: {
      id: number, pre?: number, next?: number,
    }[] = []
    cells.map((item: Cell) => {
      if(item.isNode()) {
        const id = parseInt(item.id.split('--')[1])
        const { x, y } = nodeList.value?.find(vv => item.id === vv.id) || {};
        list1.push({id, x: x || 120, y: y || 50, canvasId: props.curCanvas.id || null, corpusType: item.data?.corpusType})
      }
      if (item.isEdge()) {
        // 使用item.source而不是id的原因是防止新增的连接线，id还没更新出来；
        // @ts-ignore
        const ids = item.source?.port?.split('port--')
        // @ts-ignore
        const nextStr = item.target.cell || item.target
        list2.push({
          id: parseInt(ids[1]),
          pre: parseInt(ids[0]),
          next: nextStr.split('--')[1]
        })
      }
    })
    if (list1 && list1.length > 0) {
      copyCellList.value = {
        isMasterCanvas: props.curCanvas.isMasterCanvas || false,
        nodes: list1,
        edges: list2
      }
    }
  })
  graph.value.bindKey(['meta+v', 'ctrl+v'],async () => {
    // isMoves.value = false
    if (copyCellList.value.isMasterCanvas !== props.curCanvas.isMasterCanvas) {
      return ElMessage({
        type: 'warning',
        message: `您复制的语料所属流程类型与当下流程类型不符！`
      })
    }
    if (copyCellList.value && copyCellList.value.nodes.length > 0) {
      const posHtml = graphRef.value.querySelector('.add-dom') && document.querySelector('.add-dom')?.getBoundingClientRect()
      const posCavas = posHtml && posHtml.x && posHtml.y ? graph.value.clientToLocal({
        x: posHtml?.x,
        y: posHtml?.y + 200
      }) : {
        x: 50,
        y: 200
      }
      emits('paste', copyCellList.value, posCavas)
      x6History.value.push({
        data: copyCellList.value.pasteIds,
        type: '画布-复制'
      })
    }
  })
}
const clearAudio = () => {
  if (currentAudio.value) {
    currentAudio.value.pause()
    currentAudio.value.src = ''
  }
  currentAudio.value = null
  currentAudioName.value = null
}
const playList = (urls: string[], i: number = 0) => {
  if (!urls || i >= urls.length) {
    if (currentAudio.value) {
      currentAudio.value.src = ''
      currentAudio.value.pause()
    }
    return
  }
  if (!currentAudio.value) {
    currentAudio.value = new Audio()
  }
  if (currentAudio.value) {
    currentAudio.value.src = urls[i]
    currentAudio.value.playbackRate = playbackRate.value || 1
    currentAudio.value.loop = false
    currentAudio.value.addEventListener('loadedmetadata', () => {
      currentAudio.value && currentAudio.value.play();
    })
    currentAudio.value.addEventListener('ended', function () {
      playList(urls, i + 1);
    }, false)
  }
}

const handlePlayRateChange = () => {
  if (currentAudio.value) {
    currentAudio.value.playbackRate = playbackRate.value || 1
  }
}

onMounted(() => {
  init()
})

onActivated(() => {
  init()
})

const clearAll = () => {
  graph.value?.clearKeys()
  graph.value?.dispose()
  if (currentAudio.value) {
    currentAudio.value.pause()
    currentAudio.value.src = ''
  }
  currentAudio.value = null
  currentAudioName.value = null
  nodeList.value = null
  edgeList.value = null
  graph.value = null
  graphRef.value = null
}
onDeactivated(() => {
  clearAll()
})
onUnmounted(() => {
  clearAll()
})
onBeforeRouteLeave(() => {
  clearAll()
})
</script>

<style lang="postcss" type="text/postcss">

.graph-container {
  width: 100%;
  height: 100%;
  background-color: #fff;
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
  .x6-node-selected .dom-body {
    opacity: 0.9;
    box-sizing: border-box;
    &::before {
      content: "";
      position: absolute;
      top: -3px;
      left: -3px;
      right:-3px;
      bottom: -3px;
      border-radius: 3px;
      background:
        linear-gradient(90deg, rgba(26, 115, 232) 50%, transparent 0) repeat-x,
        linear-gradient(90deg, rgba(26, 115, 232) 50%, transparent 0) repeat-x,
        linear-gradient(0deg, rgba(26, 115, 232) 50%, transparent 0) repeat-y,
        linear-gradient(0deg, rgba(26, 115, 232) 50%, transparent 0) repeat-y;
      background-size: 6px 2px, 6px 2px, 2px 6px, 2px 6px;
      background-position: 0 0, 0 100%, 0 0, 100% 0;
      /* animation: node-change 0.5s infinite linear; */
    }
  }
  @keyframes node-change {
    100% {
      background-position: 6px 0, -6px 100%, 0 -6px, 100% 6px;
    }
  }
  .x6-edge-selected path {
    /* animation-name:  edge-change;
    animation-duration: 0.5s;
    animation-iteration-count: infinite; */
    stroke-width: 3px;
    stroke-dasharray: 5px, 2px;
  }
  @keyframes edge-change {
    0% {
      stroke-dashoffset: 4px;
    }
    50% {
      stroke-dashoffset: 2px;
    }
    100% {
      stroke-dashoffset: 0;
    }
  }

  .btn-box {
    position: absolute;
    left: 0;
    top: 0;
    background-color: rgba(122, 122, 122, 0.2);
    border-radius: 4px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    border: 0;
    .corpus-box {
      width: 120px;
      height: 60px;
      background-color: var(--primary-black-color-50);
      border-radius: 4px;
      display: flex;
      align-items: center;
      flex-direction: column;
      position: relative;
      overflow: hidden;
      cursor: pointer;
      &:nth-child(n + 2) {
        margin-top: 8px;
      }
      &:hover {
        background-color: #fff;
        border: 1px solid var(--primary-black-color-200);
        .name {
          font-weight: 600;
        }
        .branch span{
          background-color: var(--el-color-primary);
        }
        .link {
          background-color: var(--el-color-primary);
        }
      }
      .name {
        font-size: 14px;
        color: var(--primary-black-color-600);
        margin: 14px auto 8px;
      }
      .branch {
        display: flex;
        justify-content: space-around;
        width: 100%;
        span {
          border-radius: 2px;
          height: 12px;
          width: 25px;
          background-color: var(--primary-black-color-200);
        }
      }
      .link {
        width: 100%;
        background-color: var(--primary-black-color-200);
        height: 18px;
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
    .el-button:nth-child(n+2) {
      margin-left: 0;
      margin-top: 10px;
    }
  }
}
.graphClass {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 2001;
}
</style>
