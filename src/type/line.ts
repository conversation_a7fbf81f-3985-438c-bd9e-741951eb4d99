import { SupplierLineInfo } from './supplier'
export interface SupplierLineParams {
  callLineSupplierName?: string,
  callLineSupplierNumber?: string,
  cityCodes?: string[],
  displayCallNumber?: string,
  enableStatus?: string,
  masterCallNumber?: string,
  primaryIndustries?: string[],
  serviceProviders?: string[],
  isForEncryptionPhones?: boolean,
  supplyLineName?: string,
  supplyLineNumber?: string
}
export interface SupplierLineInfoItem extends SupplierLineInfo {}

export interface SupplierParams {
  masterCallNumber?: string,
  supplierName?: string,
  supplierNumber?: string,
  supplyLineType?: string
  supplyLineName?: string,
  supplyLineNumber?: string,
  recentMin?: number | null
  isPending?: boolean;
}
export interface TenantParam{
  tenantLineName?: string,
  tenantLineNumber?: string,
  tenantLineType?: string,
  tenantName?: string,
  tenantNumber?: string,
  account?: string,
  groupId?: string,
  recentMin?: number | null,
}

export interface SupplierMonitorDetailsParams extends TenantParam {
  account?: string,
  enableStatus?: string,
  supplyLineNumber?: string
}

export interface SupplierDosageDailyParams extends SupplierParams {
  date?: string,
}
export interface SupplierDosageDetailsParams extends TenantParam {
  date?: string,
  supplyLineNumber?: string,
}

export interface TenantLineParam extends TenantParam{
  status?: string,
  supplierName?: string,
  supplierNumber?: string,
}
export interface TenantMonitorDetailsParams {
  enableStatus?: string,
  supplierName?: string,
  supplierNumber?: string,
  supplyLineName?: string,
  tenantLineNumber?: string,
  recentMin?: number
}
export interface TenantDosageDailyParam extends TenantParam{
  date?: string,
}
export interface TenantDosageDetailsParam extends SupplierParams{
  date?: string,
  tenantLineNumber?: string,
}

export interface TodayInfo {
  totalCallNum?: number
  todayConnected: string,
  todaySilenceCall: string,
  todayOneSecondConnected: string,
  todayTwoSecondConnected: string,
  todayCallFailed: string,
  todaySilenceHangup: string, // 标签沉默挂机
  todayAssistant: string, // 标签小助理
  todayPromptSound: string, // 标签运营商提示音
  todayClassANum: string,
  todayClassBNum: string,
  todayClassCNum: string,
  todayClassDNum: string,
  todayTransCallSeatNum: string, // 转人工占比
}

interface CurrentTodayInfo extends TodayInfo {
  currentlyCallNum: number
  currentlyConnected: string,
  currentlyConnectedRate: string,
  currentlyConnectedNum: number,
  currentlySilenceCall: string,
  currentlyOneSecondConnected: string,
  currentlyTwoSecondConnected: string,
  currentlyCallFailed: string,
  currentlyRoutingFail?: string, // 路由失败
  currentlySilenceHangup: string, // 标签沉默挂机
  currentlyAssistant: string, // 标签小助理
  currentlyPromptSound: string, // 标签运营商提示音
  currentlyClassANum: string,
  currentlyClassBNum: string,
  currentlyClassCNum: string,
  currentlyClassDNum: string,
  currentlyTransCallSeatNum: string, // 转人工占比
}

export interface SupplierMonitorInfoItem extends SupplierParams, CurrentTodayInfo {
  concurrency: string,
  callSupplierNumber: string,
  callSupplierName: string,
  isTempStop?: boolean,
  concurrentLimit?: number,
  isPriority?: boolean
}

export interface LineDosageItem {
  statisticDate: string,
  calculateNumOfSixty: number,
  calculateNumOfSix: number,
  totalCallNum: number,
  totalConnectNum: number,
  connectRate: string,
  totalConnectedDuration: number,
  averageConnectedDuration: number,
  silenceCallProportion: string,
  oneSecondConnectedProportion: string,
  twoSecondConnectedProportion: string
  callFailedProportion: string,
  transCallSeatNum: string,
  classANum: string;
  classBNum: string;
  classCNum: string;
  classDNum: string;
  promptSound: number;
  silenceHangup: number;
  assistant: number;
  unitPrice?: number // 供应线路单价
}
export interface SupplierDosageDailyItem extends SupplierParams, LineDosageItem {}

export interface SupplierDosageDetailsItem extends TenantParam, LineDosageItem {
  id: number,
  supplyLineNumber: string,
  createTime: string,
  updateTime: string
}
export interface TenantLineItem extends TenantParam{
  concurrentLimit: number,
  status: string,
  updateTime: string,
  groupId: string,
  tenantId: number
}
export interface MonitorInfoItem extends TodayInfo {
  supplyLineNumber?: string,
  supplyLineName?: string,
  tenantLineNumber?: string,
  tenantLineName?: string,
  tenantNumber?: string,
  supplierNumber?: string,
}

export enum MonitorTypeEnum {
  '外呼接通率' = 'CONNECT',
  '无声通话占比' = 'SILENCE',
  '沉默挂机占比' = 'SILENCE_HANGUP',
  '小助理占比' = 'ASSISTANT',
  '秒挂（1S）占比' = 'ONE_SECOND',
  '秒挂（2S）占比' = 'TWO_SECOND',
  '送呼失败占比' = 'FAIL',
  '转人工占比' = 'TRANS_CALL_SEAT',
  'A类占比' = 'CLASS_A',
  'B类占比' = 'CLASS_B',
  'C类占比' = 'CLASS_C',
  'D类占比' = 'CLASS_D',
  '运营商提示音占比' = 'PROMPT_SOUND',
}
export interface MonitorChartSearchInfo {
  minutes?: number[],
  date?: string,
  dates?: string[],
  supplyLineNumber?: string,
  tenantLineNumber?: string,
  type: MonitorTypeEnum,
  size?: number,
  operators?: string[],
  province?: string,
}
export interface MonitorChartInfo {
  [key: string]: {
    num: number,
    total: number,
  }
}
export interface TenantMonitorInfoItem extends TenantParam, CurrentTodayInfo {
  account: string,
  concurrency: string,
  status: string,
  priority: number,
  isTempStop?: boolean,
  concurrentLimit?: number,
  isPriority?: boolean,
}

export interface TenantDosageDailyItem extends TenantParam, LineDosageItem {}
export interface TenantDosageDetailsItem extends SupplierParams, LineDosageItem {
  id: number,
  createTime: string,
  updateTime: string,
  tenantLineNumber: string,
}
