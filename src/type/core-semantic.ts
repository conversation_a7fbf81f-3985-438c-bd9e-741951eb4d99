export interface AiCorePhrase {
  active?: number
  id?: number
  phraseName: string
  createTime?: string
  updateTime?: string
  semanticId: number
}

export interface AiSemantics {
  id?: number
  active?: number
  sequence?: number
  semantic: string
  aiCorePhrases?: AiCorePhrase[]
  secondIndustry?: string
  secondIndustryId?: number
  createTime?: string
  updateTime?: string
}

export interface AiLabelSemantics {
  semanticId: number,
  semantic: string,
  semanticLabelList: string[]
}

/**
 * 语义短语增删记录列表
 */
export interface AiCorePhraseRecord {
  deleteList: AiCorePhrase[]
  saveList: AiCorePhrase[]
  secondIndustryId?: number
}

export interface AiSemanticsResponse {
  aiCorePhrases?: AiCorePhrase[]
  deleteAICorePhrases: string[]
  insertAICorePhrases: string[]
  updateTime?: string
}

export interface SecondIndustrySemanticItem {
  id: number
  secondIndustry: string
  updateTime: string
  createTime: string
  publishStatus: boolean
  primaryIndustryId: number
}

// 语义标签 搜索类型 枚举
export enum SemanticsLabelSearchTypeEnum {
  '语义标签' = 'semantic_label',
  '语义' = 'semantic',
}

// 语义标签
export interface SemanticsLabelItem {
  createTime?: string;
  id?: number;
  secondIndustryId?: number;
  semanticLabel?: string;
  sequence?: number;
  updateTime?: string;
}

// 语义标签 关联的语义
export interface SemanticsRelationItem {
  semanticId?: number[];
  semanticLabelId?: string;
}

// 语义测试 接口参数
export interface SemanticsTestParam {
  secondIndustryId?: number;
  content?: string;
}

// 语义检查 接口参数
export interface SemanticsCheckParam {
  secondIndustryId?: number;
  scriptId?: number;
}

// 语义检查 语义列表
export interface SemanticsCheckItem {
  unUsedSemanticList?: AiSemantics[];
  usedSemanticList?: AiSemantics[];
}
