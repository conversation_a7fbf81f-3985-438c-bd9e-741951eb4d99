import { RestrictModal } from './common';
import { InterruptTypeEnum, scriptUnitContent } from './speech-craft'
import { FollowUpStatusEnum } from './clue'
import { SmsVariableItem } from './merchant'
import dayjs from 'dayjs'

export enum TaskStatusEnum {
  '待执行' = '待执行',
  '进行中' = '进行中',
  '已停止' = '已停止',
  '未完成' = '未完成',
  '全部' = '全部',
}

export enum HangupEnum {
  'AI' = 0,
  '客户',
  '坐席'
}

export enum TaskTypeEnum {
  'AI外呼' = 'AI_AUTO',
  '人机协同' = 'AI_MANUAL',
}

export enum RecordTypeEnum {
  'AI外呼' = 'AI_AUTO',
  '人机协同' = 'AI_MANUAL',
  '人工直呼' = 'MANUAL_DIRECT_CALL',
}

export enum OccupyRateEnum {
  '高' = 6,
  '中' = 3,
  '低' = 1
}

export enum CallTeamPushEnum {
  '轮循' = 'ROUND_ROBIN'
}

export enum CallRatioTypeEnum {
  '首呼优先' = 0,
  '多轮次呼叫按比例',
}

export enum CallTeamHandleEnum {
  '接听' = 'ANSWER',
  '监听' = 'MONITOR',
}

export enum ImportTypeEnum {
  // '单个导入' = 'SINGLE',
  '批量导入' = 'BATCH',
  '接口导入' = 'API',
  '添加呼叫' = 'ADDCALL',
}

export const CauseList = {
  'NO_USER_RESPONSE': {
    name: 'NO_USER_RESPONSE(18)',
    value: 'NO_USER_RESPONSE'
  },
  'ORIGINATOR_CANCEL': {
    name: 'ORIGINATOR_CANCEL(16)',
    value: 'ORIGINATOR_CANCEL'
  },
  'NORMAL_CLEARING': {
    name: 'NORMAL_CLEARING(16)',
    value: 'NORMAL_CLEARING'
  },
  'USER_BUSY': {
    name: 'USER_BUSY(17)',
    value: 'USER_BUSY'
  },
  'UNALLOCATED_NUMBER': {
    name: 'UNALLOCATED_NUMBER(1)',
    value: 'UNALLOCATED_NUMBER'
  },
  'RECOVERY_ON_TIMER_EXPIRE ': {
    name: 'RECOVERY_ON_TIMER_EXPIRE(102)',
    value: 'RECOVERY_ON_TIMER_EXPIRE'
  },
  'CALL_REJECTED': {
    name: 'CALL_REJECTED(21)',
    value: 'CALL_REJECTED'
  },
  'NORMAL_TEMPORARY_FAILURE': {
    name: 'NORMAL_TEMPORARY_FAILURE(41)',
    value: 'NORMAL_TEMPORARY_FAILURE'
  },
  'NORMAL_UNSPECIFIED': {
    name: 'NORMAL_UNSPECIFIED(31)',
    value: 'NORMAL_UNSPECIFIED'
  },
  'NO_ANSWER': {
    name: 'NO_ANSWER(19)',
    value: 'NO_ANSWER'
  },
  'INCOMPATIBLE_DESTINATION': {
    name: 'INCOMPATIBLE_DESTINATION(88)',
    value: 'INCOMPATIBLE_DESTINATION'
  }
}

export class TaskManageOrigin implements TemplateBaseItem {
  // 存在默认值
  callTeamPushType = CallTeamPushEnum['轮循'] // 坐席推送方式
  nextDayCall = 0 // 默认不隔日续呼
  occupyRate = OccupyRateEnum['中'] // 坐席占用等级
  callRatioType = 1 // 请选择资源分配方式，默认是首呼优先
  autoReCall = 0 // 自动补呼，默认否

  id = undefined
  templateName = ''
  comment = ''
  taskName = ''
  taskType
  speechCraftName = ''
  speechCraftId = undefined
  scriptStringId = ''
  version = undefined
  callTeamIds = undefined // 坐席组
  callTeamHandleType = undefined // 坐席处理方式
  lineRatio = undefined // 集线比
  startWorkTimeList = []
  endWorkTimeList = []
  firstRecallTime = undefined
  secondRecallTime = undefined
  hangUpSms = undefined
  hangUpExcluded = undefined
  scriptSms = undefined
  tenantBlackList = undefined

  // 屏蔽地区属性
  allRestrictProvince = undefined
  allRestrictCity = undefined
  ydRestrictProvince = undefined
  ydRestrictCity = undefined
  ltRestrictProvince = undefined
  ltRestrictCity = undefined
  dxRestrictCity = undefined
  dxRestrictProvince = undefined
  virtualRestrictCity = undefined
  virtualRestrictProvince = undefined
  unknownRestrictCity = undefined
  unknownRestrictProvince = undefined
  constructor(taskType: TaskTypeEnum) {
    this.taskType = taskType
  }
}

export interface TaskManageModel {
  ids?: string
  ifFindAll?: string
  taskName?: string
  taskType?: TaskTypeEnum
  callStatus?: TaskStatusEnum
  speechCraftName?: string
  lineCodes?: string[]
  startTime?: string
  endTime?: string
}

export interface TaskPageModel extends TaskManageModel {
  startPage: number,
  pageNum: number,
  groupId?: string, // 用于通过groupId查询接口
}

export interface TemplateBaseItem extends RestrictModal {
  id?: number,
  groupId?: string,
  taskIds?: string,
  templateName?: string,
  templateStatus?: number, // 模板是否停用： 0否；1是
  nextDayCall?: number, // 是否隔日续呼： 0否；1是
  comment?: string,
  taskName?: string,
  taskType?: TaskTypeEnum,
  speechCraftName?: string,
  speechCraftId?: number
  scriptStringId?: string
  version?: number
  startWorkTimeList?: string[],
  endWorkTimeList?: string[],
  startWorkTimes?: string
  endWorkTimes?: string
  callRatioType?: CallRatioTypeEnum,
  virtualSeatRatio?: number,
  autoReCall?: number,
  firstRecallTime?: number | null,
  secondRecallTime?: number | null,
  isAutoStop?: number // 是否被自动止损 0 否 1是
  taskEndTime?: string
  callTeamIds?: number[] // 坐席组
  callTeamPushType?: CallTeamPushEnum // 坐席推送方式
  callTeamHandleType?: CallTeamHandleEnum // 坐席处理方式
  lineRatio?: number // 集线比
  occupyRate?: OccupyRateEnum // 坐席占用等级
  updateTime?: string
  hangUpSms?: {
    intentionType?: string,
    labelIds: string[],
    smsTemplateId?: number,
    triggerOrder: number
  }[]
  hangUpExcluded?: string[]
  scriptSms?: {
    smsTemplateId?: number,
    // triggerId: number,
    triggerName: string
  }[],
  tenantBlackList?: number[]
}

export interface TaskManageItem extends TemplateBaseItem {
  id?: number
  groupId?: string
  account?: string
  createTime?: string
  updateTime?: string
  ifLock?: number // 任务是否被锁定
  lineId?: number
  lineName?: string
  callStatus?: TaskStatusEnum
  phoneNum?: number // 今日、任务名单总量
  calledPhoneNum?: number // 任务呼叫过名单量
  callingPhoneNum?: number // 任务待呼名单量
  recallingPhoneNum?: number // 任务待补呼名单量
  finishedPhoneNum?: number // 完成名单数
  finishedPhoneRate?: number // 名单数完成率
  callCycle?: number // 呼叫轮次
  aiAnswerNum?: number // 外呼并发
  phoneOpPercent?: string // 数据所属网段
  tenantName?: string // 商户名称
  tenantNo?: string // 商户编号
  ifSendSms?: string // 是否下发短信
  batchStatus?: string // 批量操作执行状态
  taskStartTime?: string // 任务开始时间
  taskEndTime?: string // 任务结束时间
  expectedFinishTime?: string // 预计完成时间
  variableSms?: SmsVariableItem[] | null
  smsTemplateAbnormal?: number // 短信通道状态：1异常， 0正常
  putThroughPhoneNum?: number // 任务接通名单数
  triggerSmsNumber?: number // 短信触发数
  sendSmsNumber?: number // 短信发送数
  putThroughPhoneRate?: number // 接通率
  triggerSmsRate?: number // 短信触发率
  sendSmsRate?: number // 短信成功率
  templateId?: string // 模板编号
}

export interface TaskManageRates {
  calledNum?: number
  reachRate?: number // 触达率
  finishedNum?: number
  finishedRate?: number // 完成率
  putThroughNum?: number // 接通
  putThroughRate?: number //接通率
  phoneIntentionRate?: number // 意向率
  phoneIntentionNum?: number // 意向客户数
}

export interface ManualTaskManageSeatDetail {
  id: number,
  account?: string,
  name: string,
  callSeatId?: number,
  callSeatRestTimeStart?: string,
  callSeatStatus?: string,
}

export interface ManualTaskManageStatics {
  principleConcurrency?: number // 理论并发
  realConcurrent?: number // 实际并发
  lineRatio?: number // 集线比
  transferRate?: number // 近10分钟转接绿
  putThroughPhoneRate?: number // 近10分钟接通率
  occupyRate?: OccupyRateEnum,
  virtualSeatRatio?: number // 虚拟坐席系数 0-100
  seatDetails?: ManualTaskManageSeatDetail[] // 坐席信息
  taskId?: number
  taskName?: string
  signedSeats?: number // 签入坐席数量
}

export class ManualTaskManageStaticsOrigin {
  aiAnswerNum = undefined
  realConcurrent = undefined
  lineRatio = undefined
  transferRate = undefined
  putThroughPhoneRate = undefined
  seatDetails = undefined
  taskId = undefined
  taskName = undefined
}

export interface TaskLineInfo {
  lineId?: number
  lineName?: string
  lineNum?: number
}

export interface TaskRestrictModal extends RestrictModal {
  id?: number
  taskIds?: string
}

// 在显示中，运营端则正常显示。在商户端进行归纳
// 【黑名单】和【频率限制】=>【未接通】，
// 【获取线路异常、没有话术、没有商户线路】=>【呼叫异常】

export enum CallStatusEnum {
  '呼叫成功' = '7',
  '补偿失败' = '6',
  '未接通' = '0',
  '路由失败' = '1',
  '获取线路异常' = '2',
  '已屏蔽' = '3',
  '没有话术' = '4',
  '没有商户线路' = '5',
  '平台黑名单' = '10',
  '商户黑名单' = '11',
  '平台频率限制' = '15,16',
}

// 商户端呼叫状态所有【路由失败、获取线路异常、没有话术、没有商户线路等】状态都是未接通
// 商户端呼叫状态枚举1，用于通话记录
export enum CallStatusEnum1 {
  '呼叫成功' = '7',
  '未接通' = '0,1,2,4,5,6,8,9,12,13',
  '商户黑名单' = '11',
  '拦截' = '10,15,16',
  '已屏蔽' = '3',
}

// 商户端呼叫状态枚举2，用于批量添加呼叫
export enum CallStatusEnum2 {
  '呼叫成功' = '7',
  '未接通' = '0,2,4,5,6,8,9,10,11,12,13,15,16',
  '未接通（其他）' = '1', // 路由失败，不给商户看到具体名称
  '已屏蔽' = '3',
}

export class SearchRecordFormOrigin {
  needOrder // '0' | '1'，是否保持排序，默认当日排序,非当日不排序
  emptyLabelSearch = undefined // // N：不查空标签，空：查询空标签
  followUpStatus = undefined
  callTeamIds = undefined
  callSeatIds = undefined
  followUpNote = undefined
  ifFastRecall // 仅商户端传，运营端不传
  adminFlag
  phone = undefined
  taskName = undefined
  scriptStringIdList = undefined
  taskId = undefined
  name = undefined
  intentionLabels = undefined
  intentionLabelName = undefined
  intentionLabelNameContains = undefined
  hitSemanticIds = undefined
  hitSemanticIdsContains = undefined
  whoHangup = undefined
  intentionClass = undefined
  operator = undefined
  province = undefined
  city = undefined
  callDurationLeft = undefined
  callDurationRight = undefined
  cycleCountLeft = undefined
  cycleCountRight = undefined
  sayCountLeft = undefined
  sayCountRight = undefined
  postingDurationLeft = undefined // 话后
  postingDurationRight = undefined
  isPostingOutOfTime = undefined
  waitmsecLeft = undefined
  waitmsecRight = undefined
  ifSendSms = undefined
  customerReplyContent = undefined
  tenantName = undefined // 商户名称
  merchantName = undefined // 旧版商户名称
  merchantLineCode = undefined // 商户线路编号
  lineCode = undefined // 供应线路名称
  tenantBlacklist = undefined // 商户黑名单
  cause = undefined // 挂机原因
  aiCallIp = undefined // 中继ip
  fsIp = undefined // 机器人IP
  account = undefined // 商户账号
  talkTimeStartStart = undefined // 接通时间
  talkTimeStartEnd = undefined
  noReceptionReason = undefined // 未介入原因
  listenDurationLeft = undefined // 人工监听
  listenDurationRight = undefined
  transToCallSeatDurationLeft = undefined // 转人工等待
  transToCallSeatDurationRight = undefined
  receptionDurationLeft = undefined // 人工接待
  manualIntentionLabels = undefined // 人工标签
  manualIntentionClass = undefined // 人工分类
  receptionDurationRight = undefined
  isTransToCallSeat = undefined // 是否转人工
  isConvertToClue = undefined // 是否转为线索
  calloutStartTime = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
  calloutEndTime = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')

  constructor(accountType?: number, createTime?: string) {
    this.ifFastRecall = accountType ? '1' : undefined;
    this.adminFlag = accountType ? '1' : '0';
    this.calloutStartTime = createTime ? dayjs(createTime).startOf('day').format('YYYY-MM-DD HH:mm:ss') : dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss');
    this.calloutEndTime = createTime ? dayjs(createTime).endOf('day').format('YYYY-MM-DD HH:mm:ss') : dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss');
    this.needOrder = createTime && dayjs(createTime).isBefore(dayjs().startOf('day')) ? '0' : '1'
  }
}

export interface TaskCallSearchModal {
  ifFastRecall?: string // 快速补偿：商户端传'1'，运营端筛选（1快速补偿，-1）
  adminFlag?: string //  0是运营端标记 1是商户端。用于返回数据中的呼叫状态是否需要合并为未呼通（1表示需要）
  orderByTime?: string // 批量添加呼叫中是否需要排序
  excludePutThrough?: string // '0' 不排除 | '1' 排除
  needOrder?: string // '0' 不排序 | '1' 排序
  emptyLabelSearch?: string // N：不查空标签，空：查询空标签
  taskId?: string | number
  taskIds?: string | number
  taskName?: string
  tenantBlacklist?: string[] // 商户黑名单
  scriptStringIdList?: string // 话术scriptStringId字符串，都好分开
  phone?: string
  operator?: string
  province?: string
  city?: string
  name?: string
  account?: string // 商户账号
  tenantName?: string // 商户名称
  merchantName?: string // 旧版商户名称
  merchantLineCode?: string // 商户线路编号
  lineCode?: string // 供应线路名称
  cause?: string // 挂机原因
  aiCallIp?: string // 中继ip
  fsIp?: string // 机器人IP
  callStatus?: string
  calledNumLeft?: number // 拨打
  calledNumRight?: number
  putThroughNumLeft?: number // 接通
  putThroughNumRight?: number
  callDurationLeft?: number // 通时
  callDurationRight?: number
  waitmsecLeft?: number // 等待时长
  waitmsecRight?: number
  cycleCountLeft?: number // 对话
  cycleCountRight?: number
  sayCountLeft?: number // 说话
  sayCountRight?: number
  noReceptionReason?: string // 未介入原因
  listenDurationLeft?: number // 人工监听
  listenDurationRight?: number
  transToCallSeatDurationLeft?: number // 转人工等待
  transToCallSeatDurationRight?: number
  receptionDurationLeft?: number // 人工接待
  receptionDurationRight?: number
  postingDurationLeft?: number // 话后
  postingDurationRight?: number
  addStartTime?: string // 加入时间
  addEndTime?: string
  lastCallStartTime?: string // 最后外呼时间
  lastCallEndTime?: string
  calloutStartTime?: string // 外呼时间
  calloutEndTime?: string
  talkTimeStartStart?: string // 接通时间
  talkTimeStartEnd?: string
  intentionLabelName?: string
  // 人机协同 AI标签内容
  intentionLabels?: string
  intentionLabelNameContains?: string
  hitSemanticIds?: string // 核心语义
  hitSemanticIdsContains?: string
  intentionClass?: string
  manualIntentionLabels?: string // 人工标签
  manualIntentionClass?: string // 人工分类
  isPostingOutOfTime?: boolean // 是否话后处理超时
  customerReplyContent?: string // 用户回复
  ifSendSms?: string
  whoHangup?: HangupEnum
  startPage?: number
  pageNum?: number
  isTransToCallSeat?: boolean // 是否转人工
  isConvertToClue?: boolean // 是否转为线索
  callTeamIds?: string
  callSeatIds?: string
  followUpNote?: string
  followUpStatus?: FollowUpStatusEnum
}

export interface TaskCallItemBase {
  id?: number | string
  taskId?: number
  taskName?: string
  phone?: string
  operator?: string
  msg?: string
  name?: string
  province?: string
  city?: string
  recordId?: string
  phoneRecordId?: string
  phoneId?: string
  callId?: string
  callStatus?: CallStatusEnum
  // callStatusStr?: CallStatusEnum
}

export interface TaskCallItem extends TaskCallItemBase {
  putThroughNum?: string // 接通次数
  calledNum?: string // 呼叫次数
  addTime?: string
  scriptStringId?: string
  createTime?: string
  finalCallTime?: string
}

export interface MerchantAiCallRecordItem {
  id?: string
  recordId?: string
  callId?: string
  // phone?: string // 后端不再返回

  operator?: string // 运营商
  name?: string
  province?: string // 省市
  city?: string

  account?: string // 账号名称
  callStatus?: CallStatusEnum // 呼叫状态
  intentionClass?: string // 意向分类
  intentionLabels?: string // 意向标签
  ifSendSms?: string // 是否发送短信， 是/否
  sayCount?: number // 说话次数
  cycleCount?: number // 对话轮次
  callDuration?: number // 通话时长ms
  callOutTime?: string // 外呼时间
  talkTimeStart?: string // 通话开始
  talkTimeEnd?: string // 通话结束
  taskName?: string // 任务名称
  scriptStringId?: string // 话术大版本id
  speechCraftId?: number // 话术小版本id
  isConvertToClue  ?: boolean // 是否转为线索
  whoHangup?: HangupEnum // 挂机发
  wholeAudioFileUrl?: string // 音频地址
}

export interface MerchantAiMaunalCallRecordItem extends MerchantAiCallRecordItem {
  clueId?: number // 线索id
  callTeamName?: string // 坐席组
  callTeamId?: number // 坐席组id
  callSeatId?: number // 坐席
  postingDuration?: number // 话后处理时长
  isPostingOutOfTime?: boolean // 是否话后超时
  formId?: number // 表单id
  followUpNote?: string // 跟进备注
  followUpStatus?: FollowUpStatusEnum// 跟进状态
  startAnswerTime?: string // 开始接听时间
  endAnswerTime?: string // 结束接听时间
  manualIntentionLabels?: string // 人工标签
  manualIntentionClass?: string // 人工分类
  isConvertToClue?: boolean // 是否转为线索
  
  isTransToCallSeat?: boolean // 是否转人工
  startPopWinTime?: string // 转人工等待 = 结束-开始
  endPopWinTime?: string
  // misCallSeatIds?: string[] // 漏接坐席
  misCallSeatIdsText?: string // 漏接坐席
  noReceptionReason?: string // 未介入原因
  startMonitorTime?: string // 开始监听时间
  endMonitorTime?: string // 结束监听时间
  transToCallSeatDuration?: number // 转人工等待
  listenDuration?: number // 人工监听
  taskId?: number
  receptionDuration?: number // 人工接待
  triggerMonitorTime?: string // 触发监听时间
  triggerAnswerTime?: string // 触发接听时间
}

export interface TaskCallRecordItem extends MerchantAiMaunalCallRecordItem {
  tenantName?: string // 商户名称
  secondIndustryName?: string // 项目名称
  programName?: string // 行业名称
  productName?: string // 产品名称
  waitmsec?: number // 等待时长
  lineId?: string // 供应线路id
  lineCode?: string // 供应线路code
  merchantLineCode?: string // 商户线路code
  merchantLineId?: string // 商户线路id
  aiCallIp?: string // 中继ip
  fsIp?: string // 机器人IP
  cause?: string // 挂机原因
  errorCode?: string // 挂机码
  hitAdvanceIds?: string // 命中高级规则ID
  hitSemantic?: string // 命中语义
  hitSemanticIds?: string
  status?: number // 当呼叫状态为平台黑名单和频率限制时的，黑名单或者频率的id，用于翻译挂机原因
}

export interface TaskDetailSearchBaseModel {
  name: string,
  id: string
  phone: string
  operator: string
  phoneArea: null | string[]
  startTime: string
  endTime: string
  currentPage: number
  pageSize: number

}

export interface TaskDetailSearchModel extends TaskDetailSearchBaseModel {
  callStatus: string,
  time: string[],

}

export interface TaskDetailInfoBaseItem {
  id: number,
  lineId: number,
  phone: string,
  name: string,
  address: string,
  operator: string,
  status: string,
  callStatus: string,
  createTime: string,
}

export interface TaskDetailInfoItem extends TaskDetailInfoBaseItem {
  callStatus: string,

}

export interface TaskBatchItem extends TaskManageItem {
  operateTime?: string
  firstRecallTimeStr?: string
  timeStr?: string
  autoReCallStr?: string
  callRatioTypeStr?: string
  batchStatus?: string
  isAutoStopStr?: string
  secondaryIndustry?: string
}

export interface RecordDialogueData {
  id: number;
  dialogTime?: string;
  content: string;
  urls: string[];
  speakMs?: number | null;
  hitSemantic: string | null;
  hitPhrase: string | null;
  hitIntention: string | null;
  hitBranch: string | null;
  corpusName: string | null;
  interruptType: InterruptTypeEnum;
  type: number; // 0: Ai， 1-2：客户， 3-4： 坐席
  unitContent?: string;
  dialogCorpusTempUuid?: string;
  /**
   * @deprecated 不再使用，使用type
   */
  robot: boolean; // 不再使用，使用type

  // 用于对话详情组件，展示当前语料的打断设置
  corpusConfig?: scriptUnitContent[] | null;
}

export enum SheetNameEnum {
  '操作日期' = 'operateTime',
  '任务ID' = 'id',
  '商户名称' = 'tenantName',
  '商户账号' = 'tenantNo',
  '任务名称' = 'taskName',
  '任务状态' = 'callStatus',
  '锁定状态' = 'ifLockStr',
  '短信通道状态' = 'smsTemplateAbnormal',
  '短信触发率' = 'triggerSmsNumber',
  '短信成功率' = 'sendSmsNumber',
  '预期完成时间' = 'expectedFinishTime',
  '话术名称' = 'speechCraftName',
  '任务创建时间' = 'createTime',
  '任务名单总量' = 'phoneNum',
  '任务已呼叫名单数' = 'calledPhoneNum',
  '任务接通名单数' = 'putThroughPhoneNum',
  '任务待补呼名单量' = 'recallingPhoneNum',
  '今日导入名单量' = 'phoneNum',
  '拨打时间段(24H)' = 'timeStr',
  '是否止损' = 'isAutoStopStr',
  '是否补呼' = 'autoReCallStr',
  '补呼间隔(min)' = 'firstRecallTimeStr',
  '线路名称' = 'lineName',
  '外呼并发' = 'aiAnswerNum',
  '资源分配模式' = 'callRatioTypeStr',
  '是否投送短信' = 'ifSendSms',
  '数据所属网段' = 'phoneOpPercent',
}

// 漏斗数据，接口数据
export interface FunnelDataType {
  phoneNum?: number
  calledNum?: number
  putThroughNum?: number
  putThroughRate?: number
  intentionNum?: number
  intentionRate?: number
  triggerSmsNum?: number // 短信触发数量
  triggerSmsRate?: number // 短信触发率
  successSmsNum?: number // 短信发送成功数量
  successSmsRate?: number // 短信发送成功率
}

// 意向分类，接口数据
export interface CategoryDataType {
  className?: string
  ratio?: number
  num?: number
}

// 意向分类，图表数据
export interface CategoryChartItem {
  name?: string
  value?: number
  num?: number
}

// 接通统计，接口数据
export interface ThroughDataType {
  // 呼叫量
  calledNum?: number
  // 呼叫接通量
  putThroughNum?: number
  // 呼叫接通比率
  putThroughRates?: number
}

// 接通率分布，接口数据
export interface RateDataType {
  time?: string
  rate?: number
  // 呼叫量
  calledNum?: number
  // 呼叫接通量
  putThroughNum?: number
}

// 账号下，话术维度 意向标签统计
export interface LabelDataItem {
  intentionLabelName?: string
  intentionLabelId?: number
  intentionLabelNum?: number
  putThroughNum?: number
  labelHitRates?: number
}

// 账号下，话术维度 意向分类统计
export interface IntentionDataItem {
  intentionClass?: string
  intentionClassNum?: number
  putThroughNum?: number
  intentionClassRates?: number
}

// 地区分布，接口数据
export interface RegionDateType {
  // 实际呼叫数
  calledNum?: number
  // 接口冗余字段（图表里暂时用不到）
  num?: number
  // 计划呼叫数
  phoneNum?: number
  // 省份名称
  province?: string
  // 名单接通数
  putThroughNum?: number
  // 名单接通率
  putThroughRate?: number
  // 剩余呼叫数
  remainingNum?: number
}

// 地区分布，图表数据
export interface RegionChartItem {
  // 省份名称
  name: string
  // 数据1，可视化，圆圈大小，比如接通数
  value1: number
  // 数据2，可视化，省份颜色，比如接通率
  value2?: number
  // 数据3，其他，仅作tooltip展示
  value3?: number
  // 数据4，其他，仅作tooltip展示
  value4?: number
  // 数据5，其他，仅作tooltip展示
  value5?: number
  // 数据6，其他，仅作tooltip展示
  value6?: number
  // 数据7，其他，仅作tooltip展示
  value7?: number
  // 省份地图样式，比如背景、边框、阴影等，属性名和ECharts保持一致
  itemStyle?: {
    // [prop: string]: string,
    areaColor?: string,
    borderColor?: string,
    shadowColor?: string,
  },
}

export enum AccountStatisticsTypeEnum {
  '外呼接通率' = 'CONNECT',
  '意向客户占比' = 'INTENTION',
  '无声通话占比' = 'SILENCE',
  '沉默挂机占比' = 'SILENCE_HANGUP',
  '小助理占比' = 'ASSISTANT',
  '秒挂（1S）占比' = 'ONE_SECOND',
  '秒挂（2S）占比' = 'TWO_SECOND',
  // '送呼失败占比' = 'FAIL',
  // '转人工占比' = 'TRANS_CALL_SEAT',
  'A类占比' = 'CLASS_A',
  'B类占比' = 'CLASS_B',
  'C类占比' = 'CLASS_C',
  'D类占比' = 'CLASS_D',
  'E类占比' = 'CLASS_E',
  'F类占比' = 'CLASS_F',
  'G类占比' = 'CLASS_G',
  '其他类占比' = 'CLASS_OTHER',
  '平均通话时长' =  'CLASS_ALL_DURATION',
  'A类平均通话时长' =  'CLASS_A_DURATION',
  'B类平均通话时长' =  'CLASS_B_DURATION',
  'C类平均通话时长' =  'CLASS_C_DURATION',
  'D类平均通话时长' =  'CLASS_D_DURATION',
  'E类平均通话时长' =  'CLASS_E_DURATION',
  'F类平均通话时长' =  'CLASS_F_DURATION',
  'G类平均通话时长' =  'CLASS_G_DURATION',
  '其他类平均通话时长' =  'CLASS_OTHER_DURATION'
}

// 账号统计报表筛选参数
export interface AccountStatisticsSearchParams {
  taskStatusList?: TaskStatusEnum[],
  aiOutboundTaskType?: TaskTypeEnum,
  scriptStringIdList?: string[],
  queryDate?: string,
  taskIdList?: number[],
  operator?: string,
  provinceCode?: string,
  province?: string,
  cityCode?: string,
  groupId?: string,
  type?: AccountStatisticsTypeEnum,
  timeGap?: number,
}

export interface AccountChartStatistics {
  totalCallNum?: number
  totalConnectNum: number,
  silenceCallNum: number,
  intentionNum: number, // 意向客户数
  oneSecondConnectedNum: number,
  twoSecondConnectedNum: number,
  silenceHangupNum: number,
  assistantNum: number,
  classANum: number,
  classBNum: number,
  classCNum: number,
  classDNum: number,
  classENum: number,
  classFNum: number,
  classGNum: number,
  classOtherNum: number,
  totalCallDurations: number, // 总通话时长
  totalCallDuration: number,
  classACallDuration: number,
  classBCallDuration: number,
  classCCallDuration: number,
  classDCallDuration: number,
  classECallDuration: number,
  classFCallDuration: number,
  classGCallDuration: number,
  classOtherCallDuration: number,
  // 前端计算的平均值
  avgCallDuration?: number,
  avgClassACallDuration?: number,
  avgClassBCallDuration?: number,
  avgClassCCallDuration?: number,
  avgClassDCallDuration?: number,
  avgClassECallDuration?: number,
  avgClassFCallDuration?: number,
  avgClassGCallDuration?: number,
  avgClassOtherCallDuration?: number,

}
// 账号统计报表
export interface AccountStatistics {
  waitingTasksNum?: number | null
  executingTasksNum?: number | null
  executingRates?: number | null

  calledNum?: number | null
  planedNum?: number | null
  calledRates?: number | null

  outboundCalledNum?: number | null
  outboundTotalNum?: number | null
  outboundCalledRates?: number | null

  successSmsNum?: number | null
  triggerSmsNum?: number | null
  successSmsRate?: number | null

  firstCallRemainNum?: number | null
  recalledRemainNum?: number | null
  averageCallDurations?: number | null
  intentionNums?: number | null
}

export interface ImportRecordItem {
  id?: number
  taskId: number; // 任务ID
  taskName: string; // 任务名称
  taskType: TaskTypeEnum; // 任务类型
  importType: ImportTypeEnum; // 导入类型
  importSuccessCount: number; // 导入成功的数量
  importFailCount: number; // 导入失败的数量
  importTime: string; // 导入时间
}

export interface ImportSearchModal {
  taskName?: string; // 任务名称
  taskType?: TaskTypeEnum; // 任务类型
  importType?: ImportTypeEnum; // 导入类型
  importTimeStart?: string; // 导入时间
  importTimeEnd?: string; // 导入时间
}

export interface ImportFailItem {
  comment: string;
  name: string;
  phone: string;
  text: string;
  failReason: string;
}

export interface TaskIntentionStatisticsItem {
  intentionANum: number;
  intentionBNum: number;
  intentionCNum: number;
  intentionDNum: number;
  intentionENum: number;
  intentionFNum: number;
  intentionGNum: number;
  intentionOthersNum: number;
  taskId: string;
  taskName: string;
  throughputNum: number;
}

export interface TaskFilterParamsInTool {
  callStatus?: string[];
  callingPhoneNumMax?: number; // 首呼剩余
  callingPhoneNumMin?: number;
  concurrentMax?: number;
  concurrentMin?: number;
  endTime?: string;
  expectedFinishTime?: string;
  expectedStartTime?: string;
  remainingNumMin?: number;  // 名单剩余
  remainingNumMax?: number;
  ifAutoRecall?: string;
  ifAutoStop?: string;
  lineCodes?: string[];
  phoneNumMax?: number;
  phoneNumMin?: number;
  recallingPhoneNumMin?: number; // 补呼剩余
  recallingPhoneNumMax?: number;
  scriptStringIds?: string[];
  smsTemplateAbnormal?: number;
  startTime?: string;
  taskIds?: number[];
  taskType?: TaskTypeEnum;
  loadType?: number
}

export class TaskFilterParamsInToolOrigin implements TaskFilterParamsInTool {
  startTime?: string = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss');
  endTime?: string = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss');;
  loadType: number = 0 // 默认覆盖0，可以选择递增1

  callStatus?: string[];
  callingPhoneNumMax?: number;
  callingPhoneNumMin?: number;
  concurrentMax?: number;
  concurrentMin?: number;
  expectedFinishTime?: string;
  expectedStartTime?: string;
  remainingNumMin?: number;
  remainingNumMax?: number;
  ifAutoRecall?: string;
  ifAutoStop?: string;
  lineCodes?: string[];
  phoneNumMax?: number;
  phoneNumMin?: number;
  recallingPhoneNumMin?: number;
  recallingPhoneNumMax?: number;
  scriptStringIds?: string[];
  smsTemplateAbnormal?: number;
  taskIds?: number[];
  taskType?: TaskTypeEnum;
}