import { IntentionType, LabelItem, } from './IntentionType'
import { CorpusConditionItem } from './corpus'
import { scriptCorpusModel, } from '@/api/speech-craft'

export enum SpeechCraftStatusEnum {
  '生效中' = 'ACTIVE',
  '已驳回' = 'REJECT',
  '审核中' = 'VERIFY',
  '编辑中' = 'EDIT',
  '预发布' = 'PREVIEW', // 预发布
  '已停用' = 'STOP', // 停用
}

export enum ContentTypeEnum {
  '普通文本' = 'TEXT',
  '变量' = 'VARIABLE',
}

export interface SpeechCraftInfoItem {
  id?: number
  lockAccount?: string // 编辑中话术，锁定编辑的账号名称
  editPermission?: boolean // 是否有编辑权限
  ownerAccount?: string // 所属账号
  watchAccounts?: string[] // 可见账号
  scriptName: string
  multiContentVersion?: boolean // 是否使用新版本的话术打断交互，true表示新的，false和空表示旧的
  status?: SpeechCraftStatusEnum
  createTime?: string
  updateTime?: string
  isDeleted?: boolean
  head_corpus_id?: null | number
  scriptStringId?: string
  version?: number
  primaryIndustry?: string
  secondaryIndustry?: string
  secondaryIndustryId?: number
  transferHuman?: boolean
  smsTriggerNames?: string[]
  lastUsingDate?: string
  remark?: string
}

export interface ScriptBaseInfo extends CorpusOpenScopeConfig {
  id?: number,
  weight?: number,
  name?: string,
  scriptId?: number,
  isMasterCanvas?: boolean,
  headCorpusId?: number | null,
  knowledgeGroupId?: number,
}

export interface ScriptInfo extends ScriptBaseInfo {
  createTime?: string,
  updateTime?: string,
  preCanvasId?: null | number,
  nextCanvasId?: null | number,
  canvasCorpusDataMap: {
    [propName: string]: CanvasCorpus;
  },
  canvasBranchDataMap: {
    [propName: string]: CanvasBranch;
  },
}

/**
 * 话术审核，话术审核记录
 * 页面展示和接口数据共用
 */
export interface ScriptCheckRecord {
  scriptName?: string | number | null
  userAccount?: string | number | null
  commitType?: string | number | null
  checkRes?: string | number | null
  startCommitTime?: string
  endCommitTime?: string
  startCheckTime?: string
  endCheckTime?: string
  defaultTime?: string | null
  userName?: string | number | null
  checkOpinion?: string | number | null
  checkTime?: string | number | null
}

/**
 * 话术审核，每个话术的基本信息
 */
export interface ScriptCheckItem {
  id: number
  scriptName?: string | null
  commitType?: string | null
  updateTime?: string | null
  scriptId?: number | null
  scriptStringId?: number | null
}

/**
 * 话术审核，审批，接口参数
 */
export interface ScriptCheckApproveParam {
  id: number | string
  checkRes: string
  opinion: string
}

/**
 * 话术审核，当前话术的音频单项
 */
export interface ScriptCheckAudioItem {
  id: number // 音频ID
  updateStatus?: string | number | null // 是否更新
  contentName?: string | null // 语料名称
  content?: string | null // 文字内容
  updateTime?: string | null // 最后更新时间
  audioPath?: string | null // 音频文件URL
  corpusType?: CorpusTypeEnum // 语料类型
  contentType?: ContentTypeEnum // 文本类型
}

/**
 * 话术审核，操作日志
 */
export interface ScriptOperationLog {
  scriptStringId: string
  userAccount?: string
  createTime?: string
  operateType?: string
  operateContent?: string
  opinion?: string
}

export interface CanvasBranch {
  branchId: number,
  preCorpusId: number,
  nextCorpusId: number,
  color?: number,
  name: string,
  branchContent?: string,
}

export interface CanvasCorpus {
  corpusId: number,
  corX: number,
  corY: number,
  connectType?: ConnectTypeEnum,
  branches: ScriptBranch[],
  eventTriggerValueIds?: number[] | null,
  name: string,
  listenInOrTakeOver: boolean,
  content: string,
  corpusType: CorpusTypeEnum,
  connectCorpusId: null | number,
  percent?: null | number | string,
  smsTriggerName?: string,
  aiIntentionType: {
    intentionType: string,
    intentionName: string
  } | null
}

export enum ConnectTypeEnum {
  '挂机' = 'HANG_UP',
  '回原主动流程' = 'RETURN_MASTER_PROCESS',
  '指定主动流程' = 'SELECT_MASTER_PROCESS',
  '回原语境' = 'RETURN_ORIGIN',
}

export enum CorpusTypeEnum {
  '主动流程-普通语料' = 'MASTER_ORDINARY',
  '主动流程-连接语料' = 'MASTER_CONNECT',
  '深层沟通-普通语料' = 'KNOWLEDGE_ORDINARY',
  '深层沟通-连接语料' = 'KNOWLEDGE_CONNECT',
  '基本问答' = 'KNOWLEDGE_BASE_QA',
  '沉默语料' = 'FUNC_SILENCE',
  '重复语料' = 'FUNC_REPEAT',
  '最高优先' = 'FUNC_PRIOR_QA',
  '打断垫句' = 'PRE_INTERRUPT',
  '续播垫句' = 'PRE_CONTINUE',
  '承接语料' = 'PRE_UNDERTAKE',
}

export enum QueryTypeEnum {
  '业务问答' = 'INDUSTRY_QUERY',
  '通用问答' = 'COMMON_QUERY',
}

export const corpusTypeOption = {
  [CorpusTypeEnum['主动流程-普通语料']]: {
    name: '主动流程-普通语料', value: CorpusTypeEnum['主动流程-普通语料'],
    keys: [
      'id', 'scriptId', 'isDeleted', 'name', 'isTopHead', 'isHead', 'branchList', 'isOpenContext', 'openScopeType', 'groupOpenScope',
      'scriptMultiContents', 'maxWaitingTime', 'aiLabels', 'aiIntentionType', 'eventTriggerValueIds', 'listenInOrTakeOver', 'smsTriggerName'
    ] as unknown as (keyof ScriptCorpusItem)[],
    api: (params: ScriptCorpusItem) => scriptCorpusModel.saveMasterOrdinaryCorpus(params)
  },
  [CorpusTypeEnum['主动流程-连接语料']]: {
    name: '主动流程-连接语料', value: CorpusTypeEnum['主动流程-连接语料'],
    keys: [
      'id', 'scriptId', 'isDeleted', 'name', 'connectType', 'connectCorpusId',
      'scriptMultiContents', 'aiLabels', 'aiIntentionType', 'eventTriggerValueIds', 'smsTriggerName',
    ] as unknown as (keyof ScriptCorpusItem)[],
    api: (params: ScriptCorpusItem) => scriptCorpusModel.saveMasterConnectCorpus(params)
  },
  [CorpusTypeEnum['深层沟通-普通语料']]: {
    name: '深层沟通-普通语料', value: CorpusTypeEnum['深层沟通-普通语料'],
    keys: [
      'id', 'scriptId', 'isDeleted', 'isTopHead', 'isHead', 'name', 'isOpenContext', 'openScopeType', 'groupOpenScope', 'branchList', 'smsTriggerName',
      'scriptMultiContents', 'maxWaitingTime', 'aiLabels', 'aiIntentionType', 'eventTriggerValueIds', 'listenInOrTakeOver', 'knowledgeGroupId',
      'semCombineEntity',
    ] as unknown as (keyof ScriptCorpusItem)[],
    api: (params: ScriptCorpusItem) => scriptCorpusModel.saveKnowledgeOrdinaryCorpus(params)
  },
  [CorpusTypeEnum['深层沟通-连接语料']]: {
    name: '深层沟通-连接语料', value: CorpusTypeEnum['深层沟通-连接语料'],
    keys: [
      'id', 'scriptId', 'isDeleted', 'name', 'connectType', 'connectCorpusId', 'knowledgeGroupId',
      'scriptMultiContents', 'aiLabels', 'aiIntentionType', 'eventTriggerValueIds', 'smsTriggerName',
    ] as unknown as (keyof ScriptCorpusItem)[],
    api: (params: ScriptCorpusItem) => scriptCorpusModel.saveKnowledgeConnectCorpus(params)
  },
  [CorpusTypeEnum['基本问答']]: {
    name: '基本问答', value: CorpusTypeEnum['基本问答'],
    keys: [
      'id', 'scriptId', 'isDeleted', 'name', 'connectType', 'connectCorpusId', 'listenInOrTakeOver',
      'smsTriggerName', 'queryType', 'scriptMultiContents', 'maxWaitingTime', 'aiLabels', 'aiIntentionType', 'eventTriggerValueIds', 'knowledgeGroupId',
      'isOpenContext', 'openScopeType', 'groupOpenScope', 'semCombineEntity',
    ] as unknown as (keyof ScriptCorpusItem)[],
    api: (params: ScriptCorpusItem) => scriptCorpusModel.saveKnowledgeBaseQA(params)
  },
  [CorpusTypeEnum['沉默语料']]: {
    name: '沉默', value: CorpusTypeEnum['沉默语料'],
    keys: [
      'id', 'scriptId', 'isDeleted', 'name', 'eventTriggerValueIds', 'listenInOrTakeOver', 'smsTriggerName',
      'scriptMultiContents', 'maxWaitingTime', 'aiLabels', 'aiIntentionType',
    ] as unknown as (keyof ScriptCorpusItem)[],
    api: (params: ScriptCorpusItem) => scriptCorpusModel.saveFuncSilenceCorpus(params)
  },
  [CorpusTypeEnum['重复语料']]: {
    name: '重复', value: CorpusTypeEnum['重复语料'],
    keys: [
      'id', 'scriptId', 'isDeleted', 'name', 'eventTriggerValueIds', 'listenInOrTakeOver', 'smsTriggerName',
      'scriptMultiContents', 'maxWaitingTime', 'aiLabels', 'aiIntentionType',
    ] as unknown as (keyof ScriptCorpusItem)[],
    api: (params: ScriptCorpusItem) => scriptCorpusModel.saveFuncRepeatCorpus(params)
  },
  [CorpusTypeEnum['最高优先']]: {
    name: '最高优先', value: CorpusTypeEnum['最高优先'],
    keys: [ //'responseType',
      'id', 'scriptId', 'isDeleted', 'name', 'connectType', 'connectCorpusId', 'listenInOrTakeOver', 'smsTriggerName',
      'scriptMultiContents', 'maxWaitingTime', 'aiLabels', 'aiIntentionType', 'eventTriggerValueIds',
      'isOpenContext', 'openScopeType', 'groupOpenScope', 'semCombineEntity',
    ] as unknown as (keyof ScriptCorpusItem)[],
    api: (params: ScriptCorpusItem) => scriptCorpusModel.saveFuncPriorQACorpus(params)
  },
  [CorpusTypeEnum['打断垫句']]: {
    name: '打断垫句', value: CorpusTypeEnum['打断垫句'],
    keys: [
      'id', 'scriptId', 'isDeleted', 'name', 'connectType',
      'scriptMultiContents',
    ] as unknown as (keyof ScriptCorpusItem)[],
    api: (params: ScriptCorpusItem) => scriptCorpusModel.savePreInterrupt(params)
  },
  [CorpusTypeEnum['续播垫句']]: {
    name: '续播垫句', value: CorpusTypeEnum['续播垫句'],
    keys: [
      'id', 'scriptId', 'isDeleted', 'name', 'connectType',
      'scriptMultiContents',
    ] as unknown as (keyof ScriptCorpusItem)[],
    api: (params: ScriptCorpusItem) => scriptCorpusModel.savePreContinue(params)
  },
  [CorpusTypeEnum['承接语料']]: {
    name: '承接语料', value: CorpusTypeEnum['承接语料'],
    keys: [
      'id', 'scriptId', 'isDeleted', 'name', 'connectType',
      'scriptMultiContents', 'aiLabels', 'aiIntentionType', 'maxWaitingTime', 'eventTriggerValueIds', 'smsTriggerName',
    ] as unknown as (keyof ScriptCorpusItem)[],
    api: (params: ScriptCorpusItem) => scriptCorpusModel.savePreUndertake(params)
  },
}

export class ContentDataOrigin implements scriptUnitContent {
  content
  isPlayed = false
  deleted = false
  contentType = ContentTypeEnum['普通文本']
  corpusId
  corpusType
  constructor(type: CorpusTypeEnum, corpusId?: number, content?: string) {
    this.content = content || ''
    this.corpusType = type
    this.corpusId = corpusId
  }
}

export class BranchDataOrigin implements CorpusPriorItem {
  constructor(type: CorpusPriorTypeEnum, corpusId?: number, scriptId?: number) {
    this.type = type
    this.preCorpusId = corpusId
    this.scriptId = scriptId
  }
  id = undefined
  preCorpusId
  type
  name = undefined
  scriptId
  color = undefined
  queryField = undefined
  infoQueryValues = []
  semCombineEntity = {
    excludeSemConditions: [],
    satisfySemConditions: [],
  }
}

// 非画布语料
export class CorpusDataOrigin implements ScriptCorpusItem {
  constructor(scriptId: number, type: CorpusTypeEnum, groupId?: number,isHead?: boolean, isTopHead?: boolean) {
    this.scriptId = scriptId
    this.corpusType = type
    this.knowledgeGroupId = groupId || undefined // 知识库分组名称，基本问答、深层沟通（普通、连接）语料必填
    this.isHead = isHead || false
    this.isTopHead = isTopHead || false
    this.scriptMultiContents = [{
      scriptUnitContents: [new ContentDataOrigin(type)],
      scriptId: scriptId,
      deleted: false
    }]
    switch(type) {
      case CorpusTypeEnum['最高优先']: {
        this.connectType = ConnectTypeEnum['挂机']
        this.isOpenContext = true
        this.openScopeType = OpenScopeTypeEnum['继承原语境']
        break;
      }
      case CorpusTypeEnum['基本问答']: {
        this.connectType = ConnectTypeEnum['回原主动流程']
        this.isOpenContext = true
        this.openScopeType = OpenScopeTypeEnum['继承原语境']
        this.queryType = QueryTypeEnum['业务问答']
        break;
      }
      case  CorpusTypeEnum['主动流程-连接语料']: {
        this.connectType = ConnectTypeEnum['挂机']
        break;
      }
      case  CorpusTypeEnum['深层沟通-连接语料']: {
        this.connectType = ConnectTypeEnum['挂机']
        break;
      }
      // 沉默、重复
      default: {
        this.connectType = undefined
        break;
      }
    }
  }
  scriptId: number
  corpusType: CorpusTypeEnum
  connectType: ConnectTypeEnum | undefined
  connectCorpusId = null

  // 开放范围均视为封闭
  isOpenContext = false
  openScopeType: OpenScopeTypeEnum | undefined = undefined
  groupOpenScope = undefined
  // 满足排除条件
  semCombineEntity = {
    excludeSemConditions: [],
    satisfySemConditions: [],
  }

  id = undefined
  name = ''
  scriptMultiContents
  isHead: boolean | undefined
  isTopHead: boolean | undefined
  isDeleted =  false
  knowledgeGroupId
  queryType

  branchList = undefined
  corX = undefined
  corY = undefined
  nextCanvasId  = null

  maxWaitingTime = 5000
  eventTriggerValueIds = []
  listenInOrTakeOver = false
  smsTriggerName = undefined
  aiIntentionType = undefined
  aiIntentionTypeId = undefined
  aiLabels = []
  aiLabelIds = []
  // interruptType = InterruptTypeEnum['不允许打断']
  // allowedInterruptTime = undefined
  priorGroup = undefined
  isPlayed = undefined
}

export enum ResponseTypeEnum {
  'POLITE_RESPONSE' = 'POLITE_RESPONSE', // 礼貌响应
  'PREEMPT_RESPONSE' = 'PREEMPT_RESPONSE', // 抢先响应
}

export const responseTypeOption = {
  [ResponseTypeEnum['POLITE_RESPONSE']]: { name: '礼貌响应', value: ResponseTypeEnum['POLITE_RESPONSE'], },
  [ResponseTypeEnum['PREEMPT_RESPONSE']]: { name: '抢先响应', value: ResponseTypeEnum['PREEMPT_RESPONSE'], },
}

export enum InterruptNormalTypeEnum {
  '不允许打断' = 'CAN_NOT_BE_INTERRUPTED',
  '支持发声打断但不回复' = 'SUPPORT_SOUND_INTERRUPT_NO_REPLY',
  '支持发声打断并回复' = 'SUPPORT_SOUND_INTERRUPT_WITH_REPLY',
  '支持核心短语打断但不回复' = 'SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY',
  '支持核心短语打断并回复' = 'SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY',
}
export enum InterruptHangUpTypeEnum {
  '不允许打断' = 'CAN_NOT_BE_INTERRUPTED',
  '指定语料打断' = 'HANG_UP_SPECIAL_CORPUS_INTERRUPT',
}
export enum InterruptTypeEnum {
  '不允许打断' = 'CAN_NOT_BE_INTERRUPTED',
  '支持发声打断但不回复' = 'SUPPORT_SOUND_INTERRUPT_NO_REPLY',
  '支持发声打断并回复' = 'SUPPORT_SOUND_INTERRUPT_WITH_REPLY',
  '支持核心短语打断但不回复' = 'SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY',
  '支持核心短语打断并回复' = 'SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY',
  '指定语料打断' = 'HANG_UP_SPECIAL_CORPUS_INTERRUPT',
}

// 返回设置-续播类型枚举
export enum ReturnTypeEnum {
  '重播' = 'REPLAY',
  '承接' = 'UNDERTAKE',
  '播放未命中' = 'PLAY_DEFAULT'
}

export enum OpenScopeTypeEnum {
  '全部' = 'ALL',
  // '继承主动流程' = 'SUCCEED_FLOW',
  '继承原语境' = 'SUCCEED_CONTEXT',
  '自定义' = 'CUSTOM',
}

export enum CorpusPriorTypeEnum {
  '查询分支' = 'QUERY_BRANCH',
  '普通分支' = 'GENERAL_BRANCH',
  '知识库' = 'KNOWLEDGE_GROUPS',
  '未命中' = 'MISS',
  '沉默' = 'SILENCE',
}
export interface ScriptCorpusOtherItem {
  id?: number,
  corpusType?: CorpusTypeEnum, // 语料类型
  aiLabelIds?: number[], // 意向标签ID
  aiLabels?: LabelItem[],
  aiIntentionType?: IntentionType | null,
  aiIntentionTypeId?: number, // 意向分类ID
  maxWaitingTime?: number, // 最长等待时间
  eventTriggerValueIds?: number[],
  listenInOrTakeOver?: boolean, // 是否支持转人工
  smsTriggerName?: string, // 触发点名称
}

/** 开放范围涉及属性 */
export interface CorpusOpenScopeConfig {
  isOpenContext?: boolean, // 是否开放语境
  openScopeType?: OpenScopeTypeEnum, // 开放范围
  groupOpenScope?: number[], // 开放的具体分组
}


/** 非挂机语料-语句-打断设置 */
export interface ContentInterruptConfigItem {
  interruptType?: InterruptTypeEnum,
  allowedInterruptTime?: number,
  preInterruptCorpusId?: number,
  preContinueCorpusIdForInterrupt?: number,
  scriptUnitContentId?: number,
  contentName?: string // 后端无该数据，语句名称
}

/** 挂机语料-语句-打断设置 */
export interface ContentHangUpInterruptConfigItem {
  interruptType?: InterruptTypeEnum,
  interruptCorpusIdsForEnd?: number[],
  scriptUnitContentId?: number,
  contentName?: string // 后端无该数据，语句名称
}

/** 普通语料-语句-返回设置 */
export interface ContentReturnConfigItem {
  scriptUnitContentId?: number,
  corpusReturnType?: ReturnTypeEnum,
  preContinueCorpusIdForReturn?: number,
  preUndertakeCorpusId?: number,
  contentName?: string // 后端无该数据，语句名称
}

/** 语料-打断设置 */
export interface CorpusInterruptConfig {
  scriptCorpusId?: number,
  dataList?: ContentInterruptConfigItem[] | ContentHangUpInterruptConfigItem[] | null
}

/** 语料-返回设置 */
export interface CorpusReturnConfig extends ContentReturnConfigItem {
  scriptCorpusId?: number,
  returnPlayDefault?: boolean,
  preContinueCorpusIdBeforeDefault?: number,
  dataList?: ContentReturnConfigItem[] | null
}

export interface scriptUnitContent extends ContentInterruptConfigItem, ContentHangUpInterruptConfigItem, ContentReturnConfigItem{
  id?: number,
  corpusId?: number,
  content: string,
  isPlayed?: boolean,
  deleted?: boolean,
  audioPath?: string,
  orders?: number,
  corpusType?: CorpusTypeEnum, // 新增必传
  contentType?:ContentTypeEnum, // 必传
  contentName?: string
}
export interface ScriptMultiContent {
  scriptUnitContents: scriptUnitContent[],
  corpusId?: number;
  deleted?: boolean;
  id?: number;
  orders?: number;
  scriptId?: number;
  weight?: number;
}

export interface ScriptCorpusItem extends ScriptCorpusOtherItem, CorpusReturnConfig, CorpusOpenScopeConfig {
  id?: number,
  scriptId?: number,
  name: string, // 语料名称
  content?: any, // 文字内容
  scriptMultiContents?: ScriptMultiContent[], // 文本-多段语句
  audioPath?: string,
  responseType?: ResponseTypeEnum, // 基本问答、最高优先支持设置响应时机
  connectType?: ConnectTypeEnum, // 连接类型
  connectCorpusId?: number | null,
  nextCanvasId?: number | null,
 
  corpusType?: CorpusTypeEnum, // 语料类型
  isHead?: boolean, // 是否是头节点
  weight?: number, // 权重
  isKnowledgeBase?: boolean,
  branchList?: ScriptBranch[], // 分支
  isTopHead?: boolean,
  isDeleted?: boolean,
  corX?: number,
  corY?: number,
  isMoved?: boolean,
  updateTime?: string,
  knowledgeGroupId?: number,  // 知识库分组名称，基本问答、深层沟通（普通、连接）语料必填
  queryType?: QueryTypeEnum, // 基本问答、最高优先支持设置问答类型
  // isPlayed?: boolean // 音频验听状态
  priorGroup?: {
  id?: number,
  priorList?: CorpusPriorItem[]
  } // 分支和知识库的优先级
  semCombineEntity: {
    excludeSemConditions: CorpusConditionItem[],
    satisfySemConditions: CorpusConditionItem[],
  } // 满足排除核心语义和补充短语条件
}

export interface CorpusPriorItem extends ScriptBranch {
  id?: number,
  type: CorpusPriorTypeEnum,
  backExcluded?: boolean,
}

// 分支管理
export interface ScriptNormalBranchItem {
  branchId: number;
  branchName: string;
  // branchType: string;
  canvasId: number;
  canvasName: string;
  corpusId: number;
  corpusIds: number[];
  corpusName: string;
  semCombineEntity?: {
    excludeSemConditions: CorpusConditionItem[],
    satisfySemConditions: CorpusConditionItem[],
  } // 满足排除核心语义和补充短语条件
}

export interface QaAndPriorCorpusItem {
  id: number,
  name?: string, 
  corpusType: CorpusTypeEnum,
  queryType?: QueryTypeEnum,
  knowledgeGroupId?: number,
  groupName?: string,
}


export interface ScriptBranch {
  id?: number,
  name?: string, // 分支名称
  scriptId?: number,
  color?: string, // 信息查询分支颜色
  queryField?: {
    [key: string]: string,
  },
  infoQueryValueIds?: number[] | null, // 信息查询值ids
  preCorpusId?: number, // 上游语料ID
  nextCorpusId?: number, // 下游语料ID
  isDeleted?: boolean,
  semCombineEntity?: {
    excludeSemConditions: CorpusConditionItem[],
    satisfySemConditions: CorpusConditionItem[],
  } // 满足排除核心语义和补充短语条件
}

export interface ScriptIntentionLevel {
  id?: number,
  order: number,
  category: string,
  categoryName: string,
}

export interface AudioParams {
  scriptId: number // 响度
  loudness?: number // 样本时长
  sampleTime?: number // 响度
  startTalkingSensitivity?: number  // 判断开始说话敏感度
  stopTalkingSensitivity?: number // 判断退出说话敏感度
  pauseTime?: number // 停顿时长超过
  sentencePauseTime?: number // 判断一句话说完停顿时长超过
  maxSentenceTime?: number // 最大单句时长不超过
  vocalStopWaitTime?: number // 发声打断时，最小停顿等待时间
  meaningStopWaitTime?: number // 语义打断时，最小停顿等待时间
}

export class AudioParamsOrigin implements AudioParams {
  constructor(id: number) {
    this.scriptId = id
  }
  scriptId
  loudness = 700
  sampleTime = 300
  startTalkingSensitivity = 70
  stopTalkingSensitivity = 30
  pauseTime = 300
  sentencePauseTime = 600
  maxSentenceTime = 30000
  vocalStopWaitTime = 1000
  meaningStopWaitTime = 1500
}

// 音频 音文相似度 识别状态 枚举
export enum AsrStatusEnum {
  '识别中' = 'IN_PROCESS',
  '已完成' = 'DONE',
  '失败' = 'FAILED',
}

export interface AudioItem {
  allowedInterruptTime?: number;
  asrResult?: number; // ASR识别结果
  asrStatus?: AsrStatusEnum; // ASR识别状态
  asrTxt?: string;
  audioPath?: string; // 音频URL
  content?: string; // 文字内容
  canvasName?: string; // 所属流程
  // name?: string, // 语料名称，旧字段，音频播放组件使用，保留兼容
  contentName?: string; // 语料名称，新字段
  contentType?: ContentTypeEnum; // 文本类型
  corpusId?: number;
  corpusReturnType?: ReturnTypeEnum;
  corpusType?: CorpusTypeEnum; // 语料类型
  createTime?: string; // 创建时间
  deleted?: boolean;
  id?: number;
  interruptCorpusIdForEnd?: number;
  interruptType?: InterruptTypeEnum;
  isPlayed?: boolean; // 音频验听状态
  multiContentId?: number;
  orders?: number;
  preContinueCorpusIdForInterrupt?: number;
  preContinueCorpusIdForReturn?: number;
  preInterruptCorpusId?: number;
  preUndertakeCorpusId?: number;
  updateStatus?: string;
  updateTime?: string; // 更新时间
  uploadStatus?: string; // 上传状态
  uploadTime?: string; // 上传时间
}

export interface AudioResponse {
  scriptUnitContents: AudioItem[]
  totalNums: number
  uploadNums: number
}

export interface AudioItemUploadParams {
  contentId?: number
  file?: FormData
}

export interface AudioItemEditParams {
  scriptId?: number // 话术ID
  corpusId?: number // 语料ID
  contentId?: number // 语句ID
  content?: string // 文字内容
}

// 空闲账号
export interface IdleAccount {
  id?: number | string | null
  account?: string | null
  password?: string | null
  ip?: string | null
  port?: string | null
  ifUsed?: boolean | null
  createTime?: string | null
  updateTime?: string | null
  address?: string | null
}

// 话术训练 对话详情 单条对话
export interface TrainDialogItem {
  id?: number;
  createTime?: string | null;
  updateTime?: string | null;
  dialogTime?: string | null;
  callId?: string | null;
  recordId?: string | null;
  audioFileUrl?: string | null;
  content?: string | null;
  type?: number | null; // 0: Ai， 1-2：客户， 3-4： 坐席
  recordms?: number | null;
  speakms?: number | null;
  hitSemantic?: string | null;
  hitPhrase?: string | null;
  hitIntention?: string | null;
  hitBranch?: string | null;
  corpusName?: string | null;
  interruptType?: string | null;
  phone?: string | null;
  unitContent?: string | null;
  dialogCorpusTempUuid?: string | null;
  corpusId?: number | null;

  // 用于对话详情组件，audioFileUrl是接口字段，urls是组件字段
  urls?: string[] | null;
  // 用于对话详情组件，展示当前语料的打断设置
  corpusConfig?: scriptUnitContent[] | null;
}

// 信息查询分支
export interface InfoQueryItem {
  id?: number
  createTime?: string
  updateTime?: string
  scriptLongId: number
  infoFieldName: string
  fieldDefinition: string
  infoQueryValues?: InfoQueryValueItem[] | null
}

// 信息查询单项
export interface InfoQueryValueItem {
  id?: number,
  weight?: number,
  infoQueryKeyId?: number
  value?: string
  definition?: string
  note?: string
  createTime?: string
  updateTime?: string
}

// 事件触发
export interface EventValueItem {
  valueId?: number,
  name?: string,
  eventName: string,
  note?: string,
  explanation?: string,
}

export interface EventItem {
  id?: number,
  scriptLongId?: number,
  eventName?: string,
  note?: string,
  createTime?: string
  updateTime?: string
  eventValuemap?: {
    [key: string]: EventValueItem
  }
}

// 训练历史
export interface TrainHistoryItem {
  id?: number
  account?: string
  scriptLongId?: string
  scriptStringId?: string
  recordId?: string
  createTime?: string
  updateTime?: string
  trainTime?: string
  isStringTrain?: boolean
}

// 话术统计
export interface ScriptStatisticParams {
  scriptId: number,
  hangupType?: string, //  ALL, HUMAN, ROBOT
  corpusTypes?: CorpusTypeEnum[],
  headCorpusId?: number,
  corpusId?: number,
  type?: string
}

export interface ScriptStatisticItem {
  canvasId?: number | null
  canvasName?: string // 画布语料使用
  name?: string // 标签、高级规则使用
  corpusId?: number 
  corpusType?: CorpusTypeEnum
  corpusName?: string // 非画布语料使用
  denominator: number
  numerator: number
  weight?: number
}

export interface ScriptSemanticStatisticItem {
  semanticId: number | null
  semanticName: string
  hitNum: number
  isUsedSemantic?: boolean
}

export interface CorpusStatisticDetails {
  corpusId: number
  corpusName: string
  hangupNum: number
  hitNum: number
}

export interface CorpusStatisticDetails {
  corpusId: number
  corpusName: string
  hangupNum: number
  hitNum: number
}

// 话术训练状态枚举
export enum ScriptTrainStatusEnum {
  // 空闲中
  IDLE,
  // 检查媒体
  CHECK_MEDIA,
  // 检查账号
  CHECK_ACCOUNT,
  // 检查话术
  CHECK_SCRIPT,
  // 等待来电
  WAIT_CALL,
  // 通话中
  IN_CALL,
  // 已挂断
  HANG_UP,
}

// 话术训练，文字训练，接口数据
export interface ScriptTextTrainInfo {
  id: number
  audioFileUrl?: string
  audioFinishWaitTime?: number
  callId?: string
  content?: string
  corpusName?: string
  dialogTime?: string
  finished?: boolean
  hitBranch?: string | null
  hitIntention?: string | null
  hitPhrase?: string | null
  hitSemantic?: string | null
  interruptType?: InterruptTypeEnum
  phone?: string
  success?: boolean
  type?: number
  unitContentName?: string
}

// 话术训练，文字训练，请求参数
export interface ScriptTextTrainParam {
  scriptLongId?: number
  callId?: string
  words?: string
  phone?: string
  stage?: string
}

// 话术训练，训练类型，枚举
export enum ScriptTrainTypeEnum {
  // 未开始
  NULL,
  // 语音训练
  AUDIO,
  // 文字训练
  TEXT,
}

// 话术训练，文字训练，当前AI打断状态
export enum ScriptTextTrainAiInterruptEnum {
  NOT = '不允许打断',
  ALLOW = '可打断',
  FINISH = '已说完'
}

// 知识库分组
export interface KnowledgeGroupItem {
  id?: number
  groupName: string
  scriptId?: number
  createTime?: string
  updateTime?: string
  priority?: number
}
