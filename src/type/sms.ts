import dayjs from 'dayjs'
import { SmsTemplateItem, SmsVariableColumnTypeEnum } from '@/type/merchant'
import { FollowUpTypeEnum } from '@/type/clue'

export enum SmsTypeEnum {
  '挂机短信' = 'ON_HOOK',
  '直发短信' = 'DIRECT',
  '触发短信' = 'TRIGGER',
}

export enum SmsStatusEnum {
  '发送成功' = 'SEND_SUCCESS',
  '发送失败' = 'SEND_FAIL',
  '路由失败' = 'ROUTING_FAIL',
  '提交失败' = 'SUBMIT_FAIL',
  '未回执' = 'NO_RECEIPT',
  '系统异常' = 'SYSTEM_EXCEPTION',
}

export enum SendTypeEnum {
  '初始发送' = 'INITIAL',
  '补偿发送' = 'COMPENSATION',
}

export enum BusinessTypeEnum {
  '营销短信' = 'MARKETING',
  '验证码' = 'VERIFICATION',
  '通知短信' = 'NOTIFICATION',
}

export enum SmsTemplateTypeEnum {
  '白泽短信' = 'BZ_MESSAGE',
  'M短信' = 'M_MESSAGE',
  '火山短信' = 'V_MESSAGE',
}

export enum ReceiptStatusEnum {
  '发送成功' = 'SEND_SUCCESS',
  '发送失败' = 'SEND_FAIL',
  '未回执' = 'NO_RECEIPT',
}

export enum SubmitStatusEnum {
  '提交成功' = 'SUBMIT_SUCCESS',
  '提交失败' = 'SUBMIT_FAIL',
  '路由失败' = 'ROUTING_FAIL',
  '系统异常' = 'SYSTEM_EXCEPTION',
}

export enum ShortLinkParamEnum {
  '号码' = "PHONE_NUMBER",
  '短信对接账号ID' = "SMS_ACCOUNT_NUMBER",
  '省' = "PROVINCE",
  '市' = "CITY",
  '尾号' = "PHONE_END_NUMBER",
  '渠道号' = "CHANNEL",
  '隐私号' = "ENCRYPT_NUMBER",
}

// 短信供应商 启用状态
export enum SmsProviderStatusEnum {
  '启用' = 'ENABLE',
  '停用' = 'DISABLE',
}

// 短信供应商 对接协议 枚举
export enum SmsProtocolEnum {
  'CMPP' = 'CMPP',
  'HTTP' = 'HTTP',
  'VOLCANO' = 'VOLCANO', // 火山
  'CALLBACKM' = 'CALLBACKM', // M短信回调
}

// 短信对接账号 启用状态
export enum SmsAccountStatusEnum {
  '启用' = 'ENABLE',
  '停用' = 'DISABLE',
}

// 短信对接账号 CMPP 协议版本 枚举
export enum SmsCmppProtocolVersionEnum {
  '2.0' = 2,
  '3.0' = 3,
}

// 短信对接账号 适用业务 枚举
export enum SmsAccountBusinessTypeEnum {
  '群发' = 'MASS_SENDING',
  '挂短' = 'HANGUP_SMS',
}

// 短信对接账号 服务提供商 枚举
export enum SmsAccountServiceProviderEnum {
  '中国移动' = 'CHINA_MOBILE',
  '中国联通' = 'CHINA_UNICOM',
  '中国电信' = 'CHINA_TELECOM',
  '未知' = 'UNKNOWN',
}

// 短信内容 标签类型 枚举
export enum SmsContentTagEnum {
  '变量' = 'variable',
  '普通短链' = 'shortOrdinaryUrl',
  '千人千链' = 'shortThousandUrl',
}

// 短信对接账号 挂起状态 接口参数
export interface SmsAccountPendingParams {
  smsAccountNumber?: string,
  status?: boolean,
}

// 短信供应商
export interface SmsProviderItem {
  configInfo?: string // 配置信息
  contactAddress?: string // 联系地址
  contactName?: string // 联系人
  cooperationStatus?: SmsProviderStatusEnum // 合作状态
  createTime?: string // 创建时间
  duty?: string // 职务
  email?: string // 邮箱
  id?: number
  notes?: string // 备注
  phoneNumber?: string // 联系电话
  smsProtocol?: SmsProtocolEnum // 对接协议
  supplierAddress?: string // 公司地址
  supplierName?: string // 供应商名称
  supplierNumber?: string // 供应商编号
  supplierProfile?: string // 供应商简称
  updateTime?: string // 更新时间
}

// 短信供应商 接口参数
export interface SmsProviderParams extends SmsProviderItem {
  status?: SmsProviderStatusEnum
}

// 短信供应商 账号信息
export interface SmsAccountItem {
  account?: string, // 所属商户（主账号）
  billingCycle?: number, // 计费周期
  cityCodes?: string[], // 支持省市
  configInfo?: string, // 配置信息
  connectAddress?: string, // 对接地址
  connectPort?: number, // 对接端口
  createTime?: string, // 创建时间
  disableTimeSlots?: number[], // 时间限制
  enableStatus?: SmsAccountStatusEnum | string | null, // 启用状态
  extensionCode?: string | null, // 扩展码
  groupId?: string,
  id?: number,
  isReturn?: boolean, // 是否回执
  maxChannels?: number, // 最大连接数
  notes?: string, // 运营备注
  password?: string, // 密码
  pending?: boolean, // 挂起状态
  returnTimeout?: number, // 回执超时时间
  secondIndustries?: string[], // 适用行业
  sendDelay?: number, // 发送延迟
  sendRestrictions?: string[], // 发送限制
  serviceId?: string, // 服务ID
  serviceProvider?: SmsAccountServiceProviderEnum, // 支持运营商
  singleDaySubmitLimit?: number | null, // 单日提交上限
  singleSubmitLimit?: number | null, // 单次提交上限
  connectAccount?: string, // 账号
  smsAccountBusinessType?: SmsAccountBusinessTypeEnum, // 适用业务
  smsAccountName?: string, // 账号名称
  smsAccountNumber?: string, // 账号编号
  smsProtocol?: SmsProtocolEnum, // 对接协议
  srcId?: string, // 接入码（源号码）
  submitRestrictions?: string[], // 提交限制
  submitSpeedLimit?: number | null, // 提交速率限制
  supplierId?: number, // 供应商ID
  supplierName?: string, // 供应商名称
  supplierNumber?: string, // 供应商编号
  supplierProfile?: string, // 供应商简称
  tenantSmsTemplates?: SmsTemplateItem[], // 所属短信模板
  unitPrice?: number, // 单价（元）
  updateTime?: string, // 更新时间
  version?: number | null, // 协议版本
}

export interface SmsAccountWarningConfig {
  enableStatus?: string;
  id?: number;
  receiptCount?: number | null;
  receiptFailCount?: number | null;
  sendCount?: number | null;
  sendFailCount?: number | null;
  smsAccountNumber?: string;
  submitCount?: number | null;
  submitFailCount?: number | null;
}

// 短信供应商 账号信息 搜索参数
export interface SmsProviderAccountSearch extends SmsAccountItem {
}

// 短信供应商 账号信息 接口参数
export interface SmsProviderAccountParams extends SmsAccountItem {
  enableStatus?: SmsAccountStatusEnum | string | null, // 启用状态
  secondIndustries?: string[], // 适用行业
  smsAccountName?: string, // 账号名称
  smsAccountNumber?: string, // 账号编号
  supplierId?: number, // 供应商ID
  tenantId?: number, // 商户ID
}

// 短信对接账号 CMPP 协议版本 映射表
export const SmsCmppProtocolVersionList = [
  ['2.0', SmsCmppProtocolVersionEnum['2.0']],
  ['3.0', SmsCmppProtocolVersionEnum['3.0']],
]

// 普通短链
export interface SmsUrlItem {
  id?: number,
  linkName?: string,
  linkNumber?: string,
  shortUrl?: string,
  originalUrl?: string,
  groupId?: string,
  account?: string,
  domain?: string,
  domainMap?: Record<string, number>,
  enableStatus?: string, // 'ENABLE' 'DISABLE'
  tenantSmsTemplates?: SmsTemplateItem[],
  usedShortUrls?: string[],
  createTime?: string,
  updateTime?: string,
  expireTime?: string
  expireDays?: number
  needRegister?: boolean
  paramTypes?: ShortLinkParamEnum[] // 拼接信息，仅千人千链使用
}

export type SmsStatisticSearch = {
  linkNumber?: string
  domain?: string
  originalUrl?: string
  size?: number
  date?: string
}

export interface SmsStatisticItem extends SmsUrlItem {
  sendCount: number
  clickCount: number
  clickPercent: number
}

// 商户端展示的短信记录
interface MerchantSmsRecordItem {
  id?: string;
  account?: string;
  groupId?: string;
  recordId?: string;
  operator?: string;
  city?: string;
  province?: string;
  messageType?: SmsTypeEnum;
  scriptName?: string;
  taskName?: string;
  callSeatId?: number;
  callTeamId?: number
  callTeamName?: string;
  triggerTime?: string;
  submitStatus?: SubmitStatusEnum;
  submitTime?: string;
  receiptStatus?: ReceiptStatusEnum;
  receiptTime?: string;
  receiptTimeout?: string;
  smsStatus?: SmsStatusEnum;
  smsTemplateName?: string;
  smsTemplateId?: string;
  smsContent?: string;
  businessType?: BusinessTypeEnum;
  tenantName?: string;
}
// 运营端端展示的短信记录
export interface SmsRecord extends MerchantSmsRecordItem {
  sendType?: SendTypeEnum;
  compensationStatus?: string;
  shortLinkOrdinaryNumbers?: string,
  shortLinkOrdinaryUrls?: string,
  shortLinkThousandNumbers?: string,
  shortLinkThousandUrls?: string,
  billingUnits?: number;
  costCount?: number;
  failureCause?: string;
  industrySecondFieldId?: number;
  productId?: string;
  programId?: string;
  returnCode?: string;
  smsAccountName?: string;
  smsAccountNumber?: string;
  smsSupplierName?: string;
  smsSupplierNumber?: string;
  wordCount?: number;
}

export interface SmsSearchModal {
  needOrder?: string // '0' | '1'，是否保持排序，默认当日排序,非当日不排序
  lastCompensation?: string; // 商户端传，运营端不传（记录是否是最后一次补偿操作）
  phone?: string;
  operator?: string;
  province?: string;
  city?: string;
  account?: string;
  messageTypeList?: SmsTypeEnum[];
  sendType?: SendTypeEnum;
  taskIdList?: string[];
  scriptStringId?: string[];
  callSeatIdList?: number[];
  callTeamIdList?: number[];
  triggerTimeStart?: string;
  triggerTimeEnd?: string;
  submitStatus?: SubmitStatusEnum;
  submitTimeStart?: string;
  submitTimeEnd?: string;
  receiptStatusList?: ReceiptStatusEnum[];
  receiptTimeStart?: string;
  receiptTimeEnd?: string;
  receiptTimeout?: string;
  compensationStatus?: string;
  smsStatusList?: string[];
  smsTemplateId?: string;
  smsContent?: string;
  smsShortLinkUrl?: string;
  businessType?: string;
  smsAccountNumber?: string;
  returnCode?: string;
  smsSupplierNumber?: string;
  groupId?: string;
  startPage?: number
  pageNum?: number
  // 暂未用到
  programId?: number
  productId?: number
  industrySecondFieldId?: number
}

export class SmsSearchOrigin implements SmsSearchModal {
  needOrder;
  phone = undefined;
  operator = undefined;
  province = undefined;
  city = undefined;
  account = undefined;
  messageTypeList = undefined;
  sendType = undefined;
  lastCompensation;
  taskIdList = undefined;
  scriptStringId = undefined;
  callSeatIdList = undefined;
  callTeamIdList = undefined;
  triggerTimeStart = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss');
  triggerTimeEnd = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss');
  submitStatus = undefined;
  submitTimeStart = undefined;
  submitTimeEnd = undefined;
  receiptStatusList = undefined;
  receiptTimeStart = undefined;
  receiptTimeEnd = undefined;
  receiptTimeout = undefined;
  compensationStatus = undefined;
  smsStatusList = undefined;
  smsTemplateId = undefined;
  smsContent = undefined;
  smsShortLinkUrl = undefined;
  businessType = undefined;
  smsAccountNumber = undefined;
  returnCode = undefined;
  smsSupplierNumber = undefined;
  groupId = undefined;
  startPage = undefined;
  pageNum = undefined;
  programId = undefined;
  productId = undefined;
  industrySecondFieldId = undefined;

  constructor(accountType: number, createTime?: string) {
    this.lastCompensation = accountType === 1 ? '1' : undefined
    this.needOrder = createTime && dayjs(createTime).isBefore(dayjs().startOf('day')) ? '0' : '1'
  }
}

// 短信发送参数 自定义变量
export interface VariableSmsPojoItem {
  variableName?: string,
  variableType?: SmsVariableColumnTypeEnum,
  variableValue?: any,
}

// 短信发送参数
export interface SmsSendParam {
  // 接口参数，通用，线索详情、人机协同、人工直呼
  clueId?: number
  smsTemplateId?: number
  variableSmsPojoList?: VariableSmsPojoItem[],
  callSeatId?: number
  callTeamId?: number
  // 接口参数，仅适用于人机协同和人工直呼
  callRecordId?: string
  // 页面展示
  phoneNumber?: string
  // 组件数据，区分短信发送类型
  type?: FollowUpTypeEnum,
}

// 短信内容 Delta文档结构 属性 单项
export interface DeltaAttributeItem {
  tag: SmsContentTagEnum, // 标签类型
  display: string, // 展示文本
  value: string, // 表单数据 接口数据
}

export interface UpwardSmsRecord {
  id: string;
  phone: string;
  operator?: string;
  operatorName?: string;
  name?: string;
  company?: string;
  province?: string;
  originalSmsContent?: string;
  originalSmsTime?: string;
  userReplyTime?: string;
  systemReceiveTime?: string;
  upwardSmsContent?: string;
  lineNumber?: string;
  smsResponseNumber?: string;
  smsSupplierStatus?: string;
}

export interface UpwardSmsParams {
  phone?: string;
  name?: string;
  company?: string;
  province?: string;
  groupId?: string;
  systemReceiveStartTime?: string;
  systemReceiveEndTime?: string;
  originalSmsStartTime?: string;
  originalSmsEndTime?: string;
  userReplyStartTime?: string;
  userReplyEndTime?: string;
  smsTemplateId?: number;
  smsContent?: string;
  upwardSmsContent?: string;
  remark?: string;
  startPage?: number;
  pageNum?: number;
}

export class UpwardSmsOrigin implements UpwardSmsParams {
  phone = undefined;
  name = undefined;
  company = undefined;
  province = undefined;
  groupId = undefined;
  systemReceiveStartTime = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss');
  systemReceiveEndTime = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss');
  originalSmsStartTime = undefined;
  originalSmsEndTime = undefined;
  userReplyStartTime = undefined;
  userReplyEndTime = undefined;
  smsTemplateId = undefined;
  smsContent = undefined;
  upwardSmsContent = undefined;
  remark = undefined;
}
