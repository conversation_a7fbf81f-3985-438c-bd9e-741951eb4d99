/**
 * 人工坐席
 */
import { ClueFollowLog, ClueItem, FollowUpStatusEnum } from '@/type/clue'
import { TaskManageItem } from '@/type/task'
import { IntentionType, LabelItem } from '@/type/IntentionType'

// 通话设置
export interface CallSetting {
  transferManualSeatProcessTime?: number | null,
  waitingRingtone?: string | null,
  waitingRingtoneUrl?: string | null,
  postCallProcessTime?: number | null,
  intentionClass?: string | null,
}

// 坐席成员
export interface SeatMember {
  account?: string,
  accountId?: number,
  callSeatStatus?: SeatStatusEnum,
  callTeamId?: number,
  clueIds?: number[],
  createTime?: string,
  groupId?: string,
  id?: number,
  name?: string,
  taskIds?: number[],
  todayStatistic?: {
    acquireCount?: number,
    beReassignCount?: number,
    beRecoveredCount?: number,
    failCount?: number,
    followingCount?: number,
    successCount?: number,
  },
  updateTime?: string,
  aiCallIp?: string,
  aiCallPort?: string,
  callId?: string,
  taskId?: number | null,
  masterAccount?: {
    account: string,
  },
}

// 坐席组长
export interface SeatLeader {
  account?: string,
  accountEnable?: boolean,
  accountType?: number,
  address?: string,
  callbackUrl?: string,
  createTime?: string,
  department?: string,
  email?: string,
  gender?: string,
  groupId?: string,
  id?: number,
  isTenantManager?: boolean,
  latestLoginTime?: string,
  name?: string,
  note?: string,
  phone?: string,
  roleId?: number,
  taskTimeRange?: string,
  tenantId?: number,
  updateTime?: string,
}

// 坐席组
export interface SeatTeam {
  callSeatIds?: number[],
  callSeats?: SeatMember[],
  callTeamName?: string,
  createTime?: string,
  groupId?: string,
  id?: number,
  leaderAccount?: SeatLeader,
  leaderAccountId?: number | null,
  masterAccount?: {
    account: string,
  },
  taskIds?: number[],
  tenantLineNumber?: string,
  updateTime?: string,
}

// 坐席组 接口参数
export interface SeatTeamApiParam extends SeatTeam {
  id?: number,
  groupId?: string,
  callTeamId?: number | string,
  callTeamName?: string,
  tenantLineNumber?: string,
  leaderAccountId?: number | null,
  addCallSeatAccountIds?: number[],
}

// 坐席账号 接口参数
export interface SeatAccountParam {
  seatId?: number | string,
  callSeatStatus?: SeatStatusEnum,
  taskIds?: number[],
}

// 坐席FS账号信息
export interface SeatAccountBindInfo {
  id?: number,
  createTime?: string,
  updateTime?: string,
  seatId?: number,

  fsUser?: string,
  fsUserPasswd?: string,
  fsIp?: string,
  fsPort?: string,

  isUse?: number,
  enable?: number,
}

// 坐席通话信息
export interface SeatCallInfo {
  callId?: string,
  aiCallIp?: string,
  aiCallPort?: string,
}

// 坐席FS通话参数
export interface SeatFsCallParam extends SeatCallInfo {
  seatId?: number,
}

// 坐席通话类型枚举
export enum SeatCallTypeEnum {
  // 人机协同监听后介入
  MONITOR = 'MONITOR',
  // 人机协同直接接管
  ANSWER = 'ANSWER',
  // 人工直呼
  DIRECT = 'DIRECT',
}

// 坐席通话类型映射表
export const seatCallTypeList = {
  MONITOR: {
    value: 'MONITOR',
    name: '人机协同 监听',
  },
  ANSWER: {
    value: 'ANSWER',
    name: '人机协同 介入（接管）',
  },
  DIRECT: {
    value: 'DIRECT',
    name: '人工直呼',
  },
}

// 坐席工作状态枚举
export enum SeatStatusEnum {
  // 未上线
  OFF_LINE = 'OFF_LINE',
  // 休息
  IN_REST = 'IN_REST',

  // 人机协同 空闲
  HUMAN_MACHINE_IDLE = 'HUMAN_MACHINE_IDLE',
  // 人机协同 弹窗
  HUMAN_MACHINE_WINDOW = 'HUMAN_MACHINE_WINDOW',
  // 人机协同 监听
  HUMAN_MACHINE_LISTEN = 'HUMAN_MACHINE_LISTEN',
  // 人机协同 通话
  HUMAN_MACHINE_DIALING = 'HUMAN_MACHINE_DIALING',
  // 人机协同 话后处理
  HUMAN_MACHINE_POSTING = 'HUMAN_MACHINE_POSTING',

  // 人工直呼 空闲
  MANUAL_DIRECT_IDLE = 'MANUAL_DIRECT_IDLE',
  // 人工直呼 拨打
  MANUAL_DIRECT_CALLING = 'MANUAL_DIRECT_CALLING',
  // 人工直呼 通话
  MANUAL_DIRECT_DIALING = 'MANUAL_DIRECT_DIALING',
  // 人工直呼 话后处理
  MANUAL_DIRECT_POSTING = 'MANUAL_DIRECT_POSTING',
}

// 坐席工作状态映射表
export const seatStatusList: {
  [prop in keyof typeof SeatStatusEnum]: {
    // 接口值
    value: string,
    // 页面展示文本
    name: string,
    // 控制台日志文本
    logName: string,
    // 标签样式名
    colorClassName: string,
    // 通话类型
    callType: SeatCallTypeEnum,
  }
} = {
  OFF_LINE: {
    value: 'OFF_LINE',
    name: '未上线',
    logName: '未上线',
    colorClassName: 'red-status',
    callType: SeatCallTypeEnum.DIRECT
  },
  IN_REST: {
    value: 'IN_REST',
    name: '休息中',
    logName: '休息中',
    colorClassName: 'red-status',
    callType: SeatCallTypeEnum.DIRECT
  },
  HUMAN_MACHINE_IDLE: {
    value: 'HUMAN_MACHINE_IDLE',
    name: '空闲中',
    logName: '人机协同 空闲中',
    colorClassName: 'red-status',
    callType: SeatCallTypeEnum.MONITOR
  },
  HUMAN_MACHINE_WINDOW: {
    value: 'HUMAN_MACHINE_WINDOW',
    name: '弹窗中',
    logName: '人机协同 弹窗中',
    colorClassName: 'orange-status',
    callType: SeatCallTypeEnum.MONITOR
  },
  HUMAN_MACHINE_LISTEN: {
    value: 'HUMAN_MACHINE_LISTEN',
    name: '监听中',
    logName: '人机协同 监听中',
    colorClassName: 'orange-status',
    callType: SeatCallTypeEnum.MONITOR
  },
  HUMAN_MACHINE_DIALING: {
    value: 'HUMAN_MACHINE_DIALING',
    name: '通话中',
    logName: '人机协同 通话中',
    colorClassName: 'blue-status',
    callType: SeatCallTypeEnum.ANSWER
  },
  HUMAN_MACHINE_POSTING: {
    value: 'HUMAN_MACHINE_POSTING',
    name: '话后处理中',
    logName: '人机协同 话后处理中',
    colorClassName: 'green-status',
    callType: SeatCallTypeEnum.MONITOR
  },
  MANUAL_DIRECT_IDLE: {
    value: 'MANUAL_DIRECT_IDLE',
    name: '空闲中',
    logName: '人工直呼 空闲中',
    colorClassName: 'red-status',
    callType: SeatCallTypeEnum.DIRECT
  },
  MANUAL_DIRECT_CALLING: {
    value: 'MANUAL_DIRECT_CALLING',
    name: '拨打中',
    logName: '人工直呼 拨打中',
    colorClassName: 'orange-status',
    callType: SeatCallTypeEnum.DIRECT
  },
  MANUAL_DIRECT_DIALING: {
    value: 'MANUAL_DIRECT_DIALING',
    name: '通话中',
    logName: '人工直呼 通话中',
    colorClassName: 'blue-status',
    callType: SeatCallTypeEnum.DIRECT
  },
  MANUAL_DIRECT_POSTING: {
    value: 'MANUAL_DIRECT_POSTING',
    name: '话后处理中',
    logName: '人工直呼 话后处理中',
    colorClassName: 'green-status',
    callType: SeatCallTypeEnum.DIRECT
  },
}

// 坐席监控 实时状态 表格单行
export interface SeatMonitorRealStatusItem {
  // 账号
  account?: string | null,
  // 姓名
  name?: string | null,
  // 坐席组
  callTeam?: SeatTeam | null,
  // 签入任务
  tasks?: TaskManageItem[] | null,
  // 坐席状态
  status?: SeatStatusEnum | null,
  // 今日在线时长
  onLineMillis?: number | null,
  // 今日休息时长
  restMillis?: number | null,
  // 今日在线时长详情数据
  onLineBlocks?: SeatMonitorRealStatusDetail[],
  // 今日休息时长详情数据
  restBlocks?: SeatMonitorRealStatusDetail[],

}

export interface SeatMonitorRealStatusDetail {
  timeStart?: string,
  timeEnd?: string,
}

// 坐席监控 数据统计 表格单行
export interface SeatMonitorStatisticsItem {
  // 账号
  account?: string | null,
  // 姓名
  name?: string | null,
  // 坐席组
  callTeamName?: string | null,
  // 在线时长
  onLineTime?: number | null,
  // 休息时长
  restTime?: number | null,
  // 漏接数量
  misCount?: number | null,
  // 通话数量
  dialingCount?: number | null,
  // 总通话时长
  totalReceptionDuration?: number | null,
  // 平均通话时长
  avgReceptionDuration?: number | null,
  // 总话后处理时长
  totalPostingDuration?: number | null,
  // 平均话后处理时长
  avgPostingDuration?: number | null,
  // 获得线索数量
  receivedClueCount?: number | null,
  // 待首跟线索数量
  firstFollowClueCount?: number | null,
  // 跟进线索数量
  followedClueCount?: number | null,
  // 跟进成功线索数量
  followedSuccessClueCount?: number | null,
  // 跟进失败线索数量
  followedFailedClueCount?: number | null,
}

// 坐席呼叫接口参数
export interface SeatCallParam {
  callSeatStatus?: SeatStatusEnum,
  clueId?: number | null,
  followUpLogId?: number,
  followUpStatus?: FollowUpStatusEnum,
  formId?: string,
  isPostingOutOfTime?: boolean,
  isStar?: boolean,
  nextFollowUpTime?: string,
  note?: string,
  postId?: string | number,
  recordId?: string | null,
  waitMSec?: number,
  startMonitorTime?: string,
  endMonitorTime?: string,
  startAnswerTime?: string,
  endAnswerTime?: string,
  callId?: string | number,
  isTransToHuman?: boolean,
  isMiss?: boolean,
  noReceptionReason?: string,
  scriptId?: number,
  callRecordForManualDirectId?: number | string,
  taskId?: number,
  intentionClass?: string,
  intentionTagIds?: string,
  intentionTags?: string,
}

// 坐席通话记录
export interface SeatCallRecord {
  account?: string,
  callDuration?: number,
  callId?: string,
  callOutTime?: string,
  callSeatId?: number,
  callStatus?: string,
  callTeamId?: number,
  callTeamName?: string,
  city?: string,
  cityCode?: string,
  clueFollowUpLogId?: number,
  clueId?: number,
  createTime?: string,
  followUpNote?: string,
  followUpStatus?: string,
  formId?: string | null,
  id?: string,
  isPostingOutOfTime?: boolean,
  lineCode?: string,
  lineId?: number,
  merchantLineCode?: string,
  merchantLineId?: string,
  name?: string,
  operator?: string,
  phone?: string,
  postingDuration?: number,
  province?: string,
  provinceCode?: string,
  receptionDuration?: number,
  seatName?: string,
  talkTimeEnd?: string,
  talkTimeStart?: string,
  tenantCode?: string,
  tenantName?: string,
  updateTime?: string,
  userFullAnswerContent?: string,
  waitmsec?: number,
  whoHangup?: number,
  wholeAudioFileUrl?: string,
  postId?: string,
  aiCallIp?: string,
  aiCallPort?: string,
  recordId?: string,
  phoneRecordId?: string,
  clueUniqueId?: string,
}

// 坐席话后处理待提交信息
export interface SeatProcessInfo {
  callSeatId?: number,
  callSeatPostTimeEnd?: number,
  callSeatPostTimeStart?: number,
  clueId?: number,
  createTime?: string,
  date?: string,
  id?: number,
  updateTime?: string,
}

// 坐席呼叫响应参数
export interface SeatCallResponse {
  aiOutboundTask?: TaskManageItem,
  callRecordForManualDirect?: SeatCallRecord,
  callRecordForHumanMachine?: SeatCallRecord,
  callSeat?: SeatMember,
  currentCallSeatInfo?: SeatMember,
  callSeatPostInfo?: SeatProcessInfo,
  clue?: ClueItem,
  clueFollowUpLog?: ClueFollowLog,
  aiIntentionTypes?: IntentionType[],
  aiLabels?: LabelItem[],
}

// 坐席呼叫响应参数 人工直呼 通用
export interface SeatCallManualRes extends SeatCallRecord {
  callId?: string,
  filterReason?: string,
}

// 坐席呼叫响应参数 人机协同
export interface SeatCallMixRes {
  aiCallIp?: string,
  aiCallPort?: string,
  callId?: string,
  callRecordForHumanMachine: SeatCallRecord,
  callTeamHandleType: SeatCallTypeEnum,
  speechCraftId?: number | null,
}

// 坐席呼叫响应参数 人机协同 介入
export interface SeatCallMixInterveneRes {
  clueId?: number | null,
  formId?: string | null,
  callSeatId?: number | null,
}

// 坐席工作台 页面位置 枚举
export enum SeatPageEnum {
  // 线索
  CLUE,
  // 通话
  PHONE
}

// 坐席 心跳 响应
export interface SeatHeartResponse {
  // 任务已停止
  stoppedTasks: TaskManageItem[],
  // 被移出任务
  movedOutTasks: TaskManageItem[],
  // 任务不在拨打时段
  outWorkTimeTasks: TaskManageItem[],
  // 关联任务已启动
  startedNotCheckInTasks: TaskManageItem[],
}

// 坐席 呼叫状态 枚举
export enum CallStatusEnum {
  '呼叫成功' = 'NORMAL_CLEARING',
  '黑名单或无法定位号码' = 'UNALLOCATED_NUMBER',
  '请求超时' = 'RECOVERY_ON_TIMER_EXPIRE',
  '用户取消' = 'ORIGINATOR_CANCEL',
  '用户未回应' = 'NO_USER_RESPONSE',
  '拒接' = 'CALL_REJECTED',
  '线路临时错误' = 'NORMAL_TEMPORARY_FAILURE',
  '无法定位到目标用户' = 'INCOMPATIBLE_DESTINATION',
}

// 坐席 呼叫状态 信息
export interface CallStatusInfo {
  // 当前呼叫的线索，通话ID，一般由发起呼叫的接口响应提供
  callId?: string | null,
  // 当前呼叫的线索，呼叫状态，7是接通，其他是未接通，大部分未接通场景是0
  callStatus?: string | number | null,
  // 当前呼叫的线索，未接通，错误代码
  cause?: CallStatusEnum | null,
  // 当前呼叫的线索，未接通，错误原因，优先展示
  causeString?: keyof typeof CallStatusEnum | null,
  // 消息内容，比如坐席主动挂断时的响应结果
  content?: string | null,
}

// 坐席 客户状态记录
export interface EventInfo {
  // 接口数据
  // 电话号码
  phone?: string | null,
  // 事件动作名称
  stageString?: string | null,
  // 日期时间
  occurrenceTime?: string | null,
  // 产品
  product?: string | null,

  // 页面展示，不是接口数据
  // 产品info_key
  productInfoKey?: string,
  // 产品名称
  productDisplayName?: string,
}

// 坐席 通话携带信息
export interface CallCarryInfo {
  name?: string | null,
  age?: number | null,
}

// 坐席 WebSocket 消息
export interface SeatWebSocketMsg extends CallStatusInfo, EventInfo, CallCarryInfo {
  // FS账号
  fsUser?: string | null,
  // FS服务器地址
  fsIp?: string | null,
  // 页面主动发起的动作
  // 心跳保活 keepalive
  // 取消呼叫 hangup
  // 弹窗中 pop-up
  // 挂断电话 pop-down
  action?: 'keepalive' | 'hangup' | 'pop-up' | 'pop-down' | string | null,
  // 响应状态
  // 心跳保活 keepalive
  status?: string | null,
  // 服务器消息通知类型
  // 人工直呼呼叫状态 direct-call-status
  // 客户状态 event-push
  // 通话携带信息 phone-carry-info
  noticeType?: 'direct-call-status' | 'event-push' | 'phone-carry-info' | string | null,
}

// 坐席 设置
export interface SeatSetting {
  autoCheckInTask?: boolean // 人机协同 自动签入任务
  autoAccept?: boolean // 人机协同 自动监听/接听
  autoCallNext?: boolean // 人工直呼 自动拨打下一个
  autoResendSms?: boolean // 人工直呼 自动重发备注短信
}

// 坐席 客户状态记录 接口参数
export interface EventInfoParams {
  // 电话号码
  phone?: string
  // 查询起始时间
  startTime?: string
  // 查询终止时间
  endTime?: string
}

// 坐席 日志 类型 枚举
export enum SeatLogTypeEnum {
  '详细' = 'verbose',
  '信息' = 'info',
  '警告' = 'warn',
  '错误' = 'error',
}

// 坐席 日志 页面数据
export interface SeatLogItem {
  id?: number
  // 日志上报时间（浏览器时间）
  reportTime?: string
  // 日志类型
  type?: SeatLogTypeEnum
  // 坐席账号
  account?: string
  // 动作名称
  action?: string
  // 动作描述
  desc?: string | object,
  // 接待的客户电话号码
  phone?: string
  // 通话类型
  callType?: string
  // 坐席状态
  seatStatus?: string
  // 签入任务数量
  taskCount?: number
  // 备注
  remark?: string
  recordId?: string
  phoneRecordId?: string
  fsUser?: string
  fsIp?: string
  callId?: string
  // 接口请求参数
  params?: any
  // 接口响应参数
  response?: any
}

// 坐席 日志 请求参数
export interface SeatLogParam {
  id?: number
  reportTime?: string
  type?: SeatLogTypeEnum
  account?: string
  title?: string
  // 将页面数据的一些属性序列化成JSON字符串
  content?: string
}

// 坐席工作台 标签页 枚举
export enum WorkbenchTabEnum {
  'AI通话记录' = 'AI通话记录',
  '跟进记录' = '跟进记录',
  '客户状态记录' = '客户状态记录',
  'AI跟进记录' = 'AI跟进记录',
}

// 坐席日志 动作类型 枚举
export enum SeatLogActionEnum {
  // 坐席操作
  '坐席页面交互' = '坐席页面交互',
  '上线' = '上线',
  '下线' = '下线',
  '手动签入签出任务' = '手动签入签出任务',
  '自动签出任务' = '自动签出任务',
  '关联任务手动签入' = '关联任务手动签入',
  '关联任务自动签入' = '关联任务自动签入',
  '切换工作休息状态' = '切换工作休息状态',
  '坐席状态变化' = '坐席状态变化',
  '坐席空闲时长' = '坐席空闲时长',

  // 网络连接、请求、响应
  'Janus连接' = 'Janus连接',
  'Janus请求' = 'Janus请求',
  'Janus响应' = 'Janus响应',
  '音频ICE状态变化' = '音频ICE状态变化',
  'WebSocket连接' = 'WebSocket连接',
  'WebSocket请求' = 'WebSocket请求',
  'WebSocket响应' = 'WebSocket响应',
  '通知FS' = '通知FS',

  // 通话流程
  '人工直呼-呼叫' = '人工直呼-呼叫',
  '人工直呼-取消呼叫' = '人工直呼-取消呼叫',
  '人工直呼-呼叫失败' = '人工直呼-呼叫失败',
  '人工直呼-拨打下一个' = '人工直呼-拨打下一个',
  '人工直呼-来电' = '人工直呼-来电',
  '人工直呼-接听' = '人工直呼-接听',
  '人工直呼-挂断' = '人工直呼-挂断',
  '人工直呼-话后处理提交' = '人工直呼-话后处理提交',

  '人机协同-来电' = '人机协同-来电',
  '人机协同-拒接' = '人机协同-拒接',
  '人机协同-监听' = '人机协同-监听',
  '人机协同-退出监听' = '人机协同-退出监听',
  '人机协同-接管' = '人机协同-接管',
  '人机协同-退出接管' = '人机协同-退出接管',
  '人机协同-介入' = '人机协同-介入',
  '人机协同-挂断' = '人机协同-挂断',
  '人机协同-话后处理提交' = '人机协同-话后处理提交',

  '客户状态记录-绑定当前通话' = '客户状态记录-绑定当前通话',
  '客户状态记录-解绑当前通话' = '客户状态记录-解绑当前通话',
  '客户状态记录-全量更新' = '客户状态记录-全量更新',
  '客户状态记录-增量更新' = '客户状态记录-增量更新',

  '自动发送备注短信' = '自动发送备注短信',
}
