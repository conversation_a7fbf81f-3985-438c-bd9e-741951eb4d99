import { TaskStatusEnum, TaskManageItem, TaskTypeEnum } from '@/type/task'

export enum MQStateEnum {
 '运行中' = 'running',
 '空闲中' = 'idle',
}

export interface MonitorStatisticItem {
  account: string,
  groupId?: string,
  concurrentUsedNum: number,
  concurrentRealNum: number, // 实际并发
  planedPhoneNum: number,
  
  remainPhoneNum: number, // 首呼 所有任务
  remainPhoneNumRecall: number, // 补呼 所有任务
  remainPhoneNumProcess: number, // 首呼 进行中任务
  remainPhoneNumProcessRecall: number, // 首呼 进行中任务

  phoneNum: number, // 导入名单

  calledNum: number,
  putThroughNum: number,
  putThroughRate: number,
}
export interface MonitorTotalInfo {
  phoneNum?: number
  totalTaskNum?: number
  runningTaskNum?: number
  putThroughPhoneNum?: number
  calledPhoneNum?: number
 
  sendSmsNum?: number
  triggerSmsNum?: number
  callRecordNum?: number
  callRecordPutThroughNum?: number
}
// 将并发从全部信息剥离
export interface MonitorConcurrentInfo {
  supplyConcurrent?: number
  tenantConcurrent?: number
  realConcurrent?: number
  pauseConcurrent?: number
}

export interface MonitorSearchInfo {
  taskName?: string
  callStatus?: TaskStatusEnum
  taskType?: TaskTypeEnum,
  speechCraftName?: string
  tenantName?: string
  account?: string,
  startTime?: string
  endTime?: string
  ifFindAll?: string, // 1: 用于admin查询所有
}
export interface MonitorInfoItem extends TaskManageItem {
  tenantName?: string
  tenantCode?: string
  putThroughPhoneNum?: number
  putThroughPhoneRate?: number
  aiAnswerNum?: number
}

export interface MonitorAccountItem {
  account: string
  groupId: string
  tenantName?: string
  tenantNo?: string
  planedPhoneNum?: number
  calledPhoneNum?: number
  calledPhoneRates?: number
  outboundCalledRates?: number
  firstCallRemainNum?: number
  recalledRemainNum?: number
  averageCallDurations?: number
  intentionNums?: number
  classANum?: number
  classBNum?: number
  classCNum?: number
  classDNum?: number
  calledNum?: number
  putThroughNum?: number
  putThroughRates?: number
  totalTasksNum?: number
  executingTasksNum?: number
  outboundTotalNum?: number
  outboundCalledNum?: number
  calledPhoneRate?: number // 名单执行度,前端自己计算
  outboundCalledRate?: number // 外呼执行度,前端自己计算 
}

export interface ConcurrentInfoItem {
  supplyConcurrent?: number
  tenantConcurrent?: number
  realConcurrent?: number
  pauseConcurrent?: number
  secondIndustry?: string
}

export interface MQMonitorItem {
  id: number,
  queue: string,
  state: MQStateEnum,
  ready?: number,
  content?: string,
  date?: string,
  unAcked?: number,
  total?: number,
  incoming?: number,
  deliverGet?: number,
  ack?: number,
}
export interface XxlMonitorItem {
  triggerStatus: number,
  jobCron: string,
  executorHandler: string,
  pushABCDRecordToInsuranceTask: string,
  id: number,
  jobDesc: string,
}
export interface FsMonitorItem {
  "num"?: number,
  "fsIp"?: string,
  "calledCount"?: number,
  "calledThroughCount"?: number,
  "callDurationSec"?: number,
  "time530"?: number,
  "cycleCount"?: number,
  "abcdCount"?: number,
  "manHuangUpCount"?: number,
  "robotHuangUpCount"?: number,
  "calledRatio"?: number,
  "averageDuration"?: number,
  "time530Percent"?: number,
  "averageCycleCount"?: number,
  "abcdPercent"?: number,
  "robotHuangUpPercent"?: number
}
export interface FsResponse {
  averageValue: Record<string, {
    minScope: number,
    maxScope: number,
  }>,
  dataList: FsMonitorItem[]
}
// 坐席日志 表格数据
export interface SeatLogItem {
  id: number
  type?: string
  account?: string
  title?: string
  content?: string
  reportTime?: string
}
// 坐席日志 搜索 表单默认值
export class SeatLogSearchClass {
  type = ''
  account = ''
  title = ''
  reportTimeStart = ''
  reportTimeEnd = ''
}
// 坐席日志 搜索 接口参数
export interface SeatLogSearchParam {
  type?: string
  account?: string
  title?: string
  reportTimeStart?: string
  reportTimeEnd?: string
}
