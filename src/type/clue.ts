import { RecordTypeEnum, TaskCallRecordItem, TaskManageItem } from './task'
import { IntentionClassEnum } from './common'
import { SeatMember } from './seat'
import dayjs from 'dayjs'
import { SmsStatusEnum } from '@/type/sms'

export enum TaskStatusEnum {
  '待执行' = 'TO_BE_EXECUTED',
  '进行中' = 'IN_PROCESS',
  '已停止' = 'STOPPED',
  '未完成' = 'UN_COMPLETE',
}

export enum FormItemEnum {
  '短文本（20字符内）' = 0,
  '长文本（500字符内）',
  '单选',
  '多选',
  '级联（单选）',
  '日期',
  '时间',
  '日期范围',
  '时间范围',
  '日期时间范围',
}

export enum FollowUpStatusEnum {
  '跟进中' = 'BEING',
  '跟进成功' = 'SUCCESS',
  '跟进失败' = 'FAIL',
}

export enum FollowUpTypeEnum {
  '人机协同' = 'HUMAN_MACHINE_COOPERATION',
  '人工直呼' = 'MANUAL_DIRECT_CALL',
  '直发短信' = 'DIRECT_SMS',
}

// 线索来源/导入类型: 本地导入, 任务导入
export enum ClueFromTypeEnum {
  '本地导入' = 'LOCAL_IMPORT',
  '任务导入' = 'TASK_IMPORT',
  '接口导入' = 'INTERFACE_IMPORT',
}

// 审核状态
export enum ExamineStatusEnum {
  '未审核' = 'NO_EXAMINE',
  '审核成功' = 'PASS',
  '审核失败' = 'FAIL',
}

// 流转记录 操作 枚举
export enum OperateTypeEnum {
  '下发' = 'SEND',
  '分配' = 'DISTRIBUTE',
  '回收' = 'RECOVER',
  '归档' = 'ARCHIVE',
  '激活' = 'ACTIVE',
  '导入' = 'IMPORT',
  '加入任务' = 'JOIN_TASK',
}

export enum ClueStatusEnum {
  '待下发' = 'TO_BE_SEND',
  '待分配' = 'TO_BE_DISTRIBUTE',
  '已分配' = 'BE_DISTRIBUTED',
  '已回收' = 'BE_RECOVERED',
  '已归档' = 'BE_ARCHIVED',
}

/** 线索最近呼叫状态，后端库中仅这两个状态 */
export enum ClueCallStatusEnum {
  '呼叫成功' = 'SUCCESS_CALL',
  // '呼叫失败' = 'DIALING',
  '未接通' = 'NOT_CONNECTED',
  // '路由失败' = 'ROUTE_FAIL',
  // '已屏蔽' = 'BLOCKED',
  // '呼叫异常' = 'NO_SCRIPT,NOT_TENANT_LINE,OBTAIN_LINE_EXCEPTION',
}

export const clueCallStatusOptions = {
  '呼叫成功': {
    name: '呼叫成功',
    value: ClueCallStatusEnum['呼叫成功']
  },
  '未接通': {
    name: '未接通',
    value: ClueCallStatusEnum['未接通']
  },
}

// 线索项
export interface ClueItem {
  id?: number
  phone?: string, // 号码
  clueUniqueId?: string // 加密号码
  name?: string, // 姓名
  comment?: string // 备注
  operator?: string, // 运营商
  city?: string, // 城市
  cityCode?: string,
  province?: string, // 省份
  provinceCode?: string,
  groupId?: string, // 商户信息id
  // 话术信息
  scriptId?: number,
  scriptStringId?: string,
  // 任务信息
  taskId?: string,
  taskName?: string,

  joinedTaskId?: string, // 加入任务id
  joinedTaskName?: string, // 加入任务名
  joinedTaskStatus?: TaskStatusEnum, // 加入任务状态

  examineStatus?: ExamineStatusEnum, // 跟进成功的线索，存在审核状态

  callSeatId?: number, // 坐席id
  callTeamId?: number, // 坐席组id

  callOutTime?: string, // 呼出时间
  callStatusCode?: ClueCallStatusEnum, // 呼叫状态
  callDuration?: number, // 通话时长
  cycleCount?: number, // 对话轮次
  sayCount?: number, // 说话次数
  intentionClass?: string, // 分类
  intentionLabelIds?: number[] // 标签ids
  intentionLabels?: string[] // 标签ids

  latestCallDuration?: number, // 最近通话时长
  latestCallStatus?: ClueCallStatusEnum, // 最近通话状态
  latestFollowUpStatus?: FollowUpStatusEnum, // 跟进状态,跟进中，成功，失败
  latestFollowUpTime?: string | null, // 最近跟进时间
  latestFollowUpNote?: string, // 跟进备注
  nextFollowUpTime?: string, // 下次跟进时间
  clueFollowUpLogIds?: number[] // 跟进ids
  followCount?: number, // 跟进次数

  importTime?: string, // 导入时间
  fromType?: ClueFromTypeEnum, // 线索来源/导入类型: 本地导入, 任务导入
  canBeSend?: boolean, // 可下发状态
  beSendTime?: string, // 下发时间
  beAllocatedTime?: string, // 分配时间
  archivedTime?: string // 归档时间
  recoveryTime?: string, // 回收时间
  autoRecoveryTime?: string // 自动回收时间
  expire?: boolean, // 是否过期
  star?: boolean, // 是否星标
  clueFormIds?: number[] // 表单id
  clueStatus?: ClueStatusEnum, // 线索状态：待下发、待分配、已分配、已回收、已归档
  clueTransferRecordIds?: number[] // 流转id

  createTime?: string,
  updateTime?: string

  formId?: string

  age?: number // 年龄
}

// 线索搜索项
export interface ClueSearchInfo {
  groupId?: string
  phone?: string,
  name?: string,
  followUpStatus?: FollowUpStatusEnum,
  examineStatus?: ExamineStatusEnum,
  callTeamId?: number,
  callSeatId?: number
  cityCode?: string,
  provinceCode?: string,
  importTimeEnd?: string, // 导入时间
  importTimeStart?: string,

  beSendTimeStart?: string, // 下发时间
  beSendTimeEnd?: string,
  beAllocatedTimeStart?: string, // 分配时间
  beAllocatedTimeEnd?: string,
  autoRecoveredTimeEnd?: string, // 自动回收
  autoRecoveredTimeStart?: string,
  callOutTimeStart?: string, // 呼出
  callOutTimeEnd?: string,
  latestCallStatusList?: ClueCallStatusEnum[], // 最近跟进状态
  latestFollowUpTimeStart?: string, // 最近跟进时间
  latestFollowUpTimeEnd?: string, // 最近跟进时间
  nextFollowUpTimeStart?: string, // 下次跟进时间
  nextFollowUpTimeEnd?: string,
  isStar?: boolean // 是否星标
  isExpire?: boolean // 是否过期

  callStatusCodes?: ClueCallStatusEnum[],
  scriptStringIds?: string[],
  taskIds?: number[]
  intentionClasses?: string,
  label?: string,
  minCallDuration?: number,
  maxCallDuration?: number,
  minCycleCount?: number,
  maxCycleCount?: number,
  minSayCount?: number,
  maxSayCount?: number,
  minFollowUpCount?: number,
  maxFollowUpCount?: number,
  fromType?: ClueFromTypeEnum,
  canBeSend?: boolean,
  joinedTaskIds?: number[]
  joinedTaskStatus?: TaskStatusEnum[],
}

export enum AllocateTypeEnum {
  '平均分配' = '0',
  '按座席数' = '1'
}
export enum BeSendTypeEnum {
  '下发到坐席组' = '0',
}


export interface ClueAutoActionInfo {
  id?: number,
  autoStopTime?: string // 结束时间，为空代表永久
  status?: string // 是否开启, （0关闭，1开启）
  autoStop?: string // 是否为永久生效 0, 指定日期 1
}
// 自动导入
export interface ClueAutoImportSetting extends CallRecordFilterModal, ClueAutoActionInfo {
  id?: number,
  startWorkTimes?: string // 运行时间
  endWorkTimes?: string
  executeTimeGap?: number // 执行间隔时间
  comment?: string // 备注
}
// 自动下发信息
export interface ClueAutoSettingInfo extends ClueAutoActionInfo {
  id?: number,
  executeTimeGap?: number // 执行间隔时间
  comment?: string // 备注

  startWorkTimes?: string // 运行时间
  endWorkTimes?: string

  callTeamIds?: number[] // 坐席组ids
  distributeType?: BeSendTypeEnum //  下发类型

  callSeatIds?: number[] // 坐席ids
  allocationType?: AllocateTypeEnum // 分配方式

  workTimes?: string,  // 自动归档运行时间
  clueStatusList?: ClueStatusEnum[],
  excludeStart?: string, // 是否排除星标, （0否，1是）
  excludeNotCalled?: string, // 是否排除待首呼, （0否，1是）
}

export class ClueAutoOrigin {
  constructor(type: number) {
    if ([1,2].includes(type)) {
      this.startWorkTimes = '07:00'
      this.endWorkTimes  = '20:00'
    }
    if (type === 1) {
      this.allocationType = AllocateTypeEnum['平均分配']
      this.distributeType = BeSendTypeEnum['下发到坐席组']
    } else if(type === 2) {
      this.allocationType = AllocateTypeEnum['平均分配']
    } else {
      this.excludeNotCalled = '0'
      this.excludeStart = '0'
    }
  }
  allocationType
  distributeType
  startWorkTimes
  endWorkTimes
  workTimes = undefined
  callTeamIds = undefined
  callSeatIds = undefined
  clueStatusList = undefined
  executeTimeGap = undefined
  excludeStart
  excludeNotCalled
  comment = undefined
  status = '0'
  autoStop = '0'
}

export interface ClueSearchBaseInfo {
  groupId?: string,
  phone?: string,
  name?: string,
  followUpStatus?: FollowUpStatusEnum,
  callTeamId?: number,
  callSeatId?: number
  cityCode?: string,
  provinceCode?: string,
  importTimeStart?: string, // 导入时间
  importTimeEnd?: string,
  beSendTimeStart?: string, // 下发时间
  beSendTimeEnd?: string,
  beAllocatedTimeStart?: string, // 分配时间
  beAllocatedTimeEnd?: string,
}

export interface RecoveredClueSearchInfo extends ClueSearchBaseInfo {
  beRecoveredTimeStart?: string, // 回收时间
  beRecoveredTimeEnd?: string,
}

export interface ArchivedClueSearchInfo extends ClueSearchBaseInfo {
  beArchivedTimeStart?: string, // 归档时间
  beArchivedTimeEnd?: string
}

export class TaskCallFilterOrigin {
  taskId = undefined
  phone = undefined
  addTimeStart = undefined
  addTimeEnd = undefined
  lastCallTimeStart = undefined
  lastCallTimeEnd = undefined
  putThroughNumLeft = undefined // 接通
  putThroughNumRight = undefined
  calledNumLeft = undefined // 拨打
  calledNumRight = undefined
  statusList = undefined
  operator = undefined
  provinceCode = undefined
  cityCode = undefined

  callOutTimeStart = undefined
  callOutTimeEnd = undefined
  label = undefined
  minCallDuration = undefined
  maxCallDuration = undefined
  minCycleCount = undefined
  maxCycleCount = undefined
  minSayCount = undefined
  maxSayCount = undefined
}

export interface TaskCallFilterModal {
  taskId?: string | number
  phone?: string
  addTimeStart?: string
  addTimeEnd?: string
  lastCallTimeStart?: string
  lastCallTimeEnd?: string
  putThroughNumLeft?: number // 接通
  putThroughNumRight?: number
  calledNumLeft?: number // 拨打
  calledNumRight?: number
  statusList?: number[]
  operator?: string
  provinceCode?: string
  cityCode?: string

  callOutTimeStart?: string
  callOutTimeEnd?: string
  label?: string
  minCallDuration?: number,
  maxCallDuration?: number,
  minCycleCount?: number,
  maxCycleCount?: number,
  minSayCount?: number,
  maxSayCount?: number,
}

export interface CallRecordFilterModal {
  importSource: string //  '0' 通话记录 必传
  outboundType: string //  '0' AI外呼 必传
  taskIdList?: number[]
  callOutTimeStart?: string
  callOutTimeEnd?: string
  excludeIntentionLabels?: string[]
  containIntentionLabels?: string[],
  intentionClass?: IntentionClassEnum[],
  minCallDuration?: number,
  maxCallDuration?: number,
  minCycleCount?: number,
  maxCycleCount?: number,
  excludeEmptyLabel?: string //  '0' 不排除; '1' 排除
}

// 坐席工作台 线索列表 筛选项 星标 组件值 枚举
export enum WorkbenchClueStarEnum {
  NOT_STAR = 'NOT_STAR',
  STAR = 'STAR',
}

// 坐席工作台 线索列表 筛选项 过期 组件值 枚举
export enum WorkbenchClueExpireEnum {
  NOT_EXPIRE = 'NOT_EXPIRE',
  EXPIRE = 'EXPIRE',
}

// 坐席工作台 线索列表 筛选项
export interface WorkbenchClueSearchInfo extends ClueSearchInfo {
  autoRecoveryTimeEnd: string,
  autoRecoveryTimeStart: string,
  beAllocatedTimeEnd: string,
  beAllocatedTimeStart: string,
  cityCode: string,
  followUpLogNote: string,
  isExpire: boolean,
  isStar: boolean,
  latestCallStatus: string,
  latestFollowUpTimeEnd: string,
  latestFollowUpTimeStart: string,
  maxFollowUpCount: number,
  maxLatestCallDuration: number,
  minFollowUpCount: number,
  minLatestCallDuration: number,
  name: string,
  nextFollowUpTimeEnd: string,
  nextFollowUpTimeStart: string,
  phone: string,
  provinceCode: string
}

export class SearchFormOrigin {
  phone = undefined
  name = undefined
  callSeatId = undefined
  callTeamId = undefined
  followUpStatus = undefined
  intentionClasses = undefined
  label = undefined
  scriptStringIds = undefined
  taskIds = undefined
  provinceCode = undefined
  fromType = undefined
  canBeSend = undefined
  examineStatus = undefined
  minCallDuration = undefined
  maxCallDuration = undefined
  minCycleCount = undefined
  maxCycleCount = undefined
  minSayCount = undefined
  maxSayCount = undefined
  minFollowUpCount = undefined
  maxFollowUpCount = undefined
  callStatusCodes = undefined
  importTimeStart: string | undefined = undefined // 导入时间
  importTimeEnd: string | undefined = undefined
  latestFollowUpTimeStart = undefined // 最近跟进时间
  latestFollowUpTimeEnd = undefined // 最近跟进时间
  nextFollowUpTimeStart = undefined // 下次跟进时间
  nextFollowUpTimeEnd = undefined
  beSendTimeStart: string | undefined = undefined // 下发时间
  beSendTimeEnd: string | undefined = undefined
  beAllocatedTimeStart: string | undefined = undefined // 分配时间
  beAllocatedTimeEnd: string | undefined = undefined
  autoRecoveredTimeEnd = undefined // 自动回收
  autoRecoveredTimeStart = undefined
  callOutTimeStart = undefined
  callOutTimeEnd = undefined
  beRecoveredTimeStart: string | undefined = undefined // 回收
  beRecoveredTimeEnd: string | undefined = undefined
  beArchivedTimeStart: string | undefined = undefined // 归档
  beArchivedTimeEnd: string | undefined = undefined
  star = undefined // 星标
  note = undefined // 跟进备注
  joinedTaskStatus = undefined
  joinedTaskIds = undefined
  isStar = undefined
  isExpire = undefined // 是否过期
  groupId;

  constructor(groupId: string, type?: ClueStatusEnum) {
    this.groupId = groupId;
    switch (type) {
      case ClueStatusEnum['待下发']: {
        this.importTimeStart = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
        this.importTimeEnd = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        break
      }
      case ClueStatusEnum['待分配']: {
        this.beSendTimeStart = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
        this.beSendTimeEnd = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        break
      }
      case ClueStatusEnum['已分配']: {
        this.beAllocatedTimeStart = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
        this.beAllocatedTimeEnd = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        break
      }
      case ClueStatusEnum['已回收']: {
        this.beRecoveredTimeStart = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
        this.beRecoveredTimeEnd = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        break
      }
      case ClueStatusEnum['已归档']: {
        this.beArchivedTimeStart = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
        this.beArchivedTimeEnd = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        break
      }
    }
  }
}

// 跟进记录 短信发送记录
export interface ClueFollowSmsRecordItem {
  recordId?: string
  firstRecordId?: string
  smsStatus?: SmsStatusEnum | null
  smsContent?: string
}

// 跟进记录项
export interface ClueFollowItem {
  callRecordForHumanMachine?: TaskCallRecordItem,
  callRecordForManualDirect?: TaskCallRecordItem,
  clueFollowUpLog: ClueFollowLog
  smsRecord?: ClueFollowSmsRecordItem
}

export interface ClueFollowLog {
  id?: number,
  clueId?: number,
  callSeatId?: number,
  callDuration?: number,
  followUpStatus?: FollowUpStatusEnum,
  recordId?: string,
  followUpTime?: string,
  followUpType?: FollowUpTypeEnum | RecordTypeEnum,
  nextFollowUpTime?: string
  note?: string
  createTime?: string
  updateTime?: string
}

// 流转记录
export interface ClueTransferRecordItem {
  id?: number
  operateAccount: string
  operationType: OperateTypeEnum
  operationTime: string
}

// 线索统计项
export interface ClueStaticInfo {
  totalCount?: 0 // 总线索数
  toBeSendCount?: number // 待下发
  toBeDistributeCount?: number // 待分配
  beDistributeCount?: number // 已分配
  beRecoveredCount?: number // 已回收
  beArchivedCount?: number // 已归档
  failedCount?: number // 已失败
  successCount?: number // 已成功
  toBeFirstFollowUpCount?: number // 待首跟
}

// 团队管理-坐席情况
export interface SeatStaticInfo {
  humanMachineDialingSeats?: SeatMember[] // 人机协同，通话中坐席
  humanMachineIdleSeats?: SeatMember[] // 人机协同，空闲中坐席
  humanMachineListenSeats?: SeatMember[] // 人机协同，空闲中坐席
  humanMachinePostingSeats?: SeatMember[] // 人机协同，话后
  humanMachineWindowSeats?: SeatMember[] // 人机协同，监听
  inRestSeats?: SeatMember[] // 休息
  manualDirectCallingSeats?: SeatMember[] // 人工直呼，拨打中
  manualDirectDialingSeats?: SeatMember[] // 人工直呼，通话中
  manualDirectIdleSeats?: SeatMember[] // 人工直呼，空闲中
  manualDirectPostingSeats?: SeatMember[] // 人工直呼，话后
  offLineSeats?: SeatMember[] // 下线
}

// 团队管理-关联任务列表
export interface RelatedTaskItem extends TaskManageItem {
  activeCallSeatIds?: number[]
  callSeatCondition?: string
}

export interface RelatedTaskInfo {
  aiOutboundTask: RelatedTaskItem
  callSeatCondition: string
  activeCallSeatIds?: number[]
}

export interface TodayClueStaticInfo {
  acquireCount?: number
  failCount?: number
  followingCount?: number
  successCount?: number
}

// 坐席工作台 今日线索统计
export interface WorkbenchTodayClueStatistics extends TodayClueStaticInfo {
  beRecoveredCount?: number // 被回收
  beArchivedCount?: number // 被归档
  beReassignCount?: number // 被重新分配
}

// 坐席工作台 全部线索统计
export interface WorkbenchAllClueStatistics {
  allCluesCount?: number // 总线索数
  beingCluesCount?: number // 跟进中
  successCluesCount?: number // 跟进成功
  failCluesCount?: number // 跟进失败
  toBeFirstFollowUpCount?: number // 待首跟
}

export interface CascaderItem {
  label: string,
  children: CascaderItem[]
}

// 表单收集项
export interface CollectionFormItem {
  id?: number
  collectionItemName: string
  collectionItemType: FormItemEnum
  collectionStatus: number // 0禁用，1启用
  collectionOrder?: number // 排序
  requiredField: number
  options?: string[]
  cascadeOptions?: string[]
  groupId?: string
  updateTime?: string
  createTime?: string
}

// 线索详情下的表单信息
export interface FormRecordItem {
  callSeatId?: number
  clueId?: number
  formRecordId?: string
  formId?: string
  fromCollectionContentList: { id?: number, formCollectionId: number, content: string }[]
}
