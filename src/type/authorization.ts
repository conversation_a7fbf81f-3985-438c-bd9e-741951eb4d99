export enum MenuTypeEnum {
  '运营端' = '1',
  '商户端' = '2',
}

// 菜单单项属性
export interface MenuItem {
  id: number;
  menuType: MenuTypeEnum;
  menuCode: string;
  menuName: string;
  description?: string
}

export enum InterfaceTypeEnum {
  'TOKEN类' = 'TOKEN',
  'API类' = 'API',
  '登录类' = 'LOGIN',
}

export enum ServiceEnum {
  'AiSpeech' = 'AiSpeech',
  'AiMonitor' = 'AiMonitor',
  'AiWorkbench' = 'AiWorkbench',
}

// 接口的单项属性
export interface InterfaceItem {
  id: number;
  type: InterfaceTypeEnum; // LOGIN | TOKEN | API
  name: string;
  identifier: string; // 接口地址
  remark?: string;
  serviceId: ServiceEnum; // 服务名，AiSpeech等字符串
}

export interface MenuInterfaceItem {
  menuId?: number;
  permissionIdList?: number[];
}

export interface InterfaceLogItem {
  id: number;
  identifier: string; // 菜单接口中记录的接口地址
  realUrl: string; // 实际调用的url
  ifPass: boolean;
  interfacetype: InterfaceTypeEnum;
  name: string;
  relaxMode: boolean;
  remark: string;
  serviceId: string;
  createTime: string;
  updateTime: string;
  userName: string;
}

export interface InterfaceLogSearchParams {
  identifier?: string;
  limit: number;
  page: number;
  timeEnd: string;
  timeStart: string;
  type?: string;
  unNormal?: boolean;
}
