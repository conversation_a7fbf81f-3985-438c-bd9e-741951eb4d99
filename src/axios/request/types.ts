import type { AxiosRequestConfig, AxiosResponse } from 'axios'
export interface RequestInterceptors<T> {
  // 请求拦截
  requestInterceptors?: (config: AxiosRequestConfigDemo) => AxiosRequestConfigDemo
  requestInterceptorsCatch?: (err: any) => any
  // 响应拦截
  responseInterceptors?: (config: T) => T
  responseInterceptorsCatch?: (err: any) => any
}
// 自定义传入的参数
export interface RequestConfig<T = AxiosResponse> extends AxiosRequestConfig {
  interceptors?: RequestInterceptors<T>
}
export interface CancelRequestSource {
  [index: string]: () => void
}
export interface AxiosRequestConfigDemo extends AxiosRequestConfig {
  token?: String,
  version?: String,
  mc?: String,
  startTime?: Record<string,number>,
}
export interface ResponseData {
  total?: number | null
  totalPage?: number | null
  code: string;
  msg?: string;
  data?: unknown;
}