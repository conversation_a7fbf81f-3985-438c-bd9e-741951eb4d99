import type { AxiosInstance } from 'axios'
import axios, { AxiosResponse } from 'axios'
import type { AxiosRequestConfigDemo, RequestConfig, RequestInterceptors, ResponseData, } from './types'
import { getToken, formatMsDuration } from "@/utils/utils"
import { errorHandler } from "./status"
import { useUserStore } from "@/store/user"
import { useGlobalStore } from '@/store/globalInfo'
import router from '@/router'
import { trace, } from '@/utils/trace'

class Request {
  // axios 实例
  instance: AxiosInstance
  // 拦截器对象
  interceptorsObj?: RequestInterceptors<AxiosResponse>
  cancelRequestSourceList?: Map<string, AbortController>
  requestUrlList?: string[]
  timer?: ReturnType<typeof setTimeout> | null
  constructor(config: RequestConfig) {
    this.requestUrlList = []
    this.cancelRequestSourceList = new Map<string, AbortController>()
    this.instance = axios.create(config)
    this.interceptorsObj = config.interceptors
    this.timer = null
    // 拦截器执行顺序 接口请求 -> 实例请求 -> 全局请求 -> 实例响应 -> 全局响应 -> 接口响应
    this.instance.interceptors.request.use(
      (res: AxiosRequestConfigDemo) => {
        const userStore = useUserStore()

        // dev环境收集接口
        if (import.meta.env.MODE.includes('development')) {
          const routeStr = location.hash.slice(1).split('?')[0]
          const arr = res.url?.split('/').filter(item => !!item) || []
          if (userStore.pageApiMap[routeStr]) {
            const apiList = userStore.pageApiMap[routeStr]
            if (apiList && !apiList?.find(item => item.api === '/' + arr?.slice(1).join('/') && item.service === arr[0])) {
              userStore.pageApiMap[routeStr] = [...apiList,{
                route: routeStr,
                service: arr[0],
                api: '/' + arr?.slice(1).join('/'),
              }]
            }
          } else {
            userStore.pageApiMap[routeStr] = [{
              route: routeStr,
              service: arr[0],
              api: '/' + arr?.slice(1).join('/'),
            }]
          }
        }
        
        // console.log('##########', 'baseURL', res.baseURL, 'url', res.url)

        // 请求添加token等信息
        if (!res.url?.endsWith('/login')) {
          if (!getToken()) {
            setTimeout(() => {
              userStore.logout()
              router.push("/login")
            }, 100)
          } else {
            res.headers = {
              token: getToken(),
            }
          }
        }
        if (!res.startTime) {
          res.startTime = {}
        }
        // 埋点日志和坐席日志，登录接口无需记录接口开始时间
        if (!(res.url?.includes('traceLog/addTraceInfo') || res.url?.includes('/remote/logs/seat_workbench_web_log') || 
          res.url?.endsWith('/login')))  {
          res.startTime[res.url!] = performance.now()
        }
        return res
      },
      (err: any) => err,
    )

    // 使用实例拦截器
    this.instance.interceptors.request.use(
      this.interceptorsObj?.requestInterceptors,
      this.interceptorsObj?.requestInterceptorsCatch,
    )
    this.instance.interceptors.response.use(
      this.interceptorsObj?.responseInterceptors,
      this.interceptorsObj?.responseInterceptorsCatch,
    )
    // 全局响应拦截器保证最后执行,直接返回res.data
    this.instance.interceptors.response.use(
      async (_response: AxiosResponse<ResponseData>) => {
        const { data: response } = _response; //重命名data，避免冲突
        const { code, msg, data } = response;
        const isSuccess = !response || [0, 200, 2000].includes(+code)

        // 获取请求时长
        // @ts-ignore
        const startTime = _response.config.startTime[_response.config.url] || 0
        const endTime = performance.now();
        const duration = endTime - startTime;
        // @ts-ignore
        delete _response.config.startTime[_response.config.url]

        // 请求超过根据不同条件进行埋点（不对埋点超时进行处理）
        if (startTime && !_response.config.url?.includes('traceLog/addTraceInfo') && !_response.config.url?.includes('/remote/logs/seat_workbench_web_log')) {
          const params = {
            url: _response.config.url,
            path: location?.href || '',
            data: _response.config.data ? JSON.stringify(_response.config.data)?.slice(0, 300) : undefined,
            params: _response.config.params ? JSON.stringify(_response.config.params)?.slice(0, 300) : undefined,
            duration: formatMsDuration(duration/1000)
          }
          if(!isSuccess) {
            await trace({ page: `请求失败-${formatMsDuration(duration/1000)}s`, params})
          } else if (_response.config.url?.includes('/surveillance') || _response.config.url?.includes('/callRecordAiManual/add') || _response.config.url?.includes('/callRecord/add')) {
            (duration > 15000) && await trace({ page: `请求耗时过长-特殊-${formatMsDuration(duration/1000)}`, params: params})
          } else {
            (duration > 3000) && await trace({ page: `请求耗时过长-普通-${formatMsDuration(duration/1000)}`, params: params})
          }
        }

        if (!code) {
          return Promise.resolve(response as unknown);
        }
        if (isSuccess) {
          if (response.total && response.total > 0) {
            return Promise.resolve(response as ResponseData);
          } else {
            return Promise.resolve(data as unknown);
          }
        } else {
          errorHandler(+code, response, msg);
          return Promise.reject(response as any);
        }
      },
      async err => {
        // 用于处理取消请求
        if (!err.config) return Promise.reject(err?.response as any);

        // 获取请求时长
        const endTime = performance.now();
        // @ts-ignore
        const startTime = err.config?.startTime ? err.config.startTime[err.config.url] || 0 : 0
        // @ts-ignore
        delete err.config.startTime[err.config.url]
        const duration = endTime - startTime;
        // 失败接口均需要埋点(埋点日志和坐席日志，自身的请求出现报错除外)
        if (!err.config.url?.includes('traceLog/addTraceInfo') && !err.config.url?.includes('/remote/logs/seat_workbench_web_log')) {
          await trace({ page: `请求失败-${formatMsDuration(duration/1000)}s`, params: {
            url: err,
            path: location?.href || '',
            duration: formatMsDuration(duration/1000)
          }})
        }
        
        if (err && err.response) {
          const { status, data: dataErr, } = err.response;
          errorHandler(status, err, dataErr?.message || undefined);
        }
        return Promise.reject(err?.response as any);
      }
    )
  }

  request<T>(config: RequestConfig<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      // 如果我们为单个请求设置拦截器，这里使用单个请求的拦截器
      if (config.interceptors?.requestInterceptors) {
        config = config.interceptors.requestInterceptors(config)
      }
      const url = config.url
      if (url) {
        this.requestUrlList?.push(url)

        const controller = new AbortController();
        config.signal = controller.signal;
        if (!this.cancelRequestSourceList?.has(url) && !url?.includes('traceLog/addTraceInfo') && !url?.includes('/remote/logs/seat_workbench_web_log')) {
          // 如果当前请求不在等待中，将其添加到等待中
          this.cancelRequestSourceList?.set(url, controller);
        }
      }
      this.instance.request<any, T>(config).then(res => {
        if (config.interceptors?.responseInterceptors) {
          res = config.interceptors.responseInterceptors(res)
        }
        resolve(res)
      }).catch((err: any) => {
        reject(err)
      }).finally(() => {
        url && this.delUrl(url)
        this.timer && clearTimeout(this.timer)
        this.timer = setTimeout(() => {
          if (!this.requestUrlList || this.requestUrlList?.length < 1) {
            const globalStore = useGlobalStore()
            globalStore.loading = false
          }
          this.timer && clearTimeout(this.timer)
          this.timer = null
        }, 500);
      })
    })
  }

  // 取消请求
  cancelRequest(url: string | string[]) {
    if (typeof url === 'string') {
      if (this.cancelRequestSourceList?.has(url)) {
        this.cancelRequestSourceList && this.cancelRequestSourceList?.get(url)?.abort()
      }
    } else {
      url.forEach(u => {
        if (this.cancelRequestSourceList?.has(u)) {
          this.cancelRequestSourceList && this.cancelRequestSourceList?.get(u)?.abort()
        }
      })
    }
  }

  // 取消全部请求
  cancelAllRequest() {
    this.cancelRequestSourceList?.forEach(source => {
      source?.abort()
    })
  }

  // 请求返回后，需要删除请求队列、取消map中对应url
  private delUrl(url: string) {
    // 删除请求列表中的url
    const urlIndex = this.requestUrlList?.findIndex(u => u === url)
    urlIndex !== -1 && this.requestUrlList?.splice(urlIndex as number, 1)
    // 删除取消函数Map中的url
    this.cancelRequestSourceList?.delete(url)
  }
}

export default Request
export { RequestConfig, RequestInterceptors }
