import { http } from '@/axios'
import { MenuItem, InterfaceItem, MenuInterfaceItem, InterfaceLogSearchParams } from '@/type/authorization'
import { ResponseData } from '@/axios/request/types'
import { filterEmptyParams } from '@/utils/utils'

export const authorizationModel = {
  /** 菜单管理 */
  findMenuList: () => {
    return http({
      url: "/AiSpeech/menuPermission/findMenuList",
      method: "POST",
    }).then(res => res as unknown as MenuItem[])
  },
  deleteMenu: (params: {id: number}) => {
    return http({
      url: "/AiSpeech/menuPermission/deleteMenuById",
      method: "POST",
      params,
    }).then(res => res as unknown)
  },
  saveMenu: (data: Partial<MenuItem>) => {
    return http({
      url: "/AiSpeech/menuPermission/saveMenu",
      method: "POST",
      data
    }).then(res => res as unknown)
  },

  /** 菜单-接口关系管理 */
  findInterfacePermissionByMenuId: (params: {
    menuId: number
  }) => {
    return http({
      url: "/AiSpeech/menuPermission/findInterfacePermissionListByMenuId",
      method: "POST",
      params
    }).then(res => res as unknown as InterfaceItem[])
  },
  saveMenuPermission: (data: MenuInterfaceItem) => {
    return http({
      url: "/AiSpeech/menuPermission/saveMenuPermission",
      method: "POST",
      data
    }).then(res => res as unknown)
  },

  /** 接口管理 */
  findInterfaceList: () => {
    return http({
      url: "/AiSpeech/menuPermission/findInterfacePermissionList",
      method: "POST",
    }).then(res => res as unknown as InterfaceItem[])
  },
  deleteInterface: (params: {id: number}) => {
    return http({
      url: "/AiSpeech/menuPermission/deleteInterfacePermissionById",
      method: "POST",
      params,
    }).then(res => res as unknown)
  },
  saveInterface: (data: Partial<InterfaceItem>) => {
    return http({
      url: "/AiSpeech/menuPermission/saveInterfacePermission",
      method: "POST",
      data
    }).then(res => res as unknown as InterfaceItem)
  },
  /** 接口日志 */
  findInterfacePermissionLog: (data: Partial<InterfaceLogSearchParams>) => {
    return http({
      url: "/AiSpeech/menuPermission/findInterfacePermissionLog",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as ResponseData)
  },
}
