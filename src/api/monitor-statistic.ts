import { http } from '@/axios'
import { filterEmptyParams } from '@/utils/utils'
import { MonitorTotalInfo, MonitorSearchInfo, XxlMonitorItem, FsResponse, ConcurrentInfoItem, MonitorInfoItem } from '@/type/monitor-statistic'
import { ResponseData } from '@/axios/request/types'

// 监控平台接口公共前缀
let baseMonitorUrl: string = location.protocol === 'https:'
  ? import.meta.env.VITE_MONITOR_HTTPS
  : import.meta.env.VITE_MONITOR_HTTP

export const monitorStatisticModel = {
  findTotalData: () => {
    return http({
      url: "/AiSpeech/surveillance/taskStatus",
      method: "GET",
    }).then(res => res as unknown as MonitorTotalInfo)
  },
  findConcurrentData: () => {
    return http({
      url: "/AiSpeech/surveillance/individualConcurrent",
      method: "GET",
    }).then(res => res as unknown as MonitorTotalInfo)
  },
  findAccountData: () => {
    return http({
      url: "/AiSpeech/surveillance/accountStatus",
      method: "GET",
    }).then(res => res as unknown)
  },
  // findAccountPhoneNumData: () => {
  //   return http({
  //     url: "/AiSpeech/surveillance/outboundTaskPhoneNum",
  //     method: "GET",
  //   }).then(res => res as unknown as {
  //     phoneNum: number, account: string
  //   }[])
  // },
  getAllMainAccount: () => {
    return http({
      url: "/AiSpeech/surveillance/operationMainAdminList",
      method: "POSt",
    }).then(res => res as unknown as {
      account: string,
      groupId: string,
    }[])
  },
  findMonitorAccountList: (data: {
    tenantName?: string
    account?: string
  }) => {
    return http({
      url: "/AiSpeech/surveillance/groupStatus",
      method: "POST",
      data
    }).then(res => res as unknown)
  },
  findMonitorConcurrentList: () => {
    return http({
      url: "/AiMonitor/callLineChart/findIndustryMonitors",
      method: "POST",
    }).then(res => res as unknown as ConcurrentInfoItem[])
  },
  findMonitorListByCondition: (data: MonitorSearchInfo) => {
    return http({
      url: "/AiSpeech/aiOutboundTask/findList",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as { total?: number; data?: MonitorInfoItem[] })
  },

  /** mq接口 */
  findMonitorMQList: (params: { platform: string }) => {
    return http({
      url: baseMonitorUrl + "/mq-queue/list",
      method: "GET",
      params
    }).then(res => res as unknown)
  },

  /** Engine接口 开始 */
  getEngineList: () => {
    return http({
      url: baseMonitorUrl + "/engine-monitor/status",
      method: "GET",
    }).then(res => res as unknown)
  },
  startEngine: (data: {engineIps: string[]}) => {
    return http({
      url: baseMonitorUrl + "/engine-monitor/start",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  stopEngine: (data: {engineIps: string[]}) => {
    return http({
      url: baseMonitorUrl + "/engine-monitor/stop",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  editEngineThreshold: (data: {
    engineFlag?: string,
    threshold?: number
  }) => {
    return http({
      url:  baseMonitorUrl + "/fs-engine-manager/threshold",
      method: "GET",
      data
    }).then(res => res as unknown)
  },
  resetEngine: (data: {aiEngineFlags: string}) => {
    return http({
      url:  baseMonitorUrl + "/fs-engine-manager/engine-reset-fs",
      method: "GET",
      data,
    }).then(res => res as unknown as number)
  },
  getEngineFws: () => {
    return http({
      url:  baseMonitorUrl + "/fs-engine-manager/get-engine-fw-flag",
      method: "GET",
    }).then(res => res as unknown as string[])
  },
  startEngineByFws: (data: {fwFlags: string}) => {
    return http({
      url:  baseMonitorUrl + "/fs-engine-manager/start-engine-by-fw-flag",
      method: "GET",
      data,
    }).then(res => res as unknown)
  },
  stopEngineByFws: (data: {fwFlags: string}) => {
    return http({
      url:  baseMonitorUrl + "/fs-engine-manager/stop-engine-by-fw-flag",
      method: "GET",
      data,
    }).then(res => res as unknown)
  },
  editEngineThresholdByFws: (data: {fwFlags: string, threshold: number}) => {
    return http({
      url:  baseMonitorUrl + "/fs-engine-manager/threshold-by-fw-flag",
      method: "GET",
      data,
    }).then(res => res as unknown)
  },
  /** Engine接口 结束 */

  /** xxl接口 开始 */
  getXxlList: () => {
    return http({
      url: baseMonitorUrl + "/xxl-monitor/ai-data-list",
      method: "GET",
    }).then(res => res as unknown as XxlMonitorItem[])
  },
  startXxl: (data: {xxlId: number}) => {
    return http({
      url: baseMonitorUrl + "/xxl-monitor/ai-data-start",
      method: "GET",
      data,
    }).then(res => res as unknown)
  },
  stopXxl: (data: {xxlId: number}) => {
    return http({
      url: baseMonitorUrl + "/xxl-monitor/ai-data-stop",
      method: "GET",
      data,
    }).then(res => res as unknown)
  },
  /** xxl接口 结束 */
  /** caps */
  resetCaps: () => {
    return http({
      url: baseMonitorUrl + "/caps/line-reset",
      method: "GET",
    }).then(res => res as unknown)
  },

  /** fs */
  getFsList: (data: {
    startTime: string,
    endTime: string,
    fsIp: string,
    ifRecall: string,
  }) => {
    return http({
      url: "/AiMonitor/fs-statistics/list",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as FsResponse)
  },

  /** 推送比 */
  getRadioList: () => {
    return http({
      url: baseMonitorUrl + "/page-manager/get-radio-param",
      method: "GET",
    }).then(res => res as unknown as string)
  },
  saveRadio: (data: {
    newRadioParam: string
  }) => {
    return http({
      url:  baseMonitorUrl + "/page-manager/set-radio-param",
      method: "GET",
      data
    }).then(res => res as unknown as FsResponse)
  },

  /** 坐席日志 */
  getSeatLogList: async (data: any) => {
    return http({
      url: baseMonitorUrl + '/display/logs/trace/seat_workbench_web_log',
      method: 'POST',
      data,
    }).then(res => res as unknown as ResponseData)
  },
}
