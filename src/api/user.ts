import { http } from '@/axios'
import { AccountItem, RoleResponse, UserLoginForm } from '@/type/user'
import { MerchantAccountInfo } from '@/type/merchant'
import { filterEmptyParams } from '@/utils/utils'
import { useUserStore } from '@/store/user'
import { useMerchantStore } from '@/store/merchant'

export const userModel = {
  login: (userInfo: UserLoginForm) => {
    const data = new FormData()
    data.append("account", userInfo.account)
    data.append("password", userInfo.password)
    return http({
      data,
      url: "/AiSpeech/admin/login",
      method: "POST",
    }).then(res => res as unknown)
  },
  changePassword: (data: {
    id: number,
    newPassword: string,
    oldPassword: string,
  }) => {
    return http({
      data,
      url: "/AiSpeech/admin/updateAdminPwd",
      method: "POST",
    }).then(res => res as unknown)
  },
  getTaskTimeRange: () => {
    return http({
      url: "/AiSpeech/admin/getTaskTimeRange",
      method: "GET",
    }).then(res => res as unknown)
  },
  getMerchantTimeRange: (data: {groupId?: string}) => {
    return http({
      url: "/AiSpeech/admin/getTaskTimeRangeByOperator",
      method: "GET",
      data: data,
    }).then(res => res as unknown)
  },
  // 查看账号信息
  findAccountInfo: (data: {
    account: string,
    groupId: string,
  }) => {
    return http({
      url: "/AiSpeech/admin/findAccountInfo",
      method: "POST",
      data,
    }).then(res => res as unknown as {
      account: string,
      name: string,
      mainAccount: string,
      mainAccountName: string,
    })
  },
  

}
export const aiTeamModel = {
  searchRoleList: () => {
    return http({
      url: "/AiSpeech/adminRole/findAdminRoles",
      method: "GET",
    }).then(res => res as unknown)
  },
  findOneAdminRoleById: (params: {id: number}) => {
    return http({
      url: "/AiSpeech/adminRole/findOneAdminRoleById",
      method: "GET",
      params,
    }).then(res => res as unknown)
  },
  addRole: (data: RoleResponse) => {
    return http({
      data,
      url: "/AiSpeech/adminRole/addOneRole",
      method: "POST",
    }).then(res => res as unknown)
  },
  editRole: (data: RoleResponse) => {
    return http({
      data,
      url: "/AiSpeech/adminRole/editOneRole",
      method: "POST",
    }).then(res => res as unknown)
  },
  delRole: (params: {
    id: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/adminRole/deleteOneRoleById",
      method: "DELETE",
    }).then(res => res as unknown)
  },
  // 通过角色ID查询角色的IP白名单
  findIpListByRoleId: (data: {id: number}) => {
    return http({
      data,
      url: "/AiSpeech/adminRole/findIpListByRoleId",
      method: "GET",
    }).then(res => res as unknown as string[])
  },
  // 更新角色的IP白名单
  updateIpListById: (data: {
    id?: number,
    ipList: string[]
  }) => {
    return http({
      data,
      url: "/AiSpeech/adminRole/updateIpListById",
      method: "POST",
    }).then(res => res as unknown)
  },
  getMerchantAccountList: (data: {
    name?: string,
    account?: string,
    roleId?: number | null,
    startTime?: string,
    endTime?: string
  }) => {
    return http({
      url: "/AiSpeech/admin/getUserListByConditions",
      method: "POST",
      data: filterEmptyParams(data)
    }).then(res => res as unknown as AccountItem[])
  },
  getOperatorAccountList: () => {
    return http({
      url: "/AiSpeech/admin/getUserList",
      method: "GET",
    }).then(res => res as unknown)
  },
  deleteAccount: (params: {
    id: number
  }) => {
    return http({
      url: "/AiSpeech/admin/deleteUser",
      method: "DELETE",
      params,
    }).then(res => res as unknown)
  },
  editAccount: (data: AccountItem) => {
    return http({
      data,
      url: "/AiSpeech/admin/editUser",
      method: "POST",
    }).then(res => res as unknown)
  },
  addAccount: (data: AccountItem) => {
    return http({
      data,
      url: "/AiSpeech/admin/addUser",
      method: "POST",
    }).then(res => res as unknown)
  },
  editUserFreezeStatus: (data: {
    account: string,
    accountEnable: boolean
  }) => {
    return http({
      data,
      url: "/AiSpeech/admin/editUserFreezeStatus",
      method: "POST",
    }).then(res => res as unknown)
  },
  changeAccountPassword: (data: {
    id: number,
    newPassword: string,
  }) => {
    return http({
      data,
      url: "/AiSpeech/admin/resetPassword",
      method: "POST",
    }).then(res => res as unknown)
  },
}

// 商户管理
export const merchantUserModel = {
  // 按商户ID查询账号列表
  getAccountList: (data: { tenantId: number }) => {
    return http({
      url: "/AiSpeech/admin/getUserListByTenantId",
      method: "GET",
      data
    }).then(res => res as unknown)
  },
  // 从运营端添加商户账号
  addAccount: (data: MerchantAccountInfo) => {
    return http({
      data,
      url: "/AiSpeech/admin/addUser",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 从运营端编辑商户账号
  editAccount: (data: MerchantAccountInfo) => {
    return http({
      data,
      url: "/AiSpeech/admin/editUser",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 从运营端删除商户账号
  deleteAccount: (params: MerchantAccountInfo) => {
    return http({
      params,
      url: "/AiSpeech/admin/deleteUser",
      method: "DELETE",
    }).then(res => res as unknown)
  },
  // 从运营端重置商户账号的密码
  resetAccountPassword: (data: { userId: number }) => {
    return http({
      data,
      url: "/AiSpeech/admin/operatorResetTenantPasswordByDefault",
      method: "GET",
    }).then(res => res as unknown)
  },
}

export const operatorUserModel = {
  searchRoleList: () => {
    return http({
      url: "/AiSpeech/adminRole/findAdminRoles",
      method: "GET",
    }).then(res => res as unknown)
  },
}
