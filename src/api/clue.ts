import { http } from '@/axios'
import { ExamineStatusEnum, ClueFollowItem, ClueStaticInfo, CollectionFormItem, ClueItem, ClueTransferRecordItem,
  ClueSearchInfo, FormRecordItem, SeatStaticInfo, TodayClueStaticInfo, ClueAutoActionInfo, CallRecordFilterModal, RelatedTaskInfo,
  ClueAutoSettingInfo, ClueAutoImportSetting, } from '@/type/clue'
import { TaskCallRecordItem, TaskCallSearchModal,  } from '@/type/task'
import {  SeatMember } from '@/type/seat'
import { filterEmptyParams } from '@/utils/utils'

  // 线索管理、团队管理接口
export const clueManagerModel = {
  importCluesByExcel: (data: any, params: {groupId: string}) => {
    return http({
      data,
      params,
      url: "AiSpeech/clue/importClueByExcel",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 下载导入线索模板
  downloadTemplate: () => {
    return http({
      url: "/AiSpeech/clue/download",
      method: "GET",
      responseType: 'blob'
    }).then(res => res as unknown)
  },
  // 获取线索统计数据
  getClueStaticInfo: (data: {groupId: string}) => {
    return http({
      url: "/AiSpeech/clue/getClueStatusStatic",
      method: "GET",
      data,
    }).then(res => res as unknown as ClueStaticInfo)
  },
  // 获取坐席组线索统计数据
  getTeamClueStaticInfo: () => {
    return http({
      url: "/AiSpeech/callTeamClueManager/getClueStatusStatic",
      method: "GET",
    }).then(res => res as unknown as ClueStaticInfo)
  },
  getTransferRecordById: (data: number[]) => {
    return http({
      url: "/AiSpeech/clue/findClueTransferRecordsByIds",
      method: "POST",
      data,
    }).then(res => res as unknown as ClueTransferRecordItem[])
  },
  // 待下发列表
  getToBeSendClueList: (data: ClueSearchInfo) => {
    return http({
      url: "/AiSpeech/clue/getToBeSendClues",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as ClueItem[])
  },
  // 获取导入通话记录数量(支持任务多选版本)
  getBatchCallRecordsNum2: (data: CallRecordFilterModal) => {
    return http({
      url: "/AiSpeech/clue/pickBatchCallRecordsWithTime",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as {size: number, tempId: string})
  },
  // 获取导入通话记录数量
  getBatchCallRecordsNum: (data: TaskCallSearchModal) => {
    return http({
      url: "/AiSpeech/clue/pickBatchCallRecords",
      method: "POST",
      data,
    }).then(res => res as unknown as {size: number, tempId: string})
  },
  // 获取导入名单管理数量
  getBatchPhoneRecordsNum: (data: TaskCallSearchModal) => {
    return http({
      url: "/AiSpeech/clue/pickBatchPhoneRecords",
      method: "POST",
      data,
    }).then(res => res as unknown as {size: number, tempId: string})
  },
  // 线索导入通话记录
  addCallRecordsToClue: (params: {
    groupId: string, isIntoClueDB: boolean, tempId: string
  }) => {
    return http({
      url: "/AiSpeech/clue/batchCallRecordsIntoClues",
      method: "POST",
      params,
    }).then(res => res as unknown as {size: number, tempId: string})
  },
  addPhoneRecordsToClue: (params: {
    groupId: string, isIntoClueDB: boolean, tempId: string
  }) => {
    return http({
      url: "/AiSpeech/clue/batchPhoneRecordsIntoClues",
      method: "POST",
      params,
    }).then(res => res as unknown as {size: number, tempId: string})
  },
  // 通过文档导入线索
  importClueByExcel: (params: {file: FormData, groupId: string}) => {
    return http({
      url: "/AiSpeech/clue/importClueByExcel",
      method: "POST",
      params,
    }).then(res => res as unknown)
  },
  // 下发线索
  distributeClues: (data: {
    callTeamId: number,
    clueIds: number[]
  }) => {
    return http({
      url: "/AiSpeech/clue/sendCluesToCallTeam",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  // 加入任务
  joinToTaskClues: (data: {
    taskId: number,
    clueIds: number[]
  }) => {
    return http({
      url: "/AiSpeech/clue/joinToTask",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  // 分配线索
  distributeClues2CallSeat: (data: {
    clueIds: number[],
    callSeatIds: number[],
    autoRecoveryTime?: string
  }) => {
    return http({
      url: "/AiSpeech/callTeamClueManager/distributeCluesIntoCallSeat",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  // 分配
  redistributeClues2CallSeat: (data: {
    clueIds: number[],
    callSeatIds: number[],
    autoRecoveryTime?: string
  }) => {
    return http({
      url: "/AiSpeech/callTeamClueManager/reDistributeCluesIntoCallSeat",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  // 回收
  recoveryClues: (data: number[]) => {
    return http({
      url: "/AiSpeech/callTeamClueManager/recoverClues",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  // 批量归档线索
  batchArchiveClues: (data: number[]) => {
    return http({
      url: "/AiSpeech/clue/archivedClues",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  // 批量激活线索
  batchActiveClues: (data: 
    number[]
  ) => {
    return http({
      url: "/AiSpeech/clue/activeClues",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  // 待分配列表
  getToBeDistributeClueList: (data: ClueSearchInfo) => {
    return http({
      url: "/AiSpeech/clue/getToBeDistributeClues",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as ClueItem[])
  },
  // 已分配列表
  getBeDistributeClueList: (data: ClueSearchInfo) => {
    return http({
      url: "/AiSpeech/clue/getBeDistributedClues",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as ClueItem[])
  },
  // 已回收列表
  getBeRecoveredClueList: (data: ClueSearchInfo) => {
    return http({
      url: "/AiSpeech/clue/getBeRecoveredClues",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as ClueItem[])
  },
  // 已归档列表
  getBeArchivedClueList: (data: ClueSearchInfo) => {
    return http({
      url: "/AiSpeech/clue/getBeArchivedClues",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as ClueItem[])
  },
  // 设置线索审核状态
  setClueExamineStatus: (params: {
    clueId: number,
    examineStatus: ExamineStatusEnum
  }) => {
    return http({
      url: "/AiSpeech/clue/setClueExamineStatus",
      method: "POST",
      params,
    }).then(res => res as unknown as ClueItem[])
  },
   // 获取今日
   getClueTodayStaticInfo: () => {
    return http({
      url: "/AiSpeech/callTeamClueManager/findCallTeamTodayStatisticResponse",
      method: "GET",
    }).then(res => res as unknown as TodayClueStaticInfo)
  },
  // 获取坐席情况
  getCallTeamCondition: () => {
    return http({
      url: "/AiSpeech/callTeamClueManager/findCallTeamCondition",
      method: "GET",
    }).then(res => res as unknown as SeatStaticInfo)
  },
  // 调整任务的可签入坐席
  getCallTeamRelatedTasks: () => {
    return http({
      url: "/AiSpeech/callTeamClueManager/findCallTeamRelatedTasks",
      method: "GET",
    }).then(res => res as unknown as RelatedTaskInfo[])
  },
  // 调整任务的可签入坐席
  changeActiveCallSeats: (data: {activeCallSeats: number[], taskId: number}) => {
    return http({
      url: "/AiSpeech/callTeamClueManager/changeActiveCallSeats",
      method: "POST",
      data,
    }).then(res => res as unknown as ClueStaticInfo)
  },
  // 获取全部坐席(坐席组内)
  getTeamSeatsList: () => {
    return http({
      url: "/AiSpeech/callTeamClueManager/findAllCallSeatsInCallTeam",
      method: "POST",
    }).then(res => res as unknown as SeatMember[])
  },
  // 获取全部坐席(商户内)
  getGroupSeatsList: () => {
    return http({
      url: "/AiSpeech/callTeamClueManager/findAllCallSeatsInAccount",
      method: "POST",
    }).then(res => res as unknown as SeatMember[])
  },
  // 更新线索的姓名和备注 TODO
  updateClueComment: (data: {
    id: number,
    name: string,
    comment: string
  }) => {
    return http({
      url: "/AiSpeech/clue/editClueNameComment",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  /** 线索自动脚本开始 */
  // 获取自动导入线索信息
  getAutoImportInfo: () => {
    return http({
      url: "/AiSpeech/autoClue/findAutoTransferClueRule",
      method: "POST",
    }).then(res => res as unknown as ClueAutoImportSetting)
  },
  switchAutoImport:  (params: ClueAutoActionInfo) => {
    return http({
      url: "/AiSpeech/autoClue/editAutoTransferClueRuleStatus",
      method: "POST",
      params
    }).then(res => res as unknown)
  },
  // 获取自动下发线索信息
  getAutoSendInfo: () => {
    return http({
      url: "/AiSpeech/autoClue/findDistributeCallTeamClueRuleById",
      method: "POST",
    }).then(res => res as unknown as ClueAutoActionInfo)
  },
  
  switchAutoSend: (params: ClueAutoActionInfo) => {
    return http({
      url: "/AiSpeech/autoClue/editDistributeCallTeamClueRuleStatus",
      method: "POST",
      params
    }).then(res => res as unknown)
  },
  switchAutoAllocate: (params: ClueAutoActionInfo) => {
    return http({
      url: "/AiSpeech/autoClue/editDistributeCallSeatClueRuleStatus",
      method: "POST",
      params
    }).then(res => res as unknown)
  },
  // 修改自动导入线索信息
  saveAutoImportInfo : (data: ClueAutoImportSetting) => {
    return http({
      url: !!data.id ? "/AiSpeech/autoClue/editAutoTransferClueRule" : "/AiSpeech/autoClue/addAutoTransferClueRule",
      method: "POST",
      data
    }).then(res => res as unknown)
  },
  // 修改自动下发线索信息
  saveAutoSendInfo : (data: ClueAutoSettingInfo) => {
    return http({
      url: !!data.id ? "/AiSpeech/autoClue/editDistributeCallTeamClueRule" : "/AiSpeech/autoClue/addDistributeCallTeamClueRule",
      method: "POST",
      data
    }).then(res => res as unknown)
  },
  // 获取自动分配线索信息
  getAutoAllocateInfo: (params: {
    groupId: string,
  }) => {
    return http({
      url: "/AiSpeech/autoClue/findDistributeCallSeatClueRule",
      method: "POST",
      params,
    }).then(res => res as unknown as ClueAutoActionInfo)
  },
  // 修改自动分配线索信息
  saveAutoAllocateInfo: (data: ClueAutoSettingInfo) => {
    return http({
      url: !!data.id ? "/AiSpeech/autoClue/editDistributeCallSeatClueRule" : "/AiSpeech/autoClue/addDistributeCallSeatClueRule",
      method: "POST",
      data
    }).then(res => res as unknown)
  },

  /** 获取自动归档线索信息 开始 */ 
  getAutoArchiveInfo: () => {
    return http({
      url: "/AiSpeech/autoClue/findAutoArchiveClueRule",
      method: "POST",
    }).then(res => res as unknown as ClueAutoSettingInfo)
  },
  saveAutoArchiveInfo: (data: ClueAutoSettingInfo) => {
    return http({
      url: !!data.id ? "/AiSpeech/autoClue/editAutoArchiveClueRule" : "/AiSpeech/autoClue/addAutoArchiveClueRule",
      method: "POST",
      data
    }).then(res => res as unknown)
  },
  switchAutoArchive: (params: ClueAutoActionInfo) => {
    return http({
      url: "/AiSpeech/autoClue/editAutoArchiveClueRuleStatus",
      method: "POST",
      params
    }).then(res => res as unknown)
  },
  /** 获取自动归档线索信息 结束 */ 

  findAutoLogs: (data: {
    startTime?: string,
    endTime?: string,
    executeType: number,
    ruleId?: number,
  }) => {
    return http({
      url: "/AiSpeech/autoClue/findRuleLogList",
      method: "POST",
      data
    }).then(res => res as unknown)
  },
}
// 线索表单设置接口
export const formSettingModel = {
  getFormSettingInfo: () => {
    return http({
      url: "/AiSpeech/formCollection/findFormCollectionList",
      method: "GET",
    }).then(res => res as unknown as CollectionFormItem[])
  },
  getEnableFormSettingInfo: () => {
    return http({
      url: "/AiSpeech/formCollection/findEnableFormCollectionList",
      method: "GET",
    }).then(res => res as unknown as CollectionFormItem[])
  },
  getFormSettingInfoByAccount: (data: {account: string}) => {
    return http({
      url: "/AiSpeech/formCollection/findFormCollectionListByAccount",
      method: "GET",
      data,
    }).then(res => res as unknown as CollectionFormItem[])
  },
  deleteOneFormSetting: (data: {id: number}) => {
    return http({
      url: "/AiSpeech/formCollection/removeFormCollection",
      method: "GET",
      data,
    }).then(res => res as unknown)
  },
  changeOneFormSettingStatus: (data: {id: number, collectionStatus: number}) => {
    return http({
      url: "/AiSpeech/formCollection/changeFormCollectionStatus",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  saveOneFormSetting: (data: CollectionFormItem) => {
    return http({
      url: "/AiSpeech/formCollection/saveFormCollection",
      method: "POST",
      data
    }).then(res => res as unknown)
  },
  saveFormSettingOrder: (data: number[]) => {
    return http({
      url: "/AiSpeech/formCollection/orderFormCollectionList",
      method: "POST",
      data
    }).then(res => res as unknown as CollectionFormItem[])
  },
  getFormInfoByClueId: (data: {clueId: number}) => {
    return http({
      url: "/AiSpeech/formCollection/findFormRecordListByClueId",
      method: "GET",
      data,
    }).then(res => res as unknown)
  },
}
// 线索表单记录
export const formRecordModel = {
  getFormRecordByClueId: (data: {clueId: number}) => {
    return http({
      url: "/AiSpeech/formRecord/findFormRecordListByClueId",
      method: "GET",
      data
    }).then(res => res as unknown as FormRecordItem)
  },
  saveFormRecord: (data: FormRecordItem) => {
    return http({
      url: "/AiSpeech/formRecord/saveFormRecord",
      method: "POST",
      data
    }).then(res => res as unknown)
  },
}
