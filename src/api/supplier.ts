import { http } from '@/axios'
import {
  SearchLineParams,
  SupplierBlackGroupParam,
  SupplierGoodNumberMultipleParam,
  SupplierGoodNumberOneParam,
  SupplierInfo,
  SupplierLineInfo
} from '@/type/supplier'

export const supplierModel = {
  // 查询供应商列表
  getSupplierList: async (data: { status: string }) => {
    return http({
      url: '/AiSpeech/callLineSupply/findCallLineSuppliersByStatus',
      method: 'GET',
      data
    }).then(res => res as unknown)
  },
  // 创建供应商
  addSupplier: async (data: SupplierInfo) => {
    return http({
      url: '/AiSpeech/callLineSupply/addOneCallLineSupplier',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  // 编辑供应商
  updateSupplier: async (data: SupplierInfo) => {
    return http({
      url: '/AiSpeech/callLineSupply/updateOneCallLineSupplier',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  // 查询供应线路列表
  getLineList: async (data: SearchLineParams) => {
    return http({
      url: '/AiSpeech/callLineSupply/findSupplyLinesByConditions',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  // 创建供应线路
  addLine: async (data: SupplierLineInfo) => {
    return http({
      url: '/AiSpeech/callLineSupply/addOneSupplyLine',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  // 编辑供应线路
  updateLine: async (data: SupplierLineInfo) => {
    return http({
      url: '/AiSpeech/callLineSupply/updateOneSupplyLine',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  switchLine: async (data: {
    supplyLineNumber: string,
    pendingStatus: boolean
  }) => {
    return http({
      url: '/AiSpeech/callLineSupply/changePendingStatus',
      method: 'GET',
      data
    }).then(res => res as unknown)
  },
  // 根据线路编号查询线路信息
  getLineByNumber: async (params: { supplyLineNumber: string }) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/findSupplyLineByNumber',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
  getAllSupplyLines: async () => {
    return http({
      url: '/AiSpeech/callLineSupply/findAllSupplyLines',
      method: 'GET',
    }).then(res => res as unknown)
  },

  // ---------------------------------------- 黑名单分组 开始 ----------------------------------------

  // 查询黑名单列表
  getBlacklist: async (params: SupplierBlackGroupParam) => {
    return http({
      url: '/AiSpeech/callLineSupply/getOneBlackListGroupBySupplierId',
      method: 'GET',
      params
    }).then(res => res as unknown)
  },
  // 保存黑名单分组
  saveBlacklist: async (data: SupplierBlackGroupParam) => {
    return http({
      url: '/AiSpeech/callLineSupply/saveOneBlackListGroup',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  // 删除黑名单分组
  deleteBlacklist: async (data: SupplierBlackGroupParam) => {
    return http({
      url: '/AiSpeech/callLineSupply/deleteOneBlackListGroup',
      method: 'DELETE',
      data
    }).then(res => res as unknown)
  },

  // ---------------------------------------- 黑名单分组 结束 ----------------------------------------

  // ---------------------------------------- 靓号限制 开始 ----------------------------------------

  // 查询靓号规则列表
  getGoodNumber: async (data: SupplierGoodNumberMultipleParam) => {
    return http({
      url: '/AiSpeech/callLineSupply/getLightPhoneGroupBySupplierId',
      method: 'GET',
      data
    }).then(res => res as unknown)
  },
  // 保存单个靓号规则
  saveOneGoodNumber: async (data: SupplierGoodNumberOneParam) => {
    return http({
      url: '/AiSpeech/callLineSupply/saveOneLightPhoneGroup',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 保存多个靓号规则
  saveMultipleGoodNumber: async (data: SupplierGoodNumberMultipleParam) => {
    return http({
      url: '/AiSpeech/callLineSupply/saveLightPhoneGroupBatch',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 删除靓号规则
  deleteGoodNumber: async (data: SupplierGoodNumberOneParam) => {
    return http({
      url: '/AiSpeech/callLineSupply/deleteOneLightPhoneGroup',
      method: 'DELETE',
      data,
    }).then(res => res as unknown)
  },

  // ---------------------------------------- 靓号限制 结束 ----------------------------------------
}
