import { VolcanoTemplateLogItem, VolcanoTaskLogItem, RecordStatusEnum, MarketingPlanItem, ApplySmsTemplateItem, RecordStatusForPlanEnum } from '@/type/volcano'
import { TemplateBaseItem } from '@/type/task'
import { http } from '@/axios';
import { filterEmptyParams } from '@/utils/utils'

// 火山运营
export const volcanoModel = {
  // 火山白泽模板列表
  findTemplateLogList: async (data: {
    status?: RecordStatusEnum,
    startTime?: string,
    endTime?: string
  }) => {
    return http({
      url: "/AiSpeech/volcanoOperatorUpload/findOperatorUpload",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as VolcanoTemplateLogItem[])
  },
  // 火山上传文件，自动创建模板
  uploadVolcanoTemplate: async (data: VolcanoTemplateLogItem[]) => {
    return http({
      url: "/AiSpeech/volcanoOperatorUpload/volcanoCreateTemplate",
      method: "POST",
      data,
    }).then(res => res as unknown as VolcanoTemplateLogItem[])
  },
  // 火山人群包生成任务日志数据
  findPackageLogList: async (data: {
    status?: RecordStatusEnum,
    startTime?: string,
    endTime?: string
  }) => {
    return http({
      url: "/AiSpeech/volcanoDataNotice/findVolcanoDataNotice",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as VolcanoTaskLogItem[])
  },
  // 火山商户的模板列表
  findVolcanoTemplateList: async () => {
    return http({
      url: "/AiSpeech/aiOutboundTaskTemplate/findVolcanoTaskTemplates",
      method: "GET",
    }).then(res => res as unknown as TemplateBaseItem[])
  },
  // 火山任务批量重新执行
  autoBatch: async (data: {
    ids?: number[],
  }) => {
    return http({
      url: "/AiSpeech/volcanoDataNotice/autoMatchReDownload",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  // 火山任务批量编辑任务中的短信模板
  editTaskSmsTemplate: async (data: {
    ids?: number[],
  }) => {
    return http({
      url: "/AiSpeech/volcanoDataNotice/editTaskSmsTemplate",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  // 火山任务批量修改人群包模板
  manualBatch: async (data: {
    ids?: number[],
    modelName?: string,
  }) => {
    return http({
      url: "/AiSpeech/volcanoDataNotice/manualMatchReDownload",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  // 火山任务修改人群包名称，但需要后续重新匹配
  editModelName: async (data: {
    ids?: number[],
    modelName?: string,
  }) => {
    return http({
      url: "/AiSpeech/volcanoDataNotice/editModelName",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  // 营销计划列表
  findMarketingPlanList: async (data: {
    status?: RecordStatusForPlanEnum
    startTime?: string,
    endTime?: string
  }) => {
    return http({
      url: "/AiSpeech/volcanoMarketingPlanController/findMarketingPlan",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as MarketingPlanItem[])
  },
  // 营销计划上传文件(新建)
  uploadMarketingPlan: async (data: MarketingPlanItem[]) => {
    return http({
      url: "/AiSpeech/volcanoMarketingPlanController/marketingPlanUpload",
      method: "POST",
      data,
    }).then(res => res as unknown as MarketingPlanItem[])
  },
  // 营销计划上传文件（重传
  reUploadMarketingPlan: async (data: MarketingPlanItem[]) => {
    return http({
      url: "/AiSpeech/volcanoMarketingPlanController/marketingPlanReUpload",
      method: "POST",
      data,
    }).then(res => res as unknown as MarketingPlanItem[])
  },
  // 火山短信模板
  findApplySmsTemplateList: async (data: {
    startTime?: string,
    endTime?: string,
    status?: RecordStatusEnum
  }) => {
    return http({
      url: "/AiSpeech/volcanoMarketingPlanController/findVolcanoApplySmsTemplate",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as ApplySmsTemplateItem[])
  },
}
