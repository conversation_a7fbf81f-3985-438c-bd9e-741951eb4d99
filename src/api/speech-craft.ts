import { cancelRequest, http } from '@/axios'
import { AdvancedRulesItem, IntentionType, LabelItem, FinalIntentionRuleItem } from '@/type/IntentionType'
import { CorpusConditionItem, } from '@/type/corpus'
import {
  AiCorePhrase,
  AiCorePhraseRecord,
  SemanticsTestParam,
  SemanticsLabelItem,
  SemanticsRelationItem,
  AiSemantics,
  SecondIndustrySemanticItem,
  SemanticsLabelSearchTypeEnum,
  SemanticsCheckParam
} from '@/type/core-semantic'
import {
  AudioParams,
  EventItem,
  InfoQueryItem,
  ScriptBaseInfo,
  ScriptBranch,
  ScriptNormalBranchItem,
  ScriptCheckApproveParam,
  ScriptCheckRecord,
  ScriptCorpusItem,
  CorpusPriorItem,
  ScriptInfo,
  SpeechCraftInfoItem,
  SpeechCraftStatusEnum,
  ScriptStatisticParams,
  ScriptStatisticItem,
  CorpusTypeEnum,
  CorpusStatisticDetails,
  CorpusReturnConfig,
  CorpusInterruptConfig,
  ScriptTextTrainParam,
  KnowledgeGroupItem,
  OpenScopeTypeEnum,
  AudioItem,
  AudioItemUploadParams,
  AudioItemEditParams,
  QaAndPriorCorpusItem,
  ScriptSemanticStatisticItem,
} from '@/type/speech-craft'
import { filterEmptyParams } from '@/utils/utils'

// 话术
export const scriptTableModel = {
  findScriptByGroupId: (data: {groupId: string}) => {
    return http({
      data,
      url: "/AiSpeech/script/findAllHistoryScriptByGroupId",
      method: "GET",
    }).then(res => res as unknown)
  },
  // 通过行业和话术状态查询话术list
  findScriptListByStatusAndIndustry: (data: {
    status: SpeechCraftStatusEnum,
    secondaryIndustryId: number,
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptEditor/findAllScriptInPermissionByIndustryId",
      method: "GET",
    }).then(res => res as unknown as SpeechCraftInfoItem[])
  },
  createOneScript: (data: SpeechCraftInfoItem) => {
    return http({
      data,
      url: "/AiSpeech/scriptEditor/createOneScript",
      method: "POST",
    }).then(res => res as unknown as SpeechCraftInfoItem)
  },
  updateOneScript: (data: SpeechCraftInfoItem) => {
    return http({
      data,
      url: "/AiSpeech/script/updateOneScript",
      method: "POST",
    }).then(res => res as unknown as SpeechCraftInfoItem)
  },
  findOneScriptById: (data: {
    id: number
  }) => {
    return http({
      data,
      url: "/AiSpeech/script/findOneScriptById",
      method: "GET",
    }).then(res => res as unknown as SpeechCraftInfoItem)
  },
  // 不再在话术列表使用，用于其他页面话术下拉选项
  getScriptTables: (data: {
    name?: string,
    status: SpeechCraftStatusEnum
  }) => {
    const str = data.name ? 'findScriptListByNameAndStatus' : 'findAllScriptByStatus'
    return http({
      url: `/AiSpeech/script/${str}`,
      method: "GET",
      data
    }).then(res => res as unknown as SpeechCraftInfoItem[])
  },
  // 用于话术可见账号配置的账号列表
  findAllScriptWatchAccounts: () => {
    return http({
      url: `/AiSpeech/scriptEditor/findAllScriptEditorAccounts`,
      method: "GET",
    }).then(res => res as unknown as string[])
  },
  // 配置话术的可见账号
  editScriptWatchAccounts: (data: {
    scriptId?: number,
    watchAccounts: string[]
  }) => {
    return http({
      url: `/AiSpeech/scriptEditor/editScriptWatchPermissionForEditor`,
      method: "POST",
      data
    }).then(res => res as unknown as string[])
  },
  // 获取用户所有可见话术
  findAllScriptInPermission: (data: {
    status: SpeechCraftStatusEnum,
    days?: number,
  }) => {
    return http({
      url: `/AiSpeech/scriptEditor/findAllScriptInPermission`,
      method: "GET",
      data
    }).then(res => res as unknown as SpeechCraftInfoItem[])
  },
  // 编辑话术的备注信息
  editScriptRemark: (data: {
    scriptId?: number,
    remark?: string
  }) => {
    return http({
      url: `/AiSpeech/scriptEditor/remarkOneScript`,
      method: "GET",
      data
    }).then(res => res as unknown)
  },
  // findAllScript: () => {
  //   return http({
  //     url: `/AiSpeech/script/findAllScript`,
  //     method: "GET",
  //   }).then(res => res as unknown)
  // },
  deleteScriptTables: (params: {
    scriptId: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptEditor/deleteOneScriptLogic",
      method: "POST",
    }).then(res => res as unknown)
  },
  activeOneScript: (params: {
    scriptId: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptEditor/activeOneScript",
      method: "POST",
    }).then(res => res as unknown)
  },
  stopOneScript: (params: {
    scriptId: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptEditor/stopOneScript",
      method: "POST",
    }).then(res => res as unknown)
  },
  updateOneScriptVersion: (params: {
    scriptId: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptEditor/updateOneScriptVersion",
      method: "POST",
    }).then(res => res as unknown)
  },
  copyOneScript: (params: {
    scriptId: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptEditor/copyOneScript",
      method: "POST",
    }).then(res => res as unknown)
  },
  publishScriptTable: (data: {
    scriptId: number,
    scriptName: string
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptCheck/commit",
      method: "POST",
    }).then(res => res as unknown)
  },
  lockScript: (params: { scriptId: number }) => {
    return http({
      params,
      url: "/AiSpeech/scriptEditor/lockOneScript",
      method: "POST",
    }).then(res => res as unknown)
  },
  unLockScript: (params: { scriptId: number }) => {
    return http({
      params,
      url: "/AiSpeech/scriptEditor/unLockOneScript",
      method: "POST",
    }).then(res => res as unknown)
  },
  findTemplateByScript: (data: { scriptStringId: string }) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTaskTemplate/findTemplateByScript",
      method: "GET",
    }).then(res => res as unknown as string)
  },
}

// 话术审核
export const scriptCheckModel = {
  // 查询话术审核记录
  getScriptCheckList: (data: ScriptCheckRecord) => {
    return http({
      data: filterEmptyParams(data),
      url: '/AiSpeech/scriptCheck/getList',
      method: 'POST',
    }).then(res => res as unknown)
  },
  // 获取待审批数据
  getScriptCheckForProcess: () => {
    return http({
      url: '/AiSpeech/scriptCheck/getRecordForProcess',
      method: 'GET',
    }).then(res => res as unknown)
  },
  // 审批
  approveScript: (data: ScriptCheckApproveParam) => {
    return http({
      data,
      url: '/AiSpeech/scriptCheck/check',
      method: 'POST',
    }).then(res => res as unknown)
  },
  // 获取音频列表
  getAudioList: (params: {
    onlyUpdate: number,
    id: number
  }) => {
    return http({
      params,
      url: '/AiSpeech/scriptCheck/audioListByUpdate',
      method: 'GET',
    }).then(res => res as unknown)
  },
  // 获取操作日志
  getOperationLogList: (params: {
    id: number | string
  }) => {
    return http({
      params,
      url: '/AiSpeech/scriptOperationLog/findAll',
      method: 'GET',
    }).then(res => res as unknown)
  }
}
// 流程、画布
export const scriptCanvasModel = {
  // 主动流程画布
  deleteMasterCanvas: (params: {
    canvasId: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptMasterCanvas/deleteOneScriptCanvasById",
      method: "POST",
    }).then(res => res as unknown)
  },
  addMasterCanvasName: (data: ScriptBaseInfo) => {
    return http({
      url: "/AiSpeech/scriptMasterCanvas/addOneScriptCanvas",
      method: "POST",
      data,
    }).then(res => res as unknown as ScriptBaseInfo)
  },
  updateMasterCanvas: (data: {
    canvasId?: number;
    groupOpenScope?: number[];
    isOpenContext: boolean;
    name: string;
    openScopeType?: OpenScopeTypeEnum;
  }) => {
    return http({
      url: "/AiSpeech/scriptMasterCanvas/updateOneScriptCanvasInfoById",
      method: "POST",
      data,
    }).then(res => res as unknown as ScriptBaseInfo)
  },
  saveOneMasterCanvas: (data: ScriptInfo) => {
    return http({
      data,
      url: "/AiSpeech/scriptMasterCanvas/saveOneScriptCanvas",
      method: "POST",
    }).then(res => res as unknown)
  },
  saveMasterCanvasOrder: (data: {
    canvasIds: number[],
    scriptId: number,
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptMasterCanvas/saveCanvasList",
      method: "POST",
    }).then(res => res as unknown)
  },
  findAllMasterCanvas: (data: {
    scriptId: number
  }) => {
    return http({
      url: "/AiSpeech/scriptMasterCanvas/findAllScriptCanvasByScriptId",
      method: "GET",
      data,
    }).then(res => res as unknown as ScriptInfo[])
  },
  findOneMasterCanvas: (data: {
    canvasId: number
  }) => {
    return http({
      url: "/AiSpeech/scriptMasterCanvas/findOneScriptCanvasById",
      method: "GET",
      data,
    }).then(res => res as unknown)
  },
  // 主动流程开放范围
  findCanvasOpenScopeByCanvasId: (data: {
    canvasId: number
  }) => {
    return http({
      url: "/AiSpeech/scriptMasterCanvas/findScriptCanvasOpenScopeKnowledgeGroupById",
      method: "GET",
      data,
    }).then(res => res as unknown as KnowledgeGroupItem[])
  },

  // 深层沟通画布
  deleteDeepCanvas: (params: {
    canvasId: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptKnowledgeCanvas/deleteOneScriptCanvasById",
      method: "POST",
    }).then(res => res as unknown)
  },
  addDeepCanvasName: (data: ScriptBaseInfo) => {
    return http({
      url: "/AiSpeech/scriptKnowledgeCanvas/addOneScriptCanvas",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  updateDeepCanvas: (data: {
    canvasId?: number;
    groupOpenScope?: number[];
    isOpenContext: boolean;
    name: string;
    openScopeType?: OpenScopeTypeEnum;
  }) => {
    return http({
      url: "/AiSpeech/scriptKnowledgeCanvas/updateOneScriptCanvasInfoById",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  saveOneDeepCanvas: (data: ScriptInfo) => {
    return http({
      data,
      url: "/AiSpeech/scriptKnowledgeCanvas/saveOneScriptCanvas",
      method: "POST",
    }).then(res => res as unknown)
  },
  findAllDeepCanvas: (data: {
    groupId: number,
  }) => {
    return http({
      url: "/AiSpeech/scriptKnowledgeCanvas/findAllScriptCanvasByGroupId",
      method: "GET",
      data,
    }).then(res => res as unknown)
  },
  findOneDeepCanvas: (data: {
    canvasId: number
  }) => {
    return http({
      url: "/AiSpeech/scriptKnowledgeCanvas/findOneScriptCanvasById",
      method: "GET",
      data,
    }).then(res => res as unknown)
  },
}
// 语料-分支
export const scriptCorpusModel = {
  // 复制画布语料
  copyCanvasCorpus: async (params: {
    corpusId: number,
    scriptId: number,
    isHead: boolean,
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptCorpus/copyOneScriptCorpusByCorpusId",
      method: "POST",
    }).then(res => res as unknown as ScriptCorpusItem)
  },
  // 更新语料文字内容
  saveCorpusContent: async (data: AudioItemEditParams) => {
    return http({
      data,
      url: "/AiSpeech/scriptCorpus/updateOneCorpusContent",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 更新语料的分支知识库分组的优先级
  saveCorpusPriorInfo: async (data: {
    id?: number,
    corpusId: number,
    scriptId: number,
    priorList: CorpusPriorItem[]
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptCorpus/addOrUpdatePriorGroup",
      method: "POST",
    }).then(res => res as unknown as Object)
  },
  // 删除语料优先级分组
  deletePriorGroup: (params: {
    id: number,
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptCorpus/deletePriorGroupById",
      method: "DELETE",
    }).then(res => res as unknown)
  },
  // 更新语料文字内容对应的打断和返回设置
  // 非挂机语料保存打断设置
  saveInterruptConfig: (data: CorpusInterruptConfig) => {
    return http({
      data,
      url: "/AiSpeech/scriptCorpusConfig/saveInterruptConfig",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 挂机语料保存打断设置
  saveInterruptConfigForEnd: (data: CorpusInterruptConfig) => {
    return http({
      data,
      url: "/AiSpeech/scriptCorpusConfig/saveInterruptConfigForEnd",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 画布普通语料保存返回设置
  saveReturnConfig: (data: CorpusReturnConfig) => {
    return http({
      data,
      url: "/AiSpeech/scriptCorpusConfig/saveReturnConfig",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 画布语料逻辑删除和撤回
  deleteOneScriptCorpusById: (params: {
    corpusId: number,
    scriptId: number,
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptCorpus/deleteOneScriptCorpusById",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 主动语料-普通
  saveMasterOrdinaryCorpus: (data: ScriptCorpusItem) => {
    return http({
      data,
      url: "/AiSpeech/scriptCorpus/saveMasterOrdinaryCorpus",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 主动语料-连接
  saveMasterConnectCorpus: (data: ScriptCorpusItem) => {
    return http({
      data,
      url: "/AiSpeech/scriptCorpus/saveMasterConnectCorpus",
      method: "POST",
    }).then(res => res as unknown)
  },
  findMasterCorpus: (data: {
    corpusId: number
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptCorpus/findOneMasterScriptCorpusById",
      method: "GET",
    }).then(res => res as unknown as ScriptCorpusItem)
  },
  deleteMasterCorpus: (params: {
    corpusId: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptCorpus/deleteOneMasterScriptCorpusById",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 知识库分组
  saveKnowledgeGroup: (data: KnowledgeGroupItem) => {
    return http({
      data,
      url: data.id ? "/AiSpeech/knowledgeGroups/update" : "/AiSpeech/knowledgeGroups/create",
      method: data.id ? "PUT" : "POST",
    }).then(res => res as unknown as KnowledgeGroupItem)
  },
  findKnowledgeGroupList: (data: {scriptId: number}) => {
    return http({
      url: `/AiSpeech/knowledgeGroups/findByScriptId/${data.scriptId}`,
      method: "GET",
    }).then(res => res as unknown as KnowledgeGroupItem[])
  },
  deleteKnowledgeGroup: (data: {id: number}) => {
    return http({
      url: `/AiSpeech/knowledgeGroups/delete/${data.id}`,
      method: "DELETE",
    }).then(res => res as unknown)
  },
  saveKnowledgeGroupOrders: (data: number[]) => {
    return http({
      data,
      url: "/AiSpeech/knowledgeGroups/savaOrder",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 深层沟通语料
  // 主动语料
  saveKnowledgeOrdinaryCorpus: (data: ScriptCorpusItem) => {
    return http({
      data,
      url: "/AiSpeech/scriptKnowledgeBase/saveKnowledgeOrdinaryCorpus",
      method: "POST",
    }).then(res => res as unknown)
  },
  saveKnowledgeConnectCorpus: (data: ScriptCorpusItem) => {
    return http({
      data,
      url: "/AiSpeech/scriptKnowledgeBase/saveKnowledgeConnectCorpus",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 获取当前知识库分组的基本问答+深层沟通
  findAllKnowledgeCorpusByGroupId: (params: {
    groupId: number,
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptKnowledgeBase/findAllKnowledgeBaseByGroupId",
      method: "GET",
    }).then(res => res as unknown as ScriptCorpusItem[])
  },
  // 获取当前话术的基本问答+深层沟通+最高优先
  findKnowledgeAndPriorByScriptId: (params: {
    scriptId: number,
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptKnowledgeBase/findKnowledgeBaseAndPriorQAByScriptId",
      method: "GET",
    }).then(res => res as unknown as QaAndPriorCorpusItem[])
  },
  // 获取当前知识库分组的基本问答
  findBaseQAListByGroupId: (data: {
    groupId: number,
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptKnowledgeBase/findAllKnowledgeBaseQAByGroupId",
      method: "GET",
    }).then(res => res as unknown as ScriptCorpusItem[])
  },
  findAllDeepCorpusByScriptId: (params: {
    scriptId: number,
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptKnowledgeBase/findKnowledgeOrdinaryByScriptId",
      method: "GET",
    }).then(res => res as unknown as QaAndPriorCorpusItem[])
  },
  saveKnowledgeCorpus: (data: {
    corpusIdList: number[],
    scriptId: number,
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptKnowledgeBase/saveAllKnowledgeBase",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 基本问答语料
  saveKnowledgeBaseQA: (data: ScriptCorpusItem) => {
    return http({
      data,
      url: `/AiSpeech/scriptKnowledgeBase/saveKnowledgeBaseQA `,
      method: "POST",
    }).then(res => res as unknown)
  },
  // 批量保存知识库
  saveKnowledgeBaseQAList: (data: ScriptCorpusItem[]) => {
    return http({
      data,
      url: `/AiSpeech/scriptKnowledgeBase/saveKnowledgeBaseQAList`,
      method: "POST",
    }).then(res => res as unknown)
  },
  // 批量复制基本问答至其他分组
  copyKnowledgeBaseQAList: (data: {
    pairs: Record<number, string>,
    gid: number,
  }) => {
    return http({
      url: `/AiSpeech/scriptKnowledgeBase/batchCopyKnowledgeQA`,
      data,
      method: "POST",
    }).then(res => res as unknown)
  },
  // 批量复制基本问答至其他分组
  moveKnowledgeBaseQAList: (data: {
    ids: number[],
    gid: number,
  }) => {
    return http({
      url: `/AiSpeech/scriptKnowledgeBase/batchMoveKnowledgeQA`,
      data,
      method: "POST",
    }).then(res => res as unknown)
  },
  // 从生效中的话术导入话术中的基本问答
  loadBaseQAFromOtherScript: (data: {
    baseQACorpusPairs: Record<number, number[]>, // 分组ID：语料ID的list
    targetGid: number,
    scriptId: number,
  }) => {
    return http({
      url: `/AiSpeech/scriptKnowledgeBase/batchImportBaseQA`,
      data,
      method: "POST",
    }).then(res => res as unknown as any[])
  },
  deleteBaseQACorpus: (params: {
    corpusId: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptKnowledgeBase/deleteOneKnowledgeBaseCorpusById",
      method: "POST",
    }).then(res => res as unknown)
  },
  batchDeleteBaseQACorpus: (data: {
    knowledgeQAIds: number[],
    scriptId: number
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptKnowledgeBase/deleteKnowledgeQAScriptCorpusByIds",
      method: "DELETE",
    }).then(res => res as unknown)
  },
  deleteAllBaseQACorpus: (params: {
    scriptId: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptKnowledgeBase/deleteAllKnowledgeQAByScriptId",
      method: "DELETE",
    }).then(res => res as unknown)
  },
  findBaseQAListByScriptId: (data: {
    scriptId: number,
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptKnowledgeBase/findAllKnowledgeBaseQAByScriptId",
      method: "GET",
    }).then(res => res as unknown as ScriptCorpusItem[])
  },
  findBaseQAByName: (data: {
    name: string,
    groupId: number,
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptKnowledgeBase/findKnowledgeBaseQAByGroupIdAndName",
      method: "GET",
    }).then(res => res as unknown as ScriptCorpusItem[])
  },
  // 打断垫句\续播垫句\承接语料
  findPreCorpusList: (data: {
    scriptId: number,
    type: CorpusTypeEnum
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptPreCorpus/findPreCorpusListByCorpusType",
      method: "GET",
    }).then(res => res as unknown as ScriptCorpusItem[])
  },
  // 功能语料
  // 沉默语料
  saveFuncSilenceCorpus: (data: ScriptCorpusItem) => {
    return http({
      data,
      url: "/AiSpeech/scriptFunc/saveFuncSilenceCorpus",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 重复语料
  saveFuncRepeatCorpus: (data: ScriptCorpusItem) => {
    return http({
      data,
      url: "/AiSpeech/scriptFunc/saveFuncRepeatCorpus",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 最高优先
  saveFuncPriorQACorpus: (data: ScriptCorpusItem) => {
    return http({
      data,
      url: "/AiSpeech/scriptFunc/saveFuncPriorQACorpus",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 打断垫句
  savePreInterrupt: (data: ScriptCorpusItem) => {
    return http({
      data,
      url: "/AiSpeech/scriptPreCorpus/savePreInterruptCorpus",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 续播垫句
  savePreContinue: (data: ScriptCorpusItem) => {
    return http({
      data,
      url: "/AiSpeech/scriptPreCorpus/savePreContinueCorpus",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 承接语料
  savePreUndertake: (data: ScriptCorpusItem) => {
    return http({
      data,
      url: "/AiSpeech/scriptPreCorpus/savePreUndertakeCorpus",
      method: "POST",
    }).then(res => res as unknown)
  },
  batchSaveFuncPriorQACorpus: (data: ScriptCorpusItem[]) => {
    return http({
      data,
      url: "/AiSpeech/scriptFunc/saveFuncPriorQACorpusList",
      method: "POST",
    }).then(res => res as unknown)
  },
  deleteFuncCorpus: (params: {
    corpusId: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptFunc/deleteOneFuncScriptCorpusById",
      method: "POST",
    }).then(res => res as unknown)
  },
  findSilenceList: (data: {
    scriptId: number
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptFunc/findAllFuncSilenceByScriptId",
      method: "GET",
    }).then(res => res as unknown as ScriptCorpusItem[])
  },
  findRepeatList: (data: {
    scriptId: number
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptFunc/findAllFuncRepeatByScriptId",
      method: "GET",
    }).then(res => res as unknown as ScriptCorpusItem[])
  },
  findPriorList: (data: {
    scriptId: number,
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptFunc/findAllFuncPriorQAByScriptId",
      method: "GET",
    }).then(res => res as unknown as ScriptCorpusItem[])
  },
  findPriorByName: (data: {
    scriptId: number,
    name: string
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptFunc/findFuncPriorQAByName",
      method: "GET",
    }).then(res => (res ? [res] : []) as unknown as ScriptCorpusItem[])
  },
  findRepeatRequirement: (data: {
    scriptId: number
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptFunc/findRepeatTriggerConditionByScriptId",
      method: "GET",
    }).then(res => res as unknown)
  },
  saveRepeatRequirement: (data: ScriptBranch) => {
    return http({
      data,
      url: "/AiSpeech/scriptFunc/saveRepeatTriggerCondition",
      method: "POST",
    }).then(res => res as unknown)
  },
  findSilenceRequirement: (data: {
    scriptId: number
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptFunc/findMaxSilenceCountByScriptId",
      method: "GET",
    }).then(res => res as unknown as {
      maxSilenceCount: number,
      aiLabel: {id: number, labelName: string}
    })
  },
  saveSilenceRequirement: (params: {
    maxSilenceCount: number,
    scriptId: number,
    intentionTagId?: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/scriptFunc/saveMaxSilenceCountByScriptId",
      method: "POST",
    }).then(res => res as unknown)
  },
  saveRepeatByOrder: (data: {
    corpusIdList: number[],
    scriptId: number,
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptFunc/saveAllFuncRepeatSorted",
      method: "POST",
    }).then(res => res as unknown)
  },
  saveSilenceByOrder: (data: {
    corpusIdList: number[],
    scriptId: number,
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptFunc/saveAllFuncSilenceSorted",
      method: "POST",
    }).then(res => res as unknown)
  },
  savePriorByOrder: (data: {
    corpusIdList: number[],
    scriptId: number,
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptFunc/saveAllFuncQASorted",
      method: "POST",
    }).then(res => res as unknown)
  },
  deleteScriptBranch: (params: { id: number, scriptId: number }) => {
    return http({
      params,
      url: `/AiSpeech/scriptCorpus/deleteOneScriptBranchById`,
      method: "POST",
    }).then(res => res as unknown)
  },
  findScriptBranch: (params: { id: number }) => {
    return http({
      params,
      url: `/AiSpeech/scriptCorpus/findOneScriptBranchById`,
      method: "GET",
    }).then(res => res as unknown)
  },
  // 语料音频播放后更新语料的音频验听状态
  updateCorpusAudioStatus: (params: { id: number }) => {
    return http({
      params,
      url: `/AiSpeech/scriptCorpus/updatePlayedAudio`,
      method: "POST",
    }).then(res => res as unknown)
  },
}

// 意向标签
export const scriptIntentionModel = {
  // 分类
  findIntentionList: (id: number) => {
    return http({
      url: `/AiSpeech/intentionType/script/${id}`,
      method: "GET",
    }).then(res => res as unknown as IntentionType[])
  },
  saveIntention: (data: IntentionType) => {
    return http({
      data,
      url: "/AiSpeech/intentionType/save",
      method: "POST"
    }).then(res => res as unknown)
  },
  createDefaultIntention: (params: {
    id: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/intentionType/create-default-by-script",
      method: "POST"
    }).then(res => res as unknown)
  },
  switchIntentionSequence: (data: {
    id1: number,
    id2: number | null,
    scriptId: number
  }) => {
    return http({
      data,
      url: "/AiSpeech/intentionType/changeSequence",
      method: "GET"
    }).then(res => res as unknown)
  },
  // 标签
  // findIntentionTypeList: () => {
  //   return http({
  //     url: `/AiSpeech/intentionType/findList`,
  //     method: "POST",
  //   }).then(res => res as unknown);
  // },
  findLabelList: (id: number) => {
    return http({
      url: `/AiSpeech/label/script/${id}`,
      method: "GET",
    }).then(res => res as unknown as LabelItem[])
  },
  deleteOneLabel: (params: {
    id: number
  }) => {
    return http({
      url: `/AiSpeech/label/delete`,
      method: "GET",
      params,
    }).then(res => res as unknown)
  },
  saveOneLabel: (data: LabelItem) => {
    return http({
      url: `/AiSpeech/label/save`,
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  switchLabelSequence: (data: {
    id1: number,
    id2: number | null,
    scriptId : number,
  }) => {
    return http({
      data,
      url: "/AiSpeech/label/changeSequence",
      method: "GET"
    }).then(res => res as unknown)
  },
  // 规则
  findRulesList: (data: {
    scriptId: number
  }) => {
    return http({
      data,
      url: `/AiSpeech/advanced-rules/listNew`,
      method: "POST",
    }).then(res => res as unknown)
  },
  copyRulesList: (data: {
    scriptId: number,
    ruleIdList: number[],
    sourceScriptId?: number,
  }) => {
    return http({
      data,
      url: `/AiSpeech/advanced-rules/copyAdvancedRules`,
      method: "POST",
    }).then(res => res as unknown as {
      ruleName: string,
      failReason: string
    }[])
  },
  findRulesSetting: (data: {
    id: number
  }) => {
    return http({
      url: `/AiSpeech/advanced-rules/global_settings_query`,
      method: "GET",
      data
    }).then(res => res as unknown)
  },
  saveRulesSetting: (data: {
    forbidTagInProcess: 0 | 1,
    orderFirst: 0 | 1,
    id: number
  }) => {
    return http({
      data,
      url: `/AiSpeech/advanced-rules/global_settings`,
      method: "GET",
    }).then(res => res as unknown)
  },
  saveOneRule: (data: AdvancedRulesItem) => {
    return http({
      data,
      url: `/AiSpeech/advanced-rules/saveNew`,
      method: "POST",
    }).then(res => res as unknown)
  },
  deleteOneRule: (data: {
    id: number
  }) => {
    return http({
      data,
      url: `/AiSpeech/advanced-rules/delete`,
      method: "GET",
    }).then(res => res as unknown)
  },
  saveRulesOrder: (data: {
    advanceRulesIds: number[],
    scriptId: number
  }) => {
    return http({
      data,
      url: `/AiSpeech/advanced-rules/saveAllAdvancesRuleSorted`,
      method: "POST",
    }).then(res => res as unknown)
  },
  // 最终意向
  findFinalIntentionList: (data: {
    scriptId: number
  }) => {
    return http({
      data,
      url: `/AiSpeech/final-intention-rules/list`,
      method: "POST",
    }).then(res => res as unknown as FinalIntentionRuleItem[])
  },
  saveFinalIntentionList: (data: FinalIntentionRuleItem) => {
    return http({
      data,
      url: `/AiSpeech/final-intention-rules/save`,
      method: "POST",
    }).then(res => res as unknown)
  },
  deleteFinalIntentionList: (data: {
    id: number
  }) => {
    return http({
      data,
      url: `/AiSpeech/final-intention-rules/delete`,
      method: "GET",
    }).then(res => res as unknown)
  },
}

// 音频语料
export const scriptAudioModel = {
  // 声学参数配置
  saveAudioParameters: (data: AudioParams) => {
    return http({
      url: "/AiSpeech/acoustic-parameters/save",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  findAudioParameters: (data: {
    id: number
  }) => {
    return http({
      data,
      url: "/AiSpeech/acoustic-parameters/findById",
      method: "GET",
    }).then(res => res as unknown)
  },
  // 音频管理
  findAudioList: (data: {
    content?: string,
    isPlayed?: boolean,
    canvasName?: string
    unitContentName?: string,
    typeList?: CorpusTypeEnum[],
    uploadStatus?: string
    scriptId: number
  }) => {
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/scriptCorpus/audioList",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 上传音频文件，批量上传，但是每次传一个
  uploadMultipleAudioByOnce: async (val: AudioItemUploadParams) => {
    return http({
      data: val.file,
      params: { contentId: val.contentId },
      url: "/AiSpeech/scriptCorpus/import",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 上传音频文件，单个上传
  uploadSingleAudio: async (val: AudioItemUploadParams) => {
    return http({
      data: val.file,
      params: { contentId: val.contentId },
      url: "/AiSpeech/scriptCorpus/importSingle",
      method: "POST",
    }).then(res => res as unknown)
  },
  deleteAudioList: (data: {
    id: number,
  }) => {
    return http({
      data,
      url: "/AiSpeech/scriptCorpus/deleteAudios",
      method: "GET",
    }).then(res => res as unknown)
  },
  // 批量下载音频文件
  patchDownloadAudio: async (data: number[] ) => {
    return http({
      data,
      url: "/AiSpeech/scriptCorpus/downloadAudioFiles",
      method: "POST",
      responseType: 'blob'
    }).then(res => res as unknown)
  },
}
// 信息查询设置
export const scriptInfoModel = {
  saveInfoQuery: (data: InfoQueryItem) => {
    return http({
      url: "/AiSpeech/infoQueryKey/saveInfoQueryKey",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  deleteInfoQueryKey: (params: {
    id: number
  }) => {
    return http({
      url: "/AiSpeech/infoQueryKey/deleteInfoQueryKeyById",
      method: "DELETE",
      params,
    }).then(res => res as unknown)
  },
  deleteInfoQueryValue: (params: {
    id: number, scriptLongId: number
  }) => {
    return http({
      url: "/AiSpeech/infoQueryKey/deleteInfoQueryValueById",
      method: "DELETE",
      params,
    }).then(res => res as unknown)
  },
  findInfoQueryList: (data: {
    id: number
  }) => {
    return http({
      url: "/AiSpeech/infoQueryKey/findInfoQueryKeysByScriptLongId",
      method: "GET",
      data,
    }).then(res => res as unknown)
  },
}

// 事件触发
export const scriptEventModel = {
  saveEvent: (data: EventItem) => {
    return http({
      url: "/AiSpeech/eventTriggerKey/saveEventTriggerKey",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  deleteEvent: (params: {
    triggerKeyId: number
  }) => {
    return http({
      url: "/AiSpeech/eventTriggerKey/deleteEventTriggerKey",
      method: "POST",
      params,
    }).then(res => res as unknown)
  },
  deleteEventValueId: (params: {
    triggerKeyId: number, triggerValueId : number
  }) => {
    return http({
      url: "/AiSpeech/eventTriggerKey/deleteEventTriggerValue",
      method: "POST",
      params,
    }).then(res => res as unknown)
  },
  findEventList: (params: {
    scriptId: number
  }) => {
    return http({
      url: "/AiSpeech/eventTriggerKey/findAllEventTriggersByScriptId",
      method: "POST",
      params,
    }).then(res => res as unknown)
  },
  findEventValueByIds: (data: {
    scriptId: number,
    triggerValueIds: number[]
  }) => {
    return http({
      url: "/AiSpeech/eventTriggerKey/findEventValuesByScriptIdAndValueIds",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  getEventTypeList: () => {
    return http({
      url: "/AiSpeech/eventTriggerKey/findAllEventTriggerType",
      method: "POST",
    }).then(res => res as unknown)
  },
  getEventValuesList: (params: {
    eventTriggerType: string
  }) => {
    return http({
      url: "/AiSpeech/eventTriggerKey/findAllEventTriggerValueByType",
      method: "POST",
      params,
    }).then(res => res as unknown)
  },
}
// 语义、核心短语
export const scriptCoreSemanticModel = {
  // 查询所有行业下的所有语义
  findSemanticMap: () => {
    return http({
      url: `/AiSpeech/semantic/findSemanticMap`,
      method: "POST",
    }).then(res => res as unknown as Record<string, AiSemantics>)
  },
  // 语义
  findSemanticAllList: (params: {
    secondIndustryId: number
  }) => {
    return http({
      url: `/AiSpeech/semantic/findList`,
      method: "POST",
      params,
    }).then(res => res as unknown)
  },
  findSemanticList: (data: {
    queryStr: string,
    type: 'semantic' | 'phrase',
    secondIndustryId: number,
  }) => {
    return http({
      url: `/AiSpeech/semantic/queryBySemanticOrPhrase`,
      method: "GET",
      data,
    }).then(res => res as unknown)
  },
  // 查询当前行业下的带有语义标签的语义列表
  findLabelSemanticList: (data: { secondIndustryId: number }) => {
    return http({
      url: `/AiSpeech/semantic/findSemanticListByIndustry`,
      method: "GET",
      data,
    }).then(res => res as unknown)
  },
  // 查询当前行业下的所有的语义标签列表
  findAllSemanticLabelList: (data: { secondIndustryId: number }) => {
    return http({
      url: `/AiSpeech/semantic/findSemanticLabelListByIndustry`,
      method: "GET",
      data,
    }).then(res => res as unknown)
  },
  deleteSemantic: (params: {
    id: number
  }) => {
    return http({
      url: `/AiSpeech/semantic/logic-delete`,
      method: "GET",
      params,
    }).then(res => res as unknown)
  },
  saveSemantic: (data: AiSemantics) => {
    return http({
      url: `/AiSpeech/semantic/save`,
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  saveSemanticAndPhrases: (data: AiSemantics) => {
    return http({
      url: `/AiSpeech/semantic/saveSemanticAndPhrases`,
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  // 核心短语
  findCorePhraseAllList: (data: {
    id: number
  }) => {
    return http({
      url: `/AiSpeech/core-phrase/findList`,
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  findCorePhraseList: (semanticId: number) => {
    return http({
      url: `/AiSpeech/core-phrase/findList/${semanticId}`,
      method: "GET",
    }).then(res => res as unknown)
  },
  // 变更核心短语内容
  deleteAndSaveCorePhraseList: (data: AiCorePhraseRecord) => {
    return http({
      url: '/AiSpeech/core-phrase/deleteAndSaveList',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // saveCorePhrase: (data: AiCorePhrase) => {
  //   return http({
  //     url: `/AiSpeech/core-phrase/save`,
  //     method: "POST",
  //     data,
  //   }).then(res => res as unknown);
  // },
  saveCorePhraseList: (data: AiCorePhrase[]) => {
    return http({
      url: `/AiSpeech/core-phrase/saveList`,
      method: "POST",
      data,
    }).then(res => res as unknown)
  },

  // 查询所有二级行业及语义状态
  findAllSecondIndustries: () => {
    return http({
      url: '/AiSpeech/industryField/findAllSecondIndustries',
      method: "GET",
    }).then(res => res as unknown as SecondIndustrySemanticItem[])
  },
  //
  publishSemantics: (data: { secondIndustryId: number }) => {
    return http({
      url: '/AiSpeech/semantic/publishSemantics',
      method: "GET",
      data
    }).then(res => res as unknown)
  },
  // 查询语义标签列表
  findSemanticLabelList: async (data: {
    queryStr: string,
    type: SemanticsLabelSearchTypeEnum,
    secondIndustryId: number,
  }) => {
    return http({
      url: `/AiSpeech/semantic/findLabelList`,
      method: "GET",
      data,
    }).then(res => res as unknown)
  },
  // 保存语义标签（新建和编辑）
  saveSemanticLabel: async (data: SemanticsLabelItem) => {
    return http({
      url: `/AiSpeech/semantic/saveLabel`,
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  // 删除语义标签
  deleteSemanticLabel: async (params: SemanticsLabelItem) => {
    return http({
      url: `/AiSpeech/semantic/deleteLabel`,
      method: "POST",
      params,
    }).then(res => res as unknown)
  },
  findSemanticRelationList: async (params: SemanticsLabelItem) => {
    return http({
      url: `/AiSpeech/semantic/findSemanticListByLabelId`,
      method: "GET",
      params,
    }).then(res => res as unknown)
  },
  saveSemanticRelation: async (data: {
    semanticId: number[],
    semanticLabelId: number,
  }) => {
    return http({
      url: '/AiSpeech/semantic/saveRelation',
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  deleteSemanticRelation: async (data: SemanticsRelationItem[]) => {
    return http({
      url: '/AiSpeech/semantic/saveRelation',
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  // 测试语义
  testSemantic: async (data: SemanticsTestParam) => {
    return http({
      url: '/AiSpeech/semantic/testSemantic',
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  // 检查语义
  checkSemantic: async (params: SemanticsCheckParam) => {
    return http({
      url: '/AiSpeech/semantic/checkSemantic',
      method: "GET",
      params,
    }).then(res => res as unknown)
  },
}

// 话术训练，通用接口
export const scriptTrainCommonModel = {
  // 获取训练历史
  getTrainHistory: async (scriptStringId: string = '') => {
    return http({
      url: `/AiSpeech/trainAccount/findTrainHistory?scriptStringId=${scriptStringId}`,
      method: "GET",
    }).then(res => res as unknown)
  },
  // 获取通话记录
  getCallRecord: async (recordId: string) => {
    const url = `/AiSpeech/callRecord/callRecordByRecordId?recordId=${recordId}`
    cancelRequest(url)
    return http({
      url: url,
      method: "GET",
    }).then(res => res as unknown)
  },
  // 发布话术
  publishScript: async (data: any) => {
    return http({
      url: `/AiSpeech/scriptTrain/pushTrainScript`,
      method: "GET",
      data
    }).then(res => res as unknown)
  },
  // 检查话术是否发布成功
  checkScript: async (data: any) => {
    return http({
      url: `/AiSpeech/scriptTrain/getPushResult`,
      method: "GET",
      data
    }).then(res => res as unknown)
  },
}
// 话术训练，语音训练
export const scriptTrainAudioModel = {
  // 获取空闲FS账号
  getIdleFsAccount: async () => {
    return http({
      url: `/AiSpeech/trainAccount/getIdleAccount`,
      method: "GET"
    }).then(res => res as unknown)
  },
  // 开始训练
  startAudioTrain: async (params: any) => {
    return http({
      url: `/AiSpeech/trainAccount/startTrain`,
      method: "GET",
      params
    }).then(res => res as unknown)
  },
  // 提交事件
  submitAudioTrainEvent: async (data: any) => {
    return http({
      url: `/AiSpeech/trainAccount/fakeOperation`,
      method: "GET",
      data
    }).then(res => res as unknown)
  },
  // 结束训练
  stopAudioTrain: async (data: any) => {
    return http({
      url: `/AiSpeech/trainAccount/endTrain`,
      method: "GET",
      data
    }).then(res => res as unknown)
  },
  // 获取对话详情
  getDialogData: async (recordId: string) => {
    const url = `/AiSpeech/trainAccount/findRealTimeSentences?recordId=${recordId}`
    cancelRequest(url)
    return http({
      url: url,
      method: "GET",
    }).then(res => res as unknown)
  },
}
// 话术训练，文字训练
export const scriptTrainTextModel = {
  // 开始电话（模拟电话流程，并不是真打电话）
  initCall: async (params: ScriptTextTrainParam) => {
    return http({
      url: `/AiSpeech/trainStringSimple/startOneCallSimple`,
      method: "GET",
      params
    }).then(res => res as unknown)
  },
  // 发送文字
  sendMsg: async (params: ScriptTextTrainParam) => {
    return http({
      url: `/AiSpeech/trainStringSimple/inputWordsSimple`,
      method: "GET",
      params
    }).then(res => res as unknown)
  },
  // AI继续说当前语料的下一句话
  continueCorpus: async (params: ScriptTextTrainParam) => {
    return http({
      url: `/AiSpeech/trainStringSimple/inputContinueSimple`,
      method: "GET",
      params
    }).then(res => res as unknown)
  },
  // 打断AI
  interrupt: async (params: ScriptTextTrainParam) => {
    return http({
      url: `/AiSpeech/trainStringSimple/inputWordsMiddle`,
      method: "GET",
      params
    }).then(res => res as unknown)
  },
  // 沉默
  silence: async (params: ScriptTextTrainParam) => {
    return http({
      url: `/AiSpeech/trainStringSimple/inputSilenceSimple`,
      method: "GET",
      params
    }).then(res => res as unknown)
  },
  // 提交事件
  submitTextTrainEvent: async (params: ScriptTextTrainParam) => {
    return http({
      url: `/AiSpeech/trainStringSimple/fakeOperation`,
      method: "GET",
      params
    }).then(res => res as unknown)
  },
  // 挂断电话（模拟电话流程，并不是真打电话）
  hangupCall: async (params: ScriptTextTrainParam) => {
    return http({
      url: `/AiSpeech/trainStringSimple/hangupSimple`,
      method: "GET",
      params
    }).then(res => res as unknown)
  },
  // 获取对话详情
  getDialogData: async (recordId: string) => {
    const url = `/AiSpeech/trainStringSimple/findAllSentencesByRecordId?recordId=${recordId}`
    cancelRequest(url)
    return http({
      url: url,
      method: "GET",
    }).then(res => res as unknown)
  },
}

// 话术统计
export const scriptStatisticsModel = {
  getScriptList: (params: {
    scriptId: number
  }) => {
    return http({
      url: "/AiSpeech/scriptStatistic/getScriptList",
      method: "POST",
      params,
    }).then(res => res as unknown as SpeechCraftInfoItem[])
  },
  // 挂机分布列表
  getHangupDistributionList: (data: ScriptStatisticParams) => {
    const {corpusTypes, hangupType, scriptId, type} = data
    const params: { url?: string, method: string, data?: any, params?: any} = { method: "POST" }
    switch(type) {
      case 'master': {
        params.params = {hangupType, scriptId};
        params.url = "/AiSpeech/scriptStatistic/getMasterCanvasHangupDistributionList";
        break;
      }
      default: {
        params.data = {corpusTypes, hangupType, scriptId};
        params.url = "/AiSpeech/scriptStatistic/getNotMasterCorpusHangupDistribution";
      }
    }
    return http(params).then(res => res as unknown as ScriptStatisticItem[])
  },
  // 挂机率列表
  getHangupRateList: (data: ScriptStatisticParams) => {
    const {corpusTypes, hangupType, scriptId, type} = data
    const params: { url?: string, method: string, data?: any, params?: any} = { method: "POST" }
    switch(type) {
      case 'master': {
        params.params = {hangupType, scriptId};
        params.url = "/AiSpeech/scriptStatistic/getMasterCanvasHangupRate";
        break;
      }
      default: {
        params.data = {corpusTypes, hangupType, scriptId};
        params.url = "/AiSpeech/scriptStatistic/getNotMasterCanvasCorpusHangupRate";
      }
    }
    return http(params).then(res => res as unknown as ScriptStatisticItem[])
  },
  // 其他语料命中分布列表
  getHitDistributionList: (data: ScriptStatisticParams) => {
    const {corpusTypes, hangupType, scriptId, type} = data
    const params: {
      url?: string, method: string, data?: any, params?: any
    } = {
      method: "POST",
    }
    switch(type) {
      case 'master': {
        params.url = "/AiSpeech/scriptStatistic/getMasterCanvasHitDistributionList";
        params.params = {scriptId}
        break;
      }
      default: {
        params.url = "/AiSpeech/scriptStatistic/getNotMasterCanvasHitDistributionList"
        params.data = {scriptId, corpusTypes, hangupType}
      }
    }
    return http(params).then(res => res as unknown as ScriptStatisticItem[])
  },
  // 高级规则命中分布列表
  getHitAdvanceDistributionList: (params: {scriptId: number}) => {
    return http({
      url: "/AiSpeech/scriptStatistic/getHitAdvanceDistributionList",
      method: "POST",
      params,
    }).then(res => res as unknown as ScriptStatisticItem[])
  },
  // 标签命中分布列表
  getHitIntentionLabelDistributionList: (params: {scriptId: number}) => {
    return http({
      url: "/AiSpeech/scriptStatistic/getHitIntentionLabelDistributionList",
      method: "POST",
      params,
    }).then(res => res as unknown as ScriptStatisticItem[])
  },
  getHitSemanticDistributionList: (params: {scriptId: number}) => {
    return http({
      url: "/AiSpeech/scriptStatistic/getHitSemanticDistributionList",
      method: "POST",
      params,
    }).then(res => res as unknown as ScriptSemanticStatisticItem[])
  },
   // 命中率列表
  getHitRateList: (data: ScriptStatisticParams) => {
    return http({
      url: "/AiSpeech/scriptStatistic/getNotMasterCanvasHitDistributionList",
      method: "POST",
      data,
    }).then(res => res as unknown as ScriptStatisticItem[])
  },
  // 挂机分布画布详情
  getHangupDistributionDetails: (params: ScriptStatisticParams) => {
    let url = ''
    const {type, headCorpusId, hangupType, scriptId} = params
    switch(type) {
      case CorpusTypeEnum['主动流程-普通语料']: url = "/AiSpeech/scriptStatistic/getMasterCorpusHangupDistributionListInOneCanvas";break;
      case CorpusTypeEnum['深层沟通-普通语料']: url = "/AiSpeech/scriptStatistic/getKnowledgeCorpusHangupDistributionListInOneCanvas";break;
    }
    return http({
      url: url,
      method: "POST",
      params: {headCorpusId, hangupType, scriptId},
    }).then(res => res as unknown as ScriptStatisticItem[])
  },
  // 挂机率画布详情
  getHangupRateDetails: (params: ScriptStatisticParams) => {
    let url = ''
    const {type, headCorpusId, hangupType, scriptId} = params
    switch(type) {
      case CorpusTypeEnum['主动流程-普通语料']: url = "/AiSpeech/scriptStatistic/getMasterCorpusHangupRateListInOneCanvas";break;
      case CorpusTypeEnum['深层沟通-普通语料']: url = "/AiSpeech/scriptStatistic/getKnowledgeCorpusHangupRateListInOneCanvas";break;
    }
    return http({
      url: url,
      method: "POST",
      params: {headCorpusId, hangupType, scriptId},
    }).then(res => res as unknown as ScriptStatisticItem[])
  },
  // 命中分布画布详情
  getHitDistributionDetails: (params: ScriptStatisticParams) => {
    let url = ''
    const {type, headCorpusId, scriptId} = params
    switch(type) {
      case CorpusTypeEnum['主动流程-普通语料']: url = "/AiSpeech/scriptStatistic/getMasterCorpusHitDistributionListInOneCanvas";break;
      case CorpusTypeEnum['深层沟通-普通语料']: url = "/AiSpeech/scriptStatistic/getKnowledgeCorpusHitDistributionListInOneCanvas";break;
    }
    return http({
      url: url,
      method: "POST",
      params: {headCorpusId, scriptId},
    }).then(res => res as unknown as ScriptStatisticItem[])
  },
  // 单个语料挂断数据
  getCorpusHangupDetail: (params: ScriptStatisticParams) => {
    return http({
      url: "/AiSpeech/scriptStatistic/getCorpusHangupDetail",
      method: "POST",
      params,
    }).then(res => res as unknown as CorpusStatisticDetails[])
  },
  // 单个语料命中数据
  getCorpusHitDetail: (params: ScriptStatisticParams) => {
    return http({
      url: "/AiSpeech/scriptStatistic/getCorpusHitDetail",
      method: "POST",
      params,
    }).then(res => res as unknown as CorpusStatisticDetails[])
  },
  /** 分类统计 */
  // 分类统计-分类pie图
  getIntentionCategory: (params: ScriptStatisticParams) => {
    return http({
      url: "/AiSpeech/scriptStatistic/getIntentionCategory",
      method: "POST",
      params,
    }).then(res => res as unknown)
  },
  // 分类统计-分类详情列表
  getIntentionDetail: (params: any) => {
    return http({
      url: "/AiSpeech/scriptStatistic/getIntentionDetail",
      method: "POST",
      params,
    }).then(res => res as unknown as ScriptStatisticItem[])
  },
}

// 分支管理
export const scriptBranchModel = {
  findList: (params: {
    scriptId: number,
    branchName?: string,
    canvasName?: string,
    corpusName?: string,
  }) => {
    return http({
      url: "/AiSpeech/scriptBranch/findAllBranches",
      method: "POST",
      params: filterEmptyParams(params),
    }).then(res => res as unknown as ScriptNormalBranchItem[])
  },
  // 批量重置分支设置
  resetBranchesCondition: (data: {
    branchCorpusMap: Object,
    semCombineEntity: {
      satisfySemConditions: CorpusConditionItem[],
      excludeSemConditions: CorpusConditionItem[],
    },
  }) => {
    return http({
      url: "/AiSpeech/scriptBranch/resetBranchesCondition",
      method: "POST",
      data,
    }).then(res => res as unknown as any[])
  },
  // 批量增加分支设置
  addBranchesCondition: (data: {
    branchCorpusMap: Object,
    semCombineEntity: {
      satisfySemConditions: CorpusConditionItem[],
      excludeSemConditions: CorpusConditionItem[],
    },
  }) => {
    return http({
      url: "/AiSpeech/scriptBranch/addBranchesCondition",
      method: "POST",
      data,
    }).then(res => res as unknown as any[])
  },
}
