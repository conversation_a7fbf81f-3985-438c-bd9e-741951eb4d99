import { http, cancelRequest } from '@/axios'
import { filterEmptyParams } from '@/utils/utils'
import { useUserStore } from '@/store/user'
import { SupplierLineParams, SupplierParams, SupplierMonitorDetailsParams, SupplierDosageDailyParams, SupplierDosageDetailsParams, SupplierDosageDailyItem,
  TenantLineParam, TenantDosageDailyItem, TenantParam, TenantMonitorDetailsParams, TenantDosageDailyParam, TenantDosageDetailsParam, MonitorChartSearchInfo,
  MonitorInfoItem, } from '@/type/line'
// ---------- 线路管理-供应线路线路 开始 ----------
export const lineSupplierModel = {
  // 查询供应线路列表
  getLineList: (data: SupplierLineParams) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/findSupplyLinesByConditions',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown)
  },
  // 查询供应运行监控列表
  getMonitorList: (data: SupplierParams) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/findSupplyLineMonitorList',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown as MonitorInfoItem[])
  },
  // 查询供应运行监控详情列表
  getMonitorDetails: (data: SupplierMonitorDetailsParams) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/findSupplyLineMonitorDetailList',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown)
  },
  // 查询供应用量统计列表
  getDosageList: (data: {
    endTime?: string,
    startTime?: string
  }) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/findSupplyLineStatisticsList',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  getDosageXlsList: (data: string[]) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/findSupplyLineStatisticsDetailListByDateList',
      method: 'POST',
      data
    }).then(res => res as unknown as {
      [key: string]: SupplierDosageDailyItem[]
    })
  },
  // 查询供应用量统计- 每日列表
  getDailyDosage: (data: SupplierDosageDailyParams) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/findSupplyLineStatisticsDetailList',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown)
  },
  getDailyDosageDetails: (data: SupplierDosageDetailsParams) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/findSupplyLineStatisticsUnitDataList',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown)
  },
  editSupplyLineEverydayPrice: (data: {
     date: string,
    supplyLineNumber: string,
    unitPrice?: number
  }) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/editSupplyLineEverydayPrice',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
}

export const lineMerchantModel = {
  // 查询商户线路列表
  getLineList: (data: TenantLineParam) => {
    return http({
      url: '/AiMonitor/callLineTenantManager/findTenantLinesByConditions',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown)
  },
  // 查询商户运行监控列表
  getMonitorList: (data: TenantParam) => {
    const userInfo = useUserStore()
    const accoutType = userInfo.accountType
    return http({
      url: `/AiMonitor/callLineTenantManager/findTenantLineMonitorList`,
      method: 'POST',
      params: {isTenant: accoutType},
      data: filterEmptyParams(data),
    }).then(res => res as unknown as MonitorInfoItem[])
  },
  // 查询商户运行监控列表
  // getMonitorListByGroupId: (data: TenantParam) => {
  //   return http({
  //     url: '/AiMonitor/callLineTenantManager/findTenantLineMonitorList',
  //     method: 'POST',
  //     data: filterEmptyParams(data),
  //   }).then(res => res as unknown as MonitorInfoItem[])
  // },
  // 查询商户运行监控详情列表
  getMonitorDetails: (data: TenantMonitorDetailsParams) => {
    return http({
      url: '/AiMonitor/callLineTenantManager/findTenantLineMonitorDetailList',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown)
  },
  // 查询供应用量统计列表
  getDosageList: (data: {
    endTime?: string,
    startTime?: string
  }) => {
    return http({
      url: '/AiMonitor/callLineTenantManager/findTenantLineStatisticsList',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  getDosageXlsList: (data: string[]) => {
    return http({
      url: '/AiMonitor/callLineTenantManager/findTenantLineStatisticsDetailListByDateList',
      method: 'POST',
      data
    }).then(res => res as unknown as {
      [key: string]: TenantDosageDailyItem[]
    })
  },
  // 查询供应用量统计列表
  getDailyDosage: (data: TenantDosageDailyParam) => {
    return http({
      url: '/AiMonitor/callLineTenantManager/findTenantLineStatisticsDetailList',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown)
  },
  getDailyDosageDetails: (data: TenantDosageDetailsParam) => {
    return http({
      url: '/AiMonitor/callLineTenantManager/findTenantLineStatisticsUnitDataList',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown)
  },
}
export const lineChartModel = {
  // 供应线路管理-通时分布
  findSupplyLineCallDurationDistribution:(params: {recentMin: number, supplyLineNumber: string}) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/findSupplyLineCallDurationSecDistribution',
      method: 'POST',
      params: filterEmptyParams(params),
    }).then(res => res as unknown)
  },
  // 商户线路管理-通时分布
  findTenantLineCallDurationDistribution:(params: {recentMin: number, tenantLineNumber : string}) => {
    return http({
      url: '/AiMonitor/callLineTenantManager/findTenantLineCallDurationSecDistribution',
      method: 'POST',
      params: filterEmptyParams(params),
    }).then(res => res as unknown)
  },
  // 查询某条线路/网关这一天的打点数据，并发分布
  getConcurrentByDate:(params: {key: string, date: string}) => {
    return http({
      url: '/AiMonitor/callLineChart/getConcurrentDotByKeyAndTime',
      method: 'POST',
      params
    }).then(res => res as unknown as Record<string, number>)
  },
  getTenantSupplyLineConcurrentByDate:(params: {supplyLineNumber: string, tenantLineNumber: string,date: string}) => {
    return http({
      url: '/AiMonitor/callLineChart/getConcurrentDotByKeyAndTimeForTenantSupplyLine',
      method: 'POST',
      params
    }).then(res => res as unknown as Record<string, number>)
  },
  
  /** 查询供应运行监控对应参数的折线图 开始 */
  // 今日
  getTodayMonitorLineInfo:(data: MonitorChartSearchInfo) => {
    cancelRequest('/AiMonitor/callLineChart/getRateChartToday')
    return http({
      url: '/AiMonitor/callLineChart/getRateChartToday',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  // 昨日
  getYesterdayMonitorLineInfo:(data: MonitorChartSearchInfo) => {
    cancelRequest('/AiMonitor/callLineChart/getRateChartYesterday')
    return http({
      url: '/AiMonitor/callLineChart/getRateChartYesterday',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  // 前日
  getBeforeYesterdayMonitorLineInfo:(data: MonitorChartSearchInfo) => {
    cancelRequest('/AiMonitor/callLineChart/getRateChartBeforeYesterday')
    return http({
      url: '/AiMonitor/callLineChart/getRateChartBeforeYesterday',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  // 历史（当前使用的是近7、15天）
  getRecentMonitorLineInfo:(data: MonitorChartSearchInfo) => {
    cancelRequest('/AiMonitor/callLineChart/getRateChartDates')
    return http({
      url: '/AiMonitor/callLineChart/getRateChartDates',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  /** 查询供应运行监控对应参数的折线图 结束 */

  /** 查询供应运行监控对应参数的geo全国数据 开始 */
  // 今日
  getTodayMonitorCountryInfo:(data: MonitorChartSearchInfo) => {
    cancelRequest('/AiMonitor/callLineChart/getCountryChartToday')
    return http({
      url: '/AiMonitor/callLineChart/getCountryChartToday',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  // 昨日
  getYesterdayMonitorCountryInfo:(data: MonitorChartSearchInfo) => {
    cancelRequest('/AiMonitor/callLineChart/getCountryChartYesterday')
    return http({
      url: '/AiMonitor/callLineChart/getCountryChartYesterday',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  // 前日
  getBeforeYesterdayMonitorCountryInfo:(data: MonitorChartSearchInfo) => {
    cancelRequest('/AiMonitor/callLineChart/getCountryChartBeforeYesterday')
    return http({
      url: '/AiMonitor/callLineChart/getCountryChartBeforeYesterday',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  // 历史 当前使用近7、15天
  getRecentMonitorCountryInfo:(data: MonitorChartSearchInfo) => {
    cancelRequest('/AiMonitor/callLineChart/getCountryChartDates')
    return http({
      url: '/AiMonitor/callLineChart/getCountryChartDates',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  /** 查询供应运行监控对应参数的geo全国数据 结束 */

  /** 查询供应运行监控对应参数的省份数据 开始 */
  // 今日
  getTodayMonitorProvinceInfo:(data: MonitorChartSearchInfo) => {
    return http({
      url: '/AiMonitor/callLineChart/getProvinceChartToday',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
   // 昨日
   getYesterdayMonitorProvinceInfo:(data: MonitorChartSearchInfo) => {
    return http({
      url: '/AiMonitor/callLineChart/getProvinceChartYesterday',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
   // 前日
   getBeforeYesterdayMonitorProvinceInfo:(data: MonitorChartSearchInfo) => {
    return http({
      url: '/AiMonitor/callLineChart/getProvinceChartBeforeYesterday',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  // 历史（近7、15天）
  getRecentMonitorProvinceInfo:(data: MonitorChartSearchInfo) => {
    return http({
      url: '/AiMonitor/callLineChart/getProvinceChartDates',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  /** 查询供应运行监控对应参数的省份数据 结束 */

  /** 查询昨日、前日的线路详情， */
  findSupplyLineMonitorListBeforeYesterday:(data: SupplierMonitorDetailsParams) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/findSupplyLineMonitorListBeforeYesterday',
      method: 'POST',
      data
    }).then(res => res as unknown as MonitorInfoItem[])
  },
  findSupplyLineMonitorListYesterday:(data: SupplierMonitorDetailsParams) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/findSupplyLineMonitorListYesterday',
      method: 'POST',
      data
    }).then(res => res as unknown as MonitorInfoItem[])
  },
  findTenantLineMonitorListBeforeYesterday:(data: SupplierMonitorDetailsParams) => {
    const userInfo = useUserStore()
    const accoutType = userInfo.accountType
    return http({
      url: '/AiMonitor/callLineTenantManager/findTenantLineMonitorListBeforeYesterday',
      method: 'POST',
      params: {isTenant: accoutType},
      data
    }).then(res => res as unknown as MonitorInfoItem[])
  },
  findTenantLineMonitorListYesterday:(data: SupplierMonitorDetailsParams) => {
    const userInfo = useUserStore()
    const accoutType = userInfo.accountType
    return http({
      url: '/AiMonitor/callLineTenantManager/findTenantLineMonitorListYesterday',
      method: 'POST',
      params: {isTenant: accoutType},
      data
    }).then(res => res as unknown as MonitorInfoItem[])
  },
  /** 查询昨日、前日的供应线路下某商户供应线路详情数据 */
  findSupplyLineMonitorDetailListBeforeYesterday:(data: SupplierMonitorDetailsParams) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/findSupplyLineMonitorDetailListBeforeYesterday',
      method: 'POST',
      data
    }).then(res => res as unknown as MonitorInfoItem[])
  },
  findSupplyLineMonitorDetailListYesterday:(data: SupplierMonitorDetailsParams) => {
    return http({
      url: '/AiMonitor/callLineSupplyManager/findSupplyLineMonitorDetailListYesterday',
      method: 'POST',
      data
    }).then(res => res as unknown as MonitorInfoItem[])
  },
   /** 查询昨日、前日的商户线路下某供应供应线路详情数据 */
   findTenantLineMonitorDetailListBeforeYesterday:(data: SupplierMonitorDetailsParams) => {
    return http({
      url: '/AiMonitor/callLineTenantManager/findTenantLineMonitorDetailListBeforeYesterday',
      method: 'POST',
      data
    }).then(res => res as unknown as MonitorInfoItem[])
  },
  findTenantLineMonitorDetailListYesterday:(data: SupplierMonitorDetailsParams) => {
    return http({
      url: '/AiMonitor/callLineTenantManager/findTenantLineMonitorDetailListYesterday',
      method: 'POST',
      data
    }).then(res => res as unknown as MonitorInfoItem[])
  },
}
