import { http } from '@/axios'
import { GatewayItem, LineInfo } from '@/type/gateway'

export const gatewayModel = {
  getGatewayList: () => {
    return http({
      url: "/AiMonitor/callLineChart/getLineGatewayDataList",
      method: "POST",
    }).then(res => res as unknown as GatewayItem[])
  },
  deleteGateway: (params: {
    lineGatewayId : number,
  }) => {
    return http({
      url: "/AiSpeech/lineGateway/deleteOneLineGatewayById",
      method: "DELETE",
      params,
    }).then(res => res as unknown)
  },
  addGateway: (data: GatewayItem) => {
    return http({
      data,
      url: "/AiSpeech/lineGateway/addOneLineGateway",
      method: "POST",
    }).then(res => res as unknown)
  },
  editGateway: (data: GatewayItem) => {
    return http({
      data,
      url: "/AiSpeech/lineGateway/editLineGateway",
      method: "POST",
    }).then(res => res as unknown)
  },
  // getGatewayById: (data: {lineGatewayId: number}) => {
  //   return http({
  //     url: "/AiSpeech/lineGateway/findOneLineGatewayById",
  //     method: "GET",
  //     data,
  //   }).then(res => res as unknown as GatewayItem)
  // },
  // addGatewayLine: (data: {
  //   lineGatewayId : number, supplyLineNumbers : string[]
  // }) => {
  //   return http({
  //     url: "/AiSpeech/lineGateway/addSupplyLinesInGateway",
  //     method: "POST",
  //     data,
  //   }).then(res => res as unknown)
  // },
  // deleteGatewayLine: (data: {
  //   lineGatewayId : number, supplyLineNumbers: string[]
  // }) => {
  //   return http({
  //     url: "/AiSpeech/lineGateway/deleteSupplyLinesInGateway",
  //     method: "POST",
  //     data,
  //   }).then(res => res as unknown)
  // },
}

