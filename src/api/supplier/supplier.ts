import { http } from '@/axios'
import {
  SupplierPlatformLineRecordItem,
  SupplierPlatformLineRecordSearchParam,
} from '@/type/supplier/supplier'
import { filterEmptyParams } from '@/utils/utils'

export const supplierModel = {

  // 供应平台-线路数据-查询线路今日运营数据
  getSupplierPlatformLineList: async (data: {
    supplyLineNumber?: string,
    recentMin: number | null,
  }) => {
    return http({
      url: '/AiMonitor/callLineMonitorForSupplier/findSupplyLineMonitorList',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown)
  },
  // 供应平台-线路数据-查询数据详情
  getSupplierPlatformLineDetail: async (data: SupplierPlatformLineRecordSearchParam) => {
    return http({
      url: '/AiMonitor/callLineMonitorForSupplier/getRecords',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown as {
      records: SupplierPlatformLineRecordItem[],
      total: number
    })
  },
  // 供应平台-线路数据-数据详情-查看当前明文数据
  convertPlainPhoneByRecordId: (data: {
    supplyLineNumber: string,
    callOutTime: string,
    recordId: string
  }) => {
    return http({
      data,
      url: "/AiMonitor/callLineMonitorForSupplier/checkPlainPhone",
      method: "POST",
    }).then(res => res as unknown as {
      phone: string,
      remainCount: number,
    })
  },
  // 供应平台-线路数据-数据详情-批量下载明文数据
  batchConvertPlainPhone: async (data: SupplierPlatformLineRecordSearchParam) => {
    return http({
      data: filterEmptyParams(data),
      url: "/AiMonitor/callLineMonitorForSupplier/exportRecords",
      method: "POST",
    }).then(res => res as unknown as {
      records: SupplierPlatformLineRecordItem[],
      total: number
    })
  },
  // 查询账号剩余明文权限查看次数
  getRemainDecryptCount: async () => {
    return http({
      url: "/AiMonitor/callLineMonitorForSupplier/getRemainDecryptCount",
      method: "POST",
    }).then(res => res as unknown as number)
  },

}
