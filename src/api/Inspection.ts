import { http } from '@/axios'
import { filterEmptyParams} from '@/utils/utils'
import { ResponseData } from '@/axios/request/types'
import { TaskCallRecordItem } from '@/type/task'
import { InspecteOrderItem, InspectionRecordDetailItem, InspecteParams, InspecteOrderSearchParams, InspecteOrderBaseItem } from '@/type/Inspection'

export const inspectionModel = {
  // 查询巡检记录
  findInspectionRecord: (data: {
    account?: string,
    timeStart?: string,
    timeEnd?: string,
    limit: number,
    page: number,
  }) => {
    return http({
      url: '/AiSpeech/callRecord/findAllInspection',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown as ResponseData)
  },
  // 查询巡检-详情（巡检的通话记录）
  findInspectionRecordDetails: (data: {
    account?: string,
    timeStart?: string,
    timeEnd?: string,
    recordId?: string,
    limit: number,
    page: number,
  }) => {
    return http({
      url: '/AiSpeech/callRecord/findInspectionDetails',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown as ResponseData)
  },
  // 巡检通话详情
  inspecteRecordDetail: (params: InspecteParams) => {
    return http({
      url: '/AiSpeech/callRecord/callDetailStatistic',
      method: 'POST',
      params,
    }).then(res => res as unknown as InspectionRecordDetailItem[])
  },
  // 巡检通话音频
  inspecteRecordAudio: (params: InspecteParams) => {
    return http({
      url: '/AiSpeech/callRecord/playRecordStatistic',
      method: 'POST',
      params,
    }).then(res => res as unknown as InspectionRecordDetailItem[])
  },
  // 查询巡检工单
  findInspectionWorkOrder: (data: Partial<InspecteOrderSearchParams>) => {
    return http({
      url: '/AiSpeech/callRecord/findAllWorkOrder',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown as ResponseData)
  },
  // 新建巡检工单
  createInspectionWorkOrder: (data: Partial<InspecteOrderBaseItem>) => {
    return http({
      url: '/AiSpeech/callRecord/addWorkOrder',
      method: 'POST',
      data,
    }).then(res => res as unknown as InspecteOrderItem)
  },
  // 处理巡检工单
  handleInspectionWorkOrder: (data: Partial<InspecteOrderItem>) => {
    return http({
      url: '/AiSpeech/callRecord/processWorkOrder',
      method: 'POST',
      data,
    }).then(res => res as unknown as InspecteOrderItem)
  },
  // 根据recordId查询通话记录
  findCallRecordByRecordId: (data: {recordId: string}) => {
    return http({
      url: `/AiSpeech/callRecord/callRecordByTypeAndRecordId`,
      method: 'GET',
      data,
    }).then(res => res as unknown as Record<string, TaskCallRecordItem | null>)
  },
}
