import { http } from '@/axios'
import { IndustryItem, ProductItem, ProductItemOrigin } from '@/type/industry'
import { MerchantProjectItem } from '@/type/merchant'
import { ResponseData } from '@/axios/request/types'

export const industryModel = {
  // 查询一级行业
  getPrimaryIndustryField: () => {
    return http({
      url: '/AiSpeech/industryField/findAllPrimaryIndustries',
      method: 'GET',
    }).then(res => res as unknown)
  },
  // 查询所有行业
  getAllIndustryField: () => {
    return http({
      url: '/AiSpeech/industryField/findAllIndustryField',
      method: 'GET',
    }).then(res => res as unknown)
  },
}

export const productModel = {
  // 查询产品列表（暂定支持分页）
  getList: (data?: {name: string}) => {
    return http({
      url: '/AiSpeech/productAdmin/findProductByName',
      method: 'GET',
      data,
    }).then(res => res as unknown as ProductItem[])
  },
  add: (data: ProductItem) => {
    return http({
      url: '/AiSpeech/productAdmin/saveProduct',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  edit: (data: ProductItem) => {
    return http({
      url: '/AiSpeech/productAdmin/editProduct',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  delete: (params: {id: number}) => {
    return http({
      url: '/AiSpeech/productAdmin/deleteProduct',
      method: 'POST',
      params,
    }).then(res => res as unknown as null | MerchantProjectItem[])
  },
  check: (params: {id: number}) => {
    return http({
      url: '/AiSpeech/industryField/check',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
}
