import { AppRouteRecordRawT } from "@/type/route"
import Layout from '@/views/layout/Index.vue'

/**
 * 商户端
 */
// 账号统计
import StatisticalReport from '@/views/merchant/statistic/statistical-report/Index.vue'
import MerchantLineReport from '@/views/merchant/statistic/line-report/Index.vue'
// AI外呼
import AITask from '@/views/merchant/ai-report/TaskManager/AITask.vue'
import MixTask from '@/views/merchant/ai-report/TaskManager/MixTask.vue'
import TemplateManagement from '@/views/merchant/ai-report/TemplateManagement/Index.vue'
import OperationTool from '@/views/merchant/ai-report/OperationTool/Index.vue'
import ImportRecord from '@/views/merchant/ai-report/ImportRecord.vue'
// 人工外呼
import SeatManager from '@/views/merchant/manual-call/SeatManager/Index.vue'
import SeatMonitor from '@/views/merchant/manual-call/SeatMonitor/Index.vue'
import ClueManager from '@/views/merchant/manual-call/ClueManager/Index.vue'
import TeamClueManager from '@/views/merchant/manual-call/TeamClueManager/Index.vue'
import Workbench from '@/views/merchant/manual-call/Workbench/Index.vue'
// 通话记录
import AIRecord from '@/views/merchant/call-record/AIRecord.vue'
import MixRecord from '@/views/merchant/call-record/MixRecord.vue'
import ManualRecord from '@/views/merchant/call-record/ManualRecord.vue'
import SmsRecord from '@/views/merchant/call-record/SmsRecord.vue'
// import UpwardSmsRecord from '@/views/merchant/call-record/UpwardSmsRecord.vue'
// 供应平台（用于供应商查看供应线路自身数据）
import SupplierPlatformLineStatistics from '@/views/merchant/supplier-platform/line-statistics/Index.vue'
// 系统设置
import MerchantCallSetting from '@/views/merchant/system-setting/MerchantCallSetting.vue'
import FormManager from '@/views/merchant/system-setting/FormManager/Index.vue'
import MerchantBlacklist from '@/views/merchant/system-setting/Blacklist/Index.vue'
// 团队管理
import MerchantAccountManagement from '@/views/merchant/team-manager/account/Index.vue'
import MerchantRoleManagement from '@/views/merchant/team-manager/role/Index.vue'
import SystemManagement from '@/views/merchant/team-manager/system/Index.vue'

/**
 * 运营端
 */
// 话术管理
import SpeechCraftManager from '@/views/operator/ai-resource/SpeechCraftManager/Index.vue'
import MainProgressManager from '@/views/operator/ai-resource/MainProgressManager/Index.vue'
import SpeechCraftCheck from '@/views/operator/ai-resource/SpeechCraftCheck/Index.vue'
import CoreSemanticManager from '@/views/operator/ai-resource/CoreSemanticManager/Index.vue'
// 商户管理
import MerchantManager from '@/views/operator/merchant-manager/Index.vue'
import MerchantLineDetail from '@/views/operator/merchant-manager/Line/Detail/Index.vue'
import SmsTemplateDetail from '@/views/operator/merchant-manager/SmsTemplate/Sms/Index.vue'
import SmsChannelDetail from '@/views/operator/merchant-manager/SmsTemplate/Channel/Index.vue'
// 火山运营
import VolcanoOperator from '@/views/operator/volcano-manager/Index.vue'
import VolcanoSmsUrl from '@/views/operator/sms-manager/sms-url/VolcanoSmsUrl.vue'
// 供应管理
import LineSupplier from '@/views/operator/supplier-manager/line-supplier/Index.vue'
import SupplierLineDetail from '@/views/operator/supplier-manager/line-supplier/SupplierLineDetail.vue'
import LineGateway from '@/views/operator/supplier-manager/line-gateway/Index.vue'
// 短信管理
import SmsProvider from '@/views/operator/sms-manager/sms-provider/Index.vue'
import SmsAccountEditDetail from '@/views/operator/sms-manager/sms-provider/Account/Detail/EditDetail.vue'
import SmsUrl from '@/views/operator/sms-manager/sms-url/Index.vue'
// 线路管理
import SupplierLineManger from '@/views/operator/line-manager/supplier-line-manger/Index.vue'
import SupplierLineMonitorDetails from '@/views/operator/line-manager/supplier-line-manger/MonitorDetails.vue'
import SupplierLineDosageDaily from '@/views/operator/line-manager/supplier-line-manger/DosageDaily.vue'
import SupplierLineDosageDailyDetails from '@/views/operator/line-manager/supplier-line-manger/DosageDailyDetails.vue'
import MerchantLineManger from '@/views/operator/line-manager/merchant-line-manger/Index.vue'
import MerchantLineMonitorDetails from '@/views/operator/line-manager/merchant-line-manger/MonitorDetails.vue'
import MerchantLineDosageDaily from '@/views/operator/line-manager/merchant-line-manger/DosageDaily.vue'
import MerchantLineDosageDailyDetails from '@/views/operator/line-manager/merchant-line-manger/DosageDailyDetails.vue'
// 数据管理
import OperationMonitor from '@/views/operator/merchant-data-manager/OperationMonitor/Index.vue'
// 数据过滤
import Whitelist from '@/views/operator/data-filter/Whitelist/Index.vue'
import Blacklist from '@/views/operator/data-filter/Blacklist/Index.vue'
import GoodNumberRestriction from '@/views/operator/data-filter/GoodNumberRestriction/Index.vue'
import ForbiddenWord from '@/views/operator/data-filter/ForbiddenWord/Index.vue'
import BlacklistRestriction from '@/views/operator/data-filter/BlacklistRestriction/Index.vue'
import FrequencyRestriction from '@/views/operator/data-filter/FrequencyRestriction/Index.vue'
// 系统管理
import IndustryManager from '@/views/operator/system-manager/IndustryManager.vue'
import ProductManager from '@/views/operator/system-manager/product-manager/Index.vue'
// 系统监控
import CommonOperationMonitor from '@/views/operator/system-monitor/common-operation-monitor/Index.vue'
import OperationalInspectionManager from '@/views/operator/system-monitor/operational-inspection-manager/Index.vue'
import SystemOperationMonitor from '@/views/operator/system-monitor/SystemOperationMonitor.vue'
import SystemOperationSwitch from '@/views/operator/system-monitor/SystemOperationSwitch.vue'
import AuthorizationManager from '@/views/operator/system-monitor/authorization-manager/Index.vue'
// 团队管理
import OperatorAccountManagement from '@/views/operator/team-manager/account/Index.vue'
import OperatorRoleManagement from '@/views/operator/team-manager/role/Index.vue'

import { useUserStore } from '@/store/user'
import routeMap from './route-map'

export const merchantRouter: AppRouteRecordRawT[] = [
  {
    path: "/merchant",
    name: "商户端",
    redirect: () => {
      const userStore = useUserStore();
      return userStore.homePage[1]
    },
    meta: {
      title: "商户端",
      id: 'common',
      type: 0,
      icon: 'merchant1',
    },
  },
  {
    path: '/merchant/statistics',
    name: 'AccountStatistics',
    meta: {
      id: 'common',
      type: 1,
      title: '账号统计',
      icon: 'statistic'
    },
    redirect: '/merchant/ai-report/task/merchant/statistics/statistical-report',
    children: [
      {
        path: '/merchant/statistics/statistical-report',
        name: 'StatisticalReport',
        meta: {
          id: routeMap['统计报表'].id,
          type: 2,
          title: routeMap['统计报表'].name,
        },
        component: StatisticalReport
      },
      {
        path: '/merchant/statistics/line-report',
        name: 'MerchantLineReport',
        meta: {
          id: routeMap['线路报表'].id,
          type: 2,
          title: routeMap['线路报表'].name,
        },
        component: MerchantLineReport
      },
    ]
  },
  {
    path: '/merchant/ai-report',
    name: 'AiReport',
    meta: {
      id: 'common',
      type: 1,
      title: 'AI外呼',
      icon: 'ai'
    },
    redirect: '/merchant/ai-report/ai-task',
    children: [
      {
        path: '/merchant/ai-report/ai-task',
        name: 'AITask',
        meta: {
          id: routeMap['AI外呼任务'].id,
          type: 2,
          title: routeMap['AI外呼任务'].name,
        },
        component: AITask
      },
      {
        path: '/merchant/ai-report/mix-task',
        name: 'MixTask',
        meta: {
          id: routeMap['人机协同任务'].id,
          type: 2,
          title: routeMap['人机协同任务'].name,
        },
        component: MixTask
      },
      {
        path: '/merchant/ai-report/template-management',
        name: 'TemplateManagement',
        meta: {
          id: routeMap['外呼模板'].id,
          type: 2,
          title: routeMap['外呼模板'].name,
        },
        component: TemplateManagement
      },
      {
        path: '/merchant/ai-report/operation-tool',
        name: 'OperationTool',
        meta: {
          id: routeMap['外呼工具'].id,
          type: 2,
          title: routeMap['外呼工具'].name,
        },
        component: OperationTool
      },
      {
        path: '/merchant/ai-report/import-record',
        name: 'ImportRecord',
        meta: {
          id: routeMap['导入记录'].id,
          type: 2,
          title: routeMap['导入记录'].name,
        },
        component: ImportRecord
      },
    ]
  },
  {
    path: '/merchant/manual-call',
    name: 'ManualCall',
    meta: {
      id: 'common',
      type: 1,
      title: '人工外呼',
      icon: 'manual',
    },
    redirect: '/merchant/manual-call/workbench',
    children: [
      {
        path: '/merchant/manual-call/seat-manager',
        name: 'SeatManager',
        meta: {
          id: routeMap['坐席管理'].id,
          type: 2,
          title: routeMap['坐席管理'].name,
        },
        component: SeatManager,
      },
      {
        path: '/merchant/manual-call/seat-monitor',
        name: 'SeatMonitor',
        meta: {
          id: routeMap['坐席监控'].id,
          type: 2,
          title: routeMap['坐席监控'].name,
        },
        component: SeatMonitor,
      },
      {
        path: '/merchant/manual-call/clue-manager',
        name: 'ClueManager',
        meta: {
          id: routeMap['线索管理'].id,
          type: 2,
          title: routeMap['线索管理'].name,
        },
        component: ClueManager,
      },
      {
        path: '/merchant/manual-call/seat-group-manager',
        name: 'TeamClueManager',
        meta: {
          id: routeMap['坐席组线索'].id,
          type: 2,
          title: routeMap['坐席组线索'].name,
        },
        component: TeamClueManager,
      },
      {
        path: '/merchant/manual-call/workbench',
        name: 'Workbench',
        meta: {
          id: routeMap['坐席工作台'].id,
          type: 2,
          title: routeMap['坐席工作台'].name,
        },
        component: Workbench,
      },
    ],
  },
  {
    path: '/merchant/call-record',
    name: 'CallRecord',
    meta: {
      id: 'common',
      type: 1,
      title: '通话记录',
      icon: 'call',
    },
    redirect: '/merchant/call-record/ai-record',
    children: [
      {
        path: '/merchant/call-record/ai-record',
        name: 'MerchantAiRecord',
        meta: {
          id: routeMap['AI外呼记录'].id,
          type: 2,
          title: routeMap['AI外呼记录'].name,
        },
        component: AIRecord,
      },
      {
        path: '/merchant/call-record/mix-record',
        name: 'MerchantMixRecord',
        meta: {
          id: routeMap['人机协同记录'].id,
          type: 2,
          title: routeMap['人机协同记录'].name,
        },
        component: MixRecord
      },
      {
        path: '/merchant/call-record/manual-record',
        name: 'MerchantManualRecord',
        meta: {
          id: routeMap['人工直呼记录'].id,
          type: 2,
          title: routeMap['人工直呼记录'].name,
        },
        component: ManualRecord
      },
      {
        path: '/merchant/call-record/sms-record',
        name: 'MerchantSmsRecord',
        meta: {
          id: routeMap['短信发送记录'].id,
          type: 2,
          title: routeMap['短信发送记录'].name,
        },
        component: SmsRecord
      },
      // {
      //   path: '/merchant/call-record/upward-sms-record',
      //   name: 'MerchantUpwardSmsRecord',
      //   meta: {
      //     id: routeMap['上行短信记录'].id,
      //     type: 2,
      //     title: routeMap['上行短信记录'].name,
      //   },
      //   component: UpwardSmsRecord
      // },
    ]
  },
  {
    path: '/merchant/supplier-platform',
    name: 'SupplierPlatform',
    meta: {
      id: 'common',
      type: 1,
      title: '供应平台',
      icon: 'supply'
    },
    redirect: '/merchant/supplier-platform/line-statistics',
    children: [
      {
        path: '/merchant/supplier-platform/line-statistics',
        name: 'SupplierPlatformLineStatistics',
        meta: {
          id: routeMap['供应平台-线路数据'].id,
          type: 2,
          title: routeMap['供应平台-线路数据'].name,
        },
        component: SupplierPlatformLineStatistics,
      },
    ],
  },
  {
    path: '/merchant/system-setting',
    name: 'MerchantSystemSettings',
    meta: {
      id: 'common',
      type: 1,
      title: '系统设置',
      icon: 'setting',
    },
    redirect: '/merchant/system-setting/call-settings',
    children: [
      {
        path: '/merchant/system-setting/call-settings',
        name: 'MerchantCallSetting',
        meta: {
          id: routeMap['通话设置'].id,
          type: 2,
          title: routeMap['通话设置'].name,
        },
        component: MerchantCallSetting,
      },
      {
        path: '/merchant/system-setting/form-manager',
        name: 'FormManager',
        meta: {
          id: routeMap['表单设置'].id,
          type: 2,
          title: routeMap['表单设置'].name,
        },
        component: FormManager
      },
      {
        path: '/merchant/system-setting/blacklist',
        name: 'MerchantBlacklist',
        meta: {
          id: routeMap['商户黑名单'].id,
          type: 2,
          title: routeMap['商户黑名单'].name,
        },
        component: MerchantBlacklist
      },
      
    ],
  },
  {
    path: '/merchant/team-manager',
    name: 'MerchantTeamManagement',
    meta: {
      id: 'common',
      type: 1,
      title: '团队管理',
      icon: 'team'
    },
    redirect: '/merchant/team-manager/role-management',
    children: [
      {
        path: '/merchant/team-manager/Account-management',
        name: 'MerchantAccountManagement',
        meta: {
          id: routeMap['成员管理-商户端'].id,
          type: 2,
          title: routeMap['成员管理-商户端'].name,
        },
        component: MerchantAccountManagement,
      },
      {
        path: '/merchant/team-manager/role-management',
        name: 'MerchantRoleManagement',
        meta: {
          id: routeMap['角色管理-商户端'].id,
          title: routeMap['角色管理-商户端'].name,
          type: 2,
        },
        component: MerchantRoleManagement,
      },
    ]
  }
]
export const operatorRouter: AppRouteRecordRawT[] = [
  {
    path: "/operator",
    name: "运营端",
    redirect: () => {
      const userStore = useUserStore();
      return userStore.homePage[0]
    },
    meta: {
      title: "运营端",
      id: 'common',
      icon: 'operator',
      type: 0,
    },
  },
  {
    path: '/operator/operation-monitor',
    name: 'OperationMonitor',
    meta: {
      id: routeMap['运行监控'].id,
      type: 1,
      title: routeMap['运行监控'].name,
      icon: 'operation',
    },
    component: OperationMonitor
  },
  {
    path: '/operator/statistics/statistical-report',
    name: 'OperationStatisticalReport',
    meta: {
      id: routeMap['统计报表-运营端'].id,
      type: 1,
      title: routeMap['统计报表-运营端'].name,
      icon: 'statistic'
    },
    component: StatisticalReport
  },
  {
    path: '/operator/ai-resource',
    name: 'AiResource',
    meta: {
      id: 'common',
      type: 1,
      title: '话术管理',
      icon: 'script'
    },
    redirect: '/operator/ai-resource/speech-craft-manager',
    children: [
      {
        path: '/operator/ai-resource/speech-craft-manager',
        name: 'SpeechCraftManager',
        meta: {
          id: routeMap['话术制作'].id,
          type: 2,
          title: routeMap['话术制作'].name,
        },
        component: SpeechCraftManager
      },
      {
        path: '/operator/ai-resource/main-progress-manager',
        name: 'MainProgressManager',
        meta: {
          id: routeMap['话术制作'].id,
          type: 3,
          preRoute: '/operator/ai-resource/speech-craft-manager',
          title: '编辑主流程',
        },
        component: MainProgressManager
      },
      {
        path: '/operator/ai-resource/speech-craft-check',
        name: 'SpeechCraftCheck',
        meta: {
          id: routeMap['话术审核'].id,
          type: 2,
          title: routeMap['话术审核'].name,
        },
        component: SpeechCraftCheck
      },
      // {
      //   path: '/operator/ai-resource/speech-craft-record',
      //   name: 'SpeechCraftCheckRecord',
      //   meta: {
      //     id: routeMap['话术审核记录'].id,
      //     type: 2,
      //     title: routeMap['话术审核记录'].name,
      //   },
      //   component: SpeechCraftCheckRecord
      // },
      {
        path: '/operator/ai-resource/core-semantic-manager',
        name: 'CoreSemanticManager',
        meta: {
          id: routeMap['语义管理'].id,
          type: 2,
          title: routeMap['语义管理'].name,
        },
        component: CoreSemanticManager
      },
    ]
  },
  {
    path: '/operator/merchant-manager',
    name: 'MerchantManager',
    meta: {
      id: routeMap['商户管理'].id,
      type: 1,
      title: routeMap['商户管理'].name,
      icon: 'merchant',
      keepAlive: true,
    },
    component: MerchantManager,
  },
  {
    path: '/operator/supplier-manager',
    name: 'SupplierManager',
    meta: {
      id: 'common',
      type: 1,
      title: '供应管理',
      icon: 'supply'
    },
    redirect: '/operator/supplier-manager/line-supplier',
    children: [
      {
        path: '/operator/supplier-manager/line-supplier',
        name: 'LineSupplier',
        meta: {
          id: routeMap['线路供应商'].id,
          type: 2,
          keepAlive: true,
          title: routeMap['线路供应商'].name,
        },
        component: LineSupplier,
      },
      {
        path: '/operator/supplier-manager/line-gateway',
        name: 'LineGateway',
        meta: {
          id: routeMap['供应线路网关'].id,
          type: 2,
          title: routeMap['供应线路网关'].name,
        },
        component: LineGateway
      },
    ],
  },
  {
    path: '/operator/sms-manager',
    name: 'SmsManager',
    meta: {
      id: 'common',
      type: 1,
      title: '短信管理',
      icon: 'sms'
    },
    redirect: '/operator/sms-manager/sms-provider',
    children: [
      {
        path: '/operator/sms-manager/sms-provider',
        name: 'SmsProvider',
        meta: {
          id: routeMap['短信供应商'].id,
          type: 2,
          keepAlive: true,
          title: routeMap['短信供应商'].name,
        },
        component: SmsProvider,
      },
      {
        path: '/operator/sms-manager/sms-url',
        name: 'SmsUrl',
        meta: {
          id: routeMap['短链管理'].id,
          type: 2,
          title: routeMap['短链管理'].name,
        },
        component: SmsUrl,
      },
    ],
  },
  {
    path: '/operator/line-manager',
    name: 'LineManager',
    meta: {
      id: 'common',
      type: 1,
      title: '线路运营',
      icon: 'line'
    },
    redirect: '/operator/line-manager/supplier-line-manger',
    children: [
      {
        path: '/operator/line-manager/supplier-line-manger',
        name: 'SupplierLineManger',
        meta: {
          id: routeMap['供应线路'].id,
          type: 2,
          keepAlive: true,
          title: routeMap['供应线路'].name,
        },
        component: SupplierLineManger
      },
      {
        path: '/operator/line-manager/supplier-line-manger/monitor-details',
        name: 'SupplierLineMonitorDetails',
        meta: {
          id: routeMap['供应线路'].id,
          type: 3,
          preRoute: '/operator/line-manager/supplier-line-manger',
          title: '供应线路',
        },
        component: SupplierLineMonitorDetails
      },
      {
        path: '/operator/line-manager/supplier-line-manger/dosage-daily',
        name: 'SupplierLineDosageDaily',
        meta: {
          id: routeMap['供应线路'].id,
          type: 3,
          title: '供应线路',
          keepAlive: true,
          preRoute: '/operator/line-manager/supplier-line-manger',
        },
        component: SupplierLineDosageDaily
      },
      {
        path: '/operator/line-manager/supplier-line-manger/dosage-daily/details',
        name: 'SupplierLineDosageDailyDetails',
        meta: {
          id: routeMap['供应线路'].id,
          type: 3,
          title: '供应线路',
          preRoute: '/operator/line-manager/supplier-line-manger',
        },
        component: SupplierLineDosageDailyDetails
      },
      {
        path: '/operator/line-manager/merchant-line-manger',
        name: 'MerchantLineManger',
        meta: {
          id: routeMap['商户线路'].id,
          type: 2,
          keepAlive: true,
          title: routeMap['商户线路'].name,
        },
        component: MerchantLineManger
      },
      {
        path: '/operator/line-manager/merchant-line-manger/monitor-details',
        name: 'MerchantLineMonitorDetails',
        meta: {
          id: routeMap['商户线路'].id,
          type: 3,
          title: '商户线路',
          preRoute: '/operator/line-manager/merchant-line-manger',
        },
        component: MerchantLineMonitorDetails
      },
      {
        path: '/operator/line-manager/merchant-line-manger/dosage-daily',
        name: 'MerchantLineDosageDaily',
        meta: {
          id: routeMap['商户线路'].id,
          type: 3,
          title: '商户线路',
          keepAlive: true,
          preRoute: '/operator/line-manager/merchant-line-manger',
        },
        component: MerchantLineDosageDaily
      },
      {
        path: '/operator/line-manager/merchant-line-manger/dosage-daily/details',
        name: 'MerchantLineDosageDailyDetails',
        meta: {
          id: routeMap['商户线路'].id,
          type: 3,
          title: '商户线路',
          preRoute: '/operator/line-manager/merchant-line-manger',
        },
        component: MerchantLineDosageDailyDetails
      },
    ],
  },
  {
    path: '/operator/data-manager',
    name: 'DataManager',
    meta: {
      id: 'common',
      type: 1,
      title: '数据管理',
      icon: 'data'
    },
    redirect: '/operator/data-manager/ai-record',
    children: [
      {
        path: '/operator/data-manager/ai-record',
        name: 'OperatorAIRecord',
        meta: {
          id: routeMap['AI外呼记录-运营端'].id,
          type: 2,
          title: routeMap['AI外呼记录-运营端'].name,
        },
        component: AIRecord
      },
      {
        path: '/operator/data-manager/mix-record',
        name: 'OperatorMixRecord',
        meta: {
          id: routeMap['人机协同记录-运营端'].id,
          type: 2,
          title: routeMap['人机协同记录-运营端'].name,
        },
        component: MixRecord
      },
      {
        path: '/operator/data-manager/manual-record',
        name: 'OperatorManualRecord',
        meta: {
          id: routeMap['人工直呼记录-运营端'].id,
          type: 2,
          title: routeMap['人工直呼记录-运营端'].name,
        },
        component: ManualRecord
      },
      {
        path: '/operator/data-manager/sms-record',
        name: 'OperatorSmsRecord',
        meta: {
          id: routeMap['短信发送记录-运营端'].id,
          type: 2,
          title: routeMap['短信发送记录-运营端'].name,
        },
        component: SmsRecord
      },
      // {
      //   path: '/operator/data-manager/upward-sms-record',
      //   name: 'OperatorUpwardSmsRecord',
      //   meta: {
      //     id: routeMap['上行短信记录-运营端'].id,
      //     type: 2,
      //     title: routeMap['上行短信记录-运营端'].name,
      //   },
      //   component: UpwardSmsRecord
      // },
    ],
  },
  {
    path: '/operator/data-filter',
    name: 'DateFilter',
    meta: {
      id: 'common',
      type: 1,
      title: '数据过滤',
      icon: 'filter'
    },
    redirect: '/operator/data-filter/whitelist',
    children: [
      {
        path: '/operator/data-filter/whitelist',
        name: 'Whitelist',
        meta: {
          id: routeMap['白名单'].id,
          type: 2,
          title: routeMap['白名单'].name,
        },
        component: Whitelist
      },
      {
        path: '/operator/data-filter/black-group',
        name: 'Blacklist',
        meta: {
          id: routeMap['黑名单'].id,
          type: 2,
          title: routeMap['黑名单'].name,
        },
        component: Blacklist
      },
      {
        path: '/operator/data-filter/good-number-restriction',
        name: 'GoodNumberRestriction',
        meta: {
          id: routeMap['靓号限制'].id,
          type: 2,
          title: routeMap['靓号限制'].name,
        },
        component: GoodNumberRestriction
      },
      {
        path: '/operator/data-filter/forbidden-word',
        name: 'ForbiddenWord',
        meta: {
          id: routeMap['违禁词管理'].id,
          type: 2,
          title: routeMap['违禁词管理'].name,
        },
        component: ForbiddenWord
      },
      {
        path: '/operator/data-filter/black-group-restriction',
        name: 'BlacklistRestriction',
        meta: {
          id: routeMap['黑名单限制'].id,
          type: 2,
          title: routeMap['黑名单限制'].name,
        },
        component: BlacklistRestriction
      },
      {
        path: '/operator/data-filter/frequency-restriction',
        name: 'FrequencyRestriction',
        meta: {
          id: routeMap['频率限制'].id,
          type: 2,
          title: routeMap['频率限制'].name,
        },
        component: FrequencyRestriction
      },
    ],
  },
  {
    path: '/operator/volcano',
    name: 'Volcano',
    meta: {
      id: 'common',
      type: 1,
      title: '火山运营',
      icon: 'script'
    },
    redirect: '/operator/volcano/volcano-manager',
    children: [
      {
        path: '/operator/volcano/volcano-manager',
        name: 'VolcanoManager',
        meta: {
          id: routeMap['火山商户管理'].id,
          type: 2,
          keepAlive: true,
          title: routeMap['火山商户管理'].name,
        },
        component: MerchantManager
      },
      {
        path: '/operator/volcano/volcano-sms-url',
        name: 'VolcanoSmsUrl',
        meta: {
          id: routeMap['火山短链'].id,
          type: 2,
          keepAlive: true,
          title: routeMap['火山短链'].name,
        },
        component: VolcanoSmsUrl
      },
      {
        path: '/operator/volcano/volcano-operator',
        name: 'VolcanoOperator',
        meta: {
          id: routeMap['火山运营'].id,
          type: 2,
          title: routeMap['火山运营'].name,
        },
        component: VolcanoOperator
      },
    ]
  },
  {
    path: '/operator/system-manager',
    name: 'SystemManager',
    meta: {
      id: 'common',
      type: 1,
      title: '系统管理',
      icon: 'setting'
    },
    redirect: '/operator/system-manager/industry-manager',
    children: [
      {
        path: '/operator/system-manager/industry-manager',
        name: 'IndustryManager',
        meta: {
          id: routeMap['行业管理'].id,
          type: 2,
          title: routeMap['行业管理'].name,
        },
        component: IndustryManager
      },
      {
        path: '/operator/system-manager/product-manager',
        name: 'ProductManager',
        meta: {
          id: routeMap['产品管理'].id,
          type: 2,
          title: routeMap['产品管理'].name,
        },
        component: ProductManager
      },
    ],
  },
  {
    path: '/operator/system-monitor',
    name: 'SystemMonitor',
    meta: {
      id: 'common',
      type: 1,
      title: '系统监控',
      icon: 'monitor'
    },
    redirect: '/operator/system-monitor/operation-monitor',
    children: [
      {
        path: '/operator/system-monitor/common-operation-monitor',
        name: 'CommonOperationMonitor',
        meta: {
          id: routeMap['系统监控-运营监控'].id,
          type: 2,
          title: routeMap['系统监控-运营监控'].name,
        },
        component: CommonOperationMonitor
      },
      {
        path: '/operator/system-monitor/operational-inspection-manager',
        name: 'OperationalInspectionManager',
        meta: {
          id: routeMap['系统监控-业务巡检'].id,
          type: 2,
          title: routeMap['系统监控-业务巡检'].name,
        },
        component: OperationalInspectionManager
      },
      {
        path: '/operator/system-monitor/operation-monitor',
        name: 'SystemOperationMonitor',
        meta: {
          id: routeMap['系统监控-运行监控'].id,
          type: 2,
          title: routeMap['系统监控-运行监控'].name,
        },
        component: SystemOperationMonitor
      },
      {
        path: '/operator/system-monitor/operation-switch',
        name: 'SystemOperationSwitch',
        meta: {
          id: routeMap['系统监控-运行开关'].id,
          type: 2,
          title: routeMap['系统监控-运行开关'].name,
        },
        component: SystemOperationSwitch
      },
      {
        path: '/operator/system-monitor/authorization-manager',
        name: 'AuthorizationManager',
        meta: {
          id: routeMap['系统监控-权限管理'].id,
          type: 2,
          title: routeMap['系统监控-权限管理'].name,
        },
        component: AuthorizationManager
      },
    ]
  },
  {
    path: '/operator/team-manager',
    name: 'OperatorTeamManager',
    meta: {
      id: 'common',
      type: 1,
      title: '团队管理',
      icon: 'team'
    },
    redirect: '/operator/team-manager/role-management',
    children: [
      {
        path: '/operator/team-manager/account-management',
        name: 'OperatorAccountManagement',
        meta: {
          id: routeMap['账号管理-运营端'].id,
          type: 2,
          title: routeMap['账号管理-运营端'].name,
        },
        component: OperatorAccountManagement,
      },
      {
        path: '/operator/team-manager/role-management',
        name: 'OperatorRoleManagement',
        meta: {
          id: routeMap['角色管理-运营端'].id,
          title: routeMap['角色管理-运营端'].name,
          type: 2,
        },
        component: OperatorRoleManagement,
      },
    ]
  }
]

export const operatorRouterForOut: AppRouteRecordRawT[] = [
  {
    path: "/operator",
    name: "运营端",
    redirect: () => {
      const userStore = useUserStore();
      return userStore.homePage[0]
    },
    meta: {
      title: "运营端",
      id: 'common',
      icon: 'operator',
      type: 0,
    },
  },
  {
    path: '/operator/system-monitor',
    name: 'SystemMonitor',
    meta: {
      id: 'common',
      type: 1,
      title: '系统监控',
      icon: 'monitor'
    },
    redirect: '/operator/system-monitor/operation-monitor',
    children: [
      {
        path: '/operator/system-monitor/common-operation-monitor',
        name: 'CommonOperationMonitor',
        meta: {
          id: routeMap['系统监控-运营监控'].id,
          type: 2,
          title: routeMap['系统监控-运营监控'].name,
        },
        component: CommonOperationMonitor
      },
      {
        path: '/operator/system-monitor/operation-monitor',
        name: 'SystemOperationMonitor',
        meta: {
          id: routeMap['系统监控-运行监控'].id,
          type: 2,
          title: routeMap['系统监控-运行监控'].name,
        },
        component: SystemOperationMonitor
      },
      {
        path: '/operator/system-monitor/operation-switch',
        name: 'SystemOperationSwitch',
        meta: {
          id: routeMap['系统监控-运行开关'].id,
          type: 2,
          title: routeMap['系统监控-运行开关'].name,
        },
        component: SystemOperationSwitch
      },
      {
        path: '/operator/system-monitor/authorization-manager',
        name: 'AuthorizationManager',
        meta: {
          id: routeMap['系统监控-权限管理'].id,
          type: 2,
          title: routeMap['系统监控-权限管理'].name,
        },
        component: AuthorizationManager
      },
    ]
  },
]
export const commonRouter: AppRouteRecordRawT[] = [
  {
    path: '/operator/merchant-manager/merchant-line/detail',
    name: 'MerchantLineDetail',
    meta: {
      id: [routeMap['商户管理'].id, routeMap['火山商户管理'].id, routeMap['供应线路'].id, routeMap['商户线路'].id].join(','),
      type: 3,
      // keepAlive: true,
      // preRoute: '/operator/merchant-manager',
      title: routeMap['商户管理'].name,
    },
    component: MerchantLineDetail
  },
  {
    path: '/operator/supplier-manager/line-supplier/supplier-line/detail',
    name: 'SupplierLineDetail',
    meta: {
      id: [routeMap['线路供应商'].id, routeMap['供应线路'].id, routeMap['商户线路'].id].join(','),
      type: 3,
      // keepAlive: true,
      // preRoute: '/operator/supplier-manager/line-supplier',
      title: routeMap['线路供应商'].name,
    },
    component: SupplierLineDetail
  },
  {
    path: '/operator/merchant-manager/sms-template-detail',
    name: 'SmsTemplateDetail',
    meta: {
      id: [routeMap['商户管理'].id, routeMap['火山商户管理'].id].join(','),
      type: 3,
      title: routeMap['商户管理'].name,
    },
    component: SmsTemplateDetail
  },
  {
    path: '/operator/merchant-manager/sms-Channel-detail',
    name: 'SmsChannelDetail',
    meta: {
      id: [routeMap['商户管理'].id, routeMap['火山商户管理'].id].join(','),
      type: 3,
      title: routeMap['商户管理'].name,
    },
    component: SmsChannelDetail
  },
  {
    path: '/operator/sms-manager/sms-provider/account-edit-detail',
    name: 'SmsAccountEditDetail',
    meta: {
      id: routeMap['短信供应商'].id,
      type: 3,
      title: routeMap['短信供应商'].name,
    },
    component: SmsAccountEditDetail
  },
]
export const asyncRouter = (): AppRouteRecordRawT[] => {
  const { accountType, } = useUserStore()
  const isOutMode  = ['production.manual-call.2', 'production'].includes(import.meta.env.MODE)
  return [
    {
      path: "/",
      name: "首页",
      component: Layout,
      // redirect: '/dashboard',
      meta: {
        title: "首页",
        type: 1,
        id: 'common',
      },
      children: [
        ...commonRouter,
        ...(accountType === 0
          ? (isOutMode ? operatorRouterForOut : operatorRouter)
          : merchantRouter
        ),
      ],
    },
  ]
}
