<template>
<div class="login_content">
    <div class="mask"></div>
    <div class="form-container" v-loading="loading">
      <div class="form-title">
        <SvgIcon class="tw-w-full tw-h-full" name="platform-name" color="#333"/>
      </div>
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginFormRules"
        hide-required-asterisk
      >
        <el-form-item prop="account" label-width="1">
          <div class="tw-flex tw-items-center tw-w-full">
            <el-icon :size="24" color="#969799"><SvgIcon name="user"/></el-icon>
            <span class="line"></span>
            <el-input class="tw-grow" v-model="loginForm.account" v-h5focus placeholder="请输入账户名" clearable input-style="border: 0"/>
          </div>
        </el-form-item>
        <el-form-item prop="password" label-width="1">
          <div class="tw-flex tw-items-center tw-w-full">
            <el-icon :size="24" color="#969799"><SvgIcon name="password" /></el-icon>
            <span class="line"></span>
            <el-input v-model.trim="loginForm.password" v-h5focus placeholder="请输入密码" clearable type="password" show-password input-style="border: 0" @keyup.enter="submit(loginFormRef)"/>
          </div>
        </el-form-item>
      </el-form>
      <el-button type="primary" :loading="loading" class="tw-w-full" @click="submit(loginFormRef)">立即登录</el-button>
      <div class="tips">无论做什么事，只要肯努力奋斗，是没有不成功的。</div>
  </div>


</div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { UserLoginForm } from '@/type/user'
import { useUserStore } from '@/store/user'
import { ElMessage, type FormInstance, } from 'element-plus'
import { copyText, } from '@/utils/utils'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { aiTeamModel } from '@/api/user'
import {asyncRouter} from '@/router/asyncRoute'
import { addRoute, filterRoutes } from '@/router/permission'
import { RoleResponse } from '@/type/user'
import dayjs from 'dayjs'
import { router } from "@/router"
import { useRoute } from 'vue-router'

const route = useRoute();
const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const loginForm = reactive<UserLoginForm>({
  account: '',
  password: '',
})
const userStore = useUserStore()
const loginFormRef = ref<FormInstance>()
const loginFormRules = reactive({
  account: [
    { required: true, message: '请输入账号名', trigger: 'blur' },
    { min: 2, max: 20, message: '账号名长度必须在2-20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 3, max: 20, message: '密码长度必须在8-20个字符', trigger: 'blur' }
  ],
})

const updateUserInfo = async() => {
  const roleInfo = await aiTeamModel.findOneAdminRoleById({id: userStore.roleId!}) as RoleResponse
  const permissions: {[key: string]: string[]} = roleInfo?.authorityMap || {}
  userStore.permissions = permissions
  addRoute(asyncRouter(), permissions ? Object.keys(permissions) : [])
}

const submit = (formEl: FormInstance | undefined)  => {
  if (!formEl) return
  formEl.validate(async (valid) => {
    if (valid) {
      loading.value = true
      await userStore.login(loginForm)
      await updateUserInfo()
      if (userStore.expireTime && dayjs(userStore.expireTime).isBefore(dayjs())) {
        ElMessage.warning('密码已超过半年未修改，请尽快修改密码')
      }
      loading.value = false
      router.push(userStore.accountType === 0 && userStore.roleName!== '超级管理员' ? userStore.homePage[0] : userStore.homePage[1])
    }
  })

}

// 页面处于HTTPS，提示用户手动改成HTTP
// if (location.protocol === 'https:') {
//   Confirm({
//     text: `您输入的网址格式有误，正确地址为${'http://' + location.hostname + location.pathname + location.search}，是否需要为您复制该地址？`,
//     type: 'warning',
//     title: '请修改网址',
//     confirmText: '复制',
//   }).then(() => {
//     copyText('http://' + location.hostname + location.pathname + location.search)
//   }).catch(() => {})
// }

// // 手机端判断
// const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
// const orientation = ref<'portrait' | 'landscape'>('portrait') // 默认手机竖屏
// isMobile && window.addEventListener("resize", () => {
//   orientation.value  = (window.innerWidth > window.innerHeight) ? 'landscape' : 'portrait';
// }, false);

onMounted(async () => {
  const account = route.query.account as string
  const password = route.query.password as string
  if (account && password) {
    await userStore.login({
      account,
      password,
    })
    await updateUserInfo()
    router.push(userStore.accountType === 0 && userStore.roleName!== '超级管理员' ? userStore.homePage[0] : userStore.homePage[1])
  }
})
</script>

<style scoped lang="postcss" type="text/postcss">
.login_content {
  height: 100%;
  width: 100%;
  background-image: url('@/assets/img/login_bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  .mask {
    background: rgba(22, 93, 255, 0.05);
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
  }
  .form-container {
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.75);
    position: absolute;
    left: 11vw;
    top: 20vh;
    width: 55vh;
    height: 60vh;
    padding: 6vh 6vh 2vh;
    @media screen and (max-width: 500px) {
      width: 88vw;
      left: 6vw;
    }
    .form-title {
      width: 32vh;
      height: 9vh;
    }
    .el-form {
      margin-top: 6vh;
      .el-form-item {
        border: 1px solid #E1E3E6;
        background: #fff;
        border-radius: 4px;
        height: 5.5vh;
        padding-left: 12px;
        margin-bottom: 2.5vh;
        .line {
          height: 20px;
          margin-left: 12px;
          width: 1px;
          background-color: #E1E3E6;
        }
      }
      :deep(.el-input__wrapper) {
        border: none;
        box-shadow: none;
      }
      :deep(.el-input__inner) {
        font-size: 2vh;
      }
    }
    .el-button {
      margin-top: 5vh;
      margin-bottom: 5vh;
      background-color: #165DFF;
      height: 5.5vh;
      font-weight: 600;
      letter-spacing: 2px;
      font-size: 16px;
    }
    .el-button--primary {
      border: none;
    }
    .tips {
      color: #868A9C;
      font-size: 12px;
      line-height: 20px;
      width: 100%;
      text-align: center;
      @media screen and (min-width: 1600px) {
        font-size: 14px;
      }
    }
  }
}
</style>
