<template>
  <HeaderBox title="导入记录" />
  <div class="module-container">
    <div class="search-box tw-mb-[16px]">
      <div class="tw-grid tw-grid-cols-5 tw-gap-[8px] tw-border-b-[1px] tw-pb-[8px]">
        <div class="item">
          <el-input
            v-model="searchForm.taskName"
            placeholder="请输入任务名称"
            @keyup.enter="search()"
            clearable
          >
          </el-input>
        </div>
        <div class="item">
          <el-select
            v-model="searchForm.taskType"
            placeholder="请选择任务类型"
            clearable
          >
            <el-option v-for="item in taskTypeOption" :key="item.name" :label="item.name" :value="item.value"/>
          </el-select>
        </div>
        <div class="item">
          <el-select
            v-model="searchForm.importType"
            placeholder="请选择导入类型"
            clearable
          >
            <el-option v-for="item in importTypeOption" :key="item.name" :label="item.name" :value="item.value"/>
          </el-select>
        </div>
        
        <div class="item tw-col-span-2">
          <span class="tw-w-[66px] tw-shrink-0">导入时间：</span>
          <TimePickerBox
            v-model:start="searchForm.importTimeStart"
            splitToday
            v-model:end="searchForm.importTimeEnd"
            :disabledDate="disabledDate"
            :maxRange="60*60*24*31*1000"
            clearable
          />
        </div>
      </div>
      <div class="tw-flex tw-justify-end tw-pt-[8px] tw-items-center tw-h-[32px]">
        <div>
          <el-button type="primary" @click="clearSearchForm" link>
            <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
            <span>重置</span>
          </el-button>
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
    </div>

    <el-table
      :data="tableTempData"
      v-loading="loading"
      class="tw-grow"
      row-key="id"
      :header-cell-style="tableHeaderStyle"
      stripe
    >
      <el-table-column property="taskName" label="任务名称" align="left" width="280" show-overflow-tooltip fixed="left" key="taskName"></el-table-column>
      <el-table-column property="taskType" label="任务类型" align="center" min-width="120">
        <template #default="{ row }">
          {{ findValueInEnum(row.taskType, TaskTypeEnum)||'-' }}
        </template>
      </el-table-column>
      <el-table-column property="importType" label="导入类型" align="center" min-width="120">
        <template #default="{ row }">
          {{ findValueInEnum(row.importType, ImportTypeEnum)||'-' }}
        </template>
      </el-table-column>
      <el-table-column property="importSuccessCount" label="导入成功" align="left" min-width="120">
        <template #default="{ row }">
          {{ formatNumber(row.importSuccessCount) || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="importFailCount" label="导入失败" align="left" min-width="120">
        <template #default="{ row }">
          {{ formatNumber(row.importFailCount) || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="importTime" label="导入时间" align="center" width="160" sortable :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.importTime ? dayjs(row.importTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" width="100" align="right" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="downloadXls(row)">导出失败</el-button>
        </template>
      </el-table-column> -->
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onUnmounted, onMounted, onDeactivated, } from 'vue'
import { ElMessage, } from 'element-plus'
import dayjs from 'dayjs'
import PaginationBox from '@/components/PaginationBox.vue'
import { formatterEmptyData, handleTableSort, findValueInEnum, formatNumber, enum2Options } from '@/utils/utils'
import { ImportSearchModal, ImportRecordItem, ImportTypeEnum, TaskTypeEnum } from '@/type/task'
import { importModel } from '@/api/ai-report'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from 'await-to-js'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { exportExcel } from '@/utils/export'
import TimePickerBox from '@/components/TimePickerBox.vue'

class ImportSearchOrigin {
  taskName = undefined
  taskType = undefined
  importType = undefined
  importTimeStart = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
  importTimeEnd = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
}

const loading = ref(false)

const currentPage = ref(1)
const total = ref(0)
const pageSizeList = [20, 50, 100]
const pageSize = ref(pageSizeList[0])
const tableData = ref<ImportRecordItem[] | null>([])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}

const disabledDate = (time: Date) => {
  const _minTime = dayjs().add(-1, 'month').startOf('day').valueOf()
  const _maxTime = dayjs().endOf('day').valueOf()
  return time.getTime() > _maxTime || time.getTime() < _minTime
}

const orderCol = ref('')
const orderType = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  orderCol.value = params.prop
  orderType.value = params.order
}
const tableTempData = computed(() => {
  const data = handleTableSort(tableData.value || [], orderCol.value, orderType.value)
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

const searchForm = reactive<ImportSearchModal>(new ImportSearchOrigin())
const search = async () => {
  loading.value = true
  const res = await to(importModel.findImportList(searchForm))
  tableData.value = (res[1] || []).sort((a, b) => dayjs(a.importTime).isAfter(dayjs(b.importTime)) ? -1 : 1)
  total.value = tableData.value?.length || 0
  loading.value = false
}

const clearSearchForm = () => {
  Object.assign(searchForm, new ImportSearchOrigin())
}

const downloadXls = async (row: ImportRecordItem) => {
  if (!row || !row.id) return
  loading.value = true
  const res = await to(importModel.findFailById({ batchId: row.id }))
  const data: any[] = (res[1] || []).map((item, index) => {
    const obj = item.text ? JSON.parse(item.text) : {}
    return {
      '手机号': item.phone,
      '姓名': item.name,
      '备注': item.comment,
      '失败原因': item.failReason,
      ...obj,
    }
  }) || []
  if (!data || data?.length < 1) {
    loading.value = false
    return ElMessage.warning('无导入失败数据')
  }
  exportExcel(data,  `【${row.taskName||''}】导入失败记录${row.importTime||''}.xlsx`)
  loading.value = false
}

const taskTypeOption = enum2Options(TaskTypeEnum)
const importTypeOption = enum2Options(ImportTypeEnum)

onMounted(() => {
  search()
})
onUnmounted(() => {
  tableData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.module-container {
  min-width: 1080px;
}
.sms-container {
  width: 100%;
  box-sizing: border-box;
  height: calc(100% - 48px);
  background-color: #fff;
  display: flex;
  flex-direction: column;
}
.el-table {
  font-size: 13px;
}
</style>