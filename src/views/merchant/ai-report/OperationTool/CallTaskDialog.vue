<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">批量启动任务</div>
    </template>
    <el-form
      :model="editData"
      :rules="rules"
      class="tw-py-[12px]"
      label-width="110px"
      ref="editRef"
    >
    <div class="tw-font-[600] tw-text-left tw-mb-[8px]">启动方案</div>
    <el-form-item label="启动范围：">
      <el-radio-group v-model="editData.includeAutoStop">
        <el-radio label="0" class="tw-w-[90px]">默认</el-radio>
        <el-radio label="1" class="tw-w-[90px]">含止损</el-radio>
      </el-radio-group>
    </el-form-item>
    <!-- <el-form-item v-if="props.taskType == TaskTypeEnum['AI外呼']" label="启动时间：">
      <el-radio-group v-model="editData.startTimeType" @change="clearValidate">
        <el-radio label="0" class="tw-w-[90px]">立即执行</el-radio>
        <el-radio label="1" class="tw-w-[90px]">预期开始时间</el-radio>
      </el-radio-group>
    </el-form-item> -->
    <el-form-item v-if="props.taskType == TaskTypeEnum['AI外呼']" label="启动方式：">
      <el-radio-group v-model="editData.startType" @change="clearValidate">
        <el-radio label="0" class="tw-w-[90px]">预期时间</el-radio>
        <el-radio label="1" class="tw-w-[140px]">
          所需并发(平均分配)
        </el-radio>
        <el-radio label="2" class="tw-w-[140px]">
          所需并发(按量分配)
        </el-radio>
      </el-radio-group>
    </el-form-item>
    <div class="tw-font-[600] tw-text-left tw-mb-1">启动配置</div>
    <template v-for="(_,industryItem) of neededNum" :key="industryItem">
      <div class="tw-text-[13px] tw-font-[600] tw-text-[#313233] tw-text-left tw-ml-[2em] tw-mb-[8px]">
        {{ getIndustryStr(industryItem) }}
      </div>
      <el-form-item label="选择线路：" :prop="'lineId' + industryItem">
        <el-select v-model="editData['lineId' + industryItem]" class="tw-flex-grow" placeholder="请选择线路" @change="handleSelectLine(industryItem)">
          <el-option v-for="item in lineListMap.get(industryItem) || []" :key="item.id" :label="`${item.lineName}(剩余${item.lineRemainConcurrent})`" :value="item.id" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item v-if="editData.startTimeType==1 && props.taskType === TaskTypeEnum['AI外呼']" label="预期开始时间：" :prop="'expectedStartTime'+industryItem">
        <el-time-picker v-model="editData['expectedStartTime'+industryItem]" format="YYYY-MM-DD HH:mm:ss" placeholder="预期开始时间" class="tw-grow"/>
      </el-form-item> -->
      <el-form-item v-if="editData.startType==0 && props.taskType === TaskTypeEnum['AI外呼']" label="预期完成时间：" :prop="'expectedFinishTime'+industryItem">
        <el-time-picker v-model="editData['expectedFinishTime'+industryItem]" format="YYYY-MM-DD HH:mm:ss" placeholder="预期完成时间" @change="handleChangeFinishTime(industryItem)" class="tw-flex-grow"/>
        <div class="tw-absolute tw-bottom-[-25px] tw-left-0">
          <el-button v-show="neededNumLoading" :loading="neededNumLoading" type="primary" link>并发计算中</el-button>
          <span v-show="!neededNumLoading && neededNum[industryItem] && neededNum[industryItem]<editData['lineRemainConcurrent' + industryItem]" class="tw-text-[12px] tw-text-[#13BF77] tw-flex tw-items-center">
            <el-icon><CircleCheckFilled /></el-icon>
            {{ `并发充足，约需${neededNum[industryItem] || 0}， 线路剩余${editData['lineRemainConcurrent'+industryItem] || 0}` }}
          </span>
        </div>
      </el-form-item>
      <el-form-item v-if="!!showConcurrent(industryItem)" label="所需并发：" :prop="'concurrent'+industryItem">
        <el-input-number
          v-model="editData['concurrent'+industryItem]"
          :precision="0"
          :controls="false"
          style="width:100%"
          :placeholder="`请输入所需并发，范围${editData['taskIds'+industryItem]?.split(',')?.length || 1}-${editData['lineRemainConcurrent'+industryItem]}`"
          clearable
          :min="editData['taskIds'+industryItem]?.split(',')?.length || 1"
          :max="editData['lineRemainConcurrent'+industryItem]"
        />
      </el-form-item>
    </template>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible=false" :icon="CloseBold">取消</el-button>
        <el-button type="primary" :loading="loading" @click="confirm" :icon="Select">执行</el-button>
      </span>
    </template>
  </el-dialog>
  <ProcessDialog
    v-model:visible="resultProcessVisible"
    title="批量启动结果"
    :successNumber="successNumber"
    :totalNumber="fileList?.length||0"
    :processInfo="resultProcessInfo"
  >
  <template #btn>
    <el-button type="primary" link @click="downloadFail" class="tw-underline tw-underline-offset-2">导出失败任务</el-button>
  </template>
  </ProcessDialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, } from 'vue';
import { TaskBatchItem, TaskStatusEnum, TaskTypeEnum } from '@/type/task'
import { CloseBold, Select, } from '@element-plus/icons-vue'
import { aiOutboundTaskModel } from '@/api/ai-report'
import dayjs from 'dayjs'
import { MerchantLineInfo } from '@/type/merchant'
import { ElMessage, } from 'element-plus'
import to from 'await-to-js';
import ProcessDialog from '@/components/ProcessDialog.vue'
import { merchantModel } from '@/api/merchant'
import { useUserStore } from '@/store/user'
import { CircleCheckFilled } from '@element-plus/icons-vue'
import { trace } from '@/utils/trace';

const emits = defineEmits(['close', 'confirm', 'export'])
type CallItem = {
  taskIds?: string,
  scriptId?: number,
  callStatus: TaskStatusEnum,
  speechCraftNames?: string[]
  lineId?: number,
  lineName?: string,
  lineCode?: string,
  concurrent?: number,
  totalConcurrent?: number,
  lineRemainConcurrent?: number,
  expectedFinishTime?: string,
  includeAutoStop?: string,
  startType?: string, // 启动方式
}
const rules = reactive({})
const neededNumLoading = ref(false)
const loading = ref(false)
const userInfo  = useUserStore()
const props = defineProps<{
  tableData: TaskBatchItem[]
  visible: boolean,
  taskType: TaskTypeEnum,
}>();

const dialogVisible = ref(props.visible)
const editData = reactive<Record<string, any>>({})
const editRef = ref()
const cancel = () => {
  dialogVisible.value = false
  emits('close')
}

const showConcurrent = (industryItem: string) : boolean => {
  return editData.startType !== '0'
  && editData['lineId' + industryItem]
  && props?.taskType !== TaskTypeEnum['人机协同']
  && editData['taskIds'+industryItem]?.split(',')?.length <= editData['lineRemainConcurrent'+industryItem]
}
// 校验所有行业下任务的批量启动参数是否满足要求
// 对所有任务行业执行失败的进行弹窗提示，并支持下载
const resultProcessVisible = ref(false)
const resultProcessInfo = ref<{ name: string, remark?: string }[]>([])
const successNumber = ref(0)
const fileList = ref<TaskBatchItem[]>([])
const totalList = ref<TaskBatchItem[]>([])
const neededNum = reactive<Record<string, number>>({})
const lineListMap = reactive<Map<string, MerchantLineInfo[]>>(new Map([]))

/** 选择线路 */
const handleSelectLine = (industry: string) => {
  const line = lineListMap.get(industry)?.find(item => item.id === editData['lineId' + industry])
  if (line) {
    editData['lineCode' + industry] = line.lineNumber
    editData['lineName' + industry] = line.lineName
    editData['lineRemainConcurrent' + industry] = line.lineRemainConcurrent as number
    editData['concurrent' + industry] = props.taskType === TaskTypeEnum['人机协同'] ? 0 : undefined
    if (editData.startType !== '0' || props.taskType === TaskTypeEnum['人机协同']) return
    editRef.value?.validateField('expectedFinishTime' + industry)
  }
}
/** 选择时间 */
const handleChangeFinishTime = (industry: string) => {
  editData['expectedFinishTime' + industry] = editData['expectedFinishTime' + industry] ? dayjs(editData['expectedFinishTime' + industry]).format('YYYY-MM-DD HH:mm:ss') : undefined
}

const confirm = async () => {
  fileList.value = []
  totalList.value = []
  successNumber.value = 0
  resultProcessInfo.value = []
  const res = await editRef.value?.validate()
  if (!res) return
  loading.value = true
  await to(Promise.all(
    Object.keys(neededNum).map(async item => {
      const params: CallItem = {
        taskIds: editData['taskIds' + item],
        callStatus: editData['callStatus' + item],
        lineId: editData['lineId' + item],
        lineName: editData['lineName' + item],
        lineCode: editData['lineCode' + item],
        lineRemainConcurrent: editData['lineRemainConcurrent' + item],
        includeAutoStop: editData.includeAutoStop == '0' ? undefined : '1',
      }
      if (props.taskType === TaskTypeEnum['人机协同']) {
        params.concurrent = 0
        params.totalConcurrent = undefined
        params.expectedFinishTime = undefined
      } else if (editData.startType == '0' ) {
        params.concurrent = undefined
        params.totalConcurrent = undefined
        params.expectedFinishTime = editData['expectedFinishTime' + item] ? dayjs(editData['expectedFinishTime' + item]).format('YYYY-MM-DD HH:mm:ss') : undefined
      } else if (editData.startType == '1' ) {
        params.concurrent = Math.floor(editData['concurrent' + item] / (editData['taskIds' + item]?.split(',')?.length||1))
        params.totalConcurrent = undefined
        params.expectedFinishTime = undefined
      } else if (editData.startType == '2' ) {
        params.concurrent = undefined
        params.totalConcurrent = editData['concurrent' + item]
        params.expectedFinishTime = undefined
      }
      if (params.taskIds && params.lineId && (
          ((editData.startType == '0' && params.expectedFinishTime)
          || (editData.startType == '1' && params.concurrent) && props.taskType === TaskTypeEnum['AI外呼']
          || (editData.startType == '2' && params.totalConcurrent) && props.taskType === TaskTypeEnum['AI外呼'])
          || props.taskType === TaskTypeEnum['人机协同']
        )
      ) {
        await trace({
          page: `外呼工具-批量启动-${editData['taskIds' + item]?.split(',')?.length}个任务：开始`,
          params: params,
        })
        const [err, res] = await to(
          props.taskType === TaskTypeEnum['人机协同']
          ? aiOutboundTaskModel.batchStartMixTask(params as Required<CallItem>)
          : aiOutboundTaskModel.batchStartAiTask(params as Required<CallItem>)
        ) as [any, TaskBatchItem[]]
        res?.forEach(taskItem => {
          if (taskItem.batchStatus === '执行失败') {
            resultProcessInfo.value.push({
              name: taskItem.taskName || '',
              remark: '执行失败'
            })
          } else {
            successNumber.value++
          }
          fileList.value.push(taskItem)
        })
        res && totalList.value.push(...res)
        trace({
          page: `外呼工具-批量启动-${editData['taskIds' + item]?.split(',')?.length}个任务：完成`,
          params: err || (resultProcessInfo.value?.length > 0 ? resultProcessInfo.value?.slice(0, 60) : ''),
        })
      } else {
        ElMessage({
          type: 'warning',
          message: `已忽略行业${item}的任务的启动`
        })
      }
    })
  ))
  loading.value = false
  emits('close')
  emits('confirm', totalList.value)
  
  if (resultProcessInfo.value?.length > 0) {
    resultProcessVisible.value = true
  } else if (successNumber.value !== props.tableData?.length) {
    ElMessage.warning(`共计启动${successNumber.value}个任务，与执行任务数不同，请知悉`)
  }
}
const downloadFail = () => {
  const data = fileList.value?.filter(item => item.batchStatus === '执行失败') || []
  data && data.length > 0 && emits('export', data, '导出启动失败任务.xlsx')
}
const init = async () => {
  for(let p in editData) {
    delete editData[p]
  }
  if (props.tableData?.length > 0) {
    const unknownList: string[] = []
    props.tableData.map(async item => {
      if (item.secondaryIndustry && editData['taskIds' + item.secondaryIndustry]) {
        editData['taskIds' + item.secondaryIndustry] = editData['taskIds' + item.secondaryIndustry] + ',' + item.id
        if (item.speechCraftName && !editData['speechCraftNames' + item.secondaryIndustry]?.includes(item.speechCraftName)) {
          editData['speechCraftNames' + item.secondaryIndustry]?.push(item.speechCraftName)
        }
      } else {
        if (item.secondaryIndustry) {
          neededNum[item.secondaryIndustry] = 0
          Object.assign(editData, {
            ['taskIds' + item.secondaryIndustry]: item.id + '',
            ['callStatus' + item.secondaryIndustry]: TaskStatusEnum['进行中'],
            ['scriptId' + item.secondaryIndustry]: item.speechCraftId,
            ['speechCraftNames' + item.secondaryIndustry]: [item.speechCraftName!],
            ['lineId' + item.secondaryIndustry]: undefined,
            ['lineName' + item.secondaryIndustry]: undefined,
            ['lineCode' + item.secondaryIndustry]: undefined,
            ['lineRemainConcurrent' + item.secondaryIndustry] : undefined,
            ['expectedFinishTime' + item.secondaryIndustry]: undefined,
            ['concurrent' + item.secondaryIndustry]: undefined,
          })
          Object.assign(rules, {
            ['lineId' + item.secondaryIndustry]: [
              { required: true, message: '请选择线路', trigger: 'change' },
              { validator: 
                  (rule: any, value: any, callback: any) => validLine(rule, value, callback, item.secondaryIndustry!),
                trigger: ['change']
              },
            ],
            ['expectedStartTime' + item.secondaryIndustry]: [
              { required: true, message: '请选择预期开始时间', trigger: 'change' },
              { validator: 
                  (rule: any, value: any, callback: any) => validExpectedStartTime(rule, value, callback, item.secondaryIndustry!),
                trigger: ['change'] },
            ],
            ['expectedFinishTime' + item.secondaryIndustry]: [
              { required: true, message: '请选择预期完成时间', trigger: 'change' },
              { validator: 
                  (rule: any, value: any, callback: any) => validExpectedFinishTime(rule, value, callback, item.secondaryIndustry!),
                trigger: ['change'] },
            ],
            ['concurrent' + item.secondaryIndustry]: [
              { required: true, message: '请输入所需并发', trigger: 'blur' },
            ],
          })
          // 更新线路
          if (!editData['scriptId' + item.secondaryIndustry]) {
            return ElMessage({
              type: 'error',
              message: '获取线路失败！'
            })
          }
          const data = await merchantModel.getMerchantLineListById({
            groupId: userInfo.groupId || '',
            scriptId: editData['scriptId' + item.secondaryIndustry!],
            lineType: 'AI_OUTBOUND_CALL'
          }) as MerchantLineInfo[]
          lineListMap.set(item.secondaryIndustry!, data.filter(item => item.enableStatus === 'ENABLE') || [])
        } else {
          unknownList.push(item.taskName || item.id + '')
        }
      }
    })
    if (unknownList.length > 0) {
      ElMessage.warning(`${unknownList.join(',')}未找到任务对应行业，请刷新后重试！`)
      cancel()
      return
    }
  }
  editData.includeAutoStop = '0'
  editData.startTimeType = '0'
  editData.startType = '0'
}

const validLine = async (rule: any, value: any, callback: any, industry: string) => {
  if (editData.startType === '1' && editData['lineRemainConcurrent'+industry] < (editData['taskIds'+industry]?.split(',')?.length || 1)) {
    return callback(new Error('当前线路剩余并发小于任务数，无法分配并发，请重新选择'))
  }
  return callback()
}
const validExpectedStartTime = async (rule: any, value: any, callback: any, industry: string) => {
  if (!editData['expectedStartTime'+industry]) {
    return callback(new Error(`请选择预期开始时间`))
  }
  if (dayjs(editData['expectedStartTime'+industry]).isBefore(dayjs()) || dayjs(editData['expectedStartTime'+industry]).isAfter(dayjs().add(4, 'hours'))) {
    return callback(new Error(`预期开始时间必须在当前时间之后的4个小时内`))
  }
  return callback()
}
const validExpectedFinishTime = async (rule: any, value: any, callback: any, industry: string) => {
  // 预期完成时间必填
  if (!editData['expectedFinishTime'+industry]) {
    return callback(new Error(`请选择预期完成时间`))
  }
  // 定时启动时，预期完成时间必须在开始时间之后
  if (editData.startTimeType === '1' && dayjs(editData['expectedFinishTime'+industry]).isBefore(dayjs(editData['expectedStartTime'+industry]))) {
    return callback(new Error(`预期完成时间必须在预期开始时间之后`))
  }
  // 未选择线路时，无需接口获取预计并发，进行相关校验
  if (!editData['lineCode'+industry]) {
    return callback()
  }
  // 选择线路时，接口获取预计并发，进行相关校验
  neededNumLoading.value = true
  const [_, num] = await to(aiOutboundTaskModel.getNeededConcurrent({
    taskIds: editData['taskIds'+industry]!,
    // 如果是定时启动，需要传预期开始时间
    expectedStartTime: editData.startTimeType === '1' ? editData['expectedStartTime'+industry] || undefined : undefined,
    expectedFinishTime: dayjs(editData['expectedFinishTime'+industry]).format('YYYY-MM-DD HH:mm:ss'),
  }))
  neededNumLoading.value = false
  neededNum[industry] = num || 0
  if (!num) {
    return callback(new Error(`预期完成时间过近，请重新选择`))
  }
  
  if (num && editData['lineRemainConcurrent'+industry] &&  num <= editData['lineRemainConcurrent'+industry]) {
    return callback()
  } else {
    return callback(new Error(`并发不足，约需${num}， 线路剩余${editData['lineRemainConcurrent'+industry] || 0}`))
  }
}
const getIndustryStr = (item: string) => {
  return item + '：' + editData['speechCraftNames' + item]?.join('、')
}

const clearValidate = () => {
  editRef.value?.clearValidate()
}

watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    init()
    clearValidate()
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-radio__label) {
  display: flex;
  align-items: center;
}
</style>
