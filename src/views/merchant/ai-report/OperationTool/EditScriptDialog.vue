<template>
  <el-dialog
    v-model="dialogVisible"
    width="640px"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">批量修改话术</div>
    </template>
    <el-form
      :model="editData"
      :rules="rules"
      class="tw-py-[12px]"
      label-width="110px"
      ref="editRef"
    >
      <el-form-item label="执行话术：" prop="scriptStringId">
        <el-select v-model="scriptSelectType" class="tw-w-[120px] tw-mr-[6px]" placeholder="请选择类型" @change="handleSelectTypeChange()">
          <el-option label="执行话术" :value="0" />
          <el-option label="执行模板" :value="1" />
        </el-select>
        <el-select v-if="scriptSelectType===0" v-model="editData.scriptStringId" class="tw-grow" placeholder="请选择执行话术" filterable clearable>
          <el-option v-for="item in speechCraftList" :key="item.scriptStringId" :label="item.scriptName" :value="item.scriptStringId" />
        </el-select>
        <el-select v-if="scriptSelectType===1" v-model="editData.scriptStringId" class="tw-grow" placeholder="请选择执行模板" filterable clearable @change="handleSelectTemplateChange()">
          <el-option v-for="item in taskTemplateList" :key="item.id" :value="item.id" :label="`${item.id}-${item.speechCraftName}`">
            <div class="tw-flex tw-items-center tw-w-full tw-justify-between">
              <span>{{ item.id }}</span>
              <span class="info-title">{{ item.speechCraftName }}</span>
            </div>
          </el-option>
          <template #label="{ label, value }">
            <div class="tw-flex tw-items-center tw-w-full tw-justify-between">
              <span>{{ value + '-' }}</span>
              <span class="info-title">{{ label }}</span>
            </div>
          </template>
        </el-select>
      </el-form-item>
      <div class="tw-text-[13px] tw-text-[#E54B17] tw-font-[600] tw-pl-[30px]">
        注意：该操作不会修改短信配置，如涉及触发短信和挂机短信，请从【批量编辑】中执行修改话术！
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible=false" :icon="CloseBold">取消</el-button>
        <el-button type="primary" :loading="loading" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, nextTick } from 'vue';
import { TaskBatchItem, TemplateBaseItem, TaskTypeEnum } from '@/type/task'
import { aiOutboundTaskModel } from '@/api/ai-report'
import { CloseBold, Select } from '@element-plus/icons-vue'
import to from 'await-to-js';
import type { FormInstance, } from 'element-plus'
import { merchantModel } from '@/api/merchant'
import { useUserStore } from '@/store/user'
import { aiOutboundTaskTemplateModel } from '@/api/ai-report'
import { MerchantScriptInfo, MerchantInfo, } from '@/type/merchant'
import { ElMessage, } from 'element-plus'
import { trace } from '@/utils/trace';

const emits = defineEmits(['update:visible', 'confirm', 'export'])
const loading = ref(false)
const props = defineProps<{
  taskIds: string,
  visible: boolean,
  taskType: TaskTypeEnum,
}>();

const dialogVisible = ref(props.visible)
const rules = {
  scriptStringId: [
    { required: true, message: '请选择执行话术', trigger: ['change', 'blur'] },
  ],
}
class EditOrigin  {
  speechCraftName = undefined
  speechCraftId = undefined
  scriptStringId = undefined
  version = undefined
  taskIds = undefined
  taskType =  TaskTypeEnum['AI外呼']
}
const editData = reactive<Partial<{
  taskType: TaskTypeEnum,
  speechCraftName: string,
  speechCraftId: number,
  scriptStringId: string,
  version: number,
  taskIds: string
}>>(new EditOrigin())
  const editRef = ref<FormInstance  | null>(null)
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

const speechCraftAllList = ref<MerchantScriptInfo[] | null>([])
const speechCraftMixList = ref<MerchantScriptInfo[] | null>([])
const taskTemplateList = ref<TemplateBaseItem[] | null>([])
  const speechCraftList = computed(() => props.taskType === TaskTypeEnum['人机协同'] ? speechCraftMixList.value : speechCraftAllList.value)

// 批量操作，选择话术类型切换
const scriptSelectType = ref(0) // 0：执行话术 1：执行模板
const scriptTemplateId = ref<number | undefined>(undefined)
const handleSelectTypeChange = () => {
  editData.scriptStringId = undefined
  scriptTemplateId.value = undefined
}
const handleSelectTemplateChange = async () => {
  const row = taskTemplateList.value?.find(item => item.id === scriptTemplateId.value)
  if (row) {
    editData.scriptStringId = row.scriptStringId
    editRef.value?.validate()
  }
}

const confirm = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      const script = speechCraftList.value?.find(item => item.scriptStringId == editData.scriptStringId)
      editData.speechCraftName = (script?.scriptName || '') as string
      editData.speechCraftId = (script?.scriptId ?? -1) as number
      editData.version = (script?.version  ?? -1) as number
      loading.value = true
      await trace({
        page: `外呼工具-批量修改话术-${props.taskIds?.split(',')?.length}个任务：开始`,
        params: editData,
      })
      const res = await to(aiOutboundTaskModel.batchEditScript(editData))
      if (res[0]) {
        emits('confirm', '执行失败')
        trace({
          page: `外呼工具-批量修改话术-${props.taskIds?.split(',')?.length}个任务：完成`,
          params: res[0]
        })
      } else {
        const statusMap = new Map<number, string>([])
        res[1]?.map(item => {
          statusMap.set(item.id, item.batchStatus)
        })
        emits('confirm', statusMap)
        ElMessage.success('【批量修改话术】成功')
        trace({
          page: `外呼工具-批量修改话术-${props.taskIds?.split(',')?.length}个任务：完成`,
          params: (res[1]?.flatMap(item => item?.batchStatus !== '执行成功' ? [{ id: item?.id, batchStatus: item?.batchStatus }] : [])?.slice(0, 60) || ''),
        })
      }
      cancel()
      loading.value = false
    }
  })
  
}

const init = async () => {
  loading.value = true
  Object.assign(editData, new EditOrigin())
  editData.taskIds = props.taskIds
  editData.taskType = props.taskType
  const { groupId, tenantId } = useUserStore()
  const [err1, data1] = await to(merchantModel.getScriptList({ groupId: groupId || '', id: tenantId || undefined })) as [any, MerchantInfo]
  speechCraftAllList.value = ((data1||[]).relatedScriptList?.filter(item => item.active == 'ACTIVE') as MerchantScriptInfo[]) || []
  const [err2, data2] = await to(merchantModel.getManualScriptList({ groupId: groupId || '', id: tenantId || undefined })) as [any, MerchantInfo]
  speechCraftMixList.value  = ((data2||[]).relatedScriptList?.filter(item => item.active == 'ACTIVE') as MerchantScriptInfo[]) || []
  taskTemplateList.value = await aiOutboundTaskTemplateModel.search({
    taskType: props.taskType,
  }) || []
  loading.value = false
  clearValidate()
}



const clearValidate = () => {
  editRef.value?.clearValidate()
}

watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    init()
    
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>

</style>
