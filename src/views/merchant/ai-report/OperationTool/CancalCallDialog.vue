<template>
  <el-dialog
    v-model="dialogVisible"
    width="540px"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">批量取消呼叫</div>
    </template>
    <el-form
      :model="editData"
      :rules="rules"
      class="tw-pb-[12px]"
      label-width="120px"
      ref="editRef"
    >
      <el-form-item label="取消呼叫范围：" prop="cancalRange">
        <el-checkbox-group v-model="editData.cancalRange" class="tw-ml-1">
          <el-checkbox :label="0">首呼队列</el-checkbox>
          <el-checkbox :label="1">补呼队列</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <div class="tw-text-[--primary-red-color] tw-text-left tw-text-[13px] tw-ml-[24px] tw-font-[600]">注意：如取消呼叫的号码较多，请耐心等待完成后再执行后续操作</div>

    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible=false" :icon="CloseBold">取消</el-button>
        <el-button type="primary" :loading="loading" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, } from 'vue';
import { CloseBold, Select, } from '@element-plus/icons-vue'
import { aiOutboundTaskModel } from '@/api/ai-report'
import { ElMessage, } from 'element-plus'
import to from 'await-to-js';
import { useUserStore } from '@/store/user'
import { trace } from '@/utils/trace';

const emits = defineEmits(['update:visible', 'confirm',])
const rules = {
  cancalRange: [
    { required: true, message: '请选择取消呼叫范围', trigger: ['change'] },
  ]
}
const loading = ref(false)
const userInfo  = useUserStore()
const props = defineProps<{
  taskIds: number[],
  visible: boolean,
}>();

const dialogVisible = ref(props.visible)
const editData = reactive({
  cancalRange: [0, 1],
})
const editRef = ref()
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

const confirm = async () => {
  const res = await editRef.value?.validate()
  if (!res) return
  loading.value = true
  const errMsg = []
  trace({
    page: `外呼工具-批量取消呼叫-${props.taskIds?.length}个任务：开始`,
    params: {
      taskIds: props.taskIds,
      cancalRange: editData.cancalRange,
    },
  })
  if (editData.cancalRange.includes(0)) {
    const [err] = await to(aiOutboundTaskModel.batchCancelFirstCallPhoneList(props.taskIds))
    console.log(err);
    
    if (err) errMsg.push(err)
  }
  if (editData.cancalRange.includes(1)) {
    const [err] = await to(aiOutboundTaskModel.batchCancelReCallPhoneList(props.taskIds))
    if (err) errMsg.push(err)
  }
  loading.value = false
  trace({
    page: `外呼工具-批量取消呼叫-${props.taskIds?.length}个任务：完成`,
    params: errMsg,
  })
  if (errMsg.length) return
  ElMessage.success('操作成功')
  cancel()
  emits('confirm')
}

const clearValidate = () => {
  editRef.value?.clearValidate()
}

watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    clearValidate()
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-radio__label) {
  display: flex;
  align-items: center;
}
</style>
