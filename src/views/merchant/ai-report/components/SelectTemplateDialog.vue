<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ title }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <div class="tw-flex tw-flex-col tw-items-center">
        <div class="tw-w-full tw-flex tw-justify-between tw-text-[14px] tw-my-1">
          <span class="tw-font-semibold">任务模板</span>
          <el-button type="primary" @click="addSelfTask" link>自定义创建任务</el-button>
        </div>
        <div class="tw-flex tw-items-center tw-mb-2">
          <span>搜索模板：</span>
          <el-input
            v-model.trim="searchForm.name"
            style="width:460px"
            placeholder="请输入话术名称/模板名称"
            clearable
            @keyup.enter="search"
            @blur="search"
            :suffix-icon="Search"
          >
          </el-input>
        </div>
        <div
          v-for="item in taskTemplateList"
          :key="item.id"
          class="task-item"
          :class="[{'task-item-active':item.id===selectTaskTemplate.id}]"
          @click="selectTemplate(item)"
          @dblclick="confirm"
        >
          <el-tooltip
            effect="dark"
            :content="item.comment || ''"
            class="tw-absolute tw-right-1 tw-top-1"
          >
            <el-icon class="btn-box" :size="16"><SvgIcon name="help" /></el-icon>
          </el-tooltip>
          <div class="task-content tw-pr-3">
            <span class="label">模板ID：</span>
            <span class="tw-font-semibold value">{{ item.id }}</span>
          </div>
          <div class="task-content tw-pr-3">
            <span class="label">模板名称：</span>
            <span class="tw-font-semibold value">{{ item.templateName }}</span>
          </div>
          <div class="task-content">
            <span class="label">模板类型：</span>
            <span class="value">{{ findValueInEnum(item.taskType, TaskTypeEnum)||'-' }}</span>
          </div>
          <div class="task-content">
            <span class="label">执行话术：</span>
            <span class="value">{{ item.speechCraftName }}</span>
          </div>
          <div class="task-content">
            <span class="label">拨打时段：</span>
            <span class="value"><TagsBox v-if="item.startWorkTimeList && item.endWorkTimeList" :tagsArr="concatTimeList(item.startWorkTimeList, item.endWorkTimeList)" tagsName="拨打时段" :tagsNum="4" type="" effect="light"></TagsBox></span>
          </div>
          <div class="task-content tw-grid tw-grid-cols-2">
            <div class="tw-flex tw-w-1/2">
              <span class="label">补呼：</span>
              <span class="value">{{ item.autoReCall ? (item.secondRecallTime ? '2次' : '1次') : '关闭' }}</span>
            </div>
            <div class="tw-flex tw-w-1/2">
              <span class="label">补呼间隔：</span>
              <span class="value">{{ item.autoReCall ? (item.firstRecallTime || '-') + '/' + (item.secondRecallTime || '-') : '-/-'  }}</span>
            </div>
          </div>
        </div>
      <div class="tw-my-0.5 add-item" @click="addTemplate"></div>
      </div>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button  @click="addTemplate" type="primary" link>
          <el-icon class="tw-mr-0.5" :size="16"><SvgIcon name="add2" color="inherit"/></el-icon>
          创建任务模板
        </el-button>
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button type="primary" @click="confirm" :icon="Select">选择</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, } from 'vue'
import { TemplateBaseItem, TaskTypeEnum, TaskManageOrigin } from '@/type/task'
import { Search, } from '@element-plus/icons-vue'
import { CloseBold, Select, } from '@element-plus/icons-vue'
import { ElMessage, dayjs, } from 'element-plus'
import router from '@/router'
import { useGlobalStore } from '@/store/globalInfo'
import { useTaskStore } from '@/store/taskInfo'
import { aiOutboundTaskTemplateModel } from '@/api/ai-report'
import { storeToRefs } from 'pinia'
import TagsBox from '@/components/TagsBox.vue'
import { findValueInEnum } from '@/utils/utils'
const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  taskType: TaskTypeEnum
}>();
const taskStore = useTaskStore()
const title = computed(() => {
  return props.taskType===TaskTypeEnum['人机协同'] ? '选择人机协同任务模板' : '选择AI外呼任务模板'
})
const searchForm = reactive<{name?: string, taskType?: TaskTypeEnum}>({
  name: '', taskType: undefined
})
const dialogVisible = ref(props.visible)
const taskTemplateList = ref<TemplateBaseItem[]>([])
const concatTimeList = (staArr: string[], endArr: string[]) => {
  if (!staArr || staArr.length < 1 || staArr.length !== endArr.length ) return []
  return staArr.map((item, index) => {
    return item + '-' + endArr[index]
  })
}
const selectTaskTemplate = reactive(new TaskManageOrigin(TaskTypeEnum['AI外呼']))
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const addSelfTask = () => {
  dialogVisible.value = false
  emits('confirm', null, true)
}
const selectTemplate = (item: TemplateBaseItem) => {
  Object.assign(selectTaskTemplate, item)
}
const confirm = () => {
  dialogVisible.value = false
  emits('confirm', selectTaskTemplate, true)
}
const addTemplate = () => {
  router.push({name: 'TemplateManagement'})
}
const search = async () => {
  loading.value = true
  try {
    const data = await aiOutboundTaskTemplateModel.search({
      taskType: searchForm.taskType
    }) as TemplateBaseItem[] || []
    taskTemplateList.value = data?.filter(item => {
      return (!searchForm.name || item.templateName?.includes(searchForm.name) || item.speechCraftName?.includes(searchForm.name))
      && (item.templateStatus == 0 || !item.templateStatus)
    })?.sort((a,b) => dayjs(a.updateTime).isAfter(dayjs(b.updateTime)) ? -1 : 1) || []
    if (taskTemplateList.value.length > 0) {
      Object.assign(selectTaskTemplate, selectTaskTemplate.id ? selectTaskTemplate : taskTemplateList.value[0])
    }
  } catch(err) {
    ElMessage({
      message: '获取任务模板列表失败',
      type: 'error',
    })
  }
  loading.value = false
}
watch(props, () => {
  if (props.visible) {
    dialogVisible.value = props.visible
    searchForm.taskType = props.taskType
    Object.assign(selectTaskTemplate, new TaskManageOrigin(TaskTypeEnum['AI外呼']))
    search()
  }
}, {
  deep: true,
  immediate: true,
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.dialog-form {
  .task-item {
    color: var(--primary-black-color-400);
    border-radius: 4px;
    margin-bottom: 16px;
    font-size: 13px;
    position: relative;
    cursor: pointer;
    box-sizing: border-box;
    border: 2px solid #f5f5f5;
    width: 460px;
    height: 146px;
    padding: 8px;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    align-items: self-start;
    justify-content: space-between;
    .task-content {
      display: flex;
      width: 100%;
      .label {
        width: 66px;
        text-align: right;
        flex-shrink: 0;
      }
      .value {
        overflow-x: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex-grow: 1;
        text-align: left;
        color: var(--primary-black-color-600);
      }
    }
    .btn-box {
      position: absolute;
      right: 10px;
      top: 10px;
      color: var(--el-color-primary);
    }
    &:hover{
      border: 2px solid #409eff;
      background-color: #fff;
    }
  }
  .task-item-active {
    background-color: #fff;
    border: 2px solid var(--el-color-primary);
  }
}
</style>
