<template>
  <HeaderBox v-if="!props.groupId" title="任务模板" />
  <div class="module-container" :style="!props.groupId ? {minWidth: '1080px'}: {padding: '12px 0'}" v-loading="loading">
    
    <div class="tw-w-full tw-p-[16px] tw-bg-white tw-rounded-[8px] tw-mb-[12px] tw-flex-grow-0 tw-flex tw-items-center">
      <div class="item">
        <el-input
          v-model.trim="searchForm.name"
          style="width:240px"
          placeholder="请输入话术名称/模板名称"
          clearable
          @keyup.enter="search"
        >
        </el-input>
      </div>
      <div class="item">
        <el-select v-model="searchForm.taskType" style="width:240px" placeholder="任务类型" @change="search" clearable>
          <el-option v-for="item in taskTypeOption" :label="item.name" :value="item.value" :key="item.value"/>
        </el-select>
      </div>
      <el-button type="primary" @click="search" link>
        <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
        <span>查询</span>
      </el-button>
    </div>
    <div class="tw-flex tw-items-center tw-justify-between tw-w-full tw-mb-[12px]">
      <el-radio-group v-model="searchForm.templateStatus" @change="search">
        <el-radio-button :label="0">启用</el-radio-button>
        <el-radio-button :label="1">停用</el-radio-button>
      </el-radio-group>
      <div class="tw-flex tw-items-center tw-grow-0">
        <el-button v-if="!props.readonly" type="primary" class="tw-w-[92px] tw-h-[32px]" @click="add()" :icon="Plus">新建模板</el-button>
        <CardListRadioBox v-model:active="listType" class="tw-ml-[8px]"/>
      </div>
    </div>
    <el-scrollbar v-if="listType === 'card'" class="tw-flex-grow tw-pb-[16px]">
      <ul class="tw-grid tw-grid-cols-4 2xl:tw-grid-cols-6 tw-gap-1 tw-overflow-y-auto">
        <li v-for="item in tableData" :key="item.id" class="box">
          <div class="tw-mb-[4px] tw-text-[14px] tw-text-[#313233] tw-font-[600] tw-px-[12px] tw-flex tw-items-center tw-w-full tw-leading-[18px]">
            <span class="tw-grow tw-shrink tw-line-clamp-2 tw-text-justify">{{ item.templateName }}</span>
            <el-icon v-if="!!item.nextDayCall" class="tw-ml-[4px] tw-shrink-0" :size="16" color="#165DFF"><SvgIcon name="nextday"/></el-icon>
            <el-icon v-if="(item?.hangUpSms && item?.hangUpSms?.length > 0) || (item?.scriptSms && item?.scriptSms?.length > 0)" class="tw-ml-[4px] tw-shrink-0" :size="16" color="#165DFF"><SvgIcon name="trigger-sms"/></el-icon>
            <el-icon v-if="item?.taskType === TaskTypeEnum['人机协同']" class="tw-ml-[4px] tw-shrink-0" :size="16" color="#165DFF"><SvgIcon name="trigger-human"/></el-icon>
          </div>
          <span class="items">
            <span class="label">模板编号：</span>
            <span class="value static">{{ item.id }}</span>
          </span>
          <span class="items items-2">
            <span class="label">任务名称：</span>
            <span class="tw-grow tw-line-clamp-2 tw-text-left tw-break-all">{{ item.taskName || '-' }}</span>
          </span>
          <span class="items items-2">
            <span class="label">执行话术：</span>
            <span class="tw-grow tw-text-left tw-break-all tw-line-clamp-2">{{ item.speechCraftName || '-' }}</span>
          </span>
          <span class="items">
            <span class="label">拨打时段：</span>
            <span class="tw-flex tw-flex-1 tw-overflow-hidden tw-h-[22px]">
              <TagsBox v-if="item.startWorkTimeList && item.endWorkTimeList" :tagsArr="concatTimeList(item.startWorkTimeList, item.endWorkTimeList)" tagsName="拨打时段" :tagsNum="2"></TagsBox>
            </span>
          </span>
          <span class="items">
            <span class="label">自动补呼：</span>
            <span class="value">{{ item.autoReCall ? '开启' : '关闭' }}</span>
            <span class="label">间隔：</span>
            <span class="value">{{ item.autoReCall ? (item.firstRecallTime || '-') + '/' + (item.secondRecallTime || '-') : '-' }}</span>
          </span>
          <div v-if="!props.readonly" class="btn-box tw-text-[--primary-blue-color]">
            <button @click="add(item)"> <el-icon :size="16" color="inherit"><SvgIcon name="edit"></SvgIcon></el-icon>编辑</button>
            <button v-if="!item.templateStatus || item.templateStatus == 0" class="tw-text-[--primary-red-color]" @click="switchStatus(item)">
              <el-icon :size="16" color="inherit"><SvgIcon name="stop3"></SvgIcon></el-icon>
              停用
            </button>
            <button v-else @click="switchStatus(item)">
              <el-icon :size="16" color="inherit"><SvgIcon name="start3"></SvgIcon></el-icon>
              启用
            </button>
            <button class="tw-text-[--primary-red-color]" @click="del(item)">
              <el-icon :size="16" color="inherit"><SvgIcon name="delete"></SvgIcon></el-icon>删除
            </button>
          </div>
        </li>
      </ul>
      <el-empty v-if="!tableData.length" />
    </el-scrollbar>
    <el-table
      v-if="listType === 'list'"
      :data="tableTempData"
      v-loading="loading"
      :header-cell-style="tableHeaderStyle"
      stripe
      class="tw-grow"
      row-key="id"
    >
      <el-table-column property="id" label="模板编号" align="left" fixed="left" width="80"></el-table-column>
      <el-table-column property="templateName" label="模板名称" align="left" fixed="left" width="240" show-overflow-tooltip></el-table-column>
      <el-table-column property="taskName" label="任务名称" align="left" min-width="200" show-overflow-tooltip></el-table-column>
      <el-table-column property="taskType" label="任务类型" align="center" width="80">
        <template #default="{ row }">
          <span
            v-if="row?.taskType"
            class="status-box-mini"
            :class="filterStatusStyle(row?.taskType)"
          >
            {{ findValueInEnum(row.taskType, TaskTypeEnum) || '-' }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column property="speechCraftName" label="执行话术" align="left" min-width="280" show-overflow-tooltip></el-table-column>
      <el-table-column property="hangUpSms" label="触发短信" align="left" min-width="120">
        <template #default="{ row }">
          <el-button v-if="row.scriptSms?.length" type="primary" link @click="showSmsDialog(0, row)">
            {{ row.scriptSms?.length ?? '-'}}
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column property="hangUpSms" label="挂机短信" align="left" min-width="120">
        <template #default="{ row }">
          <el-button v-if="row.hangUpSms?.length" type="primary" link @click="showSmsDialog(1, row)">
            {{ row.hangUpSms?.length ?? '-'}}
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column property="startWorkTimeList" label="拨打时段" align="left" min-width="180">
        <template #default="{ row }">
          <TagsBox :tagsArr="concatTimeList(row.startWorkTimeList, row.endWorkTimeList)" tagsName="拨打时段" :tagsNum="2"></TagsBox>
        </template>
      </el-table-column>
      <el-table-column property="autoReCall" label="自动补呼" align="center" width="80">
        <template #default="{ row }">
          {{ row.autoReCall ? '开启' : '关闭' }}
        </template>
      </el-table-column>
      <el-table-column property="firstRecallTime" label="间隔" align="center" width="80">
        <template #default="{ row }">
          {{ row.autoReCall ? (row.firstRecallTime || '-') + '/' + (row.secondRecallTime || '-') : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="firstRecallTime" label="屏蔽地区" align="left"  width="180">
        <template #default="{ row }">
            <el-button link type="primary" @click="showBlock(row)">{{ getBlockInfo(row) }}</el-button>
        </template>
      </el-table-column>
      <el-table-column v-if="!props.readonly" label="操作" min-width="100" align="right" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="add(row)">编辑</el-button>
          <el-button type="danger" link @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      v-if="listType === 'list'"
      class="tw-grow-0"
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
  <EditTaskDialog
    type="template"
    v-model:visible="editVisible"
    :groupId="props.groupId"
    :dataRow="dataRow!"
    @update:visible="closeEditVisible"
    @confirm="confirmEditVisible"
  ></EditTaskDialog>
  <TaskSmsDialog
    v-model:visible="smsVisible"
    :type="smsShowType"
    :data="dataRow!"
  />
  <el-dialog
    v-model="blockVisible"
    width="960px"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">屏蔽地区</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
    >
    <CitySettingBox
      :taskRestrictData="taskRestrictData"
      :selectedOperatorList="selectedOperatorList"
      readonly
    />
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="blockVisible=false" :icon="CloseBold">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, defineAsyncComponent, watch } from 'vue'
import { ElMessage, } from 'element-plus'
import { Plus, CloseBold } from '@element-plus/icons-vue'
import EditTaskDialog from '@/components/task/EditTaskDialog.vue'
import { aiOutboundTaskTemplateModel } from '@/api/ai-report'
import { TemplateBaseItem, TaskTypeEnum, TaskManageOrigin, } from '@/type/task'
import { tableHeaderStyle } from '@/assets/js/constant'
import PaginationBox from '@/components/PaginationBox.vue'
import {  findValueInEnum, enum2Options } from '@/utils/utils'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { useTaskStore } from '@/store/taskInfo'
import { useMerchantStore } from '@/store/merchant'
import { useUserStore } from '@/store/user'
import TagsBox from '@/components/TagsBox.vue'
import HeaderBox from '@/components/HeaderBox.vue'
import dayjs from 'dayjs'
import { RestrictModal, RestrictModalOrigin, OperatorEnum } from '@/type/common'
import TaskSmsDialog from '@/components/task/TaskSmsDialog.vue'
import SvgIcon from '@/components/SvgIcon.vue';
import { trace } from '@/utils/trace';
import to from 'await-to-js'
import Confirm from '@/components/message-box'

const props = defineProps<{
  groupId?: string // 仅运营端-商户管理-任务模板传
  readonly?: boolean
  autoShowDialog?: boolean
}>();

const emits = defineEmits(['update:autoShowDialog'])

const CitySettingBox = defineAsyncComponent({ loader:() => { return import('@/components/CitySettingBox.vue')}})

const userStore = useUserStore()
const globalStore = useGlobalStore()
const taskStore = useTaskStore()
const { loading } = storeToRefs(globalStore)
const taskTypeOption = enum2Options(TaskTypeEnum)

const searchForm = reactive({
  name: '',
  taskType: undefined,
  templateStatus: 0,
})
const filterStatusStyle = (status: TaskTypeEnum) => {
  switch (status) {
    case TaskTypeEnum['AI外呼']: return 'blue-status';
    case TaskTypeEnum['人机协同']: return 'green-status';
    default: return 'blue-status';
  }
}
const concatTimeList = (staArr: string[], endArr: string[]) => {
  if (!staArr || staArr.length < 1 || staArr.length !== endArr.length ) return []
  return staArr.map((item, index) => {
    return item + '-' + endArr[index]
  })
}
const listType = ref<'card'|'list'>('card')
const tableData = ref<TemplateBaseItem[]>([])
const pageSizeList = [20, 50, 100, 200]
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(pageSizeList[0])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  search()
}
const tableTempData = computed(() => {
  return tableData.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value) || []
})
const search = async () => {
  loading.value = true
  // 运营端通过groupId获取任务模板
  // 后端筛选模板类型和groupId
  const [err, res] = await to(aiOutboundTaskTemplateModel.search({
    taskType: searchForm.taskType || undefined,
    groupId: props.groupId || undefined
  }))
  // 前端过滤名称
  // 名称支持模板和话术名称过滤
  // 模板状态过滤： 0 | null | '0' : 启用； 1 | '1' : 停用
  tableData.value = res?.filter(item => {
    return (!searchForm.name || item.templateName?.includes(searchForm.name) || item.speechCraftName?.includes(searchForm.name))
    && (searchForm.templateStatus == item.templateStatus || (searchForm.templateStatus == 0 && !item.templateStatus))
  })?.sort((a,b) => dayjs(a.updateTime).isAfter(dayjs(b.updateTime)) ? -1 : 1) || []
  total.value = tableData.value.length
  loading.value = false
}
// 操作区
const editVisible = ref(false)
const dataRow = ref<TemplateBaseItem | null>(null)

const add = (row?: TemplateBaseItem) => {
  editVisible.value = true
  dataRow.value = row || new TaskManageOrigin(TaskTypeEnum['AI外呼'])
}

// 启用停用
const switchStatus = async (item: TemplateBaseItem) => {
  const str = !item.templateStatus || item.templateStatus == 0 ? '停用' : '启用';
  const [err1] = await to(Confirm({
    text: `<p>您确定要${str}任务模板【${item.templateName}】吗?</p>
    <p class="tw-text-[#E54B17] tw-font-[600]">请注意，此操作不会改变历史任务的话术</p>
    <p class="tw-text-[#E54B17] tw-font-[600]">请核实正在使用该模板的任务是否需要停止或修改</p>`,
    type: 'warning',
    title: `${str}确认`,
    confirmText: '确认',
  }))
  if (err1) {
    return
  }
  const params = {
    id: item.id!,
    templateStatus: !item.templateStatus || item.templateStatus == 0 ? 1 : 0
  }
  await trace({
    page: userStore.accountType == 0 ? `商户管理-任务模板-${str}模板` : `外呼模板-${str}模板`,
    params,
  })
  const [err2] = await to(aiOutboundTaskTemplateModel.switchStatus(params))
  !err2 && ElMessage.success('操作成功')
  search()
}

const del = async (item: TemplateBaseItem) => {
  const [err1] = await to(Confirm({
    text: `您确定要删除任务【${item.templateName}】吗?`,
    type: 'warning',
    title: `删除确认`,
    confirmText: '确认',
  }))
  if (err1) {
    return
  }
  const params = { id: item.id as number }
  await trace({
    page: userStore.accountType == 0 ? `商户管理-任务模板-删除模板` : `外呼模板-删除模板`,
    params,
  })
  const [err2] = await to(aiOutboundTaskTemplateModel.delete(params))
  !err2 && ElMessage.success('删除成功')
  search()
}
const closeEditVisible = () => {
  editVisible.value = false
  // Object.assign(dataRow, new DataRowOrigin())
}
const confirmEditVisible = async (params: TemplateBaseItem) => {
  loading.value = true
  if (!userStore.accountType) {
    params.groupId = props.groupId
  }
  await trace({
    page: userStore.accountType == 0 ? `商户管理-任务模板-${params.id ? '编辑模板' : '创建模板'}` : `外呼模板-${params.id ? '编辑模板' : '创建模板'}`,
    params,
  })
  const [err] = await to(aiOutboundTaskTemplateModel.edit(params))
  !err && ElMessage.success('操作成功')
  search()
  closeEditVisible()
  loading.value = false
}


/** 屏蔽地区模块 */
const blockVisible = ref(false)
const selectedOperatorList = ref<("全部" | OperatorEnum)[]>([])
const taskRestrictData = reactive<RestrictModal>(new RestrictModalOrigin())
// 进入屏蔽城市查看窗口
const showBlock = (row: TemplateBaseItem) => {
  const { allRestrictProvince, allRestrictCity, ydRestrictProvince, ydRestrictCity, ltRestrictProvince, ltRestrictCity, dxRestrictCity, dxRestrictProvince, virtualRestrictCity, virtualRestrictProvince, unknownRestrictCity, unknownRestrictProvince } = row
  Object.assign(taskRestrictData, {
    allRestrictProvince, allRestrictCity, ydRestrictProvince, ydRestrictCity, ltRestrictProvince, ltRestrictCity, dxRestrictCity, dxRestrictProvince, virtualRestrictCity, virtualRestrictProvince, unknownRestrictCity, unknownRestrictProvince
  })
  selectedOperatorList.value = []
  blockVisible.value = true
}
// 获取模板的屏蔽地区
const getBlockInfo = (row: TemplateBaseItem) => {
  const res: string[] = []
  // 全部
  if (row.allRestrictProvince && row.allRestrictCity) {
    const num1 = row.allRestrictProvince.split(',')?.length || 0
    const num2 = row.allRestrictCity.split(',')?.length || 0
    res.push(`全部：${num1}省${num2}市`)
  }
  // 移动
  if (row.ydRestrictProvince && row.ydRestrictCity) {
    const num1 = row.ydRestrictProvince.split(',')?.length || 0
    const num2 = row.ydRestrictCity.split(',')?.length || 0
    res.push(`移动：${num1}省${num2}市`)
  }
  // 联通
  if (row.ltRestrictProvince && row.ltRestrictCity) {
    const num1 = row.ltRestrictProvince.split(',')?.length || 0
    const num2 = row.ltRestrictCity.split(',')?.length || 0
    res.push(`联通：${num1}省${num2}市`)
  }
  // 电信
  if (row.dxRestrictProvince && row.dxRestrictCity) {
    const num1 = row.dxRestrictProvince.split(',')?.length || 0
    const num2 = row.dxRestrictCity.split(',')?.length || 0
    res.push(`电信：${num1}省${num2}市`)
  }
  // 未知
  if (row.unknownRestrictProvince && row.unknownRestrictCity) {
    const num1 = row.unknownRestrictProvince.split(',')?.length || 0
    const num2 = row.unknownRestrictCity.split(',')?.length || 0
    res.push(`未知：${num1}省${num2}市`)
  }
  return  res.join('，') || '-'
}

/** 触发短信\挂机短信 */
const smsVisible = ref(false)
const smsShowType = ref(0) //  [0] 触发短信弹窗 [1] 挂机短信弹窗
const showSmsDialog = (type: number, row: TemplateBaseItem) => {
  smsVisible.value = true
  smsShowType.value = type
  dataRow.value = row || null
}

const init = async () => {
  await taskStore.getAllScriptListOptions(true)
  const merchantStore = useMerchantStore()
  const groupId = !userStore.accountType ? merchantStore.currentAccount?.groupId : userStore.groupId
  await taskStore.getCallTeamListOptions(groupId, true)
  search()

  // 针对商户管理-话术跳转自动拉起创建模板
  if (!!props.autoShowDialog) {
    add()
    emits('update:autoShowDialog', false)
  }
}
// 执行区
init()

watch(() => props.groupId, n => {
  if (!n) return
  init()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.module-container {
  .el-table {
    font-size: var(--el-font-size-base)
  }
}
.item {
  font-size: 13px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 15px;
  span {
    width: 90px
  }
}
.box {
  /* width: 255px; */
  border-radius: 8px;
  height: 200px;
  padding: 12px 0 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  padding-bottom: 12px;
  /* box-shadow: 0 2px 8px 0 rgb(191 205 221 / 80%), 0 2px 8px 0 rgb(191 205 221 / 80%); */
  background-size: 200px 250%;
  background-position: -40px 40%;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  .btn-box {
    position: absolute;
    background-color: #fff;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    visibility: hidden;
    flex-shrink: 0;
    justify-content: space-around;
    border-radius: 0 0 4px 4px;
    align-items: center;
    height: 32px;
    box-sizing: border-box;
    button {
      border-top: 1px solid var(--primary-black-color-200);
      width: 100%;
      height: 32px;
      border-right: 1px solid var(--primary-black-color-200);
      font-size: 13px;
      display: flex;
      align-items: center;
      justify-content: center;
      .el-icon {
        margin-right: 4px;
      }
      &:last-child {
        border-right: 0;
      }
    }
    .el-button+.el-button {
      margin-left: 0;
    }
  }
  &:hover .btn-box{
    visibility: visible;
    transition: visibility 0.15s;
  }
}
.items {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  width: 100%;
  overflow: hidden;
  padding: 0 12px;
  .label {
    color: #969799;
    flex-shrink: 0;
    flex-grow: 0;
    height: 20px;
    line-height: 20px;
    width: 70px;
    text-align: right;
  }
  .value {
    flex-shrink: 1;
    flex-grow: 1;
    color: #626366;
    height: 20px;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: justify;
    &.static {
      flex-shrink: 0;
      flex-grow: 0;
      text-overflow: unset;
    }
  }
}
.items-2 {
  align-items: flex-start;
}

</style>
