<template>
  <el-dialog
    v-model="dialogVisible"
    width="540px"
    @close="cancel"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">编辑导入号码</div>
    </template>
    <el-form
      :model="addData"
      :rules="phoneRules"
      label-width="80px"
      ref="addFormRef"
    >
      <el-form-item label="有效时间：">
        <el-radio-group v-model="expirationType">
          <el-radio :label="0">永久</el-radio>
          <el-radio :label="1">自定义</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="expirationType===1" label="到期时间：" prop="expireDate">
        <el-date-picker
          v-model="addData.expireDate"
          style="width: 100%"
          format="YYYY-MM-DD"
          :shortcuts="shortcuts"
          :disabled-date="disabledFn"
          placeholder="请选择过期时间"
          type="date"
          :clearable="false"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, reactive, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import dayjs from 'dayjs'
import { useUserStore } from '@/store/user'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { WhiteBlackListItem, } from '@/type/dataFilter'
import to from 'await-to-js'
import { merchantBlacklistModel } from '@/api/data-filter'
import type { FormInstance } from 'element-plus'
import { shortcuts, phoneRules, BlackPhoneOrigin } from './constant'
import { trace } from '@/utils/trace'

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  editData?: WhiteBlackListItem;
  groupType: string; // black | white
}>();
const loading = ref(false)
const userStore = useUserStore()
const dialogVisible = ref(props.visible)
const addData = reactive<WhiteBlackListItem>(new BlackPhoneOrigin())
const expirationType = ref(0)
const addFormRef = ref<FormInstance | null>(null)

const confirmSaveAction = async () => {
  loading.value = true
  const params = {
    id: addData.id!,
    expireDate: expirationType.value === 0 ? '2400-01-01 00:00:00' : dayjs(addData.expireDate).format('YYYY-MM-DD HH:mm:ss'),
  }
  trace({
    page: '系统设置-黑名单管理-编辑导入号码',
    params,
  })
  const [err, _] = await to(merchantBlacklistModel.savePhone(params))
  loading.value = false
  if (!err) {
    ElMessage.success('操作成功')
    emits('confirm')
    cancel()
  }
}
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const confirm = () => {  
  addFormRef.value && addFormRef.value.validate((valid) => {
    if (valid) {
      confirmSaveAction()
    }
  })
}

// 到期时间选择限制只能选进入往后
const disabledFn = (date: Date) => {
  return dayjs(date).isBefore(dayjs().endOf('day'))
}

watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n && props.editData) {
    addFormRef.value?.clearValidate()
    Object.assign(addData, props.editData || new BlackPhoneOrigin())
    expirationType.value = dayjs(addData.expireDate).format('YYYY-MM-DD') === '2400-01-01' ? 0 : 1
  }
  if (!n) {
    Object.assign(addData, new BlackPhoneOrigin())
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.el-form {
  color: var(--primary-black-color-600);
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
.el-input-number .el-input__inner {
  text-align: left;
}
</style>