<template>
  <el-dialog
    :model-value.sync="visible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">{{ title }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="groupRules"
        label-width="80px"
        ref="editRef"
      >
        <el-form-item label="分组名称：" prop="groupName">
          <el-input v-model="editData.groupName" clearable placeholder="请填写，请勿重复命名"></el-input>
        </el-form-item>
        <el-form-item label="限制时长：">
          <el-radio-group v-model="limitType" @change="handleLimitTypeChange">
            <el-radio :label="0">永久生效</el-radio>
            <el-radio :label="1">自定义时间</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="limitType === 1" label="具体时长：" prop="limitDuration">
          <InputNumberBox v-model:value="editData.limitDuration" placeholder="时间范围【1天-730天】" :min="1" :max="730" append="天"/>
        </el-form-item>
        <el-form-item label="备注：" prop="comment">
          <el-input
            v-model.trim="editData.comment"
            type="textarea"
            placeholder="请输入备注，不超过200字"
            clearable
            :autosize="{ minRows: 4, maxRows: 6 }"
            maxlength="200"
            show-word-limit
            style="width: 100%;"
          />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="confirm">{{editData?.id ? '确认' : '新增'}}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, watch, reactive,} from 'vue'
import type { FormInstance, } from 'element-plus'
import { BlackListGroupItem, } from '@/type/dataFilter'
import InputNumberBox from '@/components/InputNumberBox.vue'
import { groupRules, BlackGroupOrigin } from './constant'
import { merchantBlacklistModel } from '@/api/data-filter'
import to from 'await-to-js'
import { trace } from '@/utils/trace'

const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean,
  editData?: BlackListGroupItem | null,
  readonly?: boolean
}>();
const visible = ref(false)
const editData = reactive<BlackListGroupItem>(new BlackGroupOrigin())
const title = computed(() => editData?.id ? '编辑黑名单分组' : '新增黑名单分组')

const limitType = ref(0) // 0 永久生效

const editRef = ref<FormInstance | null>(null)
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  emits('update:visible', false)
}

const confirm = async () => {
  if (limitType.value === 0) {
    editData.limitDuration = -1
  }
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      const params = {
        limitDuration: editData.limitDuration,
        id: editData.id,
        groupName: editData.groupName,
        comment: editData.comment,
      }
      trace({ page: `系统设置-黑名单管理-${props.editData?.id ? '编辑' : '新增'}分组`, params })
      const [_, res] = await to(merchantBlacklistModel.saveGroup(params)) as [any, BlackListGroupItem]
      res && emits('confirm', res?.id)
      cancel()
    }
  })
}

const handleLimitTypeChange = () => {
  if (limitType.value === 1 && !(editData.limitDuration && editData.limitDuration > 0)) {
    editData.limitDuration = 30
  }
}

/** watch */ 
// 监听visible
watch(() => props.visible, n => {
  visible.value = n
  if(n) {
    Object.assign(editData, props.editData || new BlackGroupOrigin())
    limitType.value = editData.limitDuration === -1 ? 0 : 1
  } else {
    Object.assign(editData, new BlackGroupOrigin())
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {

  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    .el-form-item__content {
      align-items: flex-start;
      justify-content: flex-start;
    }
    &:first-child {
      margin-top: 14px;
    }
    &.muti-items {
      margin-top: 0;
      width: 320px;
      &:last-child {
        margin-left: 20px;
      }
    }
  }
  .el-table {
    font-size: var(--el-font-size-base);
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>
