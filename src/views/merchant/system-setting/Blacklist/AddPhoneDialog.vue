<template>
  <el-dialog
    v-model="dialogVisible"
    width="540px"
    @close="cancel"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ title }}</div>
    </template>
    <el-form
      :model="addData"
      :rules="phoneRules"
      label-width="80px"
      ref="addFormRef"
      @submit.native.prevent
    >
      <el-form-item label="号码：" prop="phone">
        <el-input
          v-model="addData.phone"
          clearable
          placeholder="请输入号码"
          @keyup.enter="confirm()"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { WhiteBlackListItem, } from '@/type/dataFilter'
import to from 'await-to-js'
import { merchantBlacklistModel } from '@/api/data-filter'
import type { FormInstance } from 'element-plus'
import { BlackPhoneOrigin } from './constant'
import dayjs from 'dayjs'
import { phoneReg } from '@/utils/constant'
import { trace } from '@/utils/trace'

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  groupId: string;
}>();
const loading = ref(false)
const dialogVisible = ref(props.visible)
const title = '单个导入'
const addData = reactive<WhiteBlackListItem>(new BlackPhoneOrigin())
const expirationType = ref(0)
const addFormRef = ref<FormInstance | null>(null)

const validatePhone = (rule: any, value: any, callback: any) => {
  // 兼容11位常规手机号和+68开头的特殊16位手机号
  if (!value.match(phoneReg) && !value.startsWith('+')) {
    callback(new Error('手机号不合规'))
  } else {
    callback()
  }
}

const phoneRules = {
  phone: [
    { required: true, message: '请输入号码', trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' },
  ],
}


const confirmSaveAction = async () => {
  loading.value = true
  const params = {
    phone: addData.phone!,
    blackGroupId: props.groupId,
  }
  trace({ page: `系统设置-黑名单管理-单个号码导入`, params })
  const [err, res] = await to(merchantBlacklistModel.saveSinglePhone(params))
  loading.value = false
  if (!err) {
    ElMessage.success('操作成功')
    emits('confirm', res)
    cancel()
  }
}
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const confirm = () => {  
  addFormRef.value && addFormRef.value.validate((valid) => {
    if (valid) {
      confirmSaveAction()
    }
  })
}

watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n) {
    addFormRef.value?.clearValidate()
    Object.assign(addData, new BlackPhoneOrigin())
    expirationType.value = !addData.expireDate ? 0 : 1
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.el-form {
  color: var(--primary-black-color-600);
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
.el-input-number .el-input__inner {
  text-align: left;
}
</style>