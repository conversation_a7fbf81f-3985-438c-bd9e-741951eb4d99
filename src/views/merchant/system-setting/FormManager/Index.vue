<template>
  <HeaderBox title="表单设置" class="tw-grow-0 tw-shrink-0"/>
  <div class="module-container tw-min-w-[1080px]">
    <div class="tw-mb-[16px]">
      <!-- <div class="info-content">设置需要对线索进行信息收集的收集项，并由坐席在跟进线索时填入收集项的值。</div> -->
      <div class="tw-float-right">
        <el-button v-if="tableData?.length>0" @click="viewForm()">内容预览</el-button>
        <el-button type="primary" @click="editFormItem()">添加收集项</el-button>
      </div>
      
    </div>
    <el-table
      id="form-draggable-table"
      :data="tableData"
      style="width: 100%"
      class="tw-grow"
      row-key="collectionItemName"
      :row-style="getRowStyle"
      :header-cell-style="{padding: 'auto 8px', color: '#646566', backgroundColor: '#f7f8fa'}"
    >
      <el-table-column label=" " align="left" width="80">
        <template #default="{ row }">
          <div class="tw-cursor-not-allowed" :class="!!row.collectionStatus ? 'handle' : ''">
            <el-icon :size="16" :color="!!row.collectionStatus ? 'var(--primary-black-color-600)' : 'var(--primary-black-color-300)'"><SvgIcon name="drag"/></el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="序号" align="left" width="80">
        <template #default="{ $index }">
          {{ $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column property="collectionItemName" align="left" min-width="200" label="收集项名称">
        <template #default="{ row }">
          {{ row.collectionItemName ?? '-' }}
        </template>
      </el-table-column>
      <el-table-column align="center" property="requiredField" label="是否必要" width="120" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.requiredField ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column align="left" property="collectionItemType" label="类型" min-width="120" show-overflow-tooltip>
        <template #default="{ row }: {row: CollectionFormItem}">
          {{ typeof row.collectionItemType === 'number' ? FormItemEnum[row.collectionItemType] || '-' : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="right" width="120">
        <template #default="{ row }">
          <el-button
            :disabled="row.collectionStatus==0"
            link
            :type="row.collectionStatus==0 ? 'default' : 'primary'"
            @click="editFormItem(row)"
          >
            编辑
          </el-button>
          <el-button
            link
            :type="row.collectionStatus==1 ? 'danger': 'primary'"
            @click="switchStatus(row)"
          >
            {{ row.collectionStatus==1 ? '停用' : '启用'}}
          </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <FormRecordDialog v-if="formViewVisible" type="view" v-model:visible="formViewVisible" :formSetting="tableData.filter(item => !!item.collectionStatus)" :formRecord="formRecordData"/>
    <AddDialog :editData="editData" v-model:visible="visible" @confirm="handleConfirm"/>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, watch, nextTick, onMounted, toRaw } from 'vue'
import HeaderBox from '@/components/HeaderBox.vue'
import routeMap from '@/router/asyncRoute/route-map'
import { useUserStore } from "@/store/user";
import { FormItemEnum, CollectionFormItem, FormRecordItem } from '@/type/clue'
import AddDialog from './AddDialog.vue';
import { ElMessage,  } from 'element-plus'
import Confirm from '@/components/message-box'
import { formSettingModel } from '@/api/clue'
import FormRecordDialog from '@/components/clue/FormRecordDialog.vue'
import Sortable from "sortablejs";
// 用户权限获取
const userStore = useUserStore();
const cascaderProps = {
  value: 'label'
}
const permissions = userStore.permissions[routeMap['表单设置'].id]
class DataOrigin {
  id = undefined
  collectionItemName = ''
  collectionOrder = tableData.value?.length??1
  collectionItemType = FormItemEnum['短文本（20字符内）']
  collectionStatus = 1
  requiredField = 0
  options = undefined
  cascadeOptions = undefined
}
const tableData = ref<CollectionFormItem[]>([])
const formData = reactive<{[key: string]: any}>({})
const editData = reactive<CollectionFormItem>(new DataOrigin())
const visible = ref(false)
/** 编辑表单收集项 */
const editFormItem = (data?: CollectionFormItem) => {
  if (data) {
    Object.assign(editData, data)
  } else {
    Object.assign(editData, new DataOrigin())
    if (tableData.value?.length >= 200) {
      return ElMessage({
        type: 'warning',
        message: '收集项已经达到上限个数200个，无法继续添加！'
      })
    }
  }
  visible.value = true
}
const handleConfirm = () => {
  search()
}
/** 停用表单收集项 */
const switchStatus = (item: CollectionFormItem) => {
  Confirm({ 
    text: item.collectionStatus==0 ? `您确定要【启用】表单收集项【${item.collectionItemName}】吗?` :
    '停用后，该收集项已经收集到的息，将不再展示，并不再支持收集新的数据。确定要停用吗?',
    type: 'danger',
    title: `${item.collectionStatus==1 ? '停用' : '启用'}确认`
  }).then(async () => {
    await formSettingModel.changeOneFormSettingStatus({
      id: item.id!,
      collectionStatus: 1 - item.collectionStatus,
    })
    ElMessage({
      type: 'success',
      message: `${item.collectionStatus==1 ? '禁用' : '启用'}【${item.collectionItemName}】成功`
    })
    search()
  }).catch(() => {})
}
/** 预览表单 */
const formViewVisible = ref(false)
class FormRecordOriginItem {
  callSeatId = undefined
  clueId = undefined
  formRecordId = undefined
  fromCollectionContentList = []
}
const formRecordData = reactive<FormRecordItem>(new FormRecordOriginItem())
const viewForm = () => {
  formViewVisible.value = true
  Object.assign(formRecordData, new FormRecordOriginItem())
}
/** 选项拖拽实现 */
const sortableDom = ref()
const tableRef = ref()
const initDraggable = async () => {
  await nextTick()
  const dom = document.querySelector('#form-draggable-table .el-table__body tbody') as HTMLElement
  sortableDom.value = dom && Sortable.create(
    dom, {
    animation: 300,
    handle: ".handle",
    forceFallback: true,
    onEnd: async (evt) => {
      const newIndex = evt.newIndex as number
      const oldIndex = evt.oldIndex as number
      if (oldIndex !== newIndex && tableData.value) {
        const currRow = tableData.value.splice(oldIndex, 1);
        tableData.value.splice(newIndex, 0, currRow[0]);
        if (tableData.value.length > 0) {
          await formSettingModel.saveFormSettingOrder(tableData.value.map(item => item.id!))
        }
        ElMessage({
          type: 'success',
          message: '操作成功'
        })
        search()
      }
    },
  });
}
const search = async () => {
  tableData.value = await formSettingModel.getFormSettingInfo() || []
  tableData.value.map(item => {
    if ([FormItemEnum['多选'], FormItemEnum['日期范围'], FormItemEnum['时间范围'], FormItemEnum['日期时间范围']].includes(item.collectionItemType) ) {
      formData[item.collectionItemName] = []
    } else {
      formData[item.collectionItemName] = ''
    }
  })
  Object.assign(editData, new DataOrigin())
  editData.cascadeOptions = undefined
  // await formSettingModel.getFormSettingInfo()
}

const getRowStyle = ({row, rowIndex}: {row: CollectionFormItem, rowIndex: number}) => {
  if (rowIndex === 0) return
  if (!!row.collectionStatus) {
    return {
      'background-color': '#fff'
    }
  } else {
    return {
      'background-color': 'var(--primary-black-color-100)',
      color: 'var(--primary-black-color-400)'
    }
  }
}

onMounted(() => {
  search()
  initDraggable()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.module-container {
  .el-form {
    color: var(--primary-black-color-600);
    .el-form-item {
      margin-bottom: 0;
      :deep(.el-date-editor) {
        width: 100%;
        .el-input__wrapper {
          width: 100%;
        }
      }
      &:first-child {
        margin-top: 0;
      }
    }
    :deep(.el-form-item__label) {
      padding-right: 0;
    }
  }
  .handle {
    cursor: pointer;
  }
}
</style>