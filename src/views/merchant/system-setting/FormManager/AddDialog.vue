<template>
  <el-dialog
    v-model="dialogVisible"
    width="720px"
    class="dialog-form"
    @close="cancel"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ addData.id ? '编辑收集项' : '新增收集项' }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 140px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="addData"
        :rules="rules"
        label-width="80px"
        ref="addFormRef"
      >
        <el-form-item label="名称：" prop="collectionItemName">
          <el-input
            v-model="addData.collectionItemName"
            type="text"
            clearable maxlength="20"
            show-word-limit
            placeholder="请输入收集项名称，20字符内"
          />
        </el-form-item>
        <el-form-item label="是否必要：" prop="requiredField">
          <el-radio-group v-model="addData.requiredField">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="类型：" prop="collectionItemType">
          <el-select
            v-model="addData.collectionItemType"
            :disabled="!!addData.id"
            placeholder="请选择收集项类型"
            style="width:100%"
            @change="handleTypeChange"
          >
            <el-option
              v-for="item in formItemOption"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="[FormItemEnum['级联（单选）']].includes(addData.collectionItemType)" label="选项：">
          <el-radio-group v-model="optionType" @change="handleCascaderTypeChange">
            <el-radio :label="0">自定义</el-radio>
            <el-radio :label="1">省市</el-radio>
          </el-radio-group>
        </el-form-item>
        <input ref="fileRef" type="file" @change="handleFileChange" accept=".xls,.xlsx" class="tw-hidden"/>
        <el-form-item v-show="[FormItemEnum['单选'], FormItemEnum['多选']].includes(addData.collectionItemType)" :label-width="1" prop="options">
          <div class="info-title-deep tw-font-[600] tw-text-left tw-flex tw-items-center">
            <span>配置项</span>
            <el-tooltip>
                <el-icon :size="'var(--el-font-size-base)'" color="var(--primary-black-color-400)" class="tw-ml-[2px]"><SvgIcon name="warning"/></el-icon>
                <template v-slot:content>
                  <div class="tw-text-left">
                    <div class="tw-mb-[4px] tw-font-[600]">配置项要求：</div>
                    <li>选项不可包含英文逗号和横杠；</li>
                    <li>选项不可重复；</li>
                    <li>选项上限为1000个；</li>
                    <div class="tw-my-[4px] tw-font-[600]">文件导入要求：</div>
                    <li>默认第一行为表头，不会导入；</li>
                    <li>默认导入第一列为选项；</li>
                    <li>导入选项会自动去重；</li>
                  </div>
                </template>
              </el-tooltip>
            <el-button @click="handleUpload()" type="primary" link class="tw-ml-[2px]">文件导入</el-button>
            <el-button @click="downloadOptions()" type="primary" link>导出选项</el-button>
          </div>
          <div class="normal-options tw-pr-[12px] tw-pl-[36px] tw-w-full">
            <div
              v-for="(item, index) in optionArr"
              class="normal-options-item tw-flex tw-items-center tw-w-full tw-overflow-hidden tw-mb-[4px]"
              :key="index"
            >
              <span class="tw-flex tw-items-center tw-w-[20px]">
                <el-icon class="handle tw-cursor-pointer" :size="16"><SvgIcon name="drag" color="var(--primary-black-color-400)"/></el-icon>
              </span>
              <span class="tw-flex tw-justify-center tw-items-center tw-flex-grow">
                <span class="tw-w-[60px] tw-shrink-0 tw-text-right">字段{{ index+1 }}：</span>
                <el-input v-model="optionArr[index].value" clearable placeholder="选项值（60字以内）" maxlength="60"/>
              </span>
              <el-button
                class="tw-w-[36px] tw-text-center tw-ml-[12px]"
                :disabled="!(optionArr && optionArr.length > 1)"
                link
                :type="(optionArr && optionArr.length > 1) ? 'danger': 'default'"
                @click="delOption(index)"
              >
                删除
              </el-button>
            </div>
            
          </div>
          <div class="tw-flex tw-pl-[115px]">
            <el-button
              link
              type="primary"
              @click="addOption"
              style="height: 32px"
            >
            新增选项</el-button>
          </div>
        </el-form-item>
        <el-form-item v-show="[FormItemEnum['级联（单选）']].includes(addData.collectionItemType) && optionType===0" :label-width="1" prop="cascadeOptions">
          <div class="tw-w-full">
            <div class="info-title-deep tw-font-[600] tw-text-left tw-flex tw-items-center">
              <span>配置项</span>
              <el-tooltip>
                <el-icon :size="'var(--el-font-size-base)'" color="var(--primary-black-color-400)" class="tw-ml-[2px]"><SvgIcon name="warning"/></el-icon>
                <template v-slot:content>
                  <div class="tw-text-left">
                    <div class="tw-mb-[4px] tw-font-[600]">配置项要求：</div>
                    <li>选项不可包含英文逗号和横杠；</li>
                    <li>一级、二级选项不可为空值；</li>
                    <li>一级选项不超过1000个，总选项不超过5000个；</li>
                    <div class="tw-my-[4px] tw-font-[600]">文件导入要求：</div>
                    <li>默认第一行为表头，不会导入；</li>
                    <li>第一列为一级选项，第二列为二级选项，第三列为三级选项；</li>
                    <li>一级选项和二级选项不可为空，会自动过滤空的选项；</li>
                  </div>
                </template>
              </el-tooltip>
              <el-button @click="handleUpload()" type="primary" link class="tw-ml-[2px]">文件导入</el-button>
              <el-button @click="downloadOptions()" type="primary" link>导出选项</el-button>
            </div>
            <el-row>
              <el-col :span="8" class="cascade-left-col">
                <div class="info-title">一级选项</div>
                <div ref="dragCascaderRef" class="tw-w-full">
                  <div
                    v-for="(item, index) in cascaderOptionsArr"
                    class="cascader-first-item"
                    :class="index === sortIndex ? 'active' : ''"
                    :key="index"
                  >
                    <el-icon class="handle1 tw-cursor-pointer" :size="16" color="var(--primary-black-color-400)">
                      <SvgIcon name="drag" />
                    </el-icon>
                    <el-input v-model="cascaderOptionsArr[index].label" clearable placeholder="一级选项（60字以内）" maxlength="60" @click="dragCascaderSecondOption(index)"/>
                    
                    <el-button link @click="dragCascaderSecondOption(index)" :type="index === sortIndex ? 'primary' : 'default'">
                      <el-icon :size="16"><SvgIcon name="arrow-right" /></el-icon>
                    </el-button>
                    <el-button
                      :disabled="!(cascaderOptionsArr && cascaderOptionsArr.length > 1)"
                      link
                      :type="(cascaderOptionsArr && cascaderOptionsArr.length > 1) ? 'danger': 'default'"
                      class="tw-hidden hover:tw-inline-block"
                      @click="delCascaderOption(index)"
                    >
                      删除
                      <!-- <el-icon :size="16"><SvgIcon name="delete" /></el-icon> -->
                    </el-button>
                  </div>
                  <div class="tw-flex tw-pl-[34px]">
                    <el-button
                      link
                      type="primary"
                      @click="addCascaderOption"
                      style="height: 32px"
                    >新增选项</el-button>
                  </div>
                </div>
              </el-col>
              <el-col :span="8" class="cascade-left-col">
                <div class="info-title">二级选项</div>
                <div class="tw-w-full drag-second-cascader">
                  <div
                    v-for="(subitem, subIndex) in (cascaderOptionsArr[sortIndex]?.children || [])"
                    class="cascader-first-item"
                    :class="subIndex === sortSubIndex ? 'active' : ''"
                    :key="subIndex"
                  >
                    <el-icon class="handle2 tw-cursor-pointer" :size="16" color="var(--primary-black-color-400)">
                      <SvgIcon name="drag" />
                    </el-icon>
                    <el-input v-model="cascaderOptionsArr[sortIndex]!.children![subIndex]!.label" clearable placeholder="二级选项（60字以内）" maxlength="60" @click="dragCascaderThirdOption(subIndex)"/>
                    
                    <el-button link @click="dragCascaderThirdOption(subIndex)" :type="subIndex === sortSubIndex ? 'primary' : 'default'">
                      <el-icon :size="16"><SvgIcon name="arrow-right" /></el-icon>
                    </el-button>
                    <el-button
                      link
                      :disabled="!(cascaderOptionsArr[sortIndex]?.children && cascaderOptionsArr[sortIndex]?.children!.length > 1)"
                      :type="(cascaderOptionsArr[sortIndex]?.children && cascaderOptionsArr[sortIndex]?.children!.length > 1) ? 'danger': 'default'"
                      @click="delCascaderSecondOption(sortIndex, subIndex)"
                    >
                      删除
                      <!-- <el-icon :size="16"><SvgIcon name="delete" /></el-icon> -->
                    </el-button>
                  </div>
                  <div class="tw-pl-[34px] tw-flex">
                    <el-button
                      link
                      type="primary"
                      @click="addCascaderSecondOption(1)"
                    >
                      新增选项
                    </el-button>
                  </div>
                </div>
              </el-col>
              <el-col :span="8" class="cascade-right-col">
                <div class="info-title">三级选项</div>
                <div class="drag-third-cascader">
                  <div v-for="(subSubItem, subSubIndex) in (cascaderOptionsArr[sortIndex]?.children![sortSubIndex]?.children || [])" class="cascader-third-item">
                    <el-icon class="handle3 tw-cursor-pointer" :size="16" color="var(--primary-black-color-400)"><SvgIcon name="drag"/></el-icon>
                    <el-input v-model="cascaderOptionsArr[sortIndex]!.children![sortSubIndex]!.children![subSubIndex]!.label" clearable placeholder="三级选项（60字以内）" maxlength="60"/>
                    <el-button
                      link
                      type="danger"
                      @click="delCascaderThirdOption(sortIndex, sortSubIndex, subSubIndex)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
                <el-empty
                  v-if="!cascaderOptionsArr[sortIndex]?.children![sortSubIndex]?.children || cascaderOptionsArr[sortIndex]?.children![sortSubIndex]?.children?.length === 0"
                  description="待增选项"
                  :image-size="60"
                ></el-empty>
                <div class="tw-pl-[34px] tw-flex">
                  <el-button
                    link
                    type="primary"
                    @click="addCascaderSecondOption(2)"
                  >
                    新增选项
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, reactive, watch, nextTick } from 'vue'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { FormItemEnum, CollectionFormItem, CascaderItem } from '@/type/clue'
import { enum2Options, pickAttrFromObj } from '@/utils/utils'
import Sortable from "sortablejs";
import { formSettingModel } from '@/api/clue'
import { useUserStore } from "@/store/user";
import { useGlobalStore } from '@/store/globalInfo'
import { readXlsx, exportExcel, } from "@/utils/export"
import dayjs from 'dayjs'

const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean;
  editData: CollectionFormItem;
}>();
const userStore = useUserStore();
const globalStore = useGlobalStore()
const loading = ref(false)
const dialogVisible = ref(props.visible)
class DataOrigin {
  id = undefined
  collectionItemName = ''
  collectionOrder = undefined
  collectionItemType = FormItemEnum['短文本（20字符内）']
  collectionStatus = 1
  requiredField = 0
  options = undefined
  cascadeOptions = undefined
}
const addData = reactive<CollectionFormItem>(new DataOrigin())
const formItemOption = enum2Options(FormItemEnum)
const addFormRef = ref<FormInstance | null>(null)
const rules = {
  collectionItemName: [
    { required: true, message: '请输入收集项名称', trigger: 'blur' },
  ],
  requiredField: [
    { required: true, message: '请选择收集项类型', trigger: 'change' },
  ],
  collectionItemType: [
    { required: true, message: '请选择收集项类型', trigger: 'change' },
  ],
  options: [
    { validator: (rule: any, value: any, callback: any) => {
      if (![FormItemEnum['单选'], FormItemEnum['多选']].includes(addData.collectionItemType)) {
        return callback()
      }
      if (!optionArr.value || optionArr.value.length < 1) {
        return callback(new Error('请添加选项'))
      }
      if (optionArr.value?.length > 1000) {
        return callback(new Error('选项数量不得超过1000个'))
      }
      const arr = optionArr.value.flatMap(item => !item.value || item.value.match(/[,-]/) ? [] : [item.value])
      if ([...new Set(arr)].length !== optionArr.value.length) {
        return callback(new Error('选项存在空值或重复,不可包含英文逗号和横杠'))
      }
      return callback()
    }, trigger: ['change', 'blur']},
  ],
  cascadeOptions: [
    { validator: (rule: any, value: any, callback: any) => {
      if (!([FormItemEnum['级联（单选）']].includes(addData.collectionItemType) && optionType.value === 0)) {
        return callback()
      }
      if (!cascaderOptionsArr.value || cascaderOptionsArr.value.length < 1) {
        return callback(new Error('请添加选项'))
      }
      if (cascaderOptionsArr.value?.length > 1000) {
        return callback(new Error('一级选项数量不得超过1000个'))
      }
      let msg = ''
      const arr = cascaderOptionsArr.value.map(item => {
        if (!item.children || item.children?.length === 0) {
          msg = `【${item.label}】二级选项不可为空`
        } else {
          const arr2 = item.children.flatMap(v => {
            if (v.children?.length > 0) {
              const arr3 = v.children?.filter(vv => vv?.label && !vv.label.match(/[,-]/))
              if ([...new Set(arr3)].length !== v.children.length) {
                msg = `【${v.label}】的三级选项不可重复,不能为空,不可包含英文逗号和横杠这两种字符！`
              }
            }
            return (v.label && v.label.trim()) ? [v.label.trim()] : []
          }).filter(vv => vv && !vv.match(/[,-]/))
          if ([...new Set(arr2)].length !== item.children.length) {
            msg = `【${item.label}】的二级选项不可重复,不能为空,不可包含英文逗号和横杠这两种字符！`
          }
        }
        return item.label
      }).filter(item => !!item && !item?.match(/[,-]/))
      msg && callback(new Error(msg))
      if ([...new Set(arr)].length !== cascaderOptionsArr.value.length) {
        callback(new Error('一级选项不可重复,不能为空,不可包含英文逗号和横杠这两种字符！'))
      }
      return callback()
    }, trigger: ['change', 'blur']},
  ],
}

const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const confirm = async () => {
  if (![FormItemEnum['单选'], FormItemEnum['多选']].includes(addData.collectionItemType)) {
    addData.options = undefined
  } else {
    addData.options = optionArr.value.map(item => item.value)
  }
  if (![FormItemEnum['级联（单选）']].includes(addData.collectionItemType)) {
    addData.cascadeOptions = undefined
  } else {
    if (optionType.value === 0) {
      addData.cascadeOptions = cascaderOptionsArr.value.map(item => JSON.stringify(item))
    }
    // 自动填充省市级联
    if (optionType.value === 1) {
      const data = await globalStore.getProvinceInfo()
      addData.cascadeOptions = Object.keys(data).map(item => {
        const arr = item.split(',')
        const obj: CascaderItem = {label: arr[0], children: []}
        data[item].map(subItem => {
          obj.children?.push({label: subItem.split(',')[0], children: []})
        })
        return JSON.stringify(obj)
      }) 
    }
  }
  addFormRef.value && addFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true
        await formSettingModel.saveOneFormSetting(addData)
        ElMessage({
          message: '操作成功',
          type: 'success',
        })
        emits('confirm', addData)
        cancel()
        loading.value = false
      } catch(err) {
        ElMessage({
          message: '操作失败',
          type: 'error',
        })
        loading.value = false
      }
    }
  })
}

// 收集项类型变化处理函数
const handleTypeChange = () => {
  if (addData.collectionItemType && [FormItemEnum['单选'], FormItemEnum['多选']].includes(addData.collectionItemType) && (!optionArr.value || optionArr.value.length < 1)) {
    optionArr.value = [{index: 0, value: ''}]
    initOptionsDraggable()
  }
  if (addData.collectionItemType && [FormItemEnum['级联（单选）']].includes(addData.collectionItemType) && (!cascaderOptionsArr.value || cascaderOptionsArr.value.length < 1)) {
    cascaderOptionsArr.value = [{label: '', children: [{label: '', children: []}]}]
    initCascaderDraggable()
    dragCascaderSecondOption(0)
  }
}
/** 选项模块：增删，拖拽 开始*/
const optionArr = ref<{index: number, value: string}[]>([])
const sortableArr = ref<any[]>([])
const delOption = (index: number) => {
  optionArr.value?.splice(index, 1)
}
const addOption = async () => {
  if (optionArr.value?.length >= 1000) {
    return ElMessage({
      type: 'warning',
      message: '选项已经达到上限个数1000个，无法继续添加！'
    })
  } else {
    optionArr.value?.push({index: optionArr.value.length, value: ''})
  }
  setTimeout(() => {
    // @ts-ignore
    document.querySelector('.normal-options-item:last-child input')?.focus()
    
  }, 100)
  
}
/** 单选多选选项拖拽实现 */
const initOptionsDraggable = async () => {
  await nextTick()
  const dom = document.querySelector('.normal-options') as HTMLElement
  sortableArr.value[0] = dom && Sortable.create(
    dom, {
    animation: 300,
    handle: ".handle",
    disabled: !optionArr.value || optionArr.value.length < 1,
    forceFallback: true,
    onEnd: async (evt) => {
      const newIndex = evt.newIndex as number
      const oldIndex = evt.oldIndex as number
      if (oldIndex !== newIndex && optionArr.value) {
        const currRow = optionArr.value[oldIndex]
        optionArr.value.splice(oldIndex, 1);
        optionArr.value.splice(newIndex, 0, currRow);
        const data = [...optionArr.value]
        optionArr.value = []
        await nextTick()
        optionArr.value = data
      }
    },
  });
}
/** 单选多选选单选多选选选项模块：增删，拖拽 结束*/

/** 级联选项 */
const optionType = ref(0)
const dragCascaderRef = ref()
const cascaderOptionsArr = ref<CascaderItem[]>([])
const handleCascaderTypeChange = async (val: number) => {
  if (val === 0) {
    cascaderOptionsArr.value = [{label: '', children: [{label: '', children: []}]}]
  }
  if (val === 1) {
    const data = await globalStore.getProvinceInfo()
    addData.cascadeOptions = Object.keys(data).map(item => {
      const arr = item.split(',')
      const obj: CascaderItem = {label: arr[0], children: []}
      data[item].map(item => {
        obj.children?.push({label: item.split(',')[0], children: []})
      })
      return JSON.stringify(obj)
    }) 
  }
}
const addCascaderOption = () => {
  if (cascaderOptionsArr.value?.length >= 1000) {
    return ElMessage({
      type: 'warning',
      message: '级联一级选项已经达到上限个数1000个，无法继续添加！'
    })
  } else {
    cascaderOptionsArr.value.push({label: '', children: [{label: '', children: []}]})
  }
}
const delCascaderOption = (index: number) => {
  if (cascaderOptionsArr.value[index]) {
    cascaderOptionsArr.value?.splice(index, 1)
    if (sortIndex.value === index) {
      sortIndex.value = 0
    }
  } else {
    cascaderOptionsArr.value = []
  }
}
const addCascaderSecondOption = (n: number) => {
  if (n === 1) {
    if (cascaderOptionsArr.value[sortIndex.value].children && cascaderOptionsArr.value[sortIndex.value].children!.length >= 100) {
      return ElMessage({
        type: 'warning',
        message: '级联二级选项已经达到上限个数100个，无法继续添加！'
      })
    } else {
      cascaderOptionsArr.value[sortIndex.value].children?.push({label: '', children: []})
    }
    setTimeout(() => {
      // @ts-ignore
      document.querySelector('.cascader-second-item:last-child input')?.focus()
    }, 100)
  }
  if (n === 2) {
    if (cascaderOptionsArr.value[sortIndex.value].children![sortSubIndex.value].children && cascaderOptionsArr.value[sortIndex.value].children![sortSubIndex.value].children!.length >= 100) {
      return ElMessage({
        type: 'warning',
        message: '级联三级选项已经达到上限个数100个，无法继续添加！'
      })
    } else {
      cascaderOptionsArr.value[sortIndex.value].children![sortSubIndex.value].children ?
      cascaderOptionsArr.value[sortIndex.value].children![sortSubIndex.value].children!.push({label: '', children: []}) :
      (cascaderOptionsArr.value[sortIndex.value].children![sortSubIndex.value].children = [{label: '', children: []}])
    }
    setTimeout(() => {
      // @ts-ignore
      document.querySelector('.cascader-second-item:last-child input')?.focus()
    }, 100)
  }
}
const delCascaderSecondOption = (index: number, subIndex: number) => {
  if (cascaderOptionsArr.value[index] && cascaderOptionsArr.value[index].children && cascaderOptionsArr.value[index].children!.length > 0) {
    cascaderOptionsArr.value[index]?.children?.splice(subIndex, 1)
  } else {
    cascaderOptionsArr.value[index].children = []
  }
}
const delCascaderThirdOption = (index: number, subIndex: number, subSubIndex: number) => {
  if (cascaderOptionsArr.value[index] && cascaderOptionsArr.value[index].children && cascaderOptionsArr.value[index].children![subIndex].children.length > 0) {
    cascaderOptionsArr.value[index].children![subIndex].children.splice(subSubIndex, 1)
  } else {
    cascaderOptionsArr.value[index].children[subIndex].children = []
  }
}

// 文件导入级联
const fileRef = ref()
const handleUpload = () => {
  // @ts-ignore
  fileRef.value?.click()
}
// 上传文件转化为表格数据
const handleFileChange = async (e: Event) => {
  const {aoa} = await readXlsx(e) as { aoa: any[][] }
  fileRef.value.value = null
  if (!aoa || aoa.length <= 1) {
    return ElMessage('无法获取到文件数据')
  }
  const arr = aoa?.slice(1) || []
  if ([FormItemEnum['级联（单选）']].includes(addData.collectionItemType)) {
    if (arr.length >= 5000) {
      return ElMessage('文件数据过大，超过5000')
    }
    const cascaderOptionsTemp:CascaderItem[] = []
    const ignoreArr: string[] = []
    // 将文档转换为级联形式
    arr?.forEach(item => {
      if (!item[0] || !item[1]) {
        ignoreArr.push(`${item[0]??''}-${item[1]??''}${item[2] ? '-' + item[2] : '' }`)
        return 
      }
      const index1 = cascaderOptionsTemp?.findIndex(subItem => subItem?.label == item[0])
      if (index1 > -1) {
        const index2 = cascaderOptionsTemp[index1]?.children?.findIndex(subItem => subItem.label == item[1]) ?? -1
        if (index2 > -1) {
          if (!item[2]) return
          const children2 = cascaderOptionsTemp[index1].children[index2].children
          const index3 = children2?.findIndex(subItem => subItem.label == item[2]) ?? -1
          if (index3 === -1) {
            cascaderOptionsTemp[index1].children[index2].children.push({
              label: (item[2] + '').trim(),
              children: []
            })
          }
        } else {
          cascaderOptionsTemp[index1].children.push({
            label: (item[1] + '').trim(),
            children: item[2] ? [{
              label: (item[2] + '').trim(),
              children: []
            }] : []
          })
        }
      } else {
        cascaderOptionsTemp.push({
          label: (item[0] + '').trim() || '',
          children: [
            {
              label: (item[1] + '').trim() || '',
              children: item[2] ? [{
                label: (item[2] + '').trim() || '',
                children: []
              }] : []
            }
          ]
        })
      }
    })
    ignoreArr && ignoreArr.length > 0 && ElMessage.warning('已经过滤选项：' + ignoreArr.join('、'))
    cascaderOptionsArr.value = cascaderOptionsTemp
    addFormRef.value && addFormRef.value.validateField('cascadeOptions')
  } else {
    // 单选、多选选项导入
    if (arr.length >= 1000) {
      return ElMessage('文件数据过大，选项超过1000')
    }
    const itemsSet = new Set<string>([])
    optionArr.value = arr.flatMap((item, index) => {
      
      // 若重复选项则返回空
      const optionItem = item[0] + ''
      if (optionItem && itemsSet.has(optionItem)) {
        return []
      } else {
        itemsSet.add(optionItem)
      }
      return optionItem && optionItem?.trim() ? [{
        index: index,
        value: optionItem?.trim(),
      }] : []
    })
    if (optionArr.value.length !== arr.length) {
      ElMessage.warning('已自动过滤空数据和重复选项')
    }
    addFormRef.value && addFormRef.value.validateField('options')
  }
}
// 导出选项
const downloadOptions = () => {
  const data: any[] = []
  if ([FormItemEnum['级联（单选）']].includes(addData.collectionItemType)) {
    if (cascaderOptionsArr.value?.length === 0) {
      return ElMessage('暂无选项数据')
    }
    cascaderOptionsArr.value?.forEach(item => {
      item.children?.forEach(subItem => {
        if (subItem.children && subItem.children.length > 0) {
          subItem.children.forEach(subSubItem => {
            data.push({
              '一级选项': item.label.trim() ||'',
              '二级选项': subItem.label.trim() || '',
              '三级选项': subSubItem.label.trim() || '',
            })
          })
        } else {
          data.push({
            '一级选项': item.label.trim() ||'',
            '二级选项': subItem.label.trim() || '',
            '三级选项': '',
          })
        }
        
      })
    })
  } else {
    if (optionArr.value?.length === 0) {
      return ElMessage('暂无选项数据')
    }
    optionArr.value.forEach(item => {
      data.push({
        '选项': item.value?.trim() || ''
      })
    })
  }
  exportExcel(data, `【${dayjs().format('MM-DD')}】${addData.collectionItemName}的选项.xls`)
}

const sortIndex = ref(0) // 当前排序的二级选项的index
const sortSubIndex = ref(0) // 当前排序的二级选项的index
const cascaderSecondSort = ref<null | Sortable>(null)
const cascaderThirdSort = ref<null | Sortable>(null)
const dragCascaderSecondOption = async (index: number) => {
  sortIndex.value = index
  cascaderSecondSort.value?.destroy()
  await nextTick()
  const dom = document.querySelector('.drag-second-cascader') as HTMLElement
  cascaderSecondSort.value = dom && new Sortable(
    dom, {
    animation: 300,
    handle: ".handle2",
    forceFallback: true,
    onEnd: async (evt) => {
      const newIndex = evt.newIndex as number
      const oldIndex = evt.oldIndex as number
      const arr = cascaderOptionsArr.value[sortIndex.value].children || [] as CascaderItem[]
      if (oldIndex !== newIndex && cascaderOptionsArr.value) {
        const currRow = arr[oldIndex]
        arr.splice(oldIndex, 1);
        arr.splice(newIndex, 0, currRow);
        cascaderOptionsArr.value[sortIndex.value].children = []
        await nextTick()
        cascaderOptionsArr.value[sortIndex.value].children = arr
      }
    },
  });
  cascaderOptionsArr.value[sortIndex.value].children && cascaderOptionsArr.value[sortIndex.value].children.length > 0 && dragCascaderThirdOption(0)
}
const dragCascaderThirdOption = async (subIndex: number) => {
  sortSubIndex.value = subIndex
  cascaderThirdSort.value?.destroy()
  await nextTick()
  const dom = document.querySelector('.drag-third-cascader') as HTMLElement
  cascaderThirdSort.value = dom && new Sortable(
    dom, {
    animation: 300,
    handle: ".handle3",
    forceFallback: true,
    onEnd: async (evt) => {
      const newIndex = evt.newIndex as number
      const oldIndex = evt.oldIndex as number
      const arr = cascaderOptionsArr.value[sortIndex.value].children![sortSubIndex.value].children || [] as CascaderItem[]
      if (oldIndex !== newIndex && cascaderOptionsArr.value[sortIndex.value].children) {
        const currRow = arr[oldIndex]
        arr.splice(oldIndex, 1);
        arr.splice(newIndex, 0, currRow);
        cascaderOptionsArr.value[sortIndex.value].children![sortSubIndex.value].children = []
        await nextTick()
        cascaderOptionsArr.value[sortIndex.value].children![sortSubIndex.value].children = arr
      }
    },
  });
}
const initCascaderDraggable = async () => {
  await nextTick()
  sortableArr.value[1] = dragCascaderRef.value && new Sortable(
    dragCascaderRef.value, {
    animation: 300,
    handle: ".handle1",
    ghostClass: 'ghost-item',
    forceFallback: true,
    onEnd: async (evt) => {
      const newIndex = evt.newIndex as number
      const oldIndex = evt.oldIndex as number
      if (oldIndex !== newIndex && cascaderOptionsArr.value) {
        const currRow = cascaderOptionsArr.value[oldIndex]
        cascaderOptionsArr.value.splice(oldIndex, 1);
        cascaderOptionsArr.value.splice(newIndex, 0, currRow);
        const data = [...cascaderOptionsArr.value]
        cascaderOptionsArr.value = []
        await nextTick()
        cascaderOptionsArr.value = data
      }
    },
  });
}
watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    Object.assign(addData, pickAttrFromObj(props.editData, [
      'id', 'collectionItemName', 'collectionItemType', 'requiredField', 'options', 'collectionStatus', 'collectionOrder', 'cascadeOptions'
    ]))
    optionArr.value = addData.options ? addData.options.map((item, index) => {
      return {index: index, value: item}
    }) : []
    cascaderOptionsArr.value = (addData.cascadeOptions && addData.cascadeOptions?.length>0) ? addData.cascadeOptions.map(item => JSON.parse(item)) : [{label: '', children: [{label: ''}]}]
    if (cascaderOptionsArr.value.length === 34 && cascaderOptionsArr.value[0].label === '青海省') {
      cascaderOptionsArr.value = [{label: '', children: [{label: '', children: []}]}]
      optionType.value = 1
    } else {
      if (cascaderOptionsArr.value.length > 0) {
        dragCascaderSecondOption(0)
      } else {
        sortIndex.value = -1
      }
      optionType.value = 0
    }
    initOptionsDraggable()
    initCascaderDraggable()
    addFormRef.value && addFormRef.value.clearValidate()
  } else {
    sortableArr.value[0]?.destroy()
    sortableArr.value[1]?.destroy()
  }
}, {
  deep: true,
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
.cascade-left-col, .cascade-right-col {
  border: 1px solid var(--primary-black-color-200);
  padding: 12px 0;
  .info-title {
    padding-left: 40px;
    text-align: left;
    line-height: 16px;
    padding-bottom: 12px;
  }
  :deep(.el-input), :deep(.el-input__wrapper) {
    border: 1px solid transparent;
    box-shadow: 0 0 0 1px transparent;
    background: transparent;
  }
  :deep(.el-input__wrapper) {
    &:hover {
      border: 1px dashed var(--el-color-primary);
    }
    &:has(.el-input__inner:focus) {
      border: 1px solid var(--el-color-primary);
    }
  }
  .active :deep(.el-input__inner) {
    color: var(--el-color-primary);
    &:focus {
      color: var(--primary-black-color-600);
    }
  }
  .cascader-first-item, .cascader-third-item {
    display: flex;
    align-items: center;
    padding: 0 12px;
    &:hover {
      background-color: #EAF0FE;
    }
    
  }
}
.cascade-left-col{
  border-radius: 4px 0 0 4px;
}
.cascade-right-col{
  border-radius: 0px 4px 4px 0;
  border-left: 0px;
}
</style>
