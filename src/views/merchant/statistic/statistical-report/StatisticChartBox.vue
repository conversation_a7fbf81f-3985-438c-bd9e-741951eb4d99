<template>
  <div class="line-chart-box tw-flex tw-flex-col">
    <!-- 针对今日、【昨日、前日】、【7日，15日】，接通率折线图、地区geo/柱状图统计传参逻辑均不同 -->
    <el-scrollbar height="126px" warp-class="tw-pb-1" view-class="tw-flex tw-items-center tw-gap-x-1">
      <div v-for="item in typeList" class="button-box" :class="{active: searchForm.type===item}" @click="handleActiveTab(item)">
        <div class="tw-flex tw-flex-col tw-justify-around">
          <!-- 数据统计下：分类情况，需要进行选择分类 -->
          <el-select v-if="item?.startsWith('CLASS_') && !item?.endsWith('_DURATION')" v-model="categrory" size="small" placeholder="请选择分类结果" @change="handleActiveTab" class="transparent-select tw-mb-[8px]">
            <el-option v-for="item in categroryList" :key="item.value" :label="item.name + '类占比'" :value="item.value"/>
          </el-select>
          <el-select v-else-if="item?.startsWith('CLASS_') && item?.endsWith('_DURATION')" v-model="durationCategrory" size="small" placeholder="请选择分类结果" @change="handleActiveTab" class="transparent-select tw-mb-[8px]">
            <el-option v-for="item in durationCategroryList" :key="item.value" :label="item.name + '平均通话时长'" :value="item.value"/>
          </el-select>
          <!-- 非分类情况 -->
          <span v-else class="tw-text-[14px] tw-leading-[22px] tw-font-[600] tw-mb-[8px] tw-text-left tw-truncate" :class="searchForm.type===item ? 'tw-text-white' : 'tw-text-[var(--primary-black-color-600)]'">
            {{ findValueInEnum(item, AccountStatisticsTypeEnum) }}
          </span>
          
          <span class="tw-text-[26px] tw-leading-[28px] tw-font-[700] tw-text-left" :class="searchForm.type===item ? 'tw-text-white' : 'tw-text-[var(--el-color-primary)]'">
            {{ statisticInfo ? formatNumber((statisticInfo[accountStatisticsTypeObj[item]?.num] || 0), 2) : 0 }}
          </span>
          <span class="tw-text-[13px] tw-leading-[20px] tw-text-left tw-mt-[4px]" :class="searchForm.type===item ? 'tw-text-[var(--primary-black-color-200)]' : 'tw-text-[#898A8C]'">
            {{ !item?.endsWith('_DURATION') ? '数量' : '秒' }}
          </span>
        </div>
        <el-progress
          v-if="!item?.endsWith('_DURATION')"
          type="circle"
          class="tw-shrink-0"
          :color="searchForm.type===item ? 'var(--primary-black-color-300)':'var(--el-color-primary)'"
          :percentage="(statisticInfo && statisticInfo?.totalConnectNum) ? 
            +formatNumber((statisticInfo[accountStatisticsTypeObj[item]?.num] || 0) * 100/
            (!accountStatisticsTypeObj[item]?.isConnect ? statisticInfo?.totalConnectNum || 0 : statisticInfo?.totalCallNum || 0), 2)
            : 0"
          stroke-linecap="square"
          :width="86"
          :stroke-width="11"
        >
          <span
            class="tw-text-[16px] tw-font-[600]"
            :style="{color: searchForm.type===item ? '#fff' : 'var(--el-color-primary)'}">
            {{ (statisticInfo && statisticInfo?.totalConnectNum) ? 
            formatNumber((statisticInfo[accountStatisticsTypeObj[item]?.num] || 0) * 100/
            (!accountStatisticsTypeObj[item]?.isConnect ? statisticInfo?.totalConnectNum || 0 : statisticInfo?.totalCallNum || 0), 2) + '%'
            : 0 }}
          </span>
        </el-progress>
      </div>
    </el-scrollbar>
  </div>
  <div v-loading="!!loading1" class="tw-mt-[12px] tw-bg-white tw-w-full">
    <MixLineBarChart
      :data="lineChartMultList || []"
      :legends="accountStatisticsTypeObj[searchForm.type!]?.legend"
       :tooltip-sort="accountStatisticsTypeObj[searchForm.type!]?.isConnect ? [0, 2, 1] : [2, 0, 1]"
      title="分时段统计"
      :xName="lineChartXName"
    >
      <div class="tw-absolute tw-right-[16px] tw-top-[16px] tw-z-10">
        <!-- <span>时间颗粒度：</span> -->
        <el-select v-model="searchForm.timeGap" filterable style="width: 90px;height:24px" @change="handleIntervalChange">
          <el-option v-for="item in intervalList" :key="item.val" :label="item.name" :value="item.val" />
        </el-select>
      </div>
    </MixLineBarChart>
  </div>
  <div
    v-if="!props.filterData?.cityCode && !props.filterData?.provinceCode"
    v-loading="!!loading2"
    class="tw-flex tw-mt-[12px] tw-bg-white tw-flex-nowrap"
  >
    <div class="tw-relative tw-flex-grow">
      <GeoChart
        :data="geoData || []"
        title="分地区统计"
        @province-change="handleProvinceChange"
        :legends="accountStatisticsTypeObj[searchForm.type!]?.legend"
        :tooltip-sort="accountStatisticsTypeObj[searchForm.type!]?.isConnect ? [0, 2, 1] : [2, 0, 1]"
        canSwitch2Table
      />
    </div>
    
    <div v-loading="!!loading3" class="tw-w-[500px] tw-shrink-0 tw-grow-0 tw-ml-[10px]">
      <div>
        <MixLineBarChart
          v-if="currentProvinceData.province"
          :scrollSpan="currentProvinceData.data.length > 5 ? 5 : undefined"
          :data="currentProvinceData.data"
          :title="currentProvinceData.province"
          :extraXLabel="'省份：' + currentProvinceData.province"
          xName="城市"
          :legends="accountStatisticsTypeObj[searchForm.type!]?.legend"
          :tooltip-sort="accountStatisticsTypeObj[searchForm.type!]?.isConnect ? [0, 2, 1] : [2, 0, 1]"
        />
      </div>
      <div>
        <MixLineBarChart
          v-if="preProvinceData.province"
          :scrollSpan="preProvinceData.data.length > 5 ? 5 : undefined"
          :data="preProvinceData.data"
          :title="preProvinceData.province"
          xName="城市"
          :extraXLabel="'省份：' + preProvinceData.province"
          :legends="accountStatisticsTypeObj[searchForm.type!]?.legend"
          :tooltip-sort="accountStatisticsTypeObj[searchForm.type!]?.isConnect ? [0, 2, 1] : [2, 0, 1]"
        />
      </div>
    </div>
  </div>
  <el-empty v-else description="重置省市筛选条件后展示" class="tw-relative tw-mt-[12px] tw-mx-auto tw-bg-white tw-w-full tw-grow">
    <div class="chart-title">外呼情况展示</div>
  </el-empty>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed, onUnmounted } from 'vue'
import MixLineBarChart from '@/components/charts/MixLineBarChart.vue'
import GeoChart from '@/components/charts/GeoChart.vue'
import { dayjs, ElMessage } from 'element-plus'
import { time2Minute, findValueInEnum, formatNumber } from '@/utils/utils'
import to from 'await-to-js'
import { intervalList, accountStatisticsTypeObj, durationCategroryList, categroryList } from './constant';
import { AccountStatisticsSearchParams, AccountChartStatistics, AccountStatisticsTypeEnum } from '@/type/task'
import { statisticsModel,  } from '@/api/ai-report'
import { MonitorChartItem } from '@/type/common'
import { trace } from '@/utils/trace'
import { useUserStore } from '@/store/user'

const loading1 = ref(0) // 分时段统计
const loading2 = ref(0) // geo
const loading3 = ref(0) // 城市
const props = defineProps<{
  filterData: AccountStatisticsSearchParams | null,
  refresh: boolean,
}>()

const userInfo = useUserStore()

const statisticInfo = ref<AccountChartStatistics | null>(null)

// 全部筛选条件管理
const categrory = ref<AccountStatisticsTypeEnum>(categroryList[0].value)
const durationCategrory = ref<AccountStatisticsTypeEnum>(durationCategroryList[0].value)

const typeList = computed(() => (
  [
    'CONNECT', 'INTENTION', 'SILENCE', 'SILENCE_HANGUP', 'ASSISTANT',
    'ONE_SECOND', 'TWO_SECOND',
    categrory.value, durationCategrory.value
  ]
))
const searchForm = reactive<AccountStatisticsSearchParams>({
  ...props.filterData,
  timeGap: intervalList[0]?.val || undefined,
  type: typeList.value ? typeList.value[0] as AccountStatisticsTypeEnum : AccountStatisticsTypeEnum['外呼接通率'],
})

const emits = defineEmits([ 'update:refresh',])
// 头部时间范围、运营商、数据显示和选择

/**
 * findStatisticsByCondition
 * @description 重新获取头部信息
 */
const findStatisticsByCondition = async () => {
  if (!props.filterData) return
  loading1.value++
  const [err, res] = await to(statisticsModel.findStatisticsByCondition(props.filterData))
  statisticInfo.value = res  ? {
    ...res,
    avgCallDuration: res.totalConnectNum && res.totalCallDuration ? res.totalCallDuration / 1000 / res.totalConnectNum : 0,
    avgClassACallDuration: res.classACallDuration && res.classANum ? res.classACallDuration / 1000 / res.classANum : 0,
    avgClassBCallDuration: res.classBCallDuration && res.classBNum ? res.classBCallDuration / 1000 / res.classBNum : 0,
    avgClassCCallDuration: res.classCCallDuration && res.classCNum ? res.classCCallDuration / 1000 / res.classCNum : 0,
    avgClassDCallDuration: res.classDCallDuration && res.classDNum ? res.classDCallDuration / 1000 / res.classDNum : 0,
    avgClassECallDuration: res.classECallDuration && res.classENum ? res.classECallDuration / 1000 / res.classENum : 0,
    avgClassFCallDuration: res.classFCallDuration && res.classFNum ? res.classFCallDuration / 1000 / res.classFNum : 0,
    avgClassGCallDuration: res.classGCallDuration && res.classGNum ? res.classGCallDuration / 1000 / res.classGNum : 0,
    avgClassOtherCallDuration: res.classOtherCallDuration && res.classOtherNum ? res.classOtherCallDuration / 1000 / res.classOtherNum : 0,

  } : null
  loading1.value--
}

// 数据选择：CONNECT： 外呼接通率，2：无声占比，3：秒挂1s，4： 秒挂2s，5： 呼送失败
const handleActiveTab = (val: string) => {
  if (!val) return
  searchForm.type = val as AccountStatisticsTypeEnum
  search()
}

// 中部折线图
const lineChartMultList = ref<MonitorChartItem[]>([])
const lineChartXName = ref('时间')
// 获取折线图数据
const findTimeDistributionByCondition = async () => {
  loading1.value++
  const [_, data] = await to(statisticsModel.findTimeDistributionByCondition(searchForm)) as [any, Record<string, {num: number, total:number,}>]
  lineChartMultList.value = []
  if (data && Object.keys(data) && Object.keys(data).length > 0) {
    Object.keys(data).map(item => {
      let value1 = 0
      let value2 = 0
      let value3 = 0
      if (searchForm.type &&  [AccountStatisticsTypeEnum['外呼接通率']].includes(searchForm.type)) {
        value1 = data[item].total || 0
        value2 = +(data[item].num / data[item].total).toFixed(4)
        value3 = data[item].num || 0
      } else if (searchForm.type?.includes('_DURATION')) {
        value1 = (data[item].num || 0) / 1000
        value2 = +(data[item].num / 1000 / data[item].total).toFixed(4)
        value3 = data[item].total || 0
      } else {
        value1 = data[item].num || 0
        value2 = +(data[item].num / data[item].total).toFixed(4)
        value3 = data[item].total || 0
      }
      lineChartMultList.value.push({
        name: item.trim(),
        xName: item.split('-')[0],
        value1,
        value2,
        value3,
      })
    })
    lineChartMultList.value.sort((a, b) => {
      const bb = time2Minute(b.xName)
      const aa = time2Minute(a.xName)
      return aa - bb
    })
  }
  loading1.value--
}

const handleIntervalChange = () => {
  findTimeDistributionByCondition()
}


/** 底部geo图和柱状图 */ 
type ProvinceChart = {
  data: MonitorChartItem[]
  province?: string,
}
const geoData = ref<MonitorChartItem[]>([])
// 柱状图（当前）数据
const currentProvinceData = reactive<ProvinceChart>({ data: [] })
// 柱状图（上一个）数据
const preProvinceData = reactive<ProvinceChart>({ data: [] })

// 获取geo图和柱状图数据
const getGeoData = async () => {
  if (searchForm.provinceCode || searchForm.cityCode) return
  loading2.value++
  const [err, data] = await to(statisticsModel.findCountryDistributionByCondition({
    ...searchForm,
    timeGap: undefined,
  }))
  const dataArr: {
    name: string;
    value1: number;
    value2: number;
    value3?: number;
  }[] = []
  if (!data || !Object.keys(data)?.length) {
    geoData.value = []
    preProvinceData.data = []
    preProvinceData.province = ''
    currentProvinceData.data = []
    currentProvinceData.province = ''
    loading2.value--
    return 
  }
  data && Object.keys(data)?.map(item => {
    let value1 = 0
    let value2 = 0
    let value3 = 0
    if (searchForm.type &&  [AccountStatisticsTypeEnum['外呼接通率']].includes(searchForm.type)) {
      value1 = data[item].total || 0
      value2 = +(data[item].num / data[item].total).toFixed(4)
      value3 = data[item].num || 0
    } else if (searchForm.type?.includes('_DURATION')) {
      value1 = (data[item].num || 0) / 1000
      value2 = +(data[item].num / 1000 / data[item].total).toFixed(4)
      value3 = data[item].total || 0
    } else {
      value1 = data[item].num || 0
      value2 = +(data[item].num / data[item].total).toFixed(4)
      value3 = data[item].total || 0
    }
    dataArr.push({value1, value2, value3, name: item})
  })
  geoData.value = dataArr.sort((a,b) => b.value1 - a.value1)
  if (geoData.value?.length > 0) {
    await handleProvinceChange(geoData.value[1]?.name ? geoData.value[1].name : geoData.value[0].name)
    await handleProvinceChange(geoData.value[0].name)
  } else {
    preProvinceData.data = []
    preProvinceData.province = ''
    currentProvinceData.data = []
    currentProvinceData.province = ''
  }
  loading2.value--
}

// 点击geo省份处理函数，柱状图变化
const handleProvinceChange = async (prov: string) => {
  if (!prov || prov === currentProvinceData.province) return
  loading3.value++
  const params = JSON.parse(JSON.stringify(searchForm))
  params.province = prov
  params.timeGap = undefined
  const [err, provinceData] = await to(statisticsModel.findProvinceDistributionByCondition(params))
  if (provinceData && Object.keys(provinceData)?.length > 0) {
    preProvinceData.data = [...currentProvinceData.data]
    preProvinceData.province = currentProvinceData.province
    if (provinceData) {
      currentProvinceData.data = []
      currentProvinceData.province = prov
      Object.keys(provinceData).map(item => {
        let value1 = 0
        let value2 = 0
        let value3 = 0
        if (searchForm.type &&  [AccountStatisticsTypeEnum['外呼接通率']].includes(searchForm.type)) {
          value1 = provinceData[item].total || 0
          value2 = +(provinceData[item].num / provinceData[item].total).toFixed(4)
          value3 = provinceData[item].num || 0
        } else if (searchForm.type?.includes('_DURATION')) {
          value1 = (provinceData[item].num || 0) / 1000
          value2 = +(provinceData[item].num / 1000 / provinceData[item].total).toFixed(4)
          value3 = provinceData[item].total || 0
        } else {
          value1 = provinceData[item].num || 0
          value2 = +(provinceData[item].num / provinceData[item].total).toFixed(4)
          value3 = provinceData[item].total || 0
        }
        currentProvinceData.data.push({
          value1, value2, value3, 
          name: item
        })
      })
      currentProvinceData.data.sort((a,b) => b.value1 - a.value1)
    }
  } else {
    ElMessage.warning(`${prov}数据获取失败`)
  }
  loading3.value--
}


// 初始化所有图数据
const search = () =>{
  Object.assign(searchForm,{
    ...props.filterData,
    timeGap: searchForm.timeGap,
    type: searchForm.type,
  })
  findTimeDistributionByCondition()
  getGeoData()
  emits('update:refresh', false)
}


findStatisticsByCondition()
search()
// 主动触发刷新（外部组件）
watch(() => props.refresh, n => {
  if (!n) return
  trace({
    page: (userInfo.accountType === 0 ? '统计报表-运营端-' : '统计报表-商户端-') + userInfo.accountType,
    params: props.filterData,
  })
  findStatisticsByCondition()
  search()
})

onUnmounted(() => {
  // @ts-ignore
  lineChartMultList.value = null
  // @ts-ignore
  geoData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.line-chart-box {
  :deep(.el-radio-button__inner) {
    border: 0;
  }
  .button-box {
    width: 260px;
    height: 110px;
    flex-shrink: 0;
    flex-grow: 0;
    padding: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 8px;
    padding: 8px 12px;
    position: relative;
    &.active {
      background: linear-gradient(180deg, #0167FF 0%, #019FFF 100%);;
      color: #fff;
      .transparent-select {
        :deep(.el-input__inner) {
          color: white;
        }
        :deep(.el-input__wrapper) {
          background: transparent;
          color: white;
        }
      }
      &::after {
        content: "";
        position: absolute;
        background: inherit;
        z-index: 0;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width:0;
        height: 0;
        border-top: 6px solid #019FFF;
		    border-left: 6px solid transparent;
		    border-right: 6px solid transparent;
      }
    }
  }
}
:deep(.el-checkbox__label) {
  font-size: 13px;
}

</style>
