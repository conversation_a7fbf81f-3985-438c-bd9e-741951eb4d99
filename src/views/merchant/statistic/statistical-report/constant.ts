import { AccountStatisticsSearchParams, AccountStatisticsTypeEnum, AccountChartStatistics } from '@/type/task'
import dayjs from 'dayjs'
import { lineChartModel, lineSupplierModel, lineMerchantModel } from '@/api/line'

export class SearchRecordFormOrigin implements AccountStatisticsSearchParams{
  taskStatusList = undefined
  aiOutboundTaskType = undefined
  scriptStringIdList = undefined
  taskIdList = undefined
  operator = undefined
  provinceCode = undefined
  province = undefined
  cityCode = undefined
  queryDate = dayjs().format('YYYY-MM-DD')
  type = undefined
  groupId = undefined
}

export const timeRangeList = [
  {label: '近5分钟', value: 5},
  {label: '近15分钟', value: 15},
  {label: '近30分钟', value: 30},
  {label: '近60分钟', value: 60},
  {label: '今日', value: 0},
]

// 折线图时间颗粒度
export const intervalList = [
  { name: '5min', val: 5 },
  { name: '10min', val: 10 },
  { name: '15min', val: 15 },
  { name: '30min', val: 30 },
  { name: '60min', val: 60 },
]

// 报表日期，常用数值
export const shortcuts = [
  { text: '今日', value: dayjs() },
  { text: '昨日', value: dayjs().subtract(1, 'day') },
]
// 返回组件禁用的日期，用于限制可选日期范围
export const disabledDate = (time: Date) => {
  // 最小时间可选前15天
  const _minTime = Date.now() - 24 * 60 * 60 * 1000 * 15
  // 最大时间可选今天
  const _maxTime = Date.now()
  return time.getTime() < _minTime || time.getTime() > _maxTime
}


export const accountStatisticsTypeObj: Record<string, {
  legend: string[],
  isConnect?: boolean,
  name?: string,
  num: keyof AccountChartStatistics,
}> =  {
  [AccountStatisticsTypeEnum['外呼接通率']]: {
    legend: ['外呼量', '呼叫接通率', '接通数'],
    name: '外呼接通率',
    isConnect: true,
    num: 'totalConnectNum',
  },
  [AccountStatisticsTypeEnum['意向客户占比']]: {
    legend: ['意向客户数', '意向客户占比', '接通数'],
    name: '意向客户占比',
    num: 'intentionNum',
  },
  
  [AccountStatisticsTypeEnum['无声通话占比']]: {
    legend: ['无声通话数', '无声通话占比', '接通数'],
    name: '无声通话占比',
    isConnect: false,
    num: 'silenceCallNum',
  },
  [AccountStatisticsTypeEnum['秒挂（1S）占比']]: {
    legend: ['秒挂（1S）数', '秒挂（1S）占比', '接通数'],
    name: '秒挂（1S）数',
    isConnect: false,
    num: 'oneSecondConnectedNum',
  },
  [AccountStatisticsTypeEnum['秒挂（2S）占比']]: {
    legend: ['秒挂（2S）数', '秒挂（2S）占比', '接通数'],
    name: '秒挂（2S）占比',
    isConnect: false,
    num: 'twoSecondConnectedNum',
  },
  [AccountStatisticsTypeEnum['沉默挂机占比']]: {
    legend: ['沉默挂机数', '沉默挂机占比', '接通数'],
    name: '沉默挂机占比',
    isConnect: false,
    num: 'silenceHangupNum',
  },
  [AccountStatisticsTypeEnum['小助理占比']]: {
    legend: ['小助理通话数', '小助理占比', '接通数'],
    name: '小助理占比',
    isConnect: false,
    num: 'assistantNum',
  },
  [AccountStatisticsTypeEnum['A类占比']]: {
    legend: ['A类通话数', 'A类占比', '接通数'],
    name: 'A类占比',
    isConnect: false,
    num: 'classANum',
  },
  [AccountStatisticsTypeEnum['B类占比']]: {
    legend: ['B类通话数', 'B类占比', '接通数'],
    name: 'B类占比',
    isConnect: false,
    num: 'classBNum',
  },
  [AccountStatisticsTypeEnum['C类占比']]: {
    legend: ['C类通话数', 'C类占比', '接通数'],
    name: 'C类占比',
    isConnect: false,
    num: 'classCNum',
  },
  [AccountStatisticsTypeEnum['D类占比']]: {
    legend: ['D类通话数', 'D类占比', '接通数'],
    name: 'D类占比',
    isConnect: false,
    num: 'classDNum',
  },
  [AccountStatisticsTypeEnum['E类占比']]: {
    legend: ['E类通话数', 'E类占比', '接通数'],
    name: 'E类占比',
    isConnect: false,
    num: 'classENum',
  },
  [AccountStatisticsTypeEnum['F类占比']]: {
    legend: ['F类通话数', 'F类占比', '接通数'],
    name: 'F类占比',
    isConnect: false,
    num: 'classFNum',
  },
  [AccountStatisticsTypeEnum['G类占比']]: {
    legend: ['G类通话数', 'G类占比', '接通数'],
    name: 'G类占比',
    isConnect: false,
    num: 'classGNum',
  },
  [AccountStatisticsTypeEnum['其他类占比']]: {
    legend: ['其他类通话数', '其他类占比', '接通数'],
    name: '其他类占比',
    isConnect: false,
    num: 'classOtherNum',
  },
  // 平均通话时长全部，A-其他
  [AccountStatisticsTypeEnum['平均通话时长']]: {
    legend: ['通话时长', '平均通话时长', '接通数'],
    name: '平均通话时长',
    isConnect: false,
    num: 'avgCallDuration',
  },
  [AccountStatisticsTypeEnum['A类平均通话时长']]: {
    legend: ['通话时长', 'A类平均通话时长', '接通数'],
    name: 'A类平均通话时长',
    isConnect: false,
    num: 'avgClassACallDuration',
  },
  [AccountStatisticsTypeEnum['B类平均通话时长']]: {
    legend: ['通话时长', 'B类平均通话时长', '接通数'],
    name: 'B类平均通话时长',
    isConnect: false,
    num: 'avgClassBCallDuration',
  },
  [AccountStatisticsTypeEnum['C类平均通话时长']]: {
    legend: ['通话时长', 'C类平均通话时长', '接通数'],
    name: 'C类平均通话时长',
    isConnect: false,
    num: 'avgClassCCallDuration',
  },
  [AccountStatisticsTypeEnum['D类平均通话时长']]: {
    legend: ['通话时长', 'D类平均通话时长', '接通数'],
    name: 'D类平均通话时长',
    isConnect: false,
    num: 'avgClassDCallDuration',
  },
  [AccountStatisticsTypeEnum['E类平均通话时长']]: {
    legend: ['通话时长', 'E类平均通话时长', '接通数'],
    name: 'E类平均通话时长',
    isConnect: false,
    num: 'avgClassECallDuration',
  },
  [AccountStatisticsTypeEnum['F类平均通话时长']]: {
    legend: ['通话时长', 'E类平均通话时长', '接通数'],
    name: 'F类平均通话时长',
    isConnect: false,
    num: 'avgClassFCallDuration',
  },
  [AccountStatisticsTypeEnum['G类平均通话时长']]: {
    legend: ['通话时长', 'G类平均通话时长', '接通数'],
    name: 'G类平均通话时长',
    isConnect: false,
    num: 'avgClassGCallDuration',
  },
  [AccountStatisticsTypeEnum['其他类平均通话时长']]: {
    legend: ['通话时长', '其他类平均通话时长', '接通数'],
    name: '其他类平均通话时长',
    isConnect: false,
    num: 'avgClassOtherCallDuration',
  },
};

export const categroryList = [
  {name: 'A', value: AccountStatisticsTypeEnum['A类占比']},
  {name: 'B', value: AccountStatisticsTypeEnum['B类占比']},
  {name: 'C', value: AccountStatisticsTypeEnum['C类占比']},
  {name: 'D', value: AccountStatisticsTypeEnum['D类占比']},
  {name: 'E', value: AccountStatisticsTypeEnum['E类占比']},
  {name: 'F', value: AccountStatisticsTypeEnum['F类占比']},
  {name: 'G', value: AccountStatisticsTypeEnum['G类占比']},
  {name: '其他', value: AccountStatisticsTypeEnum['其他类占比']},
]
export const durationCategroryList = [
  {name: '', value: AccountStatisticsTypeEnum['平均通话时长']},
  {name: 'A类', value: AccountStatisticsTypeEnum['A类平均通话时长']},
  {name: 'B类', value: AccountStatisticsTypeEnum['B类平均通话时长']},
  {name: 'C类', value: AccountStatisticsTypeEnum['C类平均通话时长']},
  {name: 'D类', value: AccountStatisticsTypeEnum['D类平均通话时长']},
  {name: 'E类', value: AccountStatisticsTypeEnum['E类平均通话时长']},
  {name: 'F类', value: AccountStatisticsTypeEnum['F类平均通话时长']},
  {name: 'G类', value: AccountStatisticsTypeEnum['G类平均通话时长']},
  {name: '其他', value: AccountStatisticsTypeEnum['其他类平均通话时长']},
]
