<template>
  <div class="tw-grid tw-grid-cols-3 tw-gap-[12px] tw-w-full">
    <!-- 拨打统计 -->
    <!--转化漏斗-->
    <div v-loading="loadingFunnel" class="tw-relative tw-bg-white tw-h-[320px] tw-rounded-[4px] tw-col-span-2">
      <FunnelChart :data="funnelChartData" title="转化漏斗" :tooltipContent="funnelTooltipContent">
        <el-radio-group v-model="funnelType" @change="updateFunnel">
          <el-radio-button :label="0">意向客户</el-radio-button>
          <el-radio-button :label="1">短信发送</el-radio-button>
        </el-radio-group>
      </FunnelChart>
    </div>

    <!-- 标签统计 -->
    <div class="tw-row-span-2 tw-col-span-1 tw-h-[732px] tw-relative tw-bg-white tw-rounded-[4px]  tw-flex tw-flex-col tw-overflow-hidden tw-text-[#313233] tw-p-[16px]">
      <div class="tw-flex tw-justify-between tw-items-center tw-mb-[4px]">
        <div class="tw-text-[14px] tw-font-[600] tw-flex tw-items-center tw-shrink-0">
          标签统计
          <el-tooltip :show-after="500">
            <template #content>
              <div class="tw-text-left">
                <div>标签统计</div>
                <div>接通数：该话术呼叫成功的号码数之和</div>
                <div>命中次数：标签的命中次数</div>
                <div>命中占比：命中次数/接通数</div>
              </div>
            </template>

            <el-icon :size="12" class="tw-cursor-pointer tw-ml-[4px]" color="var(--primary-black-color-400)">
              <SvgIcon name="warning" />
            </el-icon>
          </el-tooltip>
         
        </div>
        <div class="tw-my-[4px] tw-flex tw-items-center tw-leading-[20px] info-title">
          <span>接通数：</span>
          <span>{{ labelPutThroughNum || '-' }}</span>
        </div>
      </div>
      
      <div class="tw-mb-[8px]">
        <el-input
          v-model="labelSearchName"
          @keyup.enter="updateLabel()"
          @clear="updateLabel()"
          clearable
          placeholder="输入标签名称，回车搜索"
        ></el-input>
      </div>
      <template v-if="labelTableData?.length">
        <el-table
          :data="labelTempTableData"
          v-loading="loadingLabel"
          :header-cell-style="tableHeaderStyle"
          stripe
          class="tw-grow"
          row-key="intentionLabelId"
          @sort-change="handleSortChange"
        >
          <el-table-column label="标签名称" prop="intentionLabelName" align="left" min-width="130" show-overflow-tooltip></el-table-column>
          <el-table-column label="命中次数" sortable prop="intentionLabelNum" align="left" min-width="100" show-overflow-tooltip>
            <template #header="{ column }">
              <div class="tw-flex tw-items-center tw-justify-start">
                <span>{{column.label}}</span>
                <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
                  <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
                  <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
                </div>
              </div>
            </template>
            <template #default="{ row }">
              {{ row.intentionLabelNum }}
            </template>
          </el-table-column>
          <el-table-column label="命中占比" sortable prop="labelHitRates" align="left" width="100" show-overflow-tooltip>
            <template #header="{ column }">
              <div class="tw-flex tw-items-center tw-justify-start">
                <span>{{column.label}}</span>
                <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
                  <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
                  <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
                </div>
              </div>
            </template>
            <template #default="{ row }">
              {{ row.labelHitRates ? row.labelHitRates + '%' : '-' }}
            </template>
          </el-table-column>
        </el-table>
        <PaginationBox
          class="tw-grow-0"
          :pageSize="pageSize"
          :currentPage="currentPage"
          :total="labelTableData.length || 0"
          mini
          @search="updateLabel"
          @update="updatePage"
        >
        </PaginationBox>
      </template>
      <el-empty v-else style="padding:0;"></el-empty>
    </div>

    <!--意向分类-->
    <div v-loading="loadingCategory" class="tw-col-span-2 tw-relative tw-bg-white tw-rounded-[4px] tw-h-[400px]">
      <PieChart :data="categoryChartData" :richWidth="[60, 70, 160]" title="意向分类" yName="分类占比" :tooltip-content="categoryTooltipContent">
      </PieChart>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, ref, computed, watch } from 'vue'
import { Throttle } from '@/utils/utils'
import { useTaskStore } from '@/store/taskInfo'
import dayjs from 'dayjs'
import {
  LabelDataItem,
  FunnelDataType,
  AccountStatisticsSearchParams
} from '@/type/task'
import { statisticsModel } from '@/api/ai-report'
import { useUserStore } from '@/store/user'
import { tableHeaderStyle } from '@/assets/js/constant'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { IntentionClassEnum } from '@/type/common'

// 动态引入组件
const FunnelChart = defineAsyncComponent(() => import('@/components/charts/FunnelChart.vue'))
const PieChart = defineAsyncComponent(() => import('@/components/charts/PieChart.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

// 组件props、emits
const props = defineProps<{
  needUpdate: boolean,
  filterData: AccountStatisticsSearchParams,
}>();
const emits = defineEmits(['update:needUpdate',])
// 正在加载
const taskStore = useTaskStore()

// 用户信息，更新用户运营时间
const useInfo = useUserStore()
// ---------------------------------------- 通用 结束 ----------------------------------------


// ---------------------------------------- 外呼统计 开始 ----------------------------------------

// 正在加载漏斗数据
const loadingFunnel = ref(false)
// 漏斗数据节流锁
const throttleFunnel = new Throttle(loadingFunnel)
// 转换漏斗，接口数据
const funnelList = ref<FunnelDataType>({})
// 转换漏斗，展示的是-----0：意向客户 | 1：短信发送
const funnelType = ref(0)
// 转换漏斗，图表数据
const funnelChartData = ref<{ name: string, value: number, rate?: number }[]>([])
const funnelTooltipContent = computed(() => {
  const res = [
    '计划呼叫名单：各任务导入的名单数之和', '实际呼叫名单：各任务外呼次数>=1的名单数之和',
    '接通名单：各任务接通次数>=1的名单数之和',
  ]
  return funnelType.value === 1 ? [...res, '短信触发量：各任务中名单短信触发数之和', '短信发送量：各任务中短信状态为“发送成功”的名单数之和']
  : [...res, `意向客户数：各任务分类结果为${taskStore.intentionClassScope || ''}的名单数之和`]
})

/**
 * 更新转换漏斗数据
 */
const updateFunnel = async () => {
  // 节流锁上锁
  if (throttleFunnel.check()) {
    return
  }
  throttleFunnel.lock()

  try {
    // 请求接口
    funnelList.value = <FunnelDataType>await statisticsModel.getFunnel(props.filterData)
    funnelChartData.value = [
      { name: '计划呼叫名单', value: funnelList.value.phoneNum ?? NaN },
      { name: '实际呼叫名单', value: funnelList.value.calledNum ?? NaN },
      { name: '接通名单', value: funnelList.value.putThroughNum ?? NaN, rate: funnelList.value.putThroughRate || 0 },
    ]
    if (funnelType.value === 1) {
      funnelChartData.value = [...funnelChartData.value,
        { name: '短信触发量', value: funnelList.value.triggerSmsNum || 0, rate: funnelList.value.triggerSmsRate || 0 },
        { name: '短信发送量', value: funnelList.value.successSmsNum || 0, rate: funnelList.value.successSmsRate || 0},
      ]
    } else {
      funnelChartData.value = [...funnelChartData.value,
        { name: '意向客户', value: funnelList.value.intentionNum || 0, rate: funnelList.value.intentionRate || 0},
      ]
    }
  } catch (e) {
  } finally {
    // 节流锁解锁
    throttleFunnel.unlock()
  }
}

// 正在加载意向分类
const loadingCategory = ref(false)
// 意向分类节流锁
const throttleCategory = new Throttle(loadingCategory)
// 成功获取意向分类
const finishedCategory = ref(false)
// 意向分类注释
const categoryTooltipContent = ['总接通数：各任务呼叫成功的号码数之和', `意向分类：“意向分类：意向占比 接通数”`, `意向占比：意向接通数/总接通数`, ]

// 意向分类，图表数据
const categoryChartData = ref<{
  name: string,
  value: number,
  num: number
}[]>([])

/**
 * 更新意向分类数据
 */
const updateCategory = async () => {
  // 节流锁上锁
  if (throttleCategory.check()) {
    return
  }
  throttleCategory.lock()
  try {
    // 请求接口
    const res = await statisticsModel.getIntentionList(props.filterData)
    // 转换数据
    categoryChartData.value = Object.keys(IntentionClassEnum).map(item => {
      return {
        name: item,
        value: res?.putThroughNum ? (res[`class${item === '其他' ? 'Other' : item}Num`] || 0) * 100 / (res?.putThroughNum || 1) : 0,
        num: res[`class${item === '其他' ? 'Other' : item}Num`] || 0
      }
      
    })
    // 更新状态为获取成功
    finishedCategory.value = true
  } catch (e) {
  } finally {
    // 节流锁解锁
    throttleCategory.unlock()
  }
}


/**
 * 标签统计列表
 */
//  正在加载标签统计
const loadingLabel = ref(false)
// 标签统计节流锁
const throttleLabel = new Throttle(loadingLabel)
// 标签统计数据
const labelTableData = ref<LabelDataItem[]>([])
// 标签接通数
const labelPutThroughNum = ref(0)
const updateLabel = async () => {
  if (throttleLabel.check()) {
    return
  }
  throttleLabel.lock()

  try {
    // 请求接口
    const res = await statisticsModel.getLabelList(props.filterData)
    // 转换数据
    labelTableData.value = (res?.labelList || []).filter(item => !labelSearchName.value || item.intentionLabelName?.includes(labelSearchName.value))
    labelPutThroughNum.value = res?.putThroughNum || 0
  } catch (e) {
  } finally {
    // 节流锁解锁
    throttleLabel.unlock()
  }
}
// 全部数据排序
const prop = ref('')
const order = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  prop.value = params.prop
  order.value = params.order
}
// 标签统计分页信息
const currentPage = ref(1)
const pageSize = ref(50)
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}

const labelSearchName = ref('')
const labelTempTableData = computed(() => {
  let data = labelTableData.value || []
  if (prop.value && order.value) {
    data = data.sort((a, b) => {
      if (prop.value.includes('Time')) {
        // @ts-ignore
        if (order.value === 'descending') {
          // @ts-ignore
          return dayjs(b[prop.value]).isBefore(dayjs(a[prop.value])) ? -1 : 1
        } else {
          // @ts-ignore
          return dayjs(b[prop.value]).isBefore(dayjs(a[prop.value])) ? 1 : -1
        }
      } else {
        // @ts-ignore
        return order.value === 'descending' ? b[prop.value] - a[prop.value] : a[prop.value] - b[prop.value]
      }
    })
  }
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})


/**
 * 更新外呼统计
 */
const updateCharts = () => {
  // 更新图表数据
  updateFunnel()
  updateLabel()
  updateCategory()
  emits('update:needUpdate', false)
}


const init = async () => {
  // 获取话术列表
  updateCharts()
}

// ---------------------------------------- 立即执行 开始 ----------------------------------------
init()
watch(() => props.needUpdate, n => {
  if (n) {
    init()
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">

</style>
