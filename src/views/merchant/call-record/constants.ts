import { useUserStore } from '@/store/user'
import { AiSemantics } from '@/type/core-semantic'
import to from 'await-to-js'
import { aiOutboundTaskTemplateModel, aiOutboundTaskModel } from'@/api/ai-report'
import { SeatTeam, SeatMember } from '@/type/seat'
import { seatManagerModel } from '@/api/seat'
import { CallStatusEnum, CallStatusEnum1, TaskCallRecordItem } from '@/type/task'
import { enum2Options, findValueInEnum } from '@/utils/utils'
import { CallLimit, FrequencyRestrictionTypeEnum, FrequencyRestrictionInfo, PutThroughLimit } from '@/type/dataFilter'
import { blacklistModel, merchantBlacklistModel, } from '@/api/data-filter'
import { BlackListGroupItem, } from '@/type/dataFilter'

const recordColumnMap = new Map<string, Record<string, string[][]>>([
  ['ai-record', {
    total: [[
      '号码', '姓名', '省市', '呼叫状态', '分类结果', '触发短信', '说话次数', '对话轮次', '通话时长', '外呼时间', '接通时间', '挂断时间',
      '任务名称', '执行话术', '标签', '挂断方', '转为线索'
    ], ['项目', '行业', '产品', '语义', '补偿状态', '等待时长', '商户名称', '所属账号', '供应线路名称', '商户线路编号', '挂机原因', '挂机码', '机器人IP', '中继IP']],
    default: [['号码', '省市', '呼叫状态', '分类结果', '说话次数', '对话轮次', '通话时长', '外呼时间', '标签',], ['项目', '行业', '产品', '任务名称', '执行话术', '语义', '等待时长', '所属账号', '供应线路名称', '商户线路编号', '挂机原因',]],
    disabled: [['号码', '外呼时间'], []],
  }],
  [
    'mix-record', {
      total: [[
        '号码', '姓名', '省市', '呼叫状态', 'AI分类结果', 'AI标签', '人工分类结果', '人工标签', '触发短信', '说话次数', '对话轮次', '通话时长', '外呼时间', '接通时间', '挂断时间',
        '任务名称', '执行话术', '挂断方', '是否转人工', '转人工等待', '漏接坐席', '监听时长', '接待时长', '未介入原因', '接待坐席', '所属坐席组', '话后处理时长', '话后处理超时',
        '表单收集', '跟进备注', '跟进状态', '转为线索',
      ], ['项目', '行业', '产品', '补偿状态', 'AI语义', '等待时长', '商户名称', '所属账号', '供应线路名称', '商户线路编号', '挂机原因', '挂机码', '机器人IP', '中继IP']],
      default: [['号码', '运营商', '省市', '呼叫状态', 'AI分类结果', '人工分类结果', '说话次数', '对话轮次', '通话时长', '外呼时间', 'AI标签', '人工标签',], ['项目', '行业', '产品', '任务名称', '执行话术', 'AI语义', '等待时长', '所属账号', '供应线路名称', '商户线路编号', '挂机原因',]],
      disabled: [['号码', '外呼时间'], []],
    }
  ],
  [
    'manual-record', {
      total: [[
        '号码', '姓名', '省市', '呼叫状态', '通话时长', '接通时间', '外呼时间', '挂断时间', '挂断方', '接待时长', '接待坐席', '所属坐席组', '话后处理时长', '话后处理超时',
        '表单收集', '跟进备注', '跟进状态', '触发短信',
      ], ['项目', '行业', '产品', '商户名称', '所属账号', '等待时长', '供应线路名称', '商户线路编号', '挂机原因',]],
      default: [['号码', '运营商', '省市', '呼叫状态', '通话时长', '外呼时间',], ['项目', '行业', '产品', '等待时长', '所属账号', '供应线路名称', '商户线路编号', '挂机原因',]],
      disabled: [['号码', '外呼时间'], []],
    }
  ],
  [
    'sms-record', {
      total: [[
        '号码', '省市', '短信类型', '执行话术', '任务名称', '接待坐席', '所属坐席组', '触发时间', '提交状态', '提交时间', '回执状态', '回执时间', '回执超时',
        '短信状态', '短信模板', '短信内容', '业务类型'
      ], ['项目', '行业', '产品', '商户名称', '所属账号', '发送类型', '补偿状态', '短链ID', '短链地址', '失败原因', '短信对接账号', '返回码', '短信供应商', '字数统计', '计费条数']],
      default: [['号码', '省市', '触发时间', '短信类型', '短信状态', '短信模板', '短信内容'], ['项目', '行业', '产品', '商户名称', '所属账号', '短信供应商', '短信对接账号', '失败原因',]],
      disabled: [['号码', '触发时间'], []],
    }
  ],
])

export const getColumnSettingByName = (name: string) => {
  const userInfo = useUserStore()
  const accountType = userInfo.accountType
  const obj = recordColumnMap.get(name)
  if (!obj) return {
    totalCols: [], defaultCols: [], disabledCols: [],
  }
  const totalCols: string[] = [...obj?.total[0]]
  const defaultCols: string[] = [...obj?.default[0]]
  const disabledCols: string[] = [...obj?.disabled[0]]
  if (accountType == 0) {
    totalCols.push(...obj?.total[1])
    defaultCols.push(...obj?.default[1])
    disabledCols.push(...obj?.disabled[1])
  }
  return {
    totalCols, defaultCols, disabledCols,
  }
}

export const translateHitSemantic = (ids: string, semanticMap: Record<string, AiSemantics>) => {
  if (!ids) return '-'
  const idsList = ids.split(',')
  const semanticResMap: Map<string, number> = new Map([])
  idsList.forEach(item => {
    const name = semanticMap[item]?.semantic || ''
    if (name && semanticResMap.has(name)) {
      semanticResMap.set(name, semanticResMap.get(name)! + 1)
    } else {
      semanticResMap.set(name || `未知语义(${item})`, 1)
    }
  })
  const res: string[] = []
  semanticResMap.forEach((key,value) => {
    res.push(key === 1 ? value : value + '*' + key)
  })
  return res.join('、')
}

export const filterProvinceName = (name: string) => {
  if (name.length <= 4){
    return name
  } else {
    return name.slice(0, 2) + '省'
  }
}

/**
 * 获取人工外呼团队坐席列表
 * @param accountWithName 是否在账户字段上附带坐席名称，默认true附带，false不附带
 */
export const getCallTeamAndSeatOptions = async (accountWithName: boolean = true) =>  {
  const userStore = useUserStore()
  const [_, res] = await to(userStore.accountType === 0
    ? seatManagerModel.getGlobalSeatTeam()
    : aiOutboundTaskTemplateModel.findAllCallTeams({
      groupId: userStore.groupId,
    }))
  const callTeamList: SeatTeam[] = []
  const callSeatList: SeatMember[] = []
  res?.forEach(item => {
    const { callTeamName, id, leaderAccountId, callSeats, masterAccount } = item
    callTeamList.push({
      callTeamName, id, leaderAccountId, masterAccount
    })
    callSeatList.push(...((callSeats || []).map(item => {
      return {
        masterAccount: masterAccount,
        ...item,
        // 附带名称 account(name)
        // 不附带名称 account
        account: (item.account || '') + (accountWithName ? `(${item.name || ''})` : ''),
      }
    })))
  })
  return {
    callTeamList,
    callSeatList,
  }
}

// 获取呼叫状态列表
export const getCallStatusOptions = () => {
  const userInfo = useUserStore()
  const accountType = userInfo.accountType
  return accountType == 1 ? enum2Options(CallStatusEnum1) : enum2Options(CallStatusEnum)
}

export const translateCallStatus = (status: string | number ) => {
  const userInfo = useUserStore()
  const accountType = userInfo.accountType
  return findValueInEnum(status, accountType == 1 ? CallStatusEnum1 : CallStatusEnum)
}

// 任务呼叫状态样式
export const getCallStatusClass = (val?: CallStatusEnum) => {
  if (!val) return ''
  if (CallStatusEnum['呼叫成功'].includes(val)) {
    return 'green-status'
  } else if (CallStatusEnum['未接通'].split(',').includes(val)) {
    return 'blue-status'
  } else if ( CallStatusEnum['已屏蔽'].includes(val)) {
    return 'orange-status'
  } else {
    return 'red-status'
  }
}

/**
 * 格式化频率限制文本
 * @param {'15' | '16'} status 类型，15: 拨打限制, 16: 拨通限制
 * @param {FrequencyRestrictionInfo} row 频率限制信息列表
 */
export const formatFrequencyRestrictionsText = (row: FrequencyRestrictionInfo | null = null, status: '15' | '16') => {
  if (!row) return ''
  const list1 = row.callLimit || []
  const list2 = row.putThroughLimit || []
  const resultList1 = list1.map(item => {
    return `${item.callLimit ?? ''}次/${item.callLimitTime ?? ''}天`
  })
  const resultList2 = list2.map(item => {
    return `${item.putThroughLimit ?? ''}次/${item.putThroughLimitTime ?? ''}天`
  })

  return (row.frequentType == '0'? '全局限制：' : row.productIndustryName + '：') + (resultList1?.length > 0 && status == '15' ? '拨打 '+ resultList1.join('、') : '')
  + (resultList2?.length > 0 && status == '16'? '拨通 '+ resultList2.join('、') : '')
}

/**
 * 翻译挂机原因
 * @param row 任务通话记录项目
 * @param blackListMap 黑名单映射
 * @param frequencyRestrictionMap 频率限制映射
 * @param tenantblackList 商户黑名单列表
 */
export const translateCause = (
  row: TaskCallRecordItem,
  blackListMap: Map<number, string>,
  frequencyRestrictionMap: Map<number, FrequencyRestrictionInfo>,
  tenantblackList: BlackListGroupItem[] | null,
) => {
  if (row.callStatus && [CallStatusEnum['平台黑名单']].includes(row.callStatus)) {
    return row.status && row.status > 0 ? blackListMap.get(row.status) || '-' : '-'
  } else if(row.callStatus && CallStatusEnum['商户黑名单'].split(',').includes(row.callStatus)) {
    // @ts-ignore
    return row.status && row.status > 0 ? tenantblackList.find(item => item.id === row.status)?.groupName || '-' : '-'
  } else if(row.callStatus && CallStatusEnum['平台频率限制'].split(',').includes(row.callStatus)) {
    // @ts-ignore
    return row.status && row.status > 0 ? formatFrequencyRestrictionsText(frequencyRestrictionMap.get(row.status), row.callStatus) || '-' : '-'
  } else {
    return row.cause || '-'
  }
}

// 获取黑名单（包含历史）
export const updateBlacklist = async (): Promise<Map<number, string>> => {
  const res = await blacklistModel.getAllGroupList()
  const blackListMap:Map<number, string> = new Map([])
  res.forEach(item => {
    item.id && blackListMap.set(item.id, item.groupName || '')
  })
  return blackListMap
}

// 获取商户黑名单
export const updateTenantBlacklist = async (): Promise<BlackListGroupItem[]> => {
  const userInfo = useUserStore()
  const accountType = userInfo.accountType
  let res: BlackListGroupItem[] = []
  const [err, data] = await to(accountType == 1
    ? merchantBlacklistModel.getGroupList({}) 
    : merchantBlacklistModel.getGroupListByGroupId({})
  )
  res = data || []
  return res
}

export const containList = [
  {name: '包含', value: 'contains'},
  {name: '排除', value: 'excludes'},
  {name: '包含多个', value: 'containsAll'},
]
