<template>
  <HeaderBox title="人工直呼记录" />
  <div class="call-record-container">
    <div class="tw-w-full tw-p-[16px] tw-bg-white tw-mb-[16px] tw-grow-0">
      <div class="tw-grid tw-grid-cols-4 tw-gap-[10px]" :class="isExpand ? 'tw-pb-[8px]':'tw-pb-[12px]'">
        <div class="item">
           <InputPhonesBox
            v-model:value="searchForm.phone"
            @search="search()"
          />
        </div>
        <div class="item">
          <el-select
            v-model="callStatusArr"
            placeholder="呼叫状态"
            multiple
            collapse-tags
            :max-collapse-tags="1"
            collapse-tags-tooltip
            clearable
          >
            <el-option v-for="item in callStatusOptions" :key="item.name" :label="item.name" :value="item.value"/>
          </el-select>
        </div>
        <div class="item tw-col-span-2">
          <span class="tw-w-[66px] tw-shrink-0">外呼时间：</span>
          <TimePickerBox
            v-model:start="searchForm.calloutStartTime"
            :splitToday="true"
            @change="handleTimeChange()"
            v-model:end="searchForm.calloutEndTime"
            clearable
          />
        </div>
      </div>
      <div  v-show="isExpand" class="tw-grid tw-grid-cols-4 tw-gap-[8px] tw-pb-[12px]">
        <div v-if="accountType === 0" class="item">
          <el-input
            v-model.trim="searchForm.account"
            placeholder="商户账号"
            @keyup.enter="search()"
            clearable
          >
          </el-input>
        </div>
        <div v-if="accountType === 0" class="item">
          <el-input
            v-model.trim="searchForm.tenantName"
            placeholder="商户名称"
            @keyup.enter="search()"
            clearable
          >
          </el-input>
        </div>
        <div v-if="accountType === 0" class="item tw-col-span-2">
          <span class="tw-w-[66px] tw-shrink-0">接通时间：</span>
          <TimePickerBox
            v-model:start="searchForm.talkTimeStartStart"
            v-model:end="searchForm.talkTimeStartEnd"
            :clearable="false"
            placeholder="接通时间"
          />
        </div>
        <!-- <div class="item">
          <SelectPageBox
            v-model:selectVal="searchForm.tenantBlacklist"
            :options="tenantBlacklist || []"
            name="groupName"
            val="id"
            placeholder="商户黑名单"
            filterable
            class="tw-grow"
            clearable
            multiple
          >
          </SelectPageBox>
        </div> -->
        <div class="item">
          <el-input
            v-model.trim="searchForm.name"
            placeholder="姓名"
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <OperationAndCitySelectBox
          v-model:operator="searchForm.operator"
          v-model:province="searchForm.province"
          v-model:city="searchForm.city"
        />

        <div class="item">
          <SelectBox
            v-model:selectVal="callTeamIds"
            :options="callTeamList||[]"
            name="callTeamName"
            val="id"
            placeholder="坐席组"
            filterable
            class="tw-flex-grow"
            multiple
          >
            <template v-slot:option-tips="{ option }">
              <span class="tw-text-gray-400">{{ option.masterAccount?.account || ''}}</span>
            </template>
          </SelectBox>
        </div>
        <div class="item">
          <SelectBox
            v-model:selectVal="callSeatIds"
            :options="callSeatList||[]"
            name="account"
            val="id"
            placeholder="坐席"
            filterable
            class="tw-flex-grow"
            multiple
          >
            <template v-slot:option-tips="{ option }">
              <span class="tw-text-gray-400">{{ option.masterAccount?.account || '' }}</span>
            </template>
          </SelectBox>
        </div>

        <div class="item">
          <el-input
            v-model.trim="searchForm.followUpNote"
            placeholder="跟进备注"
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div class="item">
          <el-select v-model="searchForm.followUpStatus" placeholder="跟进状态" clearable>
            <el-option v-for="item in followUpStatusOption" :label="item.name" :value="item.value" :key="item.value"/>
          </el-select>
        </div>
        <div class="item">
          <el-select v-model="searchForm.isPostingOutOfTime" placeholder="话后处理超时" clearable>
            <el-option label="是" :value="true"/>
            <el-option label="否" :value="false"/>
          </el-select>
        </div>
        <div class="item">
          <el-select v-model="searchForm.whoHangup" placeholder="挂断方" clearable>
            <el-option label="坐席" :value="2"/>
            <el-option label="客户" :value="1"/>
          </el-select>
        </div>
        <div class="item">
          <el-select v-model="searchForm.ifSendSms" placeholder="是否触发短信" clearable>
            <el-option label="是" value="是"/>
            <el-option label="否" value="否"/>
          </el-select>
        </div>
        <template v-if="accountType === 0">
          <div class="item">
            <el-input
              v-model.trim="searchForm.merchantLineCode"
              placeholder="商户线路编号"
              @keyup.enter="search()"
              clearable
            >
            </el-input>
          </div>
          <div class="item">
            <el-input
              v-model.trim="searchForm.lineCode"
              placeholder="供应线路名称"
              @keyup.enter="search()"
              clearable
            >
            </el-input>
          </div>
        </template>
        <div class="item">
          <el-input
            v-model.trim="searchForm.customerReplyContent"
            placeholder="客户回复"
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div class="item">
          <el-select v-model="searchForm.needOrder" placeholder="是否排序">
            <el-option label="排序" value="1"/>
            <el-option label="不排序" value="0"/>
          </el-select>
        </div>
        <div class="item tw-pr-[12px]">
          <span class="tw-w-[62px] tw-shrink-0">接待/通时&nbsp;</span>
          <InputNumberBox v-model:value="searchForm.callDurationLeft" :max="searchForm.callDurationRight || 10000" placeholder="最低" style="width: 45%" append="秒"/>
          <span>&nbsp;至&nbsp;</span>
          <InputNumberBox v-model:value="searchForm.callDurationRight" placeholder="最高" style="width: 45%" append="秒" :min="searchForm.callDurationLeft || 0" :max="10000"/>
        </div>
        <div class="item tw-pr-[12px]">
          <span class="label">话后&nbsp;</span>
          <InputNumberBox v-model:value="searchForm.postingDurationLeft" :max="searchForm.postingDurationRight || 10000" placeholder="最低" style="width: 45%" append="秒"/>
          <span>&nbsp;至&nbsp;</span>
          <InputNumberBox v-model:value="searchForm.postingDurationRight" placeholder="最高" style="width: 45%" append="秒" :min="searchForm.postingDurationLeft || 0" :max="10000"/>
        </div>
        <div v-if="accountType === 0" class="item tw-col-span-2 tw-pr-[12px]">
          <span class="tw-w-[70px] tw-grow-0 tws-shrink-0">等待时长&nbsp;</span>
          <InputNumberBox v-model:value="searchForm.waitmsecLeft" :max="searchForm.waitmsecRight || 300000" placeholder="最低" style="width: 45%" append="毫秒"/>
          <span>&nbsp;至&nbsp;</span>
          <InputNumberBox v-model:value="searchForm.waitmsecRight" placeholder="最高" style="width: 45%" append="毫秒" :min="searchForm.waitmsecLeft || 0" :max="300000"/>
        </div>
        <!-- <div class="item">
          <el-checkbox label="只看未读" />
          <el-checkbox label="触发短信" />
        </div> -->
      </div>
      <div class="tw-flex tw-justify-between tw-pt-[12px] tw-items-center tw-border-t-[1px] tw-h-[32px]">
        <div class="tw-float-left tw-leading-[32px]">
          <ColumnSetting
            :totalList="totalCols"
            :defaultList="defaultCols"
            :disabledList="disabledCols"
            :name="'manual-record'"
          />
        </div>
        <div class="tw-float-right tw-leading-[32px]">
          <el-button type="primary" @click="clearSearchForm" link>
            <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
            <span>重置</span>
          </el-button>
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
          <el-button type="primary" v-if="isExpand" @click="isExpand=false" link>
            收起<el-icon size="--el-font-size-base"><ArrowUp /></el-icon>
          </el-button>
          <el-button type="primary" v-else @click="isExpand=true" link>
            展开<el-icon size="--el-font-size-base"><ArrowDown /></el-icon>
          </el-button>
        </div>
      </div>
    </div>
    <el-table
      :data="tableData||[]"
      ref="tableRef"
      v-loading="loading"
      :header-cell-style="{background:'#F7F8FA', color: '#646566'}"
      stripe
      border
      class="tw-flex-grow"
      :row-key="(row: TaskCallRecordItem) => (row.recordId??'') + (row.id??'')"
    >
      <el-table-column v-if="selectCols.includes('号码')" label="号码" align="left" fixed="left" min-width="170">
        <template #default="{ row, $index }">
          <div class="phone-msg" :class="currentIndex===$index ? 'tw-text-[#165DFF]':''">
            <span>{{ filterPhone(row.recordId) + ' ' + (row.operator || '') }}</span>
            <el-tooltip content="复制" placement="right" :show-after="500">
              <el-icon :size="14" class="hover:tw-text-[var(--el-color-primary)] tw-cursor-pointer" @click="copy(row.recordId)"><SvgIcon name="copy" color="inherit"></SvgIcon></el-icon>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('姓名')" property="name" label="姓名" align="left" min-width="80" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column v-if="selectCols.includes('省市')" label="省市" align="left" min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.province || row.city ? (filterProvinceName(row.province) || '') + ' ' + (row.city || '') : '-' }}
        </template>
      </el-table-column>
      <template v-if="accountType === 0">
        <el-table-column v-if="selectCols.includes('项目')" property="programName" label="项目" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData">
          <template #default="{ row }">
            {{ row.account ? globalStore.projectGoupIdMap?.get(row.account)?.programName || '-' : '-' }}
          </template>
        </el-table-column>
        <el-table-column v-if="selectCols.includes('行业')" property="secondIndustryName" label="行业" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData">
          <template #default="{ row }">
            {{ row.account ? globalStore.projectGoupIdMap?.get(row.account)?.secondIndustryName || '-' : '-' }}
          </template>
        </el-table-column>
        <el-table-column v-if="selectCols.includes('产品')" property="productName" label="产品" align="left" min-width="200" show-overflow-tooltip :formatter="formatterEmptyData">
          <template #default="{ row }">
            {{ row.account ? globalStore.projectGoupIdMap?.get(row.account)?.productName || '-' : '-' }}
          </template>
        </el-table-column>
      </template>
      <el-table-column v-if="selectCols.includes('呼叫状态')" property="callStatus" label="呼叫状态" align="center" min-width="120">
        <template #default="{ row }">
          <span
              v-if="row?.callStatus"
              class="status-box-mini tw-min-w-[80px]"
              :class="getCallStatusClass(row?.callStatus)"
            >
            {{ translateCallStatus(row?.callStatus) }}
           </span>
           <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('通话时长')" property="callDuration" label="通话时长" align="left" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          {{ (row.callDuration??-1)>-1 ? formatDuration(row?.callDuration/1000) || '0' : '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('等待时长') && accountType === 0" property="waitmsec" label="等待时长" align="left" min-width="120">
        <template #default="{ row }">
          {{ row.waitmsec ? formatMsDuration(row.waitmsec/1000) : '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('外呼时间')" property="callOutTime" label="外呼时间" align="center" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('接通时间')" property="talkTimeStart" label="接通时间" align="center" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('挂断时间')" property="talkTimeEnd" label="挂断时间" align="center" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('商户名称') && accountType === 0" property="tenantName" label="商户名称" align="left" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column v-if="selectCols.includes('所属账号') && accountType === 0" property="account" label="所属账号" align="left" min-width="160" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column v-if="selectCols.includes('供应线路名称') && accountType === 0" property="lineCode" label="供应线路名称" align="left" min-width="300" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('商户线路编号') &&  accountType === 0" property="merchantLineCode" label="商户线路编号" align="left" min-width="160" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>

      <el-table-column v-if="selectCols.includes('挂断方')" property="whoHangup" label="挂断方" align="center" min-width="80">
        <template #default="{ row }">
          {{ findValueInEnum(row.whoHangup, HangupEnum)||'-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="accountType === 0 && selectCols.includes('挂机原因')" property="cause" label="挂机原因" align="left" min-width="240" show-overflow-tooltip :formatter="formatterEmptyData">
        <template #default="{ row }">
          {{ translateCause(row, blackListMap, frequencyRestrictionMap, tenantBlacklist) }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('接待时长')" property="callDuration" label="接待时长" align="left" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          {{ (row.callDuration??-1)>-1 ? formatDuration(row?.callDuration/1000) || '0' : '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('接待坐席')" property="callSeatId" label="接待坐席" align="left" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #default="{ row }">
          {{ callSeatList?.find(item => item.id === row.callSeatId)?.account || '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('所属坐席组')" property="callTeamName" label="所属坐席组" align="left" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column v-if="selectCols.includes('话后处理时长')" property="postingDuration" label="话后处理时长" align="left" min-width="120" show-overflow-tooltip>
        <template #default="{ row }">
          {{ (row.postingDuration??-1)>-1 ? formatDuration(row.postingDuration/1000) : '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('话后处理超时')" property="isPostingOutOfTime" label="话后处理超时" align="center" min-width="120">
        <template #default="{ row }">
          {{ findValueInStatus(row.isPostingOutOfTime) || '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('表单收集')" label="表单收集" align="center" min-width="100">
        <template #default="{ row }">
          <el-button v-if="row.clueId" link :type="row.clueId ? 'primary': 'default'" :disabled="!row.clueId" @click="checkFormRecord(row)">查看</el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('跟进备注')" property="followUpNote" label="跟进备注" align="left" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column v-if="selectCols.includes('跟进状态')" property="followUpStatus" label="跟进状态" align="center" min-width="80" :formatter="formatterEmptyData">
        <template #default="{ row }">
          <span
            v-if="row?.followUpStatus"
            class="status-box-mini"
            :class="getFollowStatusClass(row?.followUpStatus)"
          >
            {{findValueInEnum(row?.followUpStatus, FollowUpStatusEnum)}}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('触发短信')" property="ifSendSms" label="触发短信" align="center" min-width="80" :formatter="formatterEmptyData">
        <template #default="{ row }">
          <span v-if="!row.ifSendSms">-</span>
          <el-button v-else-if="row.ifSendSms === '是'" link type="primary" @click="openSmsDialog(row)">是</el-button>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="right" fixed="right" min-width="90">
        <template #default="{ row, $index }">
          <div class="tw-flex tw-justify-end tw-items-center">
            <!-- <el-button v-if="!row?.wholeAudioFileUrl" link disabled>
              <el-icon :size="20"><svg-icon name="wrong-circle" color="var(--primary-black-color-400)"></svg-icon></el-icon>
            </el-button> -->
            <el-button v-if="row?.recordId && tempAudio.callId == row.callId && audioStatus == 'play'" type="primary" link @click="handleAudioPlay(row)">
              <el-icon :size="20"><svg-icon name="stop-circle"></svg-icon></el-icon>
            </el-button>
            <el-button
              v-else
              :type="!row?.wholeAudioFileUrl ? 'default' : 'primary'"
              :disabled="!row?.wholeAudioFileUrl"
              link
              @click="handleAudioPlay(row)"
            >
              <el-icon :size="20"><svg-icon name="play-circle"></svg-icon></el-icon>
            </el-button>
            <el-button
              :type="!(row && row.callStatus == CallStatusEnum['呼叫成功'] ) ? 'default' : 'primary'"
              :disabled="!(row && row.callStatus == CallStatusEnum['呼叫成功'] )"
              link
              @click="showCallRecord($index)"
            >
              <el-icon :size="20">
                <svg-icon name="search"></svg-icon>
              </el-icon>
            </el-button>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      class="tw-grow-0"
      :pageSize="pageSize"
      :dynamic-total="dynamicTotal"
      :currentPage="currentPage"
      :pageSizeList="pageSizeList"
      :total="total"
      @search="search(currentPage)"
      @update="updatePage"
    >
    </PaginationBox>
    <div v-if="tempAudio && tempAudio.callId" class="audio-mode">
      <AudioMode
        :closeable="true"
        :audioUrl="tempAudio.wholeAudioFileUrl || ''"
        :audioName="`${tempAudio.taskName ? (tempAudio.taskName+'_') : ''}${tempAudio.phone|| '未知号码'}` "
        v-model:audioStatus="audioStatus"
        v-model:audioVolume="audioVolume"
         @update:audio-status="inspecteRecordAudio"
        @close="tempAudio.callId = ''"
      >
      </AudioMode>
    </div>
    <CallRecordDetailsDrawer
      v-model:visible="visible"
      :tableData="tableData||[]"
      :recordType="RecordTypeEnum['人工直呼']"
      :total="total"
      :currentIndex="currentIndex"
      :searchForm="searchForm"
      :speechCraftList="[]"
      @update:record="handleDetailsChange"
    />
  </div>
  <FormRecordDialog v-if="formViewVisible" title="表单查看" type="check" v-model:visible="formViewVisible" :formSetting="formSetting" :formRecord="formRecordData"/>
  <TaskSmsDialog
    v-model:visible="smsVisible"
    :type="2"
    :data="currentRecord"
  />
</template>

<script lang="ts" setup>
import { TaskCallRecordItem, TaskCallSearchModal, TaskManageItem, SearchRecordFormOrigin, CallStatusEnum,  RecordTypeEnum, HangupEnum, } from '@/type/task'
import { aiOutboundTaskModel } from '@/api/ai-report'
import { reactive, computed, ref, watch, onUnmounted, defineAsyncComponent, } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowUp, ArrowDown, } from '@element-plus/icons-vue'
import { useGlobalStore } from '@/store/globalInfo'
import { useUserStore } from '@/store/user'
import dayjs from 'dayjs'
import {
  copyText,
  filterPhone,
  formatterEmptyData,
  formatDuration,
  formatMsDuration,
  enum2Options,
  findValueInEnum,
  findValueInStatus,
} from '@/utils/utils'
import { useTaskStore } from '@/store/taskInfo'
import { ResponseData } from '@/axios/request/types'
import OperationAndCitySelectBox from '@/components/OperationAndCitySelectBox.vue'
import { SeatTeam, SeatMember, } from '@/type/seat'
import { formRecordModel, formSettingModel } from '@/api/clue'
import { FormRecordItem, CollectionFormItem, FollowUpStatusEnum, } from '@/type/clue'
import to from 'await-to-js';
import { getColumnSettingByName, getCallTeamAndSeatOptions, filterProvinceName, translateCause, translateCallStatus, getCallStatusOptions, getCallStatusClass, updateBlacklist, updateTenantBlacklist } from './constants'
import { FrequencyRestrictionInfo, BlackListGroupItem, } from '@/type/dataFilter'
import { getFollowStatusClass } from '@/views/merchant/manual-call/components/constant'
import InputNumberBox from '@/components/InputNumberBox.vue'
import HeaderBox from '@/components/HeaderBox.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import PaginationBox from '@/components/PaginationBox.vue'
import CallRecordDetailsDrawer from '@/components/record/CallRecordDetailsDrawer.vue'
import ColumnSetting from '@/components/ColumnSetting.vue'
import { onBeforeRouteLeave } from 'vue-router'
import SelectBox from '@/components/SelectBox.vue'
import TaskSmsDialog from '@/components/task/TaskSmsDialog.vue'
import InputPhonesBox from '@/components/InputPhonesBox.vue'

const FormRecordDialog = defineAsyncComponent({loader: () => import('@/components/clue/FormRecordDialog.vue')})
const AudioMode = defineAsyncComponent({loader: () => import('@/components/AudioMode.vue')})

const taskStore = useTaskStore()
const globalStore = useGlobalStore()
const userInfo = useUserStore()
const accountType = userInfo.accountType
const loading = ref(false)
const {totalCols, defaultCols, disabledCols} = getColumnSettingByName('manual-record')
const selectCols = computed(() => userInfo.colInfo['manual-record'] || [])
// 表格和分页
const tableData = ref<TaskCallRecordItem[] | null>([])
const pageSizeList = [20, 50, 100, 200]
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(pageSizeList[0])
const isExpand = ref(false)

const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  search(currentPage.value, true)
}

const copy =(val: string) => {
  copyText(val || '')
}
const audioVolume = ref<number>(70)


// 搜索数据
const searchForm = reactive<TaskCallSearchModal>(new SearchRecordFormOrigin(accountType))
// 外呼时间变化，更新是否排序筛选项和任务列表
const handleTimeChange = () => {
  if (dayjs(searchForm.calloutStartTime).isBefore(dayjs().startOf('day'))) {
    searchForm.needOrder = '0'
  } else {
    searchForm.needOrder = '1'
  }
}

// 初始化数据
const callTeamList = ref<SeatTeam[] | null>([])
const callSeatList = ref<SeatMember[] | null>([])
const callSeatIds = ref<number[]>([])
const callTeamIds = ref<number[]>([])
const followUpStatusOption = enum2Options(FollowUpStatusEnum)
const callStatusOptions = (getCallStatusOptions()).filter(v => v.value != CallStatusEnum['商户黑名单'])
const initData = async () => {

  // 初始化表单收集信息缓存，仅在商户端，运营端获取使用的其他接口。
  if (accountType > 0) {
    await taskStore.getEnableFormSetting(true)
  } else {
    // 获取产品行业项目
    globalStore.updateProjectList(true)
    // 获取黑名单
    blackListMap.value = await updateBlacklist()
    updateFrequencyRestrictionList()
  }

  // 获取坐席和坐席组
  const res = await getCallTeamAndSeatOptions()
  callTeamList.value = res?.callTeamList || []
  callSeatList.value = res?.callSeatList || []

  // 获取商户黑名单
  // tenantBlacklist.value = await updateTenantBlacklist()

  // 列表监听
  document.addEventListener('keydown', handleKeydown)
  document.addEventListener('keyup', handleKeyup)
}
const dynamicTotal = ref(false)
const callStatusArr = ref<string[]>([])

// 黑名单列表的map
const blackListMap = ref<Map<number, string>>(new Map([]))
// 商户黑名单列表的map
const tenantBlacklist = ref<BlackListGroupItem[] | null>([])
// 频率限制map
const frequencyRestrictionMap = ref<Map<number, FrequencyRestrictionInfo>>(new Map([]))
const updateFrequencyRestrictionList = async () => {
  const res = await globalStore.getAllFrequencyRestrictionList(true)
  frequencyRestrictionMap.value = new Map([])
  res.forEach(item => {
    item.id && frequencyRestrictionMap.value.set(item.id, item)
  })
}
const search = async (page: number = 1, onlyPageChange: boolean = false) => {
  if (loading.value) return ElMessage.warning('请勿频繁操作')
  loading.value = true
  dynamicTotal.value = !onlyPageChange
  searchForm.account = accountType === 0 ? searchForm.account : userInfo.account
  searchForm.callStatus = callStatusArr.value?.length > 0 ? callStatusArr.value?.join(',') : undefined
  searchForm.startPage = page > 1 ? page - 1 : 0
  currentPage.value = page
  searchForm.pageNum = pageSize.value
  searchForm.callSeatIds = callSeatIds.value?.join(',') || undefined
  searchForm.callTeamIds = callTeamIds.value?.join(',') || undefined
  const [err, res] = await to(aiOutboundTaskModel.findCallRecordManualList(searchForm, accountType === 0))
  currentIndex.value = -1
  if (searchForm.phone) {
    tableData.value = (res?.data as TaskCallRecordItem[] || []).sort((a, b) => dayjs(a.callOutTime).isAfter(dayjs(b.callOutTime)) ? -1 : 1)
  } else {
    tableData.value = res?.data as TaskCallRecordItem[] || []
  }

  loading.value = false
  if (!onlyPageChange && !err) {
    total.value = tableData.value.length === pageSize.value ? (res?.total || 0) : tableData.value.length
    const res2 = await aiOutboundTaskModel.getCallRecordManualNum(searchForm) as ResponseData
    total.value = res2?.total || 0
    dynamicTotal.value = false
  }
}
const clearSearchForm = () => {
  Object.assign(searchForm, new SearchRecordFormOrigin(accountType))
  callSeatIds.value = []
  callTeamIds.value = []
  callStatusArr.value = []
}

/** 查询表单记录 */
const formViewVisible = ref(false)
class FormRecordOriginItem {
  callSeatId = undefined
  clueId = undefined
  formRecordId = undefined
  fromCollectionContentList = []
}
const formRecordData = reactive<FormRecordItem>(new FormRecordOriginItem())
const formSetting = ref<CollectionFormItem[]>([])
const checkFormRecord = async (row: TaskCallRecordItem) => {
  if (!row.clueId) {
    return ElMessage.warning('暂无表单数据！')
  }
  const data = await formRecordModel.getFormRecordByClueId({
    clueId: row.clueId!
  })
  // 获取最新表单设置, 商户端读取缓存，运营端通过account获取
  if (accountType) {
    formSetting.value = await taskStore.getEnableFormSetting() || []
  } else {
    formSetting.value = await formSettingModel.getFormSettingInfoByAccount({account: row.account!})
  }
  Object.assign(formRecordData, data)
  formViewVisible.value = true
}

const tempAudio = reactive<{
  callId?: string,
  taskName?: string,
  wholeAudioFileUrl?: string,
  phone?: string,
}>({
  callId: undefined,
  taskName: '',
  phone: '',
  wholeAudioFileUrl: '' // 音频文件URL
})
const audioStatus = ref<'pause' | 'play' | 'none'>('none')

const handleAudioPlay = (row?: TaskCallRecordItem) => {
  if (!row) {
    Object.assign(tempAudio,  {
      callId: undefined,
      taskName: '',
      wholeAudioFileUrl: '',
      phone: '',
    })
    audioStatus.value = 'none'
  } else {
    currentIndex.value = -1
    if (tempAudio.callId === row?.callId && tempAudio.wholeAudioFileUrl === row?.wholeAudioFileUrl) {
      audioStatus.value = audioStatus.value == 'play' ? 'pause' : 'play'
    } else {
      Object.assign(tempAudio, {
        callId: row.callId,
        taskName: row.taskName,
        wholeAudioFileUrl: row.wholeAudioFileUrl,
        phone: row.recordId,
      })
      audioStatus.value = 'play'
    }
  }
}
const handleAudioChange = (flag: -1 | 1) => {
  if (!tableData.value) return
  const index2 = tableData.value.findIndex(item => item.callId === tempAudio.callId) + flag
  if (!!tableData.value[index2]) {
    if (tableData.value[index2].wholeAudioFileUrl) {
      const row = tableData.value[index2]
      Object.assign(tempAudio, {
        callId: row.callId,
        taskName: row.taskName,
        wholeAudioFileUrl: row.wholeAudioFileUrl,
        phone: row.recordId,
      })
      return 
    } else {
      // Object.assign(tempAudio, tableData.value[index2])
      handleAudioChange(flag)
    }
  } else {
    Object.assign(tempAudio, {
      callId: undefined,
      taskName: '',
      wholeAudioFileUrl: '',
      phone: '',
    })
    audioStatus.value = 'none'
    return ElMessage({
      type: 'warning',
      message: `已经到列表${flag > 0 ? '最底部' : '最顶部'}`
    })
  }
}
const handleKeyup = (e: {code: string, preventDefault: Function}) => {
  if (['ArrowRight',].includes(e.code)) {
    handleAudioChange(1)
  }
  if (['ArrowLeft',].includes(e.code)) {
    handleAudioChange(-1)
  }
  if (['ArrowDown'].includes(e.code)) {
    audioVolume.value = audioVolume.value - 10 < 0 ? 0 : audioVolume.value - 10
  }
  if (['ArrowUp'].includes(e.code)) {
    audioVolume.value = audioVolume.value + 10 > 100 ? 100 : audioVolume.value + 10
  }
  if (['Space',].includes(e.code)) {
    handleAudioPlay(tempAudio)
  }
}
const handleKeydown = (e: {code: string, preventDefault: Function}) => {
  if(['ArrowDown', 'ArrowUp', 'ArrowRight', 'ArrowLeft','Space',].includes(e.code)) {
    e.preventDefault();
  }
}

/** 巡检通话音频 */
const inspecteRecordAudio = async () => {
  taskStore.inspecteRecordAudio(audioStatus.value, tempAudio?.phone || '', RecordTypeEnum['AI外呼'])
}

// 表格区
const visible = ref(false)
const tableRef = ref()
const currentIndex = ref(-1)
const showCallRecord = (index: number) => {
  visible.value = true
  currentIndex.value = index || 0
  handleAudioPlay()
}

// 触发短信详情
const currentRecord = ref<null | {callRecordId: string, callOutTime: string}>(null)
const smsVisible = ref(false)
const openSmsDialog = (row: TaskCallRecordItem) => {
  if (!row || !row.recordId || !row.callOutTime) return ElMessage.warning('获取记录信息失败')
  currentRecord.value = {
    callRecordId: row.recordId,
    callOutTime: row.callOutTime!,
  }
  smsVisible.value = true
}

const handleDetailsChange = (s: TaskCallSearchModal, index: number, t: number, data: TaskCallRecordItem[]) => {
    Object.assign(searchForm, s)
    currentPage.value = s.startPage ? s.startPage + 1 : 1
    pageSize.value = s.pageNum || pageSizeList[0]
    currentIndex.value = index || 0
    total.value = t || 0
    tableData.value = data || []
    tableRef.value?.scrollTo({top: currentIndex.value * 42, behavior: 'smooth', });
}
watch(visible, n => {
  n ? document.removeEventListener('keyup', handleKeyup) : document.addEventListener('keyup', handleKeyup);
})
initData()
search()

const clearAll = () => {
  document.removeEventListener('keydown', handleKeydown);
  document.removeEventListener('keyup', handleKeyup);
  tableRef.value = null
  tableData.value = null
  callSeatList.value = null
  callTeamList.value = null
  tenantBlacklist.value = null
}
onUnmounted(() => {
  clearAll()
})
onBeforeRouteLeave(() => {
  clearAll()
})

</script>

<style scoped lang="postcss" type="text/postcss">
.call-record-container {
  margin: 16px 16px 12px;
  width: calc(100% - 32px);
  box-sizing: border-box;
  font-size: 13px;
  overflow-y: auto;
  min-width: 1048px;
  display: flex;
  flex-direction: column;
  .status-box-mini {
    margin: auto;
    text-align: center;
  }
  .phone-msg {
    display: flex;
    align-items: center;
    span {
      width: 130px;
      flex-shrink: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-icon {
      display: none;
    }
    &:hover .el-icon {
      display: inline-block;
    }
  }
  .audio-mode {
    position: fixed;
    left: 50%;
    top: 10px;
    transform: translateX(-50%);
    z-index: 99;
  }
  :deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) .el-input__wrapper {
    width: 100%;
  }

  .item {
    height: 32px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .label {
      flex-shrink: 0;
      flex-grow: 0;
      width: 30px;
    }
    :deep(.el-date-editor.el-input) {
      width: 47%;
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-table {
    font-size: 13px;
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
}
</style>
