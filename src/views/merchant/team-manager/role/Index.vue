<template>
  <HeaderBox title="角色管理" />
  <div class="role-container">
    <div class="tw-flex tw-justify-end tw-mb-[12px]">
      <el-button type="primary" class="tw-w-[68px] tw-h-[32px]"  @click="edit()">新建角色</el-button>
      <el-button v-if="isNotProd" class="tw-w-[68px] tw-h-[32px]" @click="export2Excel()">导出角色</el-button>
      <el-button class="tw-w-[68px] tw-h-[32px]" @click="handleUpload()">导入角色</el-button>
      <input type="file" ref="fileRef" @change="handleFileChange" accept=".xls,.xlsx" class="batch-upload tw-hidden"/>
    </div>
    <div class="tw-w-full tw-p-[16px] tw-grid tw-grid-cols-5 2xl:tw-grid-cols-7 tw-bg-white tw-mb-[16px]">
      <div class="item">
        <el-input
          v-model.trim="name"
          placeholder="请输入用户角色名称"
          clearable
          @keyup.enter="search()"
        >
        </el-input>
      </div>
      <div class="tw-pl-2 tw-flex">
        <el-button type="primary" @click="search" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>
    <el-table
      :data="tableTempData"
      v-loading="loading"
      :header-cell-style="tableHeaderStyle"
      stripe
      :max-height="maxTableHeight"
      row-key="id"
    >
      <el-table-column property="roleName" label="角色名称" align="left" width="160" show-overflow-tooltip></el-table-column>
      <el-table-column property="authorityMap" label="操作权限" align="left" min-width="300">
        <template #default="{ row }">
          <TagsBox :tagsArr="filterAuthorityCodes(row.authorityMap ? Object.keys(row.authorityMap) : [])" tagsName="操作权限" :tagsNum="5" :type="''" effect="light" :showTotal="true"></TagsBox>
        </template>
      </el-table-column>
      <el-table-column property="updateTime" label="最近修改时间" align="center" width="160" sortable>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="right" :width="hasIpEditPermission ? 150 : 120">
        <template #default="{ row }">
          <el-button type="primary" link @click="edit(row)">编辑</el-button>
          <!-- IP设置可配置，禁用非超管的超管IP配置权限 -->
          <el-button
            v-if="hasIpEditPermission"
            :disabled="row.roleName === '商户超级管理员' && userStore.roleName !== '商户超级管理员'"
            :type="row.roleName === '商户超级管理员' && userStore.roleName !== '商户超级管理员' ? 'default' : 'primary'"
            link
            @click="editIp(row)"
          >IP设置</el-button>
          <el-button type="danger" link @click="handleDel(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="tableData.length || 0"
      @search="search(true)"
      @update="updatePage"
    >
    </PaginationBox>
    <RoleEdit
      v-model:visible="addVisible"
      :editData="addData"
      @confirm="confirmAdd"
    ></RoleEdit>
    <IpEdit
      v-model:visible="addIpVisible"
      :data="addData"
      @confirm="confirmAdd"
    ></IpEdit>
  </div>
</template>

<script lang="ts" setup>
import { RoleResponse } from '@/type/user'
import { reactive, computed, ref, } from 'vue'
import { ElMessage, } from 'element-plus'
import RoleEdit from './RoleEdit.vue'
import IpEdit from './IpEdit.vue'
import PaginationBox from '@/components/PaginationBox.vue'
import {asyncRouter} from '@/router/asyncRoute/index'
import { aiTeamModel } from '@/api/user'
import { useGlobalStore } from '@/store/globalInfo'
import { storeToRefs } from 'pinia'
import { tableHeaderStyle } from '@/assets/js/constant'
import { useUserStore } from '@/store/user'
import TagsBox from '@/components/TagsBox.vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import Confirm from '@/components/message-box'
import HeaderBox from '@/components/HeaderBox.vue'
import { generateExcelByAoa, readXlsx } from '@/utils/export'
import routeMap from '@/router/asyncRoute/route-map'
import { trace } from '@/utils/trace'

// 搜索区
const name = ref('')
const globalStore = useGlobalStore()
const userStore = useUserStore()

const isNotProd = !(import.meta.env.MODE?.includes('production'))
const allAsyncRouter = asyncRouter()
const { loading } = storeToRefs(globalStore)
const hasIpEditPermission = userStore.permissions[routeMap['角色管理-商户端'].id]?.includes(routeMap['角色管理-商户端'].permissions['IP设置'])
const currentPage = ref(1)
const pageSizeList = [20, 50, 100]
const pageSize = ref(pageSizeList[0])
const tableData = ref<RoleResponse[]>([])
const routerArr: {name: string, id: string}[] = Object.values(routeMap)?.flatMap(item => {
  return [
    {name: item.name, id: item.id},
    ...item.permissions && Object.keys(item.permissions)?.map(subItem => {
      return {name: item.name + '-' + subItem, id: item.permissions[subItem]}
    })
  ]
})
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}
const tableTempData = computed(() => {
  return tableData.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})
const menuAllList = allAsyncRouter ? allAsyncRouter[0].children!.filter(item => (item.meta.type??1)>0 && (item.meta.type??1)<3) : []

const search = async (refresh?: boolean) => {
  loading.value = true
  if (!tableData.value || tableData.value.length == 0 || refresh) {
    tableData.value = await aiTeamModel.searchRoleList() as RoleResponse[] || []
  }
  if (name.value.trim()!= '') {
    tableData.value = tableData.value.filter(item => item.roleName.includes(name.value.trim()))
  }
  loading.value = false
}
// 表格区
const maxTableHeight = ref<number>(document.body.clientHeight - 260)
window.onresize = () => {
  return (() => {
    maxTableHeight.value = document.body.clientHeight -260
  })()
}
const filterAuthorityCodes = (codes?: string[]): string[] => {
  if (!codes || codes.length < 1) return []
  let res: string[] = []
  menuAllList.map(item => {
    if (item.children && item.children.length > 0) {
      item.children.map(subItem => {
        if (codes.includes(subItem.meta.id)) {
          res.push(subItem.meta.title)
        }
      })
    } else {
      if (codes.includes(item.meta.id)) {
        res.push(item.meta.title)
      }
    }
  })
  return res
}
// 操作区
const addVisible = ref(false)
class RoleResponseOrigin {
  id = undefined
  roleName = ''
  note = ''
  accountType = 1
  tenantId = userStore.tenantId
  authorityMap = {}
  createTime = undefined
  updateTime = undefined
}
const addData = reactive<RoleResponse>(new RoleResponseOrigin())
const edit = (row?: RoleResponse) => {
  addVisible.value = true
  Object.assign(addData, row || new RoleResponseOrigin())
}
const confirmAdd = () => {
  search(true)
}

const export2Excel = () => {
  if (!tableData.value || tableData.value.length === 0) {
    return ElMessage({
      type: 'warning',
      message: '导出数据为空'
    })
  }
  loading.value = true
  const arr = tableData.value.map(item => {
    const arr: string[] = []
    routerArr.map(subItem => {
      item.authorityCodes?.includes(subItem.id) && arr.push(subItem.name)
    })
    return [
      item.roleName, arr?.join(',') || ''
    ]
  })
  generateExcelByAoa([['角色名称', '操作权限'], ...arr], userStore.account + '角色.xlsx');
  loading.value = false
}
const handleUpload = () => {
  // @ts-ignore
  document.querySelector('.batch-upload')!.click()
}
const fileRef = ref(null)
// 导入文件：批量导入标签
const handleFileChange = async (e: Event) => {
  const {data: xlsData} = await readXlsx(e) as { data: Record<string, any>[] }
  if (!xlsData || xlsData?.length === 0 || userStore.accountType !== 1) {
     // @ts-ignore
     fileRef.value.value = null
    return ElMessage.warning('文档上传失败，请检查')
  }
  const errMsg: string[] = []
  const data = xlsData?.map((roleItem, index) => {
    const arr: string[] = roleItem['操作权限'] ? roleItem['操作权限'].split(',') : []
    const authorityMap: Record<string, string[]> = {}
    arr.map(item => {
      if (item.includes('-')) {
        const m = item.split('-')[0]
        const id1 = routerArr.find(v => v.name === m)?.id || ''
        const id2 = routerArr.find(v => v.name === item)?.id || ''
        if(id1 && id2 && (id1.startsWith('2') || id1.startsWith('3'))) {
          if (authorityMap[id1] && authorityMap[id1].length > 0) {
            authorityMap[id1].push(id2)
          } else {
            authorityMap[id1] = [id2]
          }
        } else {
          errMsg.push(`存在未知的角色权限：${item}`)
        }
      } else {
        const id = routerArr.find(v => v.name === item)?.id || ''
        if (id && (id.startsWith('2') || id.startsWith('3'))) {
          id && !authorityMap[id] && (authorityMap[id] = [])
        } else {
          errMsg.push(`存在未知的角色权限：${item}`)
        }
        
      }
    })
    if (!roleItem['角色名称']) {
      errMsg.push(`角色名称不能为空`)
    }
    if (!authorityMap || Object.keys(authorityMap)?.length < 1) {
      errMsg.push(`操作权限不能为空`)
    }
    return {
      roleName: roleItem['角色名称'] ? roleItem['角色名称'].trim(): '',
      authorityMap: authorityMap,
      accountType: 1,
      tenantId: userStore.tenantId,
      note: ''
    }
  })
  if (errMsg.length > 0) {
     // @ts-ignore
    fileRef.value.value = null
    return ElMessage.warning(errMsg.join('；'))
  }
  loading.value = true
  Promise.all(data?.map(async item => {
    await aiTeamModel.addRole(item)
  })).then(() => {
    ElMessage.success(`上传成功`)
    search(true)
  }).finally(() => {
    loading.value = false
    // @ts-ignore
    fileRef.value.value = null
  })
}

// 配置IP弹窗
const addIpVisible = ref(false)
const editIp = (row?: RoleResponse) => {
  addIpVisible.value = true
  Object.assign(addData, row || new RoleResponseOrigin())
}


const handleDel = async (row: RoleResponse) => {
  Confirm({
    text: `您确定要【删除】账号【${row.roleName}】吗?`,
    type: 'danger',
    title: '删除账号'
  }).then(async () => {
    const params = {
      id: row.id as number
    }
    trace({
      page: '角色管理-删除角色',
      params
    })
    await aiTeamModel.delRole(params)
    ElMessage({
      type: 'success',
      message: '账号删除成功'
    })
    search(true)
  }).catch(() => {})
}
search(true)
</script>

<style scoped lang="postcss" type="text/postcss">
.role-container {
  padding: 16px;
  width: 100%;
  box-sizing: border-box;
  min-width: 1080px;
  .item {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .label {
      width: 120px
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-table {
    font-size: 13px;
    :deep(.caret-wrapper) {
      display: none;
    }
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
}
</style>
