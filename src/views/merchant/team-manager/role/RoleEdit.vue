<template>
  <el-dialog
    v-model="visible"
    width="720px"
    @close="cancel"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">{{ addData.id ? '编辑角色' : '新增角色' }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-p-[12px]"
    >
      <el-form
        :model="addData"
        :rules="rules"
        label-width="80px"
        ref="addFormRef"
      >
        <div class="tw-text-[13px] tw-font-semibold tw-mb-[12px] tw-text-left">角色信息</div>
        <el-form-item label="角色名称：" prop="roleName">
          <el-input
            v-model="addData.roleName"
            type="text"
            clearable maxlength="20"
            show-word-limit
            placeholder="请输入角色名称,20汉字以内"
          />
        </el-form-item>
        <el-form-item label="角色备注：" prop="note">
          <el-input
            v-model="addData.note"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 6 }"
            maxlength="200"
            show-word-limit
            placeholder="请输入角色备注，200字以内"
          />
        </el-form-item>
        <div class="tw-w-full" :key="addData.authorityMap && Object.keys(addData.authorityMap).length">
          <div class="tw-my-0.5">
          </div>
          <el-row class="tw-border-b">
            <el-col class="tw-font-semibold tw-h-[24px] tw-text-left" :span="4">权限范围设置</el-col>
            <el-col :span="20" class="tw-flex">
              <el-button link type="primary" @click="selectAllCode">全选</el-button>
              <el-button link type="primary" class="tw-ml-1" @click="addData.authorityMap={}">清空</el-button>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4" class="tw-font-semibold tw-flex tw-items-center tw-border-b tw-h-[30px]">一级菜单</el-col>
            <el-col :span="5" class="tw-font-semibold tw-flex tw-items-center tw-border-b tw-h-[30px]">二级菜单</el-col>
            <el-col :span="15" class="tw-font-semibold tw-flex tw-items-center tw-border-b tw-h-[30px]">权限</el-col>
          </el-row>
          <el-row v-for="item in menuAllList" :key="item.meta.title">
            <el-col :span="4" class="tw-flex tw-items-center tw-border-b">{{ item.meta.title }}</el-col>
            <el-col :span="20">
              <template v-if="item.children && item.children.length > 0">
                <el-row v-for="subItem in item.children.filter(vv => vv.meta.type===2)" class="tw-border-b tw-min-h-[36px] tw-flex tw-items-center">
                  <el-col :span="6" class="tw-flex tw-items-center tw-justify-start">
                    <el-checkbox
                      :model-value="addData.authorityMap ? Object.keys(addData.authorityMap).includes(subItem.meta.id) : false"
                      @change="handleSubRouteChange(subItem.meta.id)"
                    >{{ subItem.meta.title }}</el-checkbox>
                  </el-col>
                  <el-col :span="18" >
                    <el-checkbox-group v-if="addData.authorityMap" v-model="addData.authorityMap[subItem.meta.id]" class="tw-flex tw-items-center tw-justify-start tw-flex-wrap">
                      <el-checkbox
                        v-for="(pVal, pName) in Object.values(routeMap).find(r => r.id == subItem.meta.id)?.permissions||{}"
                        :label="pVal"
                        :key="pVal"
                      >{{ pName }}</el-checkbox>
                    </el-checkbox-group>
                  </el-col>
                </el-row>
              </template>

              <el-row v-else class="tw-border-b tw-h-[36px] tw-items-center">
                <el-col :span="8" class="tw-flex tw-items-center tw-justify-start">
                  <el-checkbox
                    :model-value="addData.authorityMap ? Object.keys(addData.authorityMap).includes(item.meta.id) : false"
                    @change="handleSubRouteChange(item.meta.id)"
                  >{{ item.meta.title }}</el-checkbox>
                </el-col>
                <el-col :span="16" class="tw-flex tw-items-center tw-justify-start">
                  <el-checkbox-group v-if="addData.authorityMap" v-model="addData.authorityMap[item.meta.id]">
                    <el-checkbox
                      v-for="(pVal, pName) in Object.values(routeMap).find(r => r.id == item.meta.id)?.permissions||{}"
                      :label="pVal"
                    >{{ pName }}</el-checkbox>
                  </el-checkbox-group>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, reactive, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import { aiTeamModel } from '@/api/user'
import { CloseBold, Select } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import {merchantRouter, operatorRouter} from '@/router/asyncRoute/index'
import { RoleResponse } from '@/type/user'
import { AppRouteRecordRawT } from "@/type/route"
import to from 'await-to-js';
import routeMap from '@/router/asyncRoute/route-map'
import { trace } from '@/utils/trace'

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  editData: RoleResponse
}>();
const menuAllList = computed((): AppRouteRecordRawT[] => {
  return merchantRouter ? merchantRouter.filter(item => (item.meta.type??1)>0 && (item.meta.type??1)<3) : []
})
const loading = ref(false)
const visible = ref(false)
const addData = reactive<RoleResponse>(props.editData)

const addFormRef = ref<FormInstance  | null>(null)
const rules = {
  roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' },],
}

const cancel = () => {
  visible.value = false
  emits('update:visible', false)
}
const selectAllCode = () => {
  addData.authorityMap = {}
  menuAllList.value.map(item => {
    if(item.children && item.children.length > 0) {
      item.children.map(subItem => {
        if (subItem.meta.type === 2) {
          const permission = Object.values(routeMap).find(v => v.id === subItem.meta.id)?.permissions || {}
          addData.authorityMap &&
          (addData.authorityMap[subItem.meta.id] = Object.values(permission))
        }
      })
    } else {
      if ( [1,2].includes(item.meta.type??3)) {
        const permission = Object.values(routeMap).find(v => v.id === item.meta.id)?.permissions || {}
        addData.authorityMap &&
        (addData.authorityMap[item.meta.id] = Object.values(permission))
      }
    }
  })
}
const handleSubRouteChange = (id: string) => {
  if (addData.authorityMap) {
    const ids = Object.keys(addData.authorityMap)
    if (ids.includes(id)) {
      // @ts-ignore
      delete addData.authorityMap[id]
    } else {
      addData.authorityMap[id] = []
    }
  } else {
    addData.authorityMap = {
      [id]: []
    }
  }
}
const confirm = async () => {
  addFormRef.value && addFormRef.value.validate(async (valid) => {
    if (valid) {
      const { roleName, authorityMap, accountType, tenantId, note } = addData
      const params: RoleResponse = {
        roleName, authorityMap, accountType, tenantId, note
      }
      if (!authorityMap || Object.keys(authorityMap).length < 1) {
        return ElMessage({
          message: '请选择权限范围',
          type: 'error',
        })
      }
      loading.value = true
      trace({
        page: addData.id ? '角色管理-编辑角色' : '角色管理-新增角色',
        params: addData.id ? {
          id: addData.id, ...params
        } : params,
      })
      const [err, _] = await to(addData.id ? aiTeamModel.editRole({
        id: addData.id, ...params
      }) : aiTeamModel.addRole(params))
      loading.value = false
      if (err) return ElMessage.error('操作失败')
      ElMessage({
        message: '操作成功',
        type: 'success',
      })
      cancel()
      emits('confirm')
    }
  })
}
watch(() => props.visible, () => {
  visible.value = props.visible
  if (props.visible) {
    Object.assign(addData, props.editData)
    addData.authorityMap = addData.authorityMap || {}
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
.el-form {

  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>
