<template>
  <el-dialog
    v-model="visible"
    class="dialog-form"
    width="480px"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">IP设置</div>
    </template>
    <el-form
      :model="editData"
      :rules="rules"
      label-width="70px"
      class="tw-px-[12px]"
      ref="editRef"
    >
      <el-form-item label="角色名称：">
        <span>{{ props.data.roleName ?? '-' }}</span>
      </el-form-item>
      <el-form-item labelWidth="0" prop="ipList">
        <div class="tw-w-full">
          <div class="tw-flex tw-justify-start tw-h-[32px] tw-w-full">
            <div class="tw-w-[80px] tw-text-right">
              IP设置：
            </div>
            <el-button type="primary" link @click="add">+新增IP</el-button>
          </div>
          <el-row class="tw-bg-[#f7f8fa]">
            <el-col :span="4">序号</el-col>
            <el-col :span="14">IP</el-col>
            <el-col :span="6">操作</el-col>
          </el-row>
          <el-row v-for="(item, index) in editData.ipList || []" :key="index" class="tw-my-[3px]">
            <el-col :span="4">{{index + 1}}</el-col>
            <el-col :span="14">
              <el-input v-model="editData.ipList[index]" clearable placeholder="请输入IP"></el-input>
            </el-col>
            <el-col :span="6">
              <el-button link type="danger" @click="del(index)">删除</el-button>
            </el-col>
          </el-row>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, watch, reactive,} from 'vue'
import { ElMessage, } from 'element-plus'
import { ipReg } from '@/utils/constant'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { aiTeamModel } from '@/api/user'
import { RoleResponse } from '@/type/user'
import to from 'await-to-js'
import type { FormInstance, } from 'element-plus'
import { trace } from '@/utils/trace'

const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean;
  data: RoleResponse
}>();

const visible = ref(props.visible)
const editData = reactive<{
  id?: number,
  ipList: string[]
}>({
  id: undefined,
  ipList: []
})
const editRef = ref<FormInstance  | null>(null)
const loading = ref(false)
const cancel = () => {
  emits('update:visible', false)
}
const validateIpList = (rule: any, value: any, callback: any) => {
  let msg = ''
  const ipList = editData.ipList?.flatMap((item: string) => {
    const res = item?.replace(/\s+/g, '')
    !res && (msg = 'IP不能为空')
    if (res && !ipReg.test(res)) {
      msg = '输入IP有误:' + res
    }
    return res ? [res] : []
  }) || []
  const ips = [...new Set(ipList || [])]
  if (ips.length !== ipList?.length) {
    return new Error('请勿输入重复IP')
  }
  if (msg) {
    return new Error(msg)
  }
  editData.ipList = ipList
  return true
}
const rules = {
  ipList: [
    { validator: validateIpList, trigger: ['change'] },
  ],
}

const add = () => {
  editData.ipList?.push('')
}
const del = (index: number) => {
  editData.ipList?.splice(index, 1)
  editRef.value && editRef.value.validate()
}

const confirm = () => {  
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      trace({
        page: '角色管理-IP设置',
        params: editData,
      })
      const [err, _] = await to(aiTeamModel.updateIpListById(editData))
      loading.value = false
      if (!err) {
        cancel()
        ElMessage.success('IP设置成功')
        emits('confirm')
      }
    }
  })
}
watch(() => props.visible, async n => {
  visible.value = n
  if (n) {
    if (!props.data.id) {
      return
    }
    editData.id = props.data.id
    const [err, res] = await to(aiTeamModel.findIpListByRoleId({id: props.data.id}))
    editData.ipList = res || []
    editRef.value && editRef.value.clearValidate()
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {

  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>
