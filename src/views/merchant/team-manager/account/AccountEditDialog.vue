<template>
  <el-dialog
    :model-value="visible"
    width="600px"
    :close-on-click-modal="false"
    align-center
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">{{ title }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="80px"
        ref="editRef"
      >
        <div class="tw-text-left tw-text-[13px] tw-font-semibold tw-my-[12px]">账号信息</div>
        <el-form-item label="账号：" :prop="editData.id ? '': 'account'">
          <el-input v-model="editData.account" clearable placeholder="请填写账号，30字以内" :disabled="!!editData.id"></el-input>
        </el-form-item>
        <el-form-item label="设置角色：" prop="roleId">
          <el-select v-model="editData.roleId" class="tw-w-[472px]" filterable placeholder="选择角色">
            <el-option v-for="item in roleList" :key="item.id" :label="item.roleName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="!!editData.id && (editData.id + '') === masterAccountId" label="运营时间：" prop="taskTimeRange">
          <div class="tw-w-full tw-justify-between tw-flex">
            <el-time-select
              v-model="taskTimeRange[0]"
              start="00:00"
              step="00:30"
              end="23:59"
              format="HH:mm"
              placeholder="开始时间"
              style="width: 47%"
            />
            <span>-</span>
            <el-time-select
              v-model="taskTimeRange[1]"
              start="00:00"
              step="00:30"
              end="23:59"
              format="HH:mm"
              placeholder="结束时间"
              style="width: 47%"
            />
          </div>

        </el-form-item>
        <el-form-item label="初始密码：" prop="password" v-if="!editData.id">
          <el-input v-model="editData.password" type="password" show-password clearable placeholder="请设置初始密码"></el-input>
        </el-form-item>
        <el-form-item label="确认密码：" prop="password2" v-if="!editData.id && editData.password">
          <el-input v-model="editData.password2" type="password" show-password clearable placeholder="请再次确认初始密码"></el-input>
        </el-form-item>

        <div class="tw-text-left tw-text-[13px] tw-font-semibold tw-mb-[12px]">基础信息</div>
        <el-form-item label="姓名：" prop="name">
          <el-input v-model="editData.name" clearable placeholder="请填写联系人姓名"></el-input>
        </el-form-item>
        <el-form-item label="性别：" prop="gender">
          <el-select v-model="editData.gender" class="tw-w-[472px]" placeholder="请选择性别">
            <el-option label="男" value="MALE" />
            <el-option label="女" value="FEMALE" />
          </el-select>
        </el-form-item>
        <el-form-item label="联系电话：" prop="phone">
          <el-input v-model="editData.phone" clearable placeholder="请填写联系电话"></el-input>
        </el-form-item>
        <el-form-item label="部门：" prop="department">
          <el-input v-model="editData.department" clearable placeholder="请填写部门"></el-input>
        </el-form-item>
        <el-form-item label="联系地址：" prop="address">
          <el-input v-model="editData.address" clearable placeholder="请填写联系地址"></el-input>
        </el-form-item>
        <el-form-item label="邮箱：" prop="email">
          <el-input v-model="editData.email" clearable placeholder="请填写邮箱"></el-input>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, reactive, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { aiTeamModel } from '@/api/user'
import { AccountItem, RoleResponse } from '@/type/user'
import { useUserStore } from '@/store/user'
import type { FormInstance, } from 'element-plus'
import { nameReg, passwordReg, phoneReg, emailReg } from '@/utils/constant'
import to from 'await-to-js';

const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean,
  rowData: AccountItem | undefined
  roleList: RoleResponse[]
}>();
const visible = ref(props.visible)
const roleList = ref(props.roleList)
const loading = ref<boolean>(false)
const { tenantId, groupId } = useUserStore()
const masterAccountId = groupId?.split('_')[2] || ''
class AccountItemOrigin {
  id = undefined
  account = ''
  name = ''
  gender = 'MALE'
  phone = ''
  department = ''
  roleId = undefined
  password = ''
  password2 = ''
  email = ''
  address = ''
  taskTimeRange = undefined
  accountType = 1
  tenantId =  tenantId
  accountEnable = true
}
const editData = reactive<AccountItem>(props.rowData || new AccountItemOrigin())
const editRef = ref<FormInstance  | null>(null)
const title = computed(() => { 	return editData.id ? '编辑账号' : '新增账号'})
const taskTimeRange = ref(['08:00', '20:00'])
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  emits('update:visible', false)
}
const validatePhone = (rule: any, value: any, callback: any) => {
  if (!value) return callback()
  if (!value.match(phoneReg)) {
    callback(new Error('手机号必须为11位数字'))
  } else {
    callback()
  }
}
const validateEmail = (rule: any, value: any, callback: any) => {
  if (value && value.trim() && !value.match(emailReg)) {
    callback(new Error('邮箱格式错误'))
  } else {
    callback()
  }
}
const validateName = (rule: any, value: any, callback: any) => {
  if (!value.match(nameReg)) {
    callback(new Error('姓名必须为2~20位'))
  } else {
    callback()
  }
}
const validatePassword = (rule: any, value: any, callback: any) => {
  if (!value.match(passwordReg)) {
    callback(new Error('密码必须为包含数字和字母，6~16位'))
  } else {
    callback()
  }
}
const validatePassword2 = (rule: any, value: any, callback: any) => {
  if (value !== editData.password) {
    callback(new Error('请确认两次密码相同'))
  } else {
    callback()
  }
}

const validateAccount = (rule: any, value: any, callback: any) => {
  if (!(/^[0-9A-Za-z]{1,30}$/).test(value)) {
    callback(new Error('账号仅支持字母或数字，30字符以内'))
    return
  }
  callback()
}

const rules = {
  account: [
    { required: true, message: '请填写账号，30字以内', trigger: 'blur' },
    { validator: validateAccount, trigger: 'blur' },
  ],
  address: [
    { max: 40, message: '请填写地址，40字以内', trigger: 'blur' },
  ],
  roleId: [
    { required: true, message: '请选择角色', trigger: 'change' },
  ],
  name: [
    { required: true, message: '请填写姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名必须在2-20字', trigger: 'blur' },
  ],
  phone: [
    { validator: validatePhone, trigger: 'blur' },
  ],
  email: [
    { validator: validateEmail, trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请填写密码', trigger: 'blur' },
    { validator: validatePassword, trigger: 'blur' },
  ],
  password2: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validatePassword2, trigger: 'blur' },
  ]
}

const confirm = () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      let data
      if (editData.id) {
        editData.taskTimeRange = taskTimeRange.value[0] + ',' + taskTimeRange.value[1]
        data = await to(aiTeamModel.editAccount(editData))
      } else {
        data = await to(aiTeamModel.addAccount(editData))
      }
      loading.value = false
      if (data[0]) return ElMessage.error('操作失败')
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
      cancel()
      emits('confirm')
    }
  })
}
watch(() => props.visible, () => {
  roleList.value = props.roleList
  visible.value = props.visible
  if (props.visible) {
    Object.assign(editData, props.rowData || new AccountItemOrigin())
  }
  if (editData.id && editData.taskTimeRange?.split(',')?.length === 2) {
    const arr = editData.taskTimeRange?.split(',')
    taskTimeRange.value = [arr[0], arr[1]]
  } else {
    taskTimeRange.value = ['08:00', '20:00']
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {
  .el-form-item {
    margin-bottom: 14px;
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>
