<template>
  <!--模块标题-->
  <HeaderBox title="线路数据" />
  <div class="tw-p-[12px] tw-h-[calc(100vh-70px)] tw-min-w-[1080px] tw-overflow-y-hidden tw-flex tw-flex-col">
    <div class="tw-w-full tw-p-[16px] tw-bg-white tw-mb-[12px] tw-grid tw-grid-cols-6 sm:tw-grid-cols-4 tw-gap-x-[8px] tw-items-center">
      <el-input
        v-model.trim="filterParams.supplyLineNumber"
        placeholder="请输入供应线路编号"
        clearable
        @keyup.enter="searchAction"
      >
      </el-input>
      <div class="tw-flex">
        <el-button type="primary" @click="searchAction" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
      </div>
      
    </div>
    <el-radio-group v-model="filterParams.recentMin" class="tw-mb-[8px]" @change="searchAction">
      <el-radio-button v-for="item in timeRangeList" :label="item.value" :key="item.value">{{ item.label }}</el-radio-button>
    </el-radio-group>
    <el-table
      :data="tableTemp"
      v-loading="loading"
      row-key="supplyLineName"
      class="tw-grow"
      :header-cell-style="tableHeaderStyle"
      stripe
      @sort-change="handleSortChange"
    >
      <el-table-column align="left" fixed="left" prop="supplyLineNumber" :formatter="formatterEmptyData" label="线路编号" min-width="160" show-overflow-tooltip/>
      <el-table-column align="left" prop="prefix" label="前缀" width="120" :formatter="formatterEmptyData" show-overflow-tooltip/>
      <el-table-column align="left" prop="masterCallNumber" label="主叫号码" width="120" :formatter="formatterEmptyData" show-overflow-tooltip/>
      <el-table-column property="currentlyCallNum" sortable="custom" label="呼叫数" align="left" min-width="120" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="currentlyConnectedNum" sortable="custom" label="接通数" align="left" min-width="120" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="currentlyConnectedRate" sortable="custom" label="接通率" align="left" min-width="80" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="currentlySilenceCall" sortable="custom" label="无声通话" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="currentlySilenceHangup" sortable="custom" label="沉默挂机" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="currentlyAssistant" sortable="custom" label="小助理" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="supplierNumber" label="供应商编号" align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column label="操作" width="80" align="right" fixed="right">
        <template #default="{ row }">
          <div class="table-btn-box">
            <el-button type="primary" link @click="goDetails(row)">数据详情</el-button>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :pageSizeList="pageSizeList"
      :currentPage="currentPage"
      :total="tableData?.length||0"
      @search="searchAction()"
      @update="updateList"
    >
    </PaginationBox>
  </div>

  <!-- 线路数据详情（异常通话记录） -->
  <LineDetailsDrawer
    v-model:visible="drawerVisible"
    :supplyLineNumber="currentLineNumber"
  />
</template>

<script lang="ts" setup>
import { supplierModel } from '@/api/supplier/supplier'
import { ref, reactive, computed, onUnmounted, onMounted } from 'vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import HeaderBox from '@/components/HeaderBox.vue'
import PaginationBox from '@/components/PaginationBox.vue'
// import { useUserStore } from "@/store/user";
// import routeMap from '@/router/asyncRoute/route-map'
import { onBeforeRouteLeave } from 'vue-router'
import { tableHeaderStyle } from '@/assets/js/constant'
import { formatterEmptyData, handleTableSort } from '@/utils/utils'
import { timeRangeList } from '@/views/operator/line-manager/components/constant'
import { SupplierMonitorInfoItem, } from '@/type/line'
import LineDetailsDrawer from './LineDetailsDrawer.vue'
import to from 'await-to-js'

// loading信息
const loading = ref(false)

// 用户权限获取
// const userStore = useUserStore();
// const permissions = userStore.permissions[routeMap['供应平台-线路数据'].id]

// 搜索内容
const filterParams = reactive({
  supplyLineNumber: undefined,
  recentMin: 5,
})

// 列表数据
const tableData = ref<SupplierMonitorInfoItem[] | null>([])

/** 排序 开始 */
const prop = ref('')
const order = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  prop.value = params.prop
  order.value = params.order
}
const tableTemp = computed(() => {
  const data = handleTableSort(tableData.value || [], prop.value, order.value)
  return (data||[]).slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})
/** 排序 结束 */

/** 分页 开始 */
const pageSizeList = [20, 50, 100, 200]
const pageSize = ref(pageSizeList[1])
const currentPage = ref(1)
const updateList = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}
/** 分页 结束 */

// 搜索
const searchAction = async () => {
  if (!!loading.value) return
  loading.value = true
  const [_, res] = await to(supplierModel.getSupplierPlatformLineList({
    ...filterParams,
    recentMin: filterParams.recentMin ? filterParams.recentMin : null
  }))
  tableData.value = res as SupplierMonitorInfoItem[] || []
  loading.value = false
}

// 点击数据详情-展示抽屉
const drawerVisible = ref(false)
const currentLineNumber = ref<string | null>(null)
const goDetails = (row: SupplierMonitorInfoItem) => {
  if (!row || !row.supplyLineNumber) return
  currentLineNumber.value = row.supplyLineNumber
  drawerVisible.value = true
}

/** vue生命周期 */
// 初始化
onMounted(() => {
  searchAction()
})
const clearAllData = () => {
  tableData.value = null
  currentLineNumber.value = null
  drawerVisible.value = false
}
onUnmounted(() => { clearAllData() })
onBeforeRouteLeave(() => { clearAllData() })
</script>

<style scoped lang="postcss">
.el-table {
  font-size: 13px;
  :deep(.caret-wrapper) {
    display: none;
  }
  :deep(.cell) {
    padding: 0 8px;
  }
}
</style>
