<template>
  <!--模块标题-->
  <HeaderBox title="坐席工作台" />

  <!--模块主体-->
  <div v-if="havePermission" class="module-container">
    <!--坐席线索-->
    <SeatClue ref="seatClueRef" />
    <!--坐席通话-->
    <SeatPhone @update="onUpdateClue" />
  </div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, onMounted, ref } from 'vue'
import { SeatMember } from '@/type/seat'
import { useTaskStore } from '@/store/taskInfo'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'
import router from '@/router'
import { useSeatPhoneStore } from '@/store/seat-phone'
import { useSeatSettingStore } from '@/store/seat/seat-setting'
import { storeToRefs } from 'pinia'

// 动态引入组件
const HeaderBox = defineAsyncComponent(() => import('@/components/HeaderBox.vue'))
const SeatClue = defineAsyncComponent(() => import('./SeatClue/Index.vue'))
const SeatPhone = defineAsyncComponent(() => import('./SeatPhone/Index.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

const userStore = useUserStore()
const seatPhoneStore = useSeatPhoneStore()
const seatSettingStore = useSeatSettingStore()
const { seatSetting } = storeToRefs(seatSettingStore)

// 当前页面有权限
const havePermission = ref(false)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 组件 开始 ----------------------------------------

// 线索模块DOM
const seatClueRef = ref()

/**
 * 更新线索模块
 */
const onUpdateClue = () => {
  if (seatClueRef.value && seatClueRef.value?.update) {
    seatClueRef.value?.update()
  }
}

// ---------------------------------------- 组件 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

/**
 * 检查账号权限
 */
const checkPermission = async () => {
  const taskStore = useTaskStore()
  // 坐席列表
  const callSeatList: SeatMember[] = await taskStore.getCallGroupSeatListOptions(true)
  if (callSeatList?.findIndex(item => item.accountId === userStore.userId) === -1) {
    havePermission.value = false
    seatPhoneStore.resetWorkbench()
    ElMessage.warning({
      message: '该账号不是坐席身份，请联系管理员到“坐席管理”模块将此账号添加为坐席',
      duration: 5000
    })
    return router.back()
  } else {
    havePermission.value = true
  }
}
checkPermission()

onMounted(async () => {
  // 更新表单设置
  const taskStore = useTaskStore()
  await taskStore.getEnableFormSetting(true)

  // 更新坐席设置
  seatSettingStore.updateSeatSetting({
    autoCallNext: true,
  })
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.module-container {
  padding: 0;
  min-width: 1080px;
}
</style>
