<template>
  <!--模块主体 内容器-->
  <el-scrollbar class="module-container-inner">
    <!--统计模块-->
    <div class="tw-grid tw-gap-x-[12px] tw-grid-cols-3 tw-w-full">

      <!--今日线索-->
      <div class="tw-col-span-2 tw-w-full tw-px-[16px] tw-py-[12px] tw-rounded-[4px] tw-bg-white">
        <!--区块标题-->
        <div class="tw-flex tw-items-center">
          <span class="tw-font-semibold tw-text-[var(--primary-black-color-600)] tw-text-[14px]">今日线索</span>
          <el-tooltip content="刷新统计数据" placement="right" :show-after="500">
            <el-icon size="13" class="tw-cursor-pointer tw-ml-0.5" color="var(--el-color-primary)" @click="onClickRefreshTodayStatistics">
              <SvgIcon name="reset" />
            </el-icon>
          </el-tooltip>
        </div>

        <!--总览数据-->
        <div class="tw-w-full tw-flex tw-items-center tw-mt-[8px] tw-py-[8px] tw-border-b">
          <!--图标-->
          <div class="tw-flex tw-justify-center tw-items-center tw-w-[40px] tw-h-[40px] tw-rounded-[4px] tw-bg-[#dfe8fa]">
            <el-icon :size="26" color="var(--primary-blue-color)">
              <SvgIcon name="follow-acquire" />
            </el-icon>
          </div>
          <!--获得线索-->
          <div v-loading="loading[0]" class="statistics-box summary tw-items-start tw-ml-[8px]">
            <span class="statistics-title">获得线索</span>
            <span class="statistics-content">
              {{ formatNumber1(clueTodayStatistics?.acquireCount) }}
            </span>
          </div>
          <!--关联任务-->
          <div v-loading="loading[1]" class="statistics-box summary tw-items-end tw-ml-auto">
            <span class="statistics-title">关联任务</span>
            <span class="statistics-content">
              {{ formatNumber1(relatedTaskCount) }}
            </span>
          </div>
        </div>

        <!--区块内容-->
        <div v-loading="loading[0]" class="tw-grid tw-grid-cols-3 tw-gap-[8px] tw-mt-[12px]">
          <div class="statistics-box tw-items-start">
            <span class="statistics-title">跟进中</span>
            <span class="statistics-content">
              {{ formatNumber1(clueTodayStatistics?.followingCount) }}
            </span>
          </div>
          <div class="statistics-box tw-items-start">
            <span class="statistics-title">跟进成功</span>
            <span class="statistics-content">
              {{ formatNumber1(clueTodayStatistics?.successCount) }}
            </span>
          </div>
          <div class="statistics-box tw-items-start">
            <span class="statistics-title">跟进失败</span>
            <span class="statistics-content">
              {{ formatNumber1(clueTodayStatistics?.failCount) }}
            </span>
          </div>
          <div class="statistics-box tw-items-start">
            <span class="statistics-title">被回收</span>
            <span class="statistics-content">
              {{ formatNumber1(clueTodayStatistics?.beRecoveredCount) }}
            </span>
          </div>
          <div class="statistics-box tw-items-start">
            <span class="statistics-title">被归档</span>
            <span class="statistics-content">
              {{ formatNumber1(clueTodayStatistics?.beArchivedCount) }}
            </span>
          </div>
          <div class="statistics-box tw-items-start">
            <span class="statistics-title">被重新分配</span>
            <span class="statistics-content">
              {{ formatNumber1(clueTodayStatistics?.beReassignCount) }}
            </span>
          </div>
        </div>
      </div>

      <!--线索统计-->
      <div class="tw-col-span-1 tw-w-full tw-px-[16px] tw-py-[12px] tw-rounded-[4px] tw-bg-white">
        <!--区块标题-->
        <div class="tw-flex tw-items-center">
          <span class="tw-font-semibold tw-text-[var(--primary-black-color-600)] tw-text-[14px]">线索统计</span>
          <el-tooltip content="刷新统计数据" placement="right" :show-after="500">
            <el-icon size="13" class="tw-cursor-pointer tw-ml-0.5" color="var(--el-color-primary)" @click="refreshClueStatistics">
              <SvgIcon name="reset" />
            </el-icon>
          </el-tooltip>
        </div>

        <!--总览数据-->
        <div class="tw-w-full tw-flex tw-items-center tw-mt-[8px] tw-py-[8px]">
          <!--获得线索-->
          <div class="statistics-box summary tw-items-start">
            <span class="statistics-title">总线索数</span>
            <span v-loading="loading[2]" class="statistics-content">
              {{ formatNumber1(clueStatistics?.allCluesCount) }}
            </span>
          </div>
        </div>

        <!--区块内容-->
        <div v-loading="loading[2]" class="tw-grid tw-grid-cols-2 tw-gap-[8px] tw-mt-[12px]">
          <div class="statistics-box tw-items-start">
            <span class="statistics-title">待首跟</span>
            <span class="statistics-content">
              {{ formatNumber1(clueStatistics?.toBeFirstFollowUpCount) }}
            </span>
          </div>
          <div class="statistics-box tw-items-start">
            <span class="statistics-title">跟进中</span>
            <span class="statistics-content">
              {{ formatNumber1(clueStatistics?.beingCluesCount) }}
            </span>
          </div>
          <div class="statistics-box tw-items-start">
            <span class="statistics-title">跟进成功</span>
            <span class="statistics-content">
              {{ formatNumber1(clueStatistics?.successCluesCount) }}
            </span>
          </div>
          <div class="statistics-box tw-items-start">
            <span class="statistics-title">跟进失败</span>
            <span class="statistics-content">
              {{ formatNumber1(clueStatistics.failCluesCount) }}
            </span>
          </div>
        </div>
      </div>

    </div>

    <!--标签卡-->
    <TabsBox v-model:active="activeTab" :tabList="tabList" class="tw-mt-[12px]" />
    <keep-alive>
      <FollowingCluesQueue
        v-if="activeTab==='跟进中'"
        :followUpStatus="FollowUpStatusEnum[activeTab]"
        :needRefresh="needRefreshClueList"
        @update:needRefresh="handleTableUpdateNeedRefresh"
      />
      <FollowedSuccessCluesQueue
        v-else-if="activeTab==='跟进成功'"
        :followUpStatus="FollowUpStatusEnum[activeTab]"
        :needRefresh="needRefreshClueList"
        @update:needRefresh="handleTableUpdateNeedRefresh"
      />
      <FollowedFailCluesQueue
        v-else-if="activeTab==='跟进失败'"
        :followUpStatus="FollowUpStatusEnum[activeTab]"
        :needRefresh="needRefreshClueList"
        @update:needRefresh="handleTableUpdateNeedRefresh"
      />
    </keep-alive>
  </el-scrollbar>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { formatNumber1 } from '@/utils/utils'
import { FollowUpStatusEnum, WorkbenchAllClueStatistics, WorkbenchTodayClueStatistics } from '@/type/clue'
import { seatWorkbenchClueModel } from '@/api/seat'
import { TaskManageItem } from '@/type/task'
import { useSeatPhoneStore } from '@/store/seat-phone'
import TabsBox from '@/components/TabsBox.vue'
import FollowingCluesQueue from '@/views/merchant/manual-call/components/FollowingCluesQueue.vue'
import FollowedSuccessCluesQueue from '@/views/merchant/manual-call/components/FollowedSuccessCluesQueue.vue'
import FollowedFailCluesQueue from '@/views/merchant/manual-call/components/FollowedFailCluesQueue.vue'

// ---------------------------------------- 通用 开始 ----------------------------------------

const seatPhoneStore = useSeatPhoneStore()

// 今日线索 关联任务 线索统计
// 加载中
const loading = ref([false, false, false])

/**
 * 延迟
 * @param {number} ms 毫秒
 */
const delay = async (ms: number = 100) => {
  await new Promise((resolve) => {
    setTimeout(resolve, ms)
  })
}

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 今日线索 开始 ----------------------------------------

// 今日线索
const clueTodayStatistics = ref<WorkbenchTodayClueStatistics>({})

/**
 * 获取今日线索数据
 */
const refreshTodayStatistics = async () => {
  loading.value[0] = true
  try {
    clueTodayStatistics.value = <WorkbenchTodayClueStatistics>await seatWorkbenchClueModel.getTodayClueStatistics()
  } catch (err) {
  } finally {
    // await delay()
    loading.value[0] = false
  }
}
/**
 * 点击今日线索的刷新按钮
 */
const onClickRefreshTodayStatistics = () => {
  refreshTodayStatistics()
  refreshTaskStatistics()
}

// ---------------------------------------- 今日线索 结束 ----------------------------------------

// ---------------------------------------- 关联任务 开始 ----------------------------------------

// 关联任务数量
const relatedTaskCount = ref<number | null>(null)
// 关联任务列表
const relatedTaskList = ref<TaskManageItem[]>([])

/**
 * 获取关联任务
 */
const refreshTaskStatistics = async () => {
  loading.value[1] = true
  try {
    // 请求接口
    const data = <TaskManageItem[]>await seatWorkbenchClueModel.getRelatedTaskList()
    if (Array.isArray(data)) {
      // 是数组，计算数量
      relatedTaskList.value = data
      relatedTaskCount.value = data.length || 0
    } else {
      // 不是数组
      relatedTaskList.value = []
      relatedTaskCount.value = null
    }
  } catch (err) {
    relatedTaskCount.value = null
  } finally {
    // await delay()
    loading.value[1] = false
  }
}

// ---------------------------------------- 关联任务 结束 ----------------------------------------

// ---------------------------------------- 线索统计 开始 ----------------------------------------

// 线索统计
const clueStatistics = ref<WorkbenchAllClueStatistics>({})

/**
 * 获取线索统计数据
 */
const refreshClueStatistics = async () => {
  loading.value[2] = true
  try {
    clueStatistics.value = <WorkbenchAllClueStatistics>await seatWorkbenchClueModel.getAllClueStatistics()
  } catch (err) {
  } finally {
    // await delay()
    loading.value[2] = false
  }
}

// ---------------------------------------- 线索统计 结束 ----------------------------------------

// ---------------------------------------- 线索tab 结束 ----------------------------------------

// 线索tab 可选列表
const tabList = ['跟进中', '跟进成功', '跟进失败']
// 线索tab 当前
const activeTab = ref(tabList[0])

// 需要刷新线索列表
const needRefreshClueList = ref(false)

/**
 * 表格发起更新
 * @param {boolean} val 是否需要更新
 */
const handleTableUpdateNeedRefresh = (val: boolean) => {
  if (val) {
    init()
    needRefreshClueList.value = false
  }
}

// ---------------------------------------- 线索tab 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

/**
 * 初始化
 */
const init = () => {
  refreshTodayStatistics()
  refreshTaskStatistics()
  refreshClueStatistics()
}

// 表格组件会发起更新，所以这里不需要再更新一次
// init()

const update = async () => {
  needRefreshClueList.value = true
}

// 是否需要更新统计数据
watch(() => seatPhoneStore.needUpdateWorkbenchStatistics, (val: boolean) => {
  if (val) {
    seatPhoneStore.needUpdateWorkbenchStatistics = false
    init()
  }
})

defineExpose({
  update,
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.module-container-inner {
  padding: 16px;
  background-color: unset;
}
.statistics-box {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 4px;
  padding: 8px;
  border-radius: 4px;
  background-color: #F5F7FA;
  .statistics-title {
    color: #969799;
    font-size: 13px;
    font-weight: 400;
    line-height: 20px;
  }
  .statistics-content {
    color: #313233;
    font-size: 24px;
    font-weight: 700;
    line-height: normal;
  }
  &.summary {
    padding: 0;
    background-color: transparent;
    .statistics-content {
      font-size: 28px;
    }
  }
}
</style>
