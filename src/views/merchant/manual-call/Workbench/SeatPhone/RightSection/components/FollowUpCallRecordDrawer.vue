<template>
  <el-drawer
    v-model="visible"
    :before-close="closeDetails"
    :size="drawerWidth()"
    :with-header="false"
  >
    <div class="tw-bg-white tw-h-[50px] tw-px-[12px] tw-text-[16px] tw-font-semibold tw-text-left tw-text-[var(--primary-black-color-600)] tw-flex tw-items-center tw-justify-between">
      <span>{{ findValueInEnum(props?.recordType, RecordTypeEnum) ?? '通话' }}记录详情</span>
      <el-button link @click="closeDetails">
        <el-icon :size="20" color="var(--primary-black-color-400)">
          <CloseBold />
        </el-icon>
      </el-button>
    </div>
    <div v-loading="loading" class="record-detail-container">
      <div class="left">
        <CallRecordInfoBox :record-data="rowNormalData!" :recordType="props?.recordType" />
      </div>
      <div v-loading="dialogLoading" class="right">
        <CallRecordDialogBoxNew
          v-model:clearAudio="clearAudio"
          v-model:needUpdate="needUpdate"
          :client-name="rowNormalData?.name||''"
          :startEndInfo="startEndInfo"
          :info-query-map="infoQueryMap"
          :dataList="rowDialogData"
          :isTransfer="rowNormalData?.isTransToCallSeat || false"
        />
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, ref, watch, } from 'vue'
import { RecordDialogueData, RecordTypeEnum, TaskCallRecordItem } from '@/type/task'
import { aiOutboundTaskModel, } from '@/api/ai-report'
import { CloseBold, } from '@element-plus/icons-vue'
import { findValueInEnum } from '@/utils/utils'
import { InfoQueryItem, } from '@/type/speech-craft'
import { useGlobalStore } from '@/store/globalInfo'
import { storeToRefs } from 'pinia'
import { scriptInfoModel } from '@/api/speech-craft'
import CallRecordDialogBoxNew from '@/components/record/CallRecordDialogBoxNew.vue'
import CallRecordInfoBox from '@/components/record/CallRecordInfoBox.vue'
import { seatCallRecordModel } from '@/api/seat'

const props = defineProps<{
  visible: boolean; // 抽屉的可见性
  recordType: RecordTypeEnum // 通话类型
  recordData: TaskCallRecordItem
  tableData: TaskCallRecordItem[]; // 当前页的通话记录
  currentIndex: number; // 当前通话记录在当前页的通话记录的index
  total: number; // 总通话记录数（不是当页）
}>();
const emits = defineEmits(['close', 'update:visible', 'update:record'])
// 读取props数据，并用监听器监听变化
const visible = ref(props.visible || false)
const allRecordData = ref<TaskCallRecordItem[]>(props.tableData || [])
const currentIndex = ref<number>(props.currentIndex || 0)
const total = ref(props.total || 0)

// pinia数据读取
const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)

// 详情抽屉宽度
const drawerWidth = () => {
  return window.innerWidth > 1400 ? '70%' : '900px'
}
const clearAudio = ref(false) // 对于一些操作，需要是的对话组件的音频清空
/** 左侧 */
const rowNormalData = ref<TaskCallRecordItem | null>(allRecordData.value[currentIndex.value]) // 左侧通话记录数据
const audioStatus = ref<'pause' | 'play' | 'none'>('pause') // 左侧通话记录音频播放状态

// 右侧机器人用户对话
const rowDialogData = ref<RecordDialogueData[]>([]) // 数据列表
const dialogLoading = ref(false) // 针对右侧对话部分的的loading
const needUpdate = ref(false) // 通话记录对话详情是否需要更新
const infoQueryMap = ref<Map<string, InfoQueryItem>>(new Map([]))
const startEndInfo = computed(() => {
  const res = [] as { dialogTime: string, content: string }[]
  if (rowNormalData.value?.talkTimeStart) {
    res.push({ dialogTime: rowNormalData.value?.talkTimeStart, content: '电话接通' })
  }
  if (rowNormalData.value?.triggerMonitorTime) {
    res.push({ dialogTime: rowNormalData.value?.triggerMonitorTime, content: '触发转人工监听' })
  }
  if (rowNormalData.value?.startMonitorTime) {
    res.push({ dialogTime: rowNormalData.value?.startMonitorTime, content: '坐席开始监听' })
  }
  if (rowNormalData.value?.endMonitorTime) {
    res.push({ dialogTime: rowNormalData.value?.endMonitorTime, content: '坐席结束监听' })
  }
  if (rowNormalData.value?.triggerAnswerTime) {
    res.push({ dialogTime: rowNormalData.value?.triggerAnswerTime, content: '触发转人工接听' })
  }
  if (rowNormalData.value?.startAnswerTime) {
    res.push({ dialogTime: rowNormalData.value?.startAnswerTime, content: '坐席开始接听' })
  }
  if (rowNormalData.value?.endAnswerTime) {
    res.push({ dialogTime: rowNormalData.value?.endAnswerTime, content: '坐席结束接听' })
  }
  if (rowNormalData.value?.talkTimeEnd) {
    res.push({ dialogTime: rowNormalData.value?.talkTimeEnd, content: '挂断通话' })
  }
  return res
})

/** 更新机器人用户对话数据 */
const updateRecordData = async () => {
  if ((currentIndex.value ?? -1) !== -1 && allRecordData.value[currentIndex.value] && allRecordData.value[currentIndex.value].wholeAudioFileUrl) {
    dialogLoading.value = true
    rowNormalData.value = JSON.parse(JSON.stringify(allRecordData.value[currentIndex.value]))
    const callId = rowNormalData.value?.callId || ''
    const recordId = rowNormalData.value?.recordId || ''
    if (!callId) {
      infoQueryMap.value = new Map([])
      rowDialogData.value = []
      needUpdate.value = true
      dialogLoading.value = false
      return
    }
    switch (props?.recordType) {
      case RecordTypeEnum['AI外呼']: {
        rowDialogData.value = (await aiOutboundTaskModel.getAiDialogueDataList({
          callId: callId,
          recordId: recordId,
        }) as RecordDialogueData[]) || []
        break
      }
      case RecordTypeEnum['人工直呼']: {
        rowDialogData.value = (await seatCallRecordModel.getManualDialogueDataList({
          callId: callId,
          recordId: recordId,
        }) as RecordDialogueData[]) || []
        break
      }
      case RecordTypeEnum['人机协同']: {
        rowDialogData.value = (await seatCallRecordModel.getMixDialogueDataList({
          callId: callId,
          recordId: recordId,
        }) as RecordDialogueData[]) || []
        break
      }
    }
    const res = await scriptInfoModel.findInfoQueryList({ id: rowNormalData.value?.speechCraftId! }) as InfoQueryItem[] || []
    infoQueryMap.value = new Map([])
    res?.map(item => {
      infoQueryMap.value.set(item.infoFieldName, item)
    })
    dialogLoading.value = false
    needUpdate.value = true
  }
}

// 关闭抽屉
const closeDetails = () => {
  // 重置
  rowNormalData.value = {}
  rowDialogData.value = []
  emits('update:visible', false)
}

watch(props, async () => {
  visible.value = props.visible
  if (props.visible) {
    allRecordData.value = props.tableData || []
    currentIndex.value = props.currentIndex
    total.value = props.total

    rowNormalData.value = allRecordData.value[currentIndex.value]
    await updateRecordData()
  } else {
    clearAudio.value = true
    audioStatus.value = 'pause'
  }
}, { deep: true })
</script>

<style lang="postcss" scoped>
.record-detail-container {
  height: calc(100% - 50px);
  background-color: #fff;
  box-sizing: border-box;
  font-size: 13px;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: row;
  .left {
    padding: 0 0 12px 12px;
    width: 284px;
    overflow: hidden;
    height: 100%;
    flex-shrink: 0;
    flex-grow: 1;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }
  .right {
    flex-grow: 4;
    flex-shrink: 1;
    width: 900px;
    text-align: center;
    display: flex;
    flex-direction: column;
  }
}
</style>
