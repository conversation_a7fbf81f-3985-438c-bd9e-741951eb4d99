<template>
  <!--数据统计-->
  <div class="card-box">
    <!--搜索条-->
    <div class="section">
      <div>
        <div class="tw-mb-[8px] tw-text-[13px] tw-text-[#969799]">座席组名称：</div>
        <el-select v-model.trim="seatTeamNameVal" placeholder="选择坐席组名称" class="search-input" clearable filterable>
          <el-option
            v-for="(seatTeamNameItem,seatTeamNameIndex) in seatTeamNameList"
            :key="seatTeamNameIndex"
            :label="seatTeamNameItem"
            :value="seatTeamNameItem"
          />
        </el-select>
      </div>

      <div class="tw-ml-[12px]">
        <div class="tw-mb-[8px] tw-text-[13px] tw-text-[#969799]">座席账号：</div>
        <el-select v-model.trim="seatAccountVal" placeholder="选择坐席账号" class="search-input" clearable filterable>
          <el-option
            v-for="(seatAccountItem,seatAccountIndex) in seatAccountList"
            :key="seatAccountIndex"
            :label="seatAccountItem"
            :value="seatAccountItem"
          />
        </el-select>
      </div>

      <div class="tw-ml-[12px]">
        <div class="tw-mb-[8px] tw-text-[13px] tw-text-[#969799]">日期：</div>
        <el-date-picker
          v-model="dateVal"
          type="date"
          class="search-datetime"
          clearable
          placeholder="今天"
          value-format="YYYY-MM-DD"
          :shortcuts="dateShortcuts"
          :disabled-date="checkDisableDate"
        />
      </div>

      <!--因为这里的查询用到日期，而日期修改后是需要请求接口更新的，所以click回调不是用search-->
      <div class="tw-self-end tw-mb-[8px] tw-ml-[12px]">
        <el-button type="primary" link @click="updateAllList">
          <el-icon size="--el-font-size-base">
            <SvgIcon name="filter" color="var(--el-color-primary)" />
          </el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>

    <div class="section tw-justify-between">
      <!--查看方式-->
      <el-radio-group v-model="viewMode" @change="onChangeViewMode">
        <el-radio-button v-for="item in Object.values(viewModeList)" :key="item.value" :label="item.value">
          {{ item?.name || '' }}
        </el-radio-button>
      </el-radio-group>

      <!--按钮容器-->
      <div>
        <el-button plain type="primary" @click="onClickExport">
          导出数据
        </el-button>
        <el-button plain type="primary" @click="onClickRefresh">
          刷新数据
        </el-button>
      </div>
    </div>

    <!--表格-->
    <el-table
      v-loading="loadingAllList||loadingFilterList"
      :data="currentList"
      class="tw-mt-[8px]"
      stripe
      :header-cell-style="tableHeaderStyle"
    >
      <template #empty>
        <el-empty />
      </template>

      <el-table-column v-if="viewMode===viewModeList.seat.value" label="账号" align="left" fixed="left" prop="account" min-width="150" show-overflow-tooltip>
        <template #default="scope:{row:{account:string}}">
          {{ scope.row.account ?? '-' }}
        </template>
      </el-table-column>

      <el-table-column v-if="viewMode===viewModeList.seat.value" label="姓名" align="left" fixed="left" prop="name" min-width="150" show-overflow-tooltip>
        <template #default="scope:{row:{name:string}}">
          {{ scope.row.name ?? '-' }}
        </template>
      </el-table-column>

      <el-table-column label="坐席组" align="left" fixed="left" prop="callTeamName" min-width="150" show-overflow-tooltip>
        <template #default="scope:{row:{callTeamName:string}}">
          {{ scope.row.callTeamName ?? '-' }}
        </template>
      </el-table-column>

      <el-table-column label="在线时长" align="left" prop="onLineTime" min-width="110" show-overflow-tooltip>
        <template #default="scope:{row:{onLineTime:number}}">
          {{ formatMsDuration(scope.row.onLineTime / 1000) }}
        </template>
      </el-table-column>

      <el-table-column label="休息时长" align="left" prop="restTime" min-width="110" show-overflow-tooltip>
        <template #default="scope:{row:{restTime:number}}">
          {{ formatMsDuration(scope.row.restTime / 1000) }}
        </template>
      </el-table-column>

      <el-table-column label="漏接数量" align="left" prop="misCount" min-width="100" show-overflow-tooltip>
        <template #default="scope:{row:{misCount:number}}">
          {{ scope.row.misCount ?? '-' }}
        </template>
      </el-table-column>

      <el-table-column label="通话数量" align="left" prop="dialingCount" min-width="100" show-overflow-tooltip>
        <template #default="scope:{row:{dialingCount:number}}">
          {{ scope.row.dialingCount ?? '-' }}
        </template>
      </el-table-column>

      <el-table-column label="总接待时长" align="left" prop="totalReceptionDuration" min-width="110" show-overflow-tooltip>
        <template #default="scope:{row:{totalReceptionDuration:number}}">
          {{ formatMsDuration(scope.row.totalReceptionDuration / 1000) }}
        </template>
      </el-table-column>

      <el-table-column label="平均接待时长" align="left" prop="avgReceptionDuration" min-width="110" show-overflow-tooltip>
        <template #default="scope:{row:{avgReceptionDuration:number}}">
          {{ formatMsDuration(scope.row.avgReceptionDuration / 1000) }}
        </template>
      </el-table-column>

      <el-table-column label="总话后处理时长" align="left" prop="totalPostingDuration" min-width="120" show-overflow-tooltip>
        <template #default="scope:{row:{totalPostingDuration:number}}">
          {{ formatMsDuration(scope.row.totalPostingDuration / 1000) }}
        </template>
      </el-table-column>

      <el-table-column label="平均话后处理时长" align="left" prop="avgPostingDuration" min-width="130" show-overflow-tooltip>
        <template #default="scope:{row:{avgPostingDuration:number}}">
          {{ formatMsDuration(scope.row.avgPostingDuration / 1000) }}
        </template>
      </el-table-column>

      <el-table-column label="获取线索" align="left" prop="receivedClueCount" min-width="80" show-overflow-tooltip>
        <template #default="scope:{row:{receivedClueCount:number}}">
          {{ scope.row.receivedClueCount ?? '-' }}
        </template>
      </el-table-column>

      <el-table-column label="待首跟线索" align="left" prop="firstFollowClueCount" min-width="90" show-overflow-tooltip>
        <template #default="scope:{row:{firstFollowClueCount:number}}">
          {{ scope.row.firstFollowClueCount ?? '-' }}
        </template>
      </el-table-column>

      <el-table-column label="跟进线索" align="left" prop="followedClueCount" min-width="80" show-overflow-tooltip>
        <template #default="scope:{row:{followedClueCount:number}}">
          {{ scope.row.followedClueCount ?? '-' }}
        </template>
      </el-table-column>

      <el-table-column label="跟进成功" align="left" prop="followedSuccessClueCount" min-width="80" show-overflow-tooltip>
        <template #default="scope:{row:{followedSuccessClueCount:number}}">
          {{ scope.row.followedSuccessClueCount ?? '-' }}
        </template>
      </el-table-column>

      <el-table-column label="跟进失败" align="left" prop="followedFailedClueCount" min-width="80" show-overflow-tooltip>
        <template #default="scope:{row:{followedFailedClueCount:number}}">
          {{ scope.row.followedFailedClueCount ?? '-' }}
        </template>
      </el-table-column>

      <el-table-column label="跟进成功率" align="left" min-width="100" show-overflow-tooltip>
        <template #default="scope:{row:SeatMonitorStatisticsItem}">
          {{ computeFollowUpSuccessRate(scope.row) }}
        </template>
      </el-table-column>
    </el-table>

    <!--分页条-->
    <PaginationBox
      :currentPage="pageNum"
      :pageSize="pageSize"
      :pageSizeList="pageSizeList"
      :total="total"
      @search="updateList"
      @update="updateList"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, ref, toRaw } from 'vue'
import { SeatMonitorRealStatusItem, SeatMonitorStatisticsItem } from '@/type/seat'
import { formatMsDuration, formatNumberPercent, Throttle, updateCurrentPageList } from '@/utils/utils'
import { seatMonitorModel } from '@/api/seat'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/store/user'
import dayjs from 'dayjs'
import { tableHeaderStyle } from '@/assets/js/constant'
import { ElMessage } from 'element-plus'
import { exportExcel } from '@/utils/export'

// 动态引入组件
const PaginationBox = defineAsyncComponent(() => import('@/components/PaginationBox.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

const userStore = useUserStore()
const { groupId } = storeToRefs(userStore)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 数据统计 开始 ----------------------------------------

/**
 * 更新全部内容
 */
const update = async () => {
  await updateAllList()
  // 因为搜索条件是从现有列表里提取数据的
  // 所以应该等更新全部列表接口返回后立马更新
  updateSeatTeamList()
  updateSeatList()
}

// -------------------- 搜索条件 开始 --------------------

// 日期预设值
const dateShortcuts = ref([
  {
    text: '今天',
    value: dayjs().endOf('d').format('YYYY-MM-DD'),
  },
  {
    text: '昨天',
    value: dayjs().subtract(1, 'd').endOf('d').format('YYYY-MM-DD'),
  },
])

// 搜索条件，坐席组名称
const seatTeamNameVal = ref('')
// 搜索条件，坐席账号
const seatAccountVal = ref('')
// 搜索条件，日期
const dateVal = ref(dateShortcuts.value[0].value)

// 可选坐席组列表
const seatTeamNameList = ref<string[]>([])
// 可选坐席列表
const seatAccountList = ref<string[]>([])

/**
 * 更新搜索条件 可选坐席组列表
 */
const updateSeatTeamList = () => {
  const allSet = new Set(<string[]>allList.value.map((item: SeatMonitorStatisticsItem) => {
    return item?.callTeamName ?? ''
  }))
  allSet.delete('')
  seatTeamNameList.value = Array.from(allSet)
}
/**
 * 更新搜索条件 可选坐席列表
 */
const updateSeatList = (list?: SeatMonitorRealStatusItem[]) => {
  const allSet = new Set(<string[]>(list ?? allList.value).map((item: SeatMonitorStatisticsItem) => {
    return item?.account ?? ''
  }))
  allSet.delete('')
  seatAccountList.value = Array.from(allSet)
}
// 监听搜索条件坐席组名称改变，联动调整坐席列表
// watch(seatTeamNameVal, (val: string) => {
//   const SeatInCurrentTeamList = allList.value.filter((item: SeatMonitorStatisticsItem) => {
//     return item?.callTeamName === val
//   })
//   updateSeatList(SeatInCurrentTeamList)
// })
/**
 * 判断日期是否禁用（不可选）
 * @param {Date} date 日期对象
 * @returns {boolean} 是否禁用 true 禁用 false 启用
 */
const checkDisableDate = (date: Date): boolean => {
  const day = dayjs(date)
  // 未来日期禁用
  if (day.isAfter(dayjs())) {
    return true
  }
  // 超过以前特定天数的禁用
  const diff = day.diff(dayjs(), 'day')
  return diff < -30 * 4 || diff > 0
}
/**
 * 点击刷新按钮
 */
const onClickRefresh = () => {
  update()
}
/**
 * 点击导出按钮
 */
const onClickExport = () => {
  // 搜索时间
  const date = dateVal.value || dateShortcuts.value[0].value

  // 将列表数据转换成表格数据
  const list: { [prop: string]: any }[] = (toRaw(filterList.value) ?? []).map((row: SeatMonitorStatisticsItem) => {
    return {
      '账号': row.account ?? '-',
      '姓名': row.name ?? '-',
      '坐席组': row.callTeamName ?? '-',
      '在线时长': row.onLineTime ? formatMsDuration(row.onLineTime / 1000) : '-',
      '休息时长': row.restTime ? formatMsDuration(Number(row.restTime) / 1000) : '-',
      '漏接数量': row.misCount ?? '-',
      '通话数量': row.dialingCount ?? '-',
      '总接待时长': row.totalReceptionDuration ? formatMsDuration(row.totalReceptionDuration / 1000) : '-',
      '平均接待时长': row.avgReceptionDuration ? formatMsDuration(row.avgReceptionDuration / 1000) : '-',
      '总话后处理时长': row.totalPostingDuration ? formatMsDuration(row.totalPostingDuration / 1000) : '-',
      '平均话后处理时长': row.avgPostingDuration ? formatMsDuration(row.avgPostingDuration / 1000) : '-',
      '获取线索': row.receivedClueCount ?? '-',
      '待首跟线索': row.firstFollowClueCount ?? '-',
      '跟进线索': row.followedClueCount ?? '-',
      '跟进成功': row.followedSuccessClueCount ?? '-',
      '跟进失败': row.followedFailedClueCount ?? '-',
      '跟进成功率': computeFollowUpSuccessRate(row),
    }
  })

  try {
    exportExcel(
      list,
      '坐席监控数据统计_' + date + '.xlsx',
      date
    )
  } catch (e) {
    ElMessage.error('导出失败：' + e)
    console.error('导出失败', e)
  }
}

// -------------------- 搜索条件 结束 --------------------

// -------------------- 查看方式 开始 --------------------

// 查看方式 映射表
const viewModeList = {
  seat: { name: '坐席', value: 'seat' },
  seatGroup: { name: '坐席组', value: 'seatGroup' },
}
// 查看方式
const viewMode = ref<string>(viewModeList.seat.value)

/**
 * 切换查看方式
 * @param val 查看方式
 */
const onChangeViewMode = (val: string) => {
  viewMode.value = val
  updateAllList()
}

// -------------------- 查看方式 结束 --------------------

// -------------------- 表格 开始 --------------------

// 全部列表，接口数据
const allList = ref<SeatMonitorStatisticsItem[]>([])
// 正在加载全部列表
const loadingAllList = ref<boolean>(false)
// 全部列表节流锁
const throttleAllList = new Throttle(loadingAllList)

// 搜索结果列表，全部的子集
const filterList = ref<SeatMonitorStatisticsItem[]>([])
// 搜索结果列表，正在加载
const loadingFilterList = ref<boolean>(false)
// 搜索结果列表，加载节流锁
const throttleFilterList = new Throttle(loadingFilterList)

// 当前页列表，页面展示，搜索结果的子集
const currentList = ref<SeatMonitorStatisticsItem[]>([])
// 当前页码
const pageNum = ref(1)
// 每页大小
const pageSize = ref(10)
// 可选页码大小
const pageSizeList = [10, 20, 50, 100]
// 总数
const total = computed(() => {
  return filterList.value.length ?? 0
})

/**
 * 更新列表 全部
 */
const updateAllList = async () => {
  // 节流锁上锁
  if (throttleAllList.check()) {
    return
  }
  throttleAllList.lock()

  try {
    // 请求接口
    const params: { groupId: string, date?: string } = {
      groupId: groupId.value ?? '',
      date: dateVal.value.toString() || dayjs().format('YYYY-MM-DD').toString()
    }
    let res: {}
    if (viewMode.value === viewModeList.seat.value) {
      res = <SeatMonitorStatisticsItem[]>await seatMonitorModel.getSeatStatistics(params)
    } else {
      res = <SeatMonitorStatisticsItem[]>await seatMonitorModel.getSeatTeamStatistics(params)
    }
    // 重置并更新全部列表
    allList.value = Array.isArray(res) ? res : []
    // 按通话数量倒序排序
    allList.value = allList.value.sort((a: SeatMonitorStatisticsItem, b: SeatMonitorStatisticsItem) => {
      return (b.dialingCount ?? 0) - (a.dialingCount ?? 0)
    })

    // 刷新搜索结果
    search()
  } catch (e) {
    ElMessage.error('更新数据出错，请重试')
    // 发生错误，重置列表
    allList.value = []
    search()
  } finally {
    // 节流锁解锁
    throttleAllList.unlock()
  }
}
/**
 * 更新列表 搜索
 */
const search = () => {
  // 节流锁上锁
  if (throttleFilterList.check()) {
    return
  }
  throttleFilterList.lock()

  // 按搜索条件筛选列表
  filterList.value = allList.value
  if (seatTeamNameVal.value) {
    filterList.value = filterList.value.filter((item: SeatMonitorStatisticsItem) => {
      return item?.callTeamName?.includes(seatTeamNameVal.value)
    })
  }
  if (seatAccountVal.value) {
    filterList.value = filterList.value.filter((item: SeatMonitorStatisticsItem) => {
      return item?.account?.includes(seatAccountVal.value)
    })
  }
  updateList(pageNum.value, pageSize.value)

  // 节流锁解锁
  throttleFilterList.unlock()
}
/**
 * 更新列表 当前页码
 */
const updateList = (p?: number, s?: number) => {
  if (p || s) {
    // 如果参数指定了页码或/和每页大小，则按参数更新列表
    p && (pageNum.value = p)
    s && (pageSize.value = s)
    currentList.value = updateCurrentPageList(filterList.value, pageNum.value, pageSize.value)
  } else {
    // 如果参数都没有指定，则页码和页面大小保持不变，更新全部列表
    updateAllList()
  }
}
/**
 * 计算更新成功率
 * 跟进成功率=跟进成功/跟进线索
 * @param {SeatMonitorStatisticsItem} row 表格行
 * @return {string|number} 计算出的百分比字符串，无效值返回连字符-
 */
const computeFollowUpSuccessRate = (row: SeatMonitorStatisticsItem): string | number => {
  // 跟进成功和跟进线索都不是数字
  if (typeof row?.followedSuccessClueCount !== 'number' || typeof row?.followedClueCount !== 'number') {
    return '-'
  }
  // 分母是0
  if (row?.followedClueCount === 0) {
    return 0
  }
  // 正常计算
  const success = row?.followedSuccessClueCount ?? 0
  const total = row?.followedClueCount ?? 0
  const rate = success / total
  return rate > 0 ? formatNumberPercent(rate * 100, 1) : 0
}

// -------------------- 表格 结束 --------------------

// ---------------------------------------- 数据统计 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

update()

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.card-box {
  flex-direction: column;
  align-items: stretch;
  overflow: hidden;
  padding: 0;
  border-radius: 4px;
}
.section {
  display: flex;
  margin: 16px 16px 0;
}
:deep(.search-input) {
  width: 284px;
}
:deep(.search-datetime.el-input) {
  width: 284px;
  margin-left: auto;
}
:deep(.search-datetime .el-input__wrapper) {
  width: 284px;
}
</style>
