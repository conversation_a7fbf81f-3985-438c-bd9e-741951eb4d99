<template>
  <div class="card-box">
    <!--搜索条-->
    <div v-if="accountType===0" class="section">
      <!-- 针对运营端需要提供groupId -->
      <div  class="tw-mr-[12px] tw-mb-[-8px] tw-w-full tw-flex tw-flex-col">
        <div class="tw-mb-[8px] tw-flex">
          <span class="tw-text-[13px] tw-text-[#969799] ">主账号：</span>
          <el-button v-if="isSelected" link type="primary" @click="reselect(false)">重新筛选</el-button>
        </div>
        <div v-if="!isSelected" class="tw-flex tw-items-center">
          <SelectBox
            v-model:selectVal="globalStore.masterAccountGroupIds"
            :options="masterAccountList||[]"
            class="tw-w-[282px]"
            name="account"
            val="groupId"
            placeholder="选择主账号列表"
            filterable
            multiple
             @update:selectVal="handleGroupIdChange()"
          >
          </SelectBox>
          <el-button v-if="!isSelected" link type="primary" @click="reselect(true)" class="tw-ml-[12px]">
            <el-icon size="--el-font-size-base">
              <SvgIcon name="filter" color="var(--el-color-primary)" />
            </el-icon>
            <span>筛选</span>
          </el-button>
        </div>
        <el-scrollbar v-if="isSelected" class="tw-w-full" wrap-class="tw-pb-1" view-class="tw-flex  tw-text-left tw-gap-1">
          <span
            v-for="(item, index) in filteredMasterAccountList||[]"
            :key="item.groupId"
            class="account-box"
            :class="masterAccountGroupId === item.groupId ? 'active' : ''"
            @click="handleClickAccount(item.groupId)"
          >{{ (index+1) + '. ' + item.account }}</span>
        </el-scrollbar>
      </div>
    </div>
    <div class="section">
      <div>
        <div class="tw-mb-[8px] tw-text-[13px] tw-text-[#969799] tw-text-left">座席组名称：</div>
        <el-select v-model.trim="seatTeamNameVal" placeholder="选择坐席组名称" class="search-input" clearable filterable>
          <el-option
            v-for="(seatTeamNameItem,seatTeamNameIndex) in seatTeamNameList"
            :key="seatTeamNameIndex"
            :label="seatTeamNameItem"
            :value="seatTeamNameItem"
          />
        </el-select>
      </div>

      <div class="tw-ml-[12px]">
        <div class="tw-mb-[8px] tw-text-[13px] tw-text-[#969799] tw-text-left">座席账号：</div>
        <el-select v-model.trim="seatAccountVal" placeholder="选择坐席账号" class="search-input" clearable filterable>
          <el-option
            v-for="(seatAccountItem,seatAccountIndex) in seatAccountList"
            :key="seatAccountIndex"
            :label="seatAccountItem"
            :value="seatAccountItem"
          />
        </el-select>
      </div>

      <div class="tw-self-end tw-mb-[8px] tw-ml-[12px]">
        <el-button type="primary" link @click="updateAllList">
          <el-icon size="--el-font-size-base">
            <SvgIcon name="search" color="var(--el-color-primary)" />
          </el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>

    <div class="section tw-justify-end">
      <!--刷新按钮-->
      <el-button plain type="primary" @click="onClickRefresh">
        刷新数据
      </el-button>
    </div>

    <!--表格-->
    <el-table
      v-loading="loadingAllList||loadingFilterList"
      :data="currentList"
      stripe
      class="tw-mt-[8px]"
      :header-cell-style="tableHeaderStyle"
    >
      <template #empty>
        <el-empty />
      </template>

      <el-table-column label="账号" align="left" fixed="left" prop="account" min-width="150" show-overflow-tooltip>
        <template #default="scope:{row:{account:string}}">
          {{ scope.row.account ?? '-' }}
        </template>
      </el-table-column>

      <el-table-column label="姓名" align="left" fixed="left" prop="name" min-width="150" show-overflow-tooltip>
        <template #default="scope:{row:{name:string}}">
          {{ scope.row.name ?? '-' }}
        </template>
      </el-table-column>

      <el-table-column label="坐席组" align="left" fixed="left" prop="callTeam" min-width="150" show-overflow-tooltip>
        <template #default="scope:{row:{callTeam:SeatTeam}}">
          {{ scope.row.callTeam?.callTeamName ?? '-' }}
        </template>
      </el-table-column>

      <el-table-column label="在线状态" align="center" prop="status" min-width="80">
        <template #default="scope:{row:{status:string}}">
          <template v-if="scope?.row?.status">
            {{ scope.row.status === seatStatusList.OFF_LINE.value ? '离线' : '在线' }}
          </template>
          <template v-else>
            -
          </template>
        </template>
      </el-table-column>

      <el-table-column label="签入任务" align="left" prop="task" min-width="80">
        <template #default="scope:{row:{status:string,tasks:TaskManageItem[]}}">
          <template v-if="scope?.row?.status===seatStatusList.OFF_LINE.value">
            -
          </template>
          <template v-else>
            <div v-if="scope?.row?.tasks?.length" class="tw-flex tw-flex-row tw-items-center">
              <div class="tw-w-[4px] tw-h-[4px] tw-mr-[4px] tw-rounded-[4px] tw-bg-[#13BF77]"></div>
              签入
            </div>
            <div v-else class="tw-flex tw-flex-row tw-items-center">
              <div class="tw-w-[4px] tw-h-[4px] tw-mr-[4px] tw-rounded-[4px] tw-bg-[#FFAA3D]"></div>
              未签入
            </div>
          </template>
        </template>
      </el-table-column>

      <el-table-column label="任务数量" align="left" prop="task" min-width="80">
        <template #default="scope:{row:SeatMonitorRealStatusItem}">
          <div v-if="scope.row.status===SeatStatusEnum.OFF_LINE||!scope.row.tasks?.length">
            -
          </div>
          <div
            v-else
            class="tw-cursor-pointer tw-truncate tw-text-[--el-color-primary]"
            @click="onClickTaskCount(scope.row)"
          >
            {{ scope.row.tasks?.length ?? 0 }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="坐席状态" align="center" prop="status" min-width="80">
        <template #default="scope:{row:{status:string}}">
          <div class="status-box" :class="getSeatStatusColorClassName(scope.row.status)">
            {{ getSeatStatusText(scope.row.status) }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="持续时长" align="left" min-width="110">
        <template #default="scope:{row:SeatMonitorRealStatusItem}">
          {{ idleSeatData && scope.row.account ? formatDuration(idleSeatData[scope.row.account]) : '-' }}
        </template>
      </el-table-column>

      <el-table-column label="今日在线时长" align="left" prop="onLineTimes" min-width="110">
        <template #default="scope:{row:{onLineMillis:number}}">
          <div
            class="tw-cursor-pointer tw-truncate tw-text-[--el-color-primary]"
            @click="onClickTimeDetail(TimeTypeEnum.ONLINE,scope.row)"
          >
            {{ formatMsDuration(scope.row.onLineMillis / 1000) }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="今日休息时长" align="left" prop="restTime" min-width="110">
        <template #default="scope:{row:{restMillis:number}}">
          <div
            class="tw-cursor-pointer tw-truncate tw-text-[--el-color-primary]"
            @click="onClickTimeDetail(TimeTypeEnum.REST,scope.row)"
          >
            {{ formatMsDuration(scope.row.restMillis / 1000) }}
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!--分页条-->
    <PaginationBox
      :currentPage="pageNum"
      :pageSize="pageSize"
      :pageSizeList="pageSizeList"
      :total="total"
      @search="updateList"
      @update="updateList"
    />
  </div>

  <!--时长详情弹窗-->
  <el-dialog v-model="dialogTimeDetailVisible" align-center width="480px" :close-on-click-modal="false">
    <template #header>
      <div class="form-dialog-header">
        {{ dialogTimeDetailType === TimeTypeEnum.ONLINE ? '在线时长' : '休息时长' }}
      </div>
    </template>

    <div class="form-dialog-main">
      <div class="form-dialog-top">
        <template v-if="dialogTimeDetailType===TimeTypeEnum.ONLINE">
          <p>上线时间</p>
          <p class="gap"></p>
          <p>下线时间</p>
        </template>
        <template v-else>
          <p>开始时间</p>
          <p class="gap"></p>
          <p>结束时间</p>
        </template>
      </div>
      <el-scrollbar max-height="400px" view-class="form-dialog-content">
        <div class="list">
          <!--在线开始时间-->
          <!--蓝色-->
          <template v-if="dialogTimeDetailType===TimeTypeEnum.ONLINE">
            <template v-if="dialogTimeDetailOnline.length">
              <div v-for="(detailItem,detailIndex) in dialogTimeDetailOnline" :key="detailIndex" class="time blue-label">
                {{ formatTime(null, null, detailItem.timeStart) }}
              </div>
            </template>
            <template v-else>
              <el-empty />
            </template>
          </template>

          <!--休息开始时间-->
          <!--黄色-->
          <template v-if="dialogTimeDetailType===TimeTypeEnum.REST">
            <template v-if="dialogTimeDetailRest.length">
              <div v-for="(detailItem,detailIndex) in dialogTimeDetailRest" :key="detailIndex" class="time yellow-label">
                {{ formatTime(null, null, detailItem.timeStart) }}
              </div>
            </template>
            <template v-else>
              <el-empty />
            </template>
          </template>
        </div>

        <div class="gap"></div>

        <div class="list">
          <!--在线结束时间-->
          <!--黄色-->
          <template v-if="dialogTimeDetailType===TimeTypeEnum.ONLINE">
            <template v-if="dialogTimeDetailOnline.length">
              <div v-for="(detailItem,detailIndex) in dialogTimeDetailOnline" :key="detailIndex" class="time yellow-label">
                {{ formatTime(null, null, detailItem.timeEnd) }}
              </div>
            </template>
            <template v-else>
              <el-empty />
            </template>
          </template>

          <!--休息结束时间-->
          <!--蓝色-->
          <template v-if="dialogTimeDetailType===TimeTypeEnum.REST">
            <template v-if="dialogTimeDetailRest.length">
              <div v-for="(detailItem,detailIndex) in dialogTimeDetailRest" :key="detailIndex" class="time blue-label">
                {{ formatTime(null, null, detailItem.timeEnd) }}
              </div>
            </template>
            <template v-else>
              <el-empty />
            </template>
          </template>
        </div>
      </el-scrollbar>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="onClickDialogTimeDetailClose">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!--任务列表弹窗-->
  <TaskListDialog
    :visible="taskListDialogVisible"
    :data="taskListDialogData"
    @close="onCloseTaskListDialog"
  />
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, reactive, ref } from 'vue'
import {
  SeatMonitorRealStatusDetail,
  SeatMonitorRealStatusItem,
  SeatStatusEnum,
  seatStatusList,
  SeatTeam
} from '@/type/seat'
import { formatMsDuration, formatTime, Throttle, updateCurrentPageList, formatDuration } from '@/utils/utils'
import { getSeatStatusColorClassName, getSeatStatusText } from '@/utils/seat'
import { CloseBold } from '@element-plus/icons-vue'
import { seatMonitorModel } from '@/api/seat'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/store/user'
import { useGlobalStore } from '@/store/globalInfo'
import { ManualTaskManageSeatDetail, ManualTaskManageStatics, TaskManageItem } from '@/type/task'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from 'await-to-js'
import { aiOutboundTaskModel } from '@/api/ai-report'
import dayjs from 'dayjs'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import { ElMessage } from 'element-plus'
import SelectBox from '@/components/SelectBox.vue'

// 动态引入组件
const PaginationBox = defineAsyncComponent(() => import('@/components/PaginationBox.vue'))
const TaskListDialog = defineAsyncComponent(() => import('./TaskListDialog.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

const userStore = useUserStore()
const { groupId, accountType } = storeToRefs(userStore)
const globalStore = useGlobalStore()

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 系统监控-运营监控-坐席监控 开始 ----------------------------------------

// 系统监控-运营监控-坐席监控tab使用。需要获取全部主账号groupId
const isSelected = ref(false) // 主账号是否完成初筛状态，false：筛选框，多选； true: 完成初筛状态，平铺选项，进行单选
// 通过按钮切换是否完成初筛状态
const reselect = (val: boolean) => {
  isSelected.value = val
}
const masterAccountGroupId = ref<string|undefined>(undefined) // 当前选择的主账号
const masterAccountList = ref<{
  account: string,
  groupId: string,
}[]>([]) // 主账号列表，全量

// 初筛后的主账号列表（account、groupId），和缓存groupId列表对应globalStore.masterAccountGroupIds
const filteredMasterAccountList = computed(() =>  masterAccountList.value?.filter(item => globalStore.masterAccountGroupIds?.includes(item.groupId)))

// 进入该页面，读取缓存，并选择第一个选项进行搜索
const getMasterAccountList = async () => {
  const [err, res] = await to(monitorStatisticModel.getAllMainAccount())
  masterAccountList.value = (res || []).filter(item => !!item.account && !!item.groupId)
  globalStore.masterAccountGroupIds = globalStore.masterAccountGroupIds?.length > 0 ? globalStore.masterAccountGroupIds : [masterAccountList.value[0]?.groupId]
  // 如缓存中仅一个主账号，不进入完成初筛状态
  if (globalStore.masterAccountGroupIds?.length > 1) {
    isSelected.value = true
  }
  handleGroupIdChange()
}

// 主账号初筛结果变化处理函数
const handleGroupIdChange = () => {
  if (!masterAccountGroupId.value || !globalStore.masterAccountGroupIds.includes(masterAccountGroupId.value)) {
    handleClickAccount(globalStore.masterAccountGroupIds[0] || undefined)
  } else if (!globalStore.masterAccountGroupIds || globalStore.masterAccountGroupIds.length < 1) {
    handleClickAccount(undefined)
  }
}

// 完成初筛后，点击某个主账号后的处理函数（更新坐席、列表等），若为空，则清空列表
const handleClickAccount = async (val?: string) => {
  masterAccountGroupId.value = val
  // 运营端：对于无主账号情况，清空数据
  if (!masterAccountGroupId.value) {
    allList.value = []
    filterList.value = allList.value
  } else {
    await updateAllList()
    updateSeatTeamList()
    updateSeatList()
  }
}

// ---------------------------------------- 系统监控-运营监控-坐席监控 结束 ----------------------------------------

// ---------------------------------------- 实时状态 开始 ----------------------------------------

/**
 * 更新全部内容
 */
const update = async () => {
  accountType.value === 0 && await getMasterAccountList()
  await updateAllList()
  // 因为搜索条件是从现有列表里提取数据的
  // 所以应该等更新全部列表接口返回后立马更新
  updateSeatTeamList()
  updateSeatList()
}

// -------------------- 搜索条件 开始 --------------------

// 搜索条件，坐席组名称
const seatTeamNameVal = ref('')
// 搜索条件，坐席账号
const seatAccountVal = ref('')

// 可选坐席组列表
const seatTeamNameList = ref<string[]>([])
// 可选坐席列表
const seatAccountList = ref<string[]>([])

/**
 * 更新搜索条件 可选坐席组列表
 */
const updateSeatTeamList = () => {
  const allSet = new Set(<string[]>allList.value.map((item: SeatMonitorRealStatusItem) => {
    return item?.callTeam?.callTeamName ?? ''
  }))
  allSet.delete('')
  seatTeamNameList.value = Array.from(allSet)
}
/**
 * 更新搜索条件 可选坐席列表
 */
const updateSeatList = (list?: SeatMonitorRealStatusItem[]) => {
  const allSet = new Set(<string[]>(list ?? allList.value).map((item: SeatMonitorRealStatusItem) => {
    return item?.account ?? ''
  }))
  allSet.delete('')
  seatAccountList.value = Array.from(allSet)
}
// 监听搜索条件坐席组名称改变，联动调整坐席列表
// watch(seatTeamNameVal, (val: string) => {
//   const SeatInCurrentTeamList = allList.value.filter((item: SeatMonitorRealStatusItem) => {
//     return item?.callTeam?.callTeamName === val
//   })
//   updateSeatList(SeatInCurrentTeamList)
// })
/**
 * 点击刷新按钮
 */
const onClickRefresh = () => {
  update()
}

// -------------------- 搜索条件 结束 --------------------

// -------------------- 表格 开始 --------------------

// 全部列表，接口数据
const allList = ref<SeatMonitorRealStatusItem[]>([])
const idleSeatData = ref<Record<string, string> | null>(null)
// 全部列表，正在加载
const loadingAllList = ref<boolean>(false)
// 全部列表，加载节流锁
const throttleAllList = new Throttle(loadingAllList)

// 搜索结果列表，全部的子集
const filterList = ref<SeatMonitorRealStatusItem[]>([])
// 搜索结果列表，正在加载
const loadingFilterList = ref<boolean>(false)
// 搜索结果列表，加载节流锁
const throttleFilterList = new Throttle(loadingFilterList)

// 当前页列表，页面展示，搜索结果的子集
const currentList = ref<SeatMonitorRealStatusItem[]>([])
// 当前页码
const pageNum = ref(1)
// 每页大小
const pageSize = ref(20)
// 可选页码大小
const pageSizeList = [10, 20, 50, 100]
// 总数
const total = computed(() => {
  return filterList.value.length ?? 0
})

/**
 * 更新列表 全部
 */
const updateAllList = async () => {
  // 节流锁上锁
  if (throttleAllList.check()) {
    return
  }
  throttleAllList.lock()
  try {
    // 请求接口
    const res = <SeatMonitorRealStatusItem[]>await seatMonitorModel.getRealStatistics({
      groupId: accountType.value === 0 ? masterAccountGroupId.value ?? '' : groupId.value ?? '',
    })
    // 重置并更新全部列表
    allList.value = Array.isArray(res) ? res : []

    // 按在线和签入任务排序
    sortAllList()

    // 获取空闲坐席及对应的空闲时长
    const [_, data] = await to(seatMonitorModel.getSeatIDELDuration({
      groupId: accountType.value === 0 ? masterAccountGroupId.value ?? '' : groupId.value ?? ''
    }))
    idleSeatData.value = data || null

    // 刷新搜索结果
    search()
  } catch (e) {
    ElMessage.error('更新数据出错，请重试')
    // 发生错误，重置列表
    allList.value = []
    search()
  } finally {
    // 节流锁解锁
    throttleAllList.unlock()
  }
}
/**
 * 更新列表 搜索
 */
const search = () => {
  // 节流锁上锁
  if (throttleFilterList.check()) {
    return
  }
  throttleFilterList.lock()

  // 按搜索条件筛选列表
  filterList.value = allList.value
  if (seatTeamNameVal.value) {
    filterList.value = filterList.value.filter((item: SeatMonitorRealStatusItem) => {
      return item?.callTeam?.callTeamName?.includes(seatTeamNameVal.value)
    })
  }
  if (seatAccountVal.value) {
    filterList.value = filterList.value.filter((item: SeatMonitorRealStatusItem) => {
      return item?.account?.includes(seatAccountVal.value)
    })
  }
  updateList(pageNum.value, pageSize.value)

  // 节流锁解锁
  throttleFilterList.unlock()
}
/**
 * 更新列表 当前页码
 */
const updateList = (p?: number, s?: number) => {
  if (p || s) {
    // 如果参数指定了页码或/和每页大小，则按参数更新列表
    p && (pageNum.value = p)
    s && (pageSize.value = s)
    currentList.value = updateCurrentPageList(filterList.value, pageNum.value, pageSize.value)
  } else {
    // 如果参数都没有指定，则页码和页面大小保持不变，更新全部列表
    updateAllList()
  }
}
/**
 * 点击查看时长详情
 * @param {TimeTypeEnum} type 时长类型
 * @param row 表格行数据
 */
const onClickTimeDetail = (type: TimeTypeEnum, row: SeatMonitorRealStatusItem) => {
  // 更新弹窗表单内容
  dialogTimeDetailType.value = type
  dialogTimeDetailOnline.value = row?.onLineBlocks ?? []
  dialogTimeDetailRest.value = row?.restBlocks ?? []
  // 显示弹窗
  dialogTimeDetailVisible.value = true
}

/**
 * 排序全部列表
 */
const sortAllList = () => {
  // 首要排序条件：在线状态，在线在前，离线在后
  // 次要排序条件：签入任务，签入在前，未签入在后
  allList.value.sort((a: SeatMonitorRealStatusItem, b: SeatMonitorRealStatusItem) => {
    // 简化代码长度，方便阅读，此变量没有实质作用
    const OFFLINE = SeatStatusEnum.OFF_LINE

    if (a?.status !== OFFLINE && b?.status !== OFFLINE) {
      // 两个都在线，按签入任务排序

      // 两个坐席签入任务数量
      if (a?.tasks?.length && b?.tasks?.length || !a?.tasks?.length && !b?.tasks?.length) {
        // 两个都签入或都未签入，保持顺序不变
        return 0
      } else {
        // 其中一个签入，另一个不签入，签入的靠前，不签入的靠后
        return a?.tasks?.length ? -1 : 1
      }
    } else if (a?.status === OFFLINE && b?.status === OFFLINE) {
      // 两个都离线，保持顺序不变
      return 0
    } else {
      // 其中一个在线，另一个离线，在线的靠前，离线的靠后
      return a?.status === OFFLINE ? 1 : -1
    }
  })
}


// -------------------- 表格 结束 --------------------

// ---------------------------------------- 实时状态 结束 ----------------------------------------

// ---------------------------------------- 时长详情弹窗 开始 ----------------------------------------

enum TimeTypeEnum {
  ONLINE,
  REST,
}

// 时长详情弹窗 是否显示
const dialogTimeDetailVisible = ref(false)
// 时长详情弹窗 类型
const dialogTimeDetailType = ref<TimeTypeEnum>(TimeTypeEnum.ONLINE)
// 时长详情弹窗 在线时长数据
const dialogTimeDetailOnline = ref<SeatMonitorRealStatusDetail[]>([])
// 时长详情弹窗 休息时长数据
const dialogTimeDetailRest = ref<SeatMonitorRealStatusDetail[]>([])

/**
 * 时长详情弹窗 点击关闭按钮
 */
const onClickDialogTimeDetailClose = () => {
  dialogTimeDetailVisible.value = false
}

// ---------------------------------------- 时长详情弹窗 结束 ----------------------------------------

// ---------------------------------------- 任务列表弹窗 开始 ----------------------------------------

// 任务列表弹窗 是否显示
const taskListDialogVisible = ref<boolean>(false)
// 任务列表弹窗 组件数据
const taskListDialogData = ref<TaskManageItem[]>([])

/**
 * 统计表格 点击任务数量
 * @param {SeatMonitorRealStatusItem} row 表格行数据
 */
const onClickTaskCount = (row: SeatMonitorRealStatusItem) => {
  // 更新任务列表
  taskListDialogData.value = row?.tasks ?? []
  // 显示任务列表弹窗
  taskListDialogVisible.value = true
}
/**
 * 任务列表弹窗 组件关闭
 */
const onCloseTaskListDialog = () => {
  taskListDialogVisible.value = false
}

// ---------------------------------------- 任务列表弹窗 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

update()

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.card-box {
  flex-direction: column;
  align-items: stretch;
  overflow: hidden;
  padding: 0;
  border-radius: 4px;
}
.el-table {
  font-size: 13px;
  :deep(.cell) {
    padding: 0 8px;
  }
}
.section {
  display: flex;
  margin: 16px 16px 0;
}
:deep(.search-input) {
  width: 284px;
}
.form-dialog-header {
  font-size: 16px;
  font-weight: 600;
  color: #2E3033;
  text-align: left;
}
.form-dialog-main {
  padding: 0;
}
.form-dialog-footer {
  padding: 0 8px;
}
.form-dialog-top {
  display: flex;
  justify-content: space-between;
  padding: 20px 12px 8px;
  & > * {
    flex: auto;
    width: 50%;
    font-size: 13px;
    color: #969799;
  }
}
:deep(.form-dialog-content) {
  display: flex;
  justify-content: center;
  padding: 0 12px;
}
.list {
  width: 100%;
  height: 100%;
  padding: 0 0 12px;
}
.gap {
  flex: none;
  width: 12px;
}
.time {
  position: relative;
  margin-top: 12px;
  padding: 8px 0;
  border-radius: 4px;
  background-color: #F0F2F5;
  text-align: center;
  &::before {
    content: '';
    position: absolute;
    top: 17%;
    left: 0;
    width: 2px;
    height: 66%;
    border-radius: 0 2px 2px 0;
    background-color: #000;
  }
  &:first-child {
    margin-top: 0;
  }
  &.blue-label {
    &::before {
      background-color: #165DFF;
    }
  }
  &.yellow-label {
    &::before {
      background-color: #FFAA3D;
    }
  }
}
.account-box {
  border-radius: 4px;
  border: 1px solid var(--primary-black-color-300);
  padding: 5px 5px;
  line-height: 20px;
  height: 32px;
  font-size: 13px;
  color: var(--primary-black-color-400);
  cursor: pointer;
  overflow-x: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 120px;
  flex-shrink: 0;
  &.active {
    border-color: var(--el-color-primary);
    color: #fff;
    background-color: var(--el-color-primary);
  }
}
</style>
