<template>
  <HeaderBox title="线索管理" />
  <el-scrollbar class="module-container tw-min-w-[1080px]">
    <div class="tw-w-full tw-bg-white tw-py-[12px] tw-px-[16px] tw-rounded-[4px]" v-loading="loading">
      <div class="tw-flex tw-items-center ">
        <span class="tw-font-semibold tw-text-[var(--primary-black-color-600)] tw-text-[14px]">线索统计</span>
        <el-tooltip content="刷新线索统计" placement="right" :show-after="500">
          <el-icon size="13" class="tw-cursor-pointer tw-ml-0.5" @click="refreshClueStatics()" color="var(--el-color-primary)"><SvgIcon name="reset"/></el-icon>
        </el-tooltip>
      </div>
      <div class="tw-w-full tw-grid tw-grid-cols-9 tw-gap-x-[12px] tw-mt-[8px]">
        <div class="info-data-box">
          <div class="info-data-box-inner">
            <span class="info-title">总线索数</span>
            <span class="info-data-content">{{formatNumber1(clueStatics.totalCount)??'-'}}</span>
          </div>
        </div>
        <div class="info-data-box tw-col-span-3">
          <div class="info-data-box-inner">
            <span class="info-title">待下发</span>
            <span class="info-data-content">{{formatNumber1(clueStatics.toBeSendCount)??'-'}}</span>
          </div>
          <div class="info-data-box-inner">
            <span class="info-title">待分配</span>
            <span class="info-data-content">{{formatNumber1(clueStatics.toBeDistributeCount)??'-'}}</span>
          </div>
          <div class="info-data-box-inner">
            <span class="info-title">已分配</span>
            <span class="info-data-content">{{formatNumber1(clueStatics.beDistributeCount)??'-'}}</span>
          </div>
        </div>
        <div class="info-data-box tw-col-span-2">
          <div class="info-data-box-inner">
            <span class="info-title">已回收</span>
            <span class="info-data-content">{{formatNumber1(clueStatics.beRecoveredCount)??'-'}}</span>
          </div>
          <div class="info-data-box-inner">
            <span class="info-title">已归档</span>
            <span class="info-data-content">{{formatNumber1(clueStatics.beArchivedCount)??'-'}}</span>
          </div>
        </div>
        <div class="info-data-box tw-col-span-2">
          <div class="info-data-box-inner">
            <span class="info-title">已成功</span>
            <span class="info-data-content">{{formatNumber1(clueStatics.successCount)??'-'}}</span>
          </div>
          <div class="info-data-box-inner">
            <span class="info-title">已失败</span>
            <span class="info-data-content">{{formatNumber1(clueStatics.failedCount)??'-'}}</span>
          </div>
        </div>
        <div class="info-data-box">
          <div class="info-data-box-inner">
            <span class="info-title">待首跟</span>
            <span class="info-data-content">{{formatNumber1(clueStatics.toBeFirstFollowUpCount)??'-'}}</span>
          </div>
        </div>
      </div>
    </div>
    <TabsBox v-model:active="activeTab" :tabList="tabList" class="tw-mt-[12px]"></TabsBox>
    <keep-alive>
      <ToBeSendCluesQueue v-if="activeTab==='待下发'" :clueType="ClueStatusEnum[activeTab]" :groupType="0" @update:clue="refreshClueStatics()"/>
      <ToBeDistributeCluesQueue  v-else-if="activeTab==='待分配'" :clueType="ClueStatusEnum[activeTab]" @update:clue="refreshClueStatics()" :groupType="0"/>
      <BeDistributeCluesQueue  v-else-if="activeTab==='已分配'" :clueType="ClueStatusEnum[activeTab]" @update:clue="refreshClueStatics()" :groupType="0"/>
      <BeRecoveredCluesQueue  v-else-if="activeTab==='已回收'" :clueType="ClueStatusEnum[activeTab]" @update:clue="refreshClueStatics()" :groupType="0"/>
      <BeArchivedCluesQueue  v-else-if="activeTab==='已归档'" :clueType="ClueStatusEnum[activeTab]" @update:clue="refreshClueStatics()" :groupType="0"/>
    </keep-alive>
  </el-scrollbar>
  
</template>

<script lang="ts" setup>
import { reactive, defineAsyncComponent, ref, onUnmounted } from 'vue'
import { clueManagerModel } from '@/api/clue'
import HeaderBox from '@/components/HeaderBox.vue'
import { formatNumber, formatNumber1 } from '@/utils/utils'
import TabsBox from '@/components/TabsBox.vue'
import { useUserStore } from '@/store/user'
import { ClueStaticInfo, ClueStatusEnum} from '@/type/clue'
import { useTaskStore } from '@/store/taskInfo'
import to from 'await-to-js';
import { onBeforeRouteLeave } from 'vue-router'

const ToBeSendCluesQueue = defineAsyncComponent({loader: () => import('../components/ToBeSendCluesQueue.vue')})
const ToBeDistributeCluesQueue = defineAsyncComponent({loader: () => import('../components/ToBeDistributeCluesQueue.vue')})
const BeDistributeCluesQueue = defineAsyncComponent({loader: () => import('../components/BeDistributeCluesQueue.vue')})
const BeRecoveredCluesQueue = defineAsyncComponent({loader: () => import('../components/BeRecoveredCluesQueue.vue')})
const BeArchivedCluesQueue = defineAsyncComponent({loader: () => import('../components/BeArchivedCluesQueue.vue')})

const useStore = useUserStore()
/** 线索统计 模块 */ 
// 线索统计
const clueStatics = reactive<ClueStaticInfo>({
  totalCount: undefined,
  toBeSendCount: undefined, toBeDistributeCount: undefined, beDistributeCount: undefined,
  beRecoveredCount: undefined, beArchivedCount: undefined,
  successCount: undefined, failedCount: undefined,
  toBeFirstFollowUpCount: undefined
})
const loading = ref(false)
// 获取线索统计数据
const refreshClueStatics = async () => {
  loading.value = true
  const [_, data] = await to(clueManagerModel.getClueStaticInfo({groupId: useStore.groupId})) as [any, ClueStaticInfo]
  Object.assign(clueStatics, data)
  loading.value = false
}

/** 线索tab */
const tabList = ['待下发', '待分配', '已分配', '已回收', '已归档']
const activeTab = ref(tabList[0])
const init = async () => {
  const taskStore = useTaskStore()
  await taskStore.getCallTeamListOptions(undefined, true)
  await taskStore.getCallGroupSeatListOptions(true)
  await taskStore.getAllScriptListOptions(true)
  await taskStore.getEnableFormSetting(true)
  refreshClueStatics()
}
// 执行区
init()
onUnmounted(() => {
  const taskStore = useTaskStore()
  taskStore.$reset()
})
onBeforeRouteLeave(() => {
  const taskStore = useTaskStore()
  taskStore.$reset()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.info-data-content {
  font-size: 24px;
  line-height: 24px;
}
</style>