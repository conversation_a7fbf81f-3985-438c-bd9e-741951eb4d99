<template>
  <el-dialog
    v-model="dialogVisible"
    class="dialog-form"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">调整关联坐席</div>
    </template>
    <div class="tw-h-[400px] tw-my-[8px]">
      <TransferBox
        v-model:leftList="seatDisableList"
        :width="3"
        name="account"
        leftTitle="不允许签入坐席"
        v-model:rightList="seatEnableList"
        rightTitle="允许签入坐席"
      ></TransferBox>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" :icon="CloseBold">取消</el-button>
        <el-button type="primary" :loading="loading" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,watch, reactive, computed,} from 'vue'
import { Select, CloseBold, } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'
import { ElMessage, } from 'element-plus'
import { SeatMember } from '@/type/seat'
import { clueManagerModel, } from '@/api/clue'
import { useTaskStore } from '@/store/taskInfo'
import TransferBox from '@/components/TransferBox.vue'
import { useGlobalStore } from '@/store/globalInfo'
import { storeToRefs } from 'pinia'
import to from 'await-to-js';
import { onBeforeRouteLeave } from 'vue-router'

const emits = defineEmits(['update:visible', 'confirm'])
const globalStore = useGlobalStore()
const  {loading} = storeToRefs(globalStore)
const taskStore = useTaskStore()
const props = defineProps<{
  visible: boolean,
  taskData: {
    id?: number, activeCallSeatIds?: number[]
  }
}>();
const dialogVisible = ref(props.visible)
const seatAllList = ref<SeatMember[]|null>([])
const seatEnableList = ref<SeatMember[]|null>([])
const seatDisableList = ref<SeatMember[]|null>([])
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const confirm = async () => {
  if (props.taskData.id) {
    loading.value = true
    const [err, _] = await to(clueManagerModel.changeActiveCallSeats({
      activeCallSeats: seatEnableList.value?.map(item => item.id as number) || [],
      taskId: props.taskData.id as number,
    }))
    loading.value = false
    if (!err) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
      emits('confirm')
      cancel()
    }
  }
}
watch(() => props.visible, async () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    seatAllList.value = await taskStore.getCallTeamSeatListOptions()
    seatEnableList.value = []
    seatDisableList.value = []
    seatAllList.value.map(item => {
      if (props.taskData.activeCallSeatIds?.includes(item.id!)) {
        seatEnableList.value!.push(item)
      } else {
        seatDisableList.value!.push(item)
      }
    })
  }
})

onBeforeRouteLeave(() => {
  seatEnableList.value = null
  seatDisableList.value = null
  seatAllList.value = null
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.card-box {
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
}
</style>
