<template>
  <el-drawer
    v-model="dialogVisible"
    :before-close="cancel"
    size="900px"
    :with-header="false"
  >
    <div class="tw-bg-[#f2f3f5] tw-overflow-hidden tw-h-full">
      <div class="tw-px-[16px] tw-py-[12px] tw-text-left tw-bg-white tw-justify-between tw-flex tw-items-center tw-w-full">
        <span class="form-dialog-header ">关联任务</span>
      </div>
      <div class="tw-p-[16px] tw-h-[calc(100%-48px)] tw-flex tw-flex-col">
        <div class="card-box tw-bg-white">
          <div class="tw-w-[400px] tw-flex tw-items-center">
            <el-input
              v-model="taskName"
              clearable
              placeholder="请输入任务名称"
              @keyup.enter="searchTask()"
              @blur="searchTask()"
            >
            </el-input>
            <el-button type="primary" @click="searchTask(true)" link class="tw-ml-1">
              <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
              <span>查询</span>
            </el-button>
          </div>
        </div>
      
        <el-table
          class="tw-mt-[12px] tw-flex-grow tw-flex-shrink"
          :data="taskTempList"
          ref="tableRef"
          :header-cell-style="{background:'#F7F8FA', color: '#646566'}"
          stripe
          row-key="id"
        >
          <el-table-column property="taskName" label="任务名称" align="left" fixed="left" width="240" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
          <el-table-column property="scriptStringId" label="执行话术" align="left" min-width="280" show-overflow-tooltip>
            <template #default="{ row }">
              {{ speechCraftList?.find((item: SpeechCraftInfoItem) => item.scriptStringId == row.scriptStringId)?.scriptName || '-' }}
            </template>
          </el-table-column>
          <el-table-column property="startWorkTimeList" label="拨打时段" align="left" min-width="160">
            <template #default="{ row }">
              <TagsBox v-if="row.startWorkTimeList && row.endWorkTimeList" :key="row.id" :tagsArr="concatTimeList(row.startWorkTimeList, row.endWorkTimeList)" tagsName="拨打时段" :tagsNum="2"></TagsBox>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column property="createTime" label="任务创建时间" align="center" sortable min-width="160" show-overflow-tooltip>
            <template #header="{ column }">
              <div class="tw-flex tw-items-center tw-justify-start">
                <span>{{column.label}}</span>
                <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
                  <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
                  <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
                </div>
              </div>
            </template>
            <template #default="{ row }">
              {{ dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column property="callSeatCondition" label="签入坐席" align="left" min-width="80" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.callSeatCondition || '' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="right" width="80" fixed="right">
            <template #default="{ row, $index }">
              <el-button type="primary" @click="changeSeat(row)" link class="tw-ml-1">
                调整坐席
              </el-button>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty v-if="!taskTempList || taskTempList.length < 1" description="暂无数据" />
          </template>
        </el-table>
        <PaginationBox
          :pageSize="pageSize"
          class="tw-flex-grow-0 tw-flex-shrink-0"
          :currentPage="currentPage"
          :total="total"
          @search="searchTask(true)"
          @update="updatePage"
        >
        </PaginationBox>
      </div>
    </div>
    <AssociatedSeatDialog v-model:visible="seatVisible" :taskData="currentTask" @confirm="searchTask(true)"/>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref,watch, reactive, computed, defineAsyncComponent, } from 'vue'
import { CaretTop, CaretBottom, CloseBold } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'
import { SpeechCraftInfoItem, } from '@/type/speech-craft'
import dayjs from 'dayjs'
import { RelatedTaskItem, } from '@/type/clue'
import PaginationBox from '@/components/PaginationBox.vue'
import { useTaskStore } from '@/store/taskInfo'
import { formatterEmptyData,  } from '@/utils/utils'
import AssociatedSeatDialog from './AssociatedSeatDialog.vue'
import { onBeforeRouteLeave } from 'vue-router'

const TagsBox = defineAsyncComponent({loader: () => import('@/components/TagsBox.vue')})

const emits = defineEmits(['update:visible', 'update:task'])
const userInfo  = useUserStore()
const props = defineProps<{
  visible: boolean,
  taskList: RelatedTaskItem[]
}>();
const dialogVisible = ref(props.visible)
const currentTask = reactive<{
  id?: number, activeCallSeatIds?: number[]
}>({
  id: undefined, activeCallSeatIds: []
})
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

const taskName = ref('') // 搜索任务名称
const taskList = ref<RelatedTaskItem[] | null>([]) // 关联任务全部数据
// 分页信息
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(20)
const taskTempList = computed(() => {
  total.value = taskList.value?.length || 0
  return taskList.value?.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value) || []
})
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}
// 更新关联任务列表
const searchTask = async (needUpdate:boolean = false) => {
  if (needUpdate) {
    emits('update:task')
  } else {
    taskList.value = props.taskList?.filter(item => !taskName.value.trim() || item.taskName?.includes(taskName.value))
  }
}
const concatTimeList = (staArr: string[], endArr: string[]) => {
  if (!staArr || staArr.length < 1 || staArr.length !== endArr.length ) return []
  return staArr.map((item, index) => {
    return item + '-' + endArr[index]
  })
}
// 调整坐席
const seatVisible = ref(false)
const changeSeat = (row: RelatedTaskItem) => {
  currentTask.id = row.id
  currentTask.activeCallSeatIds = row.activeCallSeatIds || []
  seatVisible.value = true
}

const taskStore = useTaskStore()
const speechCraftList = ref<SpeechCraftInfoItem[] | null>([])
watch([() => props.visible, () => props.taskList], async () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    speechCraftList.value = await taskStore.getAllScriptListOptions()
    searchTask()
  }
}, {deep: true})

onBeforeRouteLeave(() => {
  speechCraftList.value = null
  taskList.value = null
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-table {
  font-size: var(--el-font-size-base);
  :deep(.cell) {
    padding: 0 6px;
  }
  :deep(.caret-wrapper) {
    display: none;
  }
  .el-empty {
    padding: 10px 0;
  }
}
</style>
