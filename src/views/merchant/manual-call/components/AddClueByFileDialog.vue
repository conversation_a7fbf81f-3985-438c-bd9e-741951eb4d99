<template>
  <el-dialog
    v-model="dialogVisible"
    width="480px"
    align-center
    @close="cancel"
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">上传本地文件</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px] tw-py-[24px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="1px"
        ref="editRef"
      >
        <el-form-item label="" prop="uploadFiles">
          <el-upload
            v-model:file-list="editData.uploadFiles"
            class="tw-flex-grow"
            :class="editData.uploadFiles && (editData.uploadFiles[0]?.size || 0) / 1024 / 1024 > 80 ? 'exceed-dom' : ''"
            drag
            ref="uploadRef"
            :action="actionUrl"
            :headers="headerInfo"
            :limit="1"
            :show-file-list="false"
            :on-exceed="exceedFileNum"
            :http-request="uploadFileAction"
            accept=".csv,.xls,.xlsx"
            :auto-upload="false"
          >
            <template #trigger>
              <div v-if="editData.uploadFiles && editData.uploadFiles?.length > 0" class="tw-leading-[22px] tw-flex tw-flex-col tw-items-start tw-justify-center tw-h-full">
                <div class="tw-text-[14px] tw-text-[var(--primary-black-color-600)] tw-flex tw-items-center">
                  <el-icon :size="16" color="var(--primary-black-color-500)" class="tw-mr-[4px]"><SvgIcon name="file" color="inherit" /></el-icon>
                  <span>{{ editData.uploadFiles[0].name }}</span>
                  <el-icon v-if="(editData.uploadFiles[0]!.size || 0)/1024/1024<= 80" class="tw-ml-[8px]" :size="16" color="#13BF77">
                    <SvgIcon name="success" color="inherit" />
                  </el-icon>
                  <el-icon v-else class="tw-ml-[8px]" :size="16" color="#E64B17">
                    <SvgIcon name="fail" color="inherit" />
                  </el-icon>
                </div>
                <div class="info-title">文件大小：{{ translateSize(editData.uploadFiles[0].size) }}</div>
                <div class="tw-mt-[16px]">
                  <el-button link type="primary" @click="editData.uploadFiles=[]">重新上传</el-button>
                  <el-button link type="primary" @click.stop="editData.uploadFiles=[]">删除</el-button>
                </div>
              </div>
              <div v-else>
                <el-icon :size="24" color="#969799" class="upload-icon">
                  <SvgIcon name="upload2" color="inherit" />
                </el-icon>
                <p class="tw-text-[14px] tw-mt-[12px] tw-leading-[22px]">拖拽或上传文件至此</p>
                <p class="info-title">限制80M大小，支持csv、xlsx</p>
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-button class="tw-float-right" type="primary" link @click="downloadTemplate">下载导入模版</el-button>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" :icon="Close">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirmLocal">
          <el-icon :size="16"><SvgIcon name="upload"></SvgIcon></el-icon>
          <span>上传</span>
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
// type
import type { UploadRawFile, UploadUserFile, FormInstance } from 'element-plus'
// 本地方法
import { getToken, pickAttrFromObj } from '@/utils/utils'
import { exportFileByBlob } from '@/utils/export'
import { trace } from '@/utils/trace'
// 来自插件、依赖
import { ref, computed, reactive, watch, onDeactivated} from 'vue'
import { useGlobalStore } from '@/store/globalInfo'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import { ElMessage, UploadRequestOptions } from 'element-plus'
import { Close, } from '@element-plus/icons-vue'
import { clueManagerModel } from '@/api/clue'
import to from 'await-to-js';
import { onBeforeRouteLeave } from 'vue-router'

const props = defineProps<{
  visible: boolean,
}>();
const emits = defineEmits(['update:visible', 'confirm'])


const globalStore = useGlobalStore()
const  {loading} = storeToRefs(globalStore)
const userStore = useUserStore()
const dialogVisible = ref(false)

/** 1.1 本地导入 */
const uploadRef = ref()
const editData = reactive<{
  uploadFiles :UploadUserFile[] | null
}>({uploadFiles: []})
const headerInfo = { token: getToken(), }
const actionUrl = computed(() => {
  let baseURL = location.protocol === 'https:'
    ? import.meta.env.VITE_API_ORIGIN_HTTPS
    : import.meta.env.VITE_API_ORIGIN_HTTP
  return baseURL + `AiSpeech/clue/importClueByExcel?groupId=${userStore.groupId}`
})
watch(() => editData.uploadFiles, () => {
  editRef.value && editRef.value.validate()
})
const exceedFileNum = () => {
  ElMessage.warning('仅支持上传一个文件！')
}
// 批量上传接口调用和处理
const uploadFileAction = async (opt: UploadRequestOptions) => {
  loading.value = true
  const data = new FormData()
  data.append('file', opt.file)
  await trace({ page: '线索管理-文件导入', params: opt.file?.name })
  const [err, _] = await to((clueManagerModel.importCluesByExcel(data, {
    groupId: userStore.groupId
  })))
  loading.value = false
  editData.uploadFiles = []
  if (!err) {
    ElMessage({
      message: `${opt.file.name || ''}已上传，请稍后到人工外外呼-线索管理查看导入数据！`,
      type: 'success',
    })
    emits('confirm')
    cancel()
  }
}
const downloadTemplate = async () => {
  const res = await clueManagerModel.downloadTemplate() as BlobPart
  exportFileByBlob(res, '模板.xlsx', 'xls')
}
const validateFiles = (rule: any, value: any, callback: any) => {
  if (!editData.uploadFiles || editData.uploadFiles.length === 0) {
    callback(new Error('请选择需要上传的文件!'))
  } else {
    if (editData.uploadFiles.length === 1 && editData.uploadFiles[0]!.size! > 1024 * 1024 * 80) {
      callback(new Error('文件大小不得超过80M!'))
    }
    const type = editData.uploadFiles[0].name?.split('.')?.at(-1)
    if (!type || !['csv', 'xls', 'xlsx'].includes(type)) {
      callback(new Error('文件类型必须为csv、xlsx、xls!'))
    }
  }
  callback()
}
const rules = {
  uploadFiles: [
    { validator: validateFiles, trigger: ['change', 'blur'] },
  ]
}
// 点击上传
const editRef = ref<FormInstance  | null>(null)
const confirmLocal = () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      uploadRef.value?.submit()
    }
  })
}
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}


const translateSize = (size?: number) => {
  if (size) {
    if (size/1024/1024 > 1) {
      const str = (size/1024/1024).toFixed(1)
      return `${str}MB`
    } else if (size/1024 > 1) {
      const str = (size/1024).toFixed(1)
      return `${str}KB`
    } else {
      return size + `B`
    }
  } else {
    return '0KB'
  }
}
watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n) {
    editRef.value && editRef.value.clearValidate()
    loading.value = false
  }
})

const clearAll = () => {
  editRef.value = null
  uploadRef.value = null
}
onDeactivated(() => {
  clearAll()
})
onBeforeRouteLeave(() => {
  clearAll()
})

</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-upload-dragger) {
  padding: 16px;
  height: 134px;
  width: 100%;
}
.exceed-dom :deep(.el-upload-dragger){
  border-color: #E54B17;
}
</style>
