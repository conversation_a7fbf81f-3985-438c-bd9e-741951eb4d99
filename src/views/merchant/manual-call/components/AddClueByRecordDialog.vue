<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    align-center
    class="dialog-form"
    @close="cancel()"
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">通话导入</div>
    </template>
    <el-scrollbar
      v-show="stepNo===0"
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-py-[12px]"
    >
      <RecordForm ref="recordFormRef" @update:data="updateData"/>
    </el-scrollbar>
    <el-scrollbar v-if="stepNo!==0"
      :max-height="'calc(100vh - 200px)'"
      view-class="tw-flex tw-flex-col tw-p-[16px] tw-leading-[30px] tw-text-justify"
    >
      <div class="tw-font-[600]">筛选结果</div>
      <div>共有【{{ filterType === 2 ? '通话记录' : '名单管理' }}】的{{ total }}条数据满足筛选条件</div>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <template v-if="stepNo===0">
          <el-button @click="cancel()" :icon="CloseBold">取消</el-button>
          <el-button :loading="loading" type="primary" @click="filterAction" :icon="Select">筛选</el-button>
        </template>
        <template v-if="stepNo===1">
          <el-button @click="cancel(true)" :icon="CloseBold">重新筛选</el-button>
          <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">上传</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
// type
import { TaskManageItem, } from '@/type/task'
import { CallRecordFilterModal, } from '@/type/clue'
// 本地组件
import RecordForm from './auto/RecordForm.vue'
// api
import { clueManagerModel, } from '@/api/clue'
// 来自插件、依赖
import { ref, onDeactivated, reactive, watch} from 'vue'
import { useGlobalStore } from '@/store/globalInfo'
import { useTaskStore } from '@/store/taskInfo'
import { useUserStore } from '@/store/user'
import { trace } from '@/utils/trace'
import type { FormInstance, } from 'element-plus'
import { CloseBold, Select, } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { ElMessage } from 'element-plus'
import to from 'await-to-js';
import { onBeforeRouteLeave } from 'vue-router'

const props = defineProps<{
  visible: boolean,
}>();
const emits = defineEmits(['update:visible', 'confirm'])
const globalStore = useGlobalStore()
const  {loading} = storeToRefs(globalStore)
const taskStore = useTaskStore()
const userStore = useUserStore()
const dialogVisible = ref(false)
// 任务
const taskList = ref<TaskManageItem[]|null>([])

/** 1.2 名单导入 */
const filterType = ref(2) // 筛选类型，1、名单， 2、通话记录
const stepNo = ref(0) // 导入类型，===2，名单导入，时，分为2步骤
/** 变量 */
const editData = ref<CallRecordFilterModal|null>(null)
const editRef = ref<FormInstance  | null>(null)
/** 1.2.1 stepNo === 0 */
const total = ref(0)
const tempId = ref('')
/** 弹窗操作 */
/**
 * 取消
 * @param isGoBack true: 重新筛选 || false：关闭弹窗
 */
const cancel = async (isGoBack: boolean = false) => {
  if (tempId.value) {
    const params = {
      tempId: tempId.value,
      groupId: userStore.groupId,
      isIntoClueDB: false,
    }
    await clueManagerModel.addCallRecordsToClue(params)
  }
  tempId.value = ''
  if (isGoBack) {
    stepNo.value = 0
  } else {
    dialogVisible.value = false
    emits('update:visible', false)
  }
}
// 筛选操作
const filterAction = () => {
  recordFormRef.value?.validForm().then(async () => {
    loading.value = true
    await trace({ page: '线索管理-通话导入-筛选数据', params: editData.value })
    const [err, res] = await to(clueManagerModel.getBatchCallRecordsNum2(editData.value!))
    loading.value = false
    if (err) return
    total.value = res?.size || 0
    tempId.value = res?.tempId || ''
    stepNo.value = 1
  })
}
/** 1.2.2 stepNo === 1 */
// 筛选完成后，提交呼叫名单
const confirm = async () => {
  if (total.value <= 0) {
    return ElMessage({
      type: 'warning',
      message: '暂无数据添加！'
    })
  }
  loading.value = true
  const params = {
    tempId: tempId.value,
    groupId: userStore.groupId,
    isIntoClueDB: true,
  }
  await trace({ page: '线索管理-通话导入-导入', params: params })
  const [err] = await to(clueManagerModel.addCallRecordsToClue(params))
  !err && ElMessage.warning('已导入，请稍后到人工外外呼-线索管理查看导入数据！')
  loading.value = false
  tempId.value = ''
  cancel()
}

// 初始化数据，监听器等
const recordFormRef = ref()
const updateData = (data: CallRecordFilterModal) => {
  editData.value = data
}
watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n) {
    stepNo.value = 0
  }
})

const clearAll = () => {
  editRef.value = null
  taskList.value = null
}
onDeactivated(() => {
  clearAll()
})
onBeforeRouteLeave(() => {
  clearAll()
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.normal-progress {
  :deep(.el-progress-bar__inner) {
    border-radius: 2px;
    background: linear-gradient(90deg, #0167FF 0%, #019FFF 100%);
  }
}
:deep(.el-step.is-simple .el-step__title) {
  font-size: var(--el-font-size-base);
}
</style>