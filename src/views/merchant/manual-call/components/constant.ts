import { CallStatusEnum, } from '@/type/task'
import { FollowUpStatusEnum, ExamineStatusEnum, ClueCallStatusEnum } from '@/type/clue'

// 线索呼叫状态样式
export const getClueCallStatusClass = (val?: ClueCallStatusEnum) => {
  if (!val) return ''
  if (ClueCallStatusEnum['呼叫成功'].includes(val)) {
    return 'green-status'
  } else if (ClueCallStatusEnum['未接通'].includes(val)) {
    return 'blue-status'
  } else {
    return 'red-status'
  }
}

// 线索跟进状态样式
export const getFollowStatusClass = (val?: FollowUpStatusEnum) => {
  switch(val) {
    case FollowUpStatusEnum['跟进失败']: return 'red-status';
    case FollowUpStatusEnum['跟进中']: return 'blue-status';
    case FollowUpStatusEnum['跟进成功']: return 'green-status';
    default: return 'blue-status';
  }
}

// 线索审核状态样式
export const getExamineStatusClass = (val?: ExamineStatusEnum | string) => {
  switch(val) {
    case ExamineStatusEnum['审核失败']: return 'red-status';
    case ExamineStatusEnum['未审核']: return 'gray-status';
    case ExamineStatusEnum['审核成功']: return 'green-status';
    default: return 'blue-status';
  }
}
