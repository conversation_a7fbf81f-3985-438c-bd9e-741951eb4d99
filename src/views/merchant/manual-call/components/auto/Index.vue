<template>
  <el-drawer
    v-model="dialogVisible"
    destroy-on-close
    :size="720"
    :before-close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-h-[40px] tw-leading-[40px] tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ title }}</div>
    </template>
    <TabsBox
      v-model:active="activeTab"
      :tabList="tabList"
    ></TabsBox>
    <div class="card-box tw-flex-col tw-justify-between tw-flex tw-h-[calc(100%-50px)] tw-overflow-hidden">
      <template v-if="activeTab==='执行规则'">
        <!-- 自动下发：1、自动分配：2 、自动归档: 3-->
        <AutoSendForm :type="props.type" v-if="[1,2,3].includes(props.type)" />
        <!-- 自动导入 -->
        <RecordForm v-if="props.type===0" isAuto/>
      </template>
      <Logs v-if="activeTab==='执行记录'" :type="props.type"/>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, } from 'vue'
import AutoSendForm from './AutoSendForm.vue'
import RecordForm from './RecordForm.vue'
import TabsBox from '@/components/TabsBox.vue'
import Logs from './Logs.vue'

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean,
  type: number, // 0: 自动导入，1：自动下发；2：自动分配
}>();
const dialogVisible = ref(props.visible)
const title = computed(() => {
  switch (props.type) {
    case 0:
      return '自动导入详情'
    case 1:
      return '自动下发详情'
    case 2:
      return '自动分配详情'
    case 3:
      return '自动归档详情'
    default:
      break;
  }
})

const tabList = ['执行规则', '执行记录']
const activeTab = ref(tabList[0])

const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

watch(() => props.visible, async () => {
  dialogVisible.value = props.visible
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.card-box {
  padding-top: 0px;
  padding-bottom: 0px;
}
</style>
