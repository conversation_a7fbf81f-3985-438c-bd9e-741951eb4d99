<template>
  <div class="tw-w-full tw-flex tw-items-center tw-py-[12px] tw-grow-0 tw-shrink-0 tw-bg-[#fff]">
    <div class="tw-w-[400px]">
      <TimePickerBox
        v-model:start="searchForm.startTime"
        v-model:end="searchForm.endTime"
        placeholder="执行时间"
        :disabled-date="checkDisableDate"
        separator="-"
        type="datetimerange"
        :clearable="false"
        :maxRange="60*60*1000*24*7"
      />
    </div>
    <div class="tw-ml-[12px]">
      <el-button type="primary" @click="search" link>
        <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
        <span>查询</span>
      </el-button>
    </div>
  </div>
  <el-table
    :data="tableTempData"
    v-loading="loading"
    ref="tableRef"
    class="tw-grow"
    :header-cell-style="{background:'#F7F8FA', color: '#646566'}"
    stripe
    row-key="id"
  >
    <el-table-column label="执行时间" property="executeTime" align="left" width="160">
      <template #default="{ row }">
        {{ row.executeTime ? dayjs(row.executeTime).format('MM-DD HH:mm') : '-' }}
      </template>
    </el-table-column>
    <el-table-column property="executeLog" label="执行内容" min-width="120" align="left" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
    <template #empty>
      <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
    </template>
  </el-table>
  <PaginationBox
    :pageSize="pageSize"
    :currentPage="currentPage"
    :total="total"
    @search="search"
    @update="updatePage"
  >
  </PaginationBox>
</template>

<script lang="ts" setup>
// type
import { clueManagerModel } from '@/api/clue'
// 外部变量\方法
import { useTaskStore } from '@/store/taskInfo'
import { formatterEmptyData, } from '@/utils/utils'
import dayjs from "dayjs";
// 组件
import PaginationBox from '@/components/PaginationBox.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import { ElMessage } from 'element-plus'

// 其他
import { onActivated, onDeactivated, computed, ref, reactive, onUnmounted, onMounted } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'
import to from 'await-to-js'

const props = defineProps<{
  type: number,
}>();
const taskStore = useTaskStore()

/** 表格和分页 */
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(20)
const loading = ref(false)
const tableData = ref<{
  executeTime: string,
  executeLog: string
}[] | null>([])
const tableTempData = computed(() => {
  total.value = tableData.value?.length || 0
  return tableData.value?.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value) || []
})
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}

const searchForm = reactive<{
  ruleId?: number,
  startTime: string,
  endTime: string,
  executeType: number
}>({
  ruleId: taskStore.autoRuleMap[props.type],
  executeType: props.type,
  startTime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
  endTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
})
const search = async () => {
  if (taskStore.autoRuleMap[props.type]) {
    loading.value = true
    const [err, data] = await to(clueManagerModel.findAutoLogs({
      ...searchForm,
      ruleId: taskStore.autoRuleMap[props.type],
    })) as [any, {
      executeTime: string,
      executeLog: string
    }[]]
    tableData.value = data || []
    loading.value = false
  } else {
    tableData.value = []
    ElMessage.warning('无法获取当前规则ID')
  }
}

const checkDisableDate = (date: Date): boolean => {
  const day = dayjs(date)
  if (day.isAfter(dayjs())) {
    return true
  }
  // 15天以前禁用
  const diff = day.diff(dayjs(), 'day')
  return diff < -7 || diff > 0
}

const init = async () => {
  search()
}
onMounted(() => {
  init()
})
onActivated(() => init())
const clearAll = () => {
  tableData.value = null
}

onDeactivated(() => {
  clearAll()
})
onUnmounted(() => {
  clearAll()
})
onBeforeRouteLeave(() => {
  clearAll()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.phone-msg {
  display: flex;
  align-items: center;
  span {
    width: 130px;
    flex-shrink: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .el-icon {
    display: none;
  }
  &:hover .el-icon {
    display: inline-block;
  }
}
.el-table {
  font-size: 13px;
  :deep(.cell) {
    padding: 0 8px;
  }
  .el-checkbox {
    height: 20px;
  }
}
:deep(.el-table .caret-wrapper) {
  display: none;
}
.table-btn-box {
  display: flex;
  .el-button {
    width: 60px;
  }
}
</style>
