<template>
  <el-table
    ref="tableRef"
    v-loading="props.loading"
    :data="tableTempData"
    :header-cell-style="tableHeaderStyle"
    :row-key="(row: TaskCallRecordItem) => row.recordId??'' + row.callId??''"
    :row-style="getRowStyle"
    @sort-change="onTableSortChange"
  >
    <el-table-column label="号码" fixed="left" align="left" width="180">
      <template #default="{ row, $index }">
        <div class="phone-msg" :class="currentIndex===$index ? 'tw-text-[#165DFF]':''">
          <span>{{ filterPhone(row.clueUniqueId, 7, 2) + ' ' + (row.operator || '') }}</span>
          <el-tooltip content="复制" placement="right" :show-after="500">
            <el-icon :size="14" class="hover:tw-text-[var(--el-color-primary)] tw-cursor-pointer" @click="copy(row.clueUniqueId)">
              <SvgIcon name="copy" color="inherit"></SvgIcon>
            </el-icon>
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
    <el-table-column property="name" label="姓名" min-width="120" align="left" :formatter="formatterEmptyData"></el-table-column>
    <el-table-column property="comment" label="备注" min-width="120" align="left" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
    <el-table-column label="省市" align="left" min-width="160" show-overflow-tooltip>
      <template #default="{ row }">
        {{ row.province || row.city ? (row.province || '') + ' ' + (row.city || '') : '-' }}
      </template>
    </el-table-column>

    <el-table-column property="followCount" label="跟进次数" align="center" min-width="100" show-overflow-tooltip>
      <template #default="{ row }">
        {{ row.followCount }}
      </template>
    </el-table-column>
    <el-table-column property="latestFollowUpTime" label="最近跟进时间" align="center" min-width="160" :formatter="formatTime">
    </el-table-column>
    <el-table-column v-if="followUpStatus===FollowUpStatusEnum['跟进中']" property="latestCallStatus" label="最近通话状态" align="center" min-width="120">
      <template #default="{ row }">
        <span
          v-if="row?.latestCallStatus"
          class="status-box-mini tw-m-auto"
          :class="getClueCallStatusClass(row?.latestCallStatus)"
        >
          {{ findValueInEnum(row.latestCallStatus, ClueCallStatusEnum) || '-' }}
        </span>
        <span v-else>-</span>
      </template>
    </el-table-column>
    <el-table-column v-if="followUpStatus===FollowUpStatusEnum['跟进中']" property="latestCallDuration" label="最近通话时长" align="left" min-width="120">
      <template #default="{ row }">
        {{ (row.latestCallDuration ?? -1) > -1 ? formatDuration(row.latestCallDuration / 1000) : '-' }}
      </template>
    </el-table-column>
    <el-table-column v-if="followUpStatus===FollowUpStatusEnum['跟进中']" property="nextFollowUpTime" label="下次跟进时间" align="center" min-width="200" :formatter="formatTime">
      <template #default="{row}:{row:ClueItem}">
        <div class="next-follow-up-time tw-flex tw-justify-center tw-items-center">
          {{ formatTime(row, null, row.nextFollowUpTime) }}
          <div class="edit-button">
            <el-button link @click="onClickEditNextFollowUpTime(row)">
              <el-icon :size="18" color="#165DFF">
                <SvgIcon name="edit2" />
              </el-icon>
            </el-button>
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column v-if="followUpStatus===FollowUpStatusEnum['跟进中']" property="expire" label="是否过期" align="center" min-width="120" :formatter="formatterEmptyData">
      <template #default="{ row }">
        {{ row.expire ? '是' : '否' }}
      </template>
    </el-table-column>
    <el-table-column v-if="followUpStatus===FollowUpStatusEnum['跟进中']" property="latestFollowUpNote" label="跟进备注" align="left" min-width="200" :formatter="formatterEmptyData">
    </el-table-column>
    <el-table-column property="beAllocatedTime" label="分配时间" align="center" min-width="160" :formatter="formatTime">
    </el-table-column>
    <el-table-column property="autoRecoveryTime" label="自动回收时间" align="center" min-width="200" :formatter="formatTime">
    </el-table-column>

    <el-table-column v-if="followUpStatus===FollowUpStatusEnum['跟进成功']" property="examineStatus" label="审核状态" align="center" min-width="80">
      <template #default="{ row }">
        <span
          v-if="row?.examineStatus"
          class="status-box-mini tw-mx-auto"
          :class="getExamineStatusClass(row?.examineStatus)"
        >
          {{ findValueInEnum(row?.examineStatus, ExamineStatusEnum) || '-' }}
        </span>
        <span v-else>-</span>
      </template>
    </el-table-column>
    <el-table-column property="star" label="星标" fixed="right" align="center" min-width="50">
      <template #default="{ row }">
        <el-button link @click="onClickStar(row)">
          <el-icon :size="18" color="#FFAA3D">
            <SvgIcon v-show="row.star" name="star-active" color="inherit" />
            <SvgIcon v-show="!row.star" name="star-inactive" color="inherit" />
          </el-icon>
        </el-button>
      </template>
    </el-table-column>

    <el-table-column label="操作" align="right" fixed="right" min-width="100">
      <template #default="{ row }">
        <slot name="operate" :row="row"></slot>
      </template>
    </el-table-column>
    <template #empty>
      <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
    </template>
  </el-table>
  <PaginationBox
    :pageSize="pageSize"
    :currentPage="currentPage"
    :total="total"
    @search="search"
    @update="updatePage"
  >
  </PaginationBox>

  <!--下次跟进时间弹窗-->
  <NextFollowUpTimeDialog
    :visible="nextFollowUpTimeDialogVisible"
    :data="nextFollowUpTimeDialogData"
    @close="onCloseNextFollowUpTimeDialog"
    @update="onUpdateNextFollowUpTimeDialog"
  />
</template>

<script lang="ts" setup>
// type
import { TaskCallRecordItem } from '@/type/task'
import { ClueCallStatusEnum, ClueItem, ExamineStatusEnum, FollowUpStatusEnum } from '@/type/clue'
import { getClueCallStatusClass, getExamineStatusClass, } from './constant'
// 外部变量\方法
import { useGlobalStore } from '@/store/globalInfo'
import { useSeatPhoneStore } from '@/store/seat-phone'
import { useSeatInfoStore } from '@/store/seat/seat-info'
import { copyText, filterPhone, findValueInEnum, formatDuration, formatterEmptyData, formatTime } from '@/utils/utils'
// 接口
import { seatWorkbenchClueModel } from '@/api/seat'
// 组件
// 其他
import { computed, defineAsyncComponent, reactive, ref, watch } from 'vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import dayjs from 'dayjs'
import { SeatPageEnum } from '@/type/seat'
import { storeToRefs } from "pinia"

// 动态引入组件
const PaginationBox = defineAsyncComponent(() => import('@/components/PaginationBox.vue'))
const NextFollowUpTimeDialog = defineAsyncComponent(() => import('./NextFollowUpTimeDialog.vue'))

const props = defineProps<{
  followUpStatus: FollowUpStatusEnum,
  tableData: ClueItem[],
  selectClues: ClueItem[],
  loading?: boolean,
}>();
const emits = defineEmits(['search'])

const globalStore = useGlobalStore()
const seatPhoneStore = useSeatPhoneStore()
const seatInfoStore = useSeatInfoStore()
const { seatPage } = storeToRefs(seatInfoStore)

/** 表格和分页 */
const tableRef = ref()
const currentIndex = ref(-1)
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(20)
const tableOriginalData = ref<ClueItem[]>([])
const tableData = ref<ClueItem[]>([])
const tableTempData = computed(() => {
  total.value = tableData.value?.length || 0
  return tableData.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

/**
 * 按照分配时间排序的回调函数
 * 类似JS原生sort方法里的回调函数
 * @param itemA
 * @param itemB
 * @return {number}
 */
const onSortAllocateTimeCallback = (itemA: ClueItem | null | undefined, itemB: ClueItem | null | undefined): number => {
  const a: string = itemA?.beAllocatedTime ?? ''
  const b: string = itemB?.beAllocatedTime ?? ''

  // 数据空
  if (!a && b) {
    // a空，b不空：a放前面，b放后面
    return -1
  } else if (a && !b) {
    // a不空，b空：a放后面，b放前面
    return 1
  } else if (!a && !b) {
    // a和b都空：保持顺序不变
    return 0
  }

  // 日期类型 Date string
  const aValid = dayjs(a).isValid()
  const bValid = dayjs(b).isValid()
  if (!aValid && bValid) {
    // a非法日期，b正确日期：a放前面，b放后面
    return -1
  } else if (aValid && !bValid) {
    // a正确日期，b非法日期：a放后面，b放前面
    return 1
  } else if (aValid && !bValid) {
    // a和b都是非法日期：保持顺序不变
    return 0
  } else {
    // a和b都是正确日期
    return dayjs(b).isBefore(dayjs(a)) ? -1 : dayjs(b).isAfter(dayjs(a)) ? 1 : 0
  }
}
/**
 * 表格组件触发排序
 * @param data 排序配置信息
 * @return {void}
 */
const onTableSortChange = (data: { column?: any, prop?: string, order?: any }): void => {
  sortAllocateTime()
}
const sortAllocateTime = (): void => {
  tableData.value = JSON.parse(JSON.stringify(tableOriginalData.value))
  tableData.value.sort(onSortAllocateTimeCallback)
  seatPhoneStore.clueList = JSON.parse(JSON.stringify(tableData.value || []))
  // console.log('seatPhoneStore.clueList', seatPhoneStore.clueList.map((item: ClueItem) => (item?.beAllocatedTime ?? '')))
}

const search = () => {
  emits('search')
}
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}

const copy = (val: string) => {
  copyText(val || '')
}

const provinceList = ref<string[]>([])
const provinceAllMap = ref<{ [key: string]: string[] }>({})

/**
 * 获取表格行样式
 * @param row 当前行数据
 */
const getRowStyle = ({ row }: { row: ClueItem }) => {
  if (row?.id === seatPhoneStore.currentClue?.id) {
    // 当前线索高亮
    return {
      'background-color': '#e8f7ff',
    }
  } else if (row?.id === seatPhoneStore.lastClue?.id) {
    // 上条线索高亮
    return {
      'background-color': '#feffe8',
    }
  } else {
    return null
  }
}

// 下次跟进时间弹窗 是否显示
const nextFollowUpTimeDialogVisible = ref(false)
// 下次跟进时间弹窗 表单数据
const nextFollowUpTimeDialogData: ClueItem = reactive({})
/**
 * 点击编辑下次跟进时间
 * @param {ClueItem} row 线索详情
 */
const onClickEditNextFollowUpTime = (row: ClueItem) => {
  Object.assign(nextFollowUpTimeDialogData, JSON.parse(JSON.stringify(row)))
  nextFollowUpTimeDialogVisible.value = true
}
/**
 * 下次跟进时间弹窗 关闭
 */
const onCloseNextFollowUpTimeDialog = () => {
  nextFollowUpTimeDialogVisible.value = false
}
/**
 * 下次跟进时间弹窗 更新
 */
const onUpdateNextFollowUpTimeDialog = () => {
  nextFollowUpTimeDialogVisible.value = false
  search()
}

/**
 * 点击星标
 */
const onClickStar = async (row: ClueItem) => {
  try {
    // 接口成功响应后更新状态
    await seatWorkbenchClueModel.switchClueStar({
      clueId: row?.id!,
      isStar: !row?.star
    })
    row.star = !row.star
  } catch (e) {
  }
}

// 呼叫弹窗关闭后，如果回到线索页，就刷新当前线索列表
watch(() => seatPhoneStore.dialogCallingVisible, (val, oldVal) => {
  if (!val && oldVal && seatPage.value === SeatPageEnum.CLUE) {
    search()
  }
})
watch([() => props.tableData], () => {
  tableOriginalData.value = JSON.parse(JSON.stringify(props.tableData || []))
  sortAllocateTime()
  // 找到当前线索在排好序的列表的位置
  const clueIndex = tableData.value?.findIndex((item: ClueItem) => {
    return item?.id === seatPhoneStore.currentClue?.id
  })
  let pageNum = 1
  if (clueIndex > -1) {
    pageNum = Math.floor(clueIndex / pageSize.value)
    pageNum = Math.min(pageNum, Math.floor(tableData.value?.length / pageSize.value))
    pageNum = Math.max(0, pageNum)
    pageNum = pageNum + 1
  }
  updatePage(pageNum, pageSize.value)
}, {
  deep: true,
  immediate: true,
})
watch(() => seatPhoneStore.needUpdateClueList, (val) => {
  if (val) {
    seatPhoneStore.needUpdateClueList = false
    search()
  }
})
const init = async () => {
  await globalStore.getProvinceInfo()
  provinceAllMap.value = globalStore.provinceAllMap
  provinceList.value = globalStore.getProvinceList || []
}
init()
</script>

<style scoped lang="postcss">
.phone-msg {
  display: flex;
  align-items: center;
  span {
    width: 140px;
    flex-shrink: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .el-icon {
    display: none;
  }
  &:hover .el-icon {
    display: inline-block;
  }
}
.el-table {
  font-size: 13px;
  .el-checkbox {
    height: 20px;
  }
}
:deep(.el-table .caret-wrapper) {
  display: none;
}
.table-btn-box {
  display: flex;
  .el-button {
    width: 60px;
  }
}
.next-follow-up-time {
  .edit-button {
    visibility: hidden;
  }
  &:hover {
    .edit-button {
      visibility: visible;
    }
  }
}
</style>
