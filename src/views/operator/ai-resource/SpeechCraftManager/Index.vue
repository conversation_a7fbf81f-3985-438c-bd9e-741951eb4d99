<template>
  <div class="tw-flex tw-items-center tw-text-[16px] tw-font-bold tw-w-full tw-min-w-[1080px] tw-h-[56px] tw-px-[24px] tw-border-b-[1px] tw-bg-white  tw-shrink-0">话术管理</div>
  <div class="script-container">
    <div v-if="permissions.includes(routeMap['话术制作'].permissions['编辑话术'])" class="tw-flex tw-justify-end">
      <el-button type="primary" :icon="Plus" @click="add">新增话术</el-button>
    </div>
    <div class="card-box tw-my-[12px]">
      <div>
        <el-input placeholder="话术名称" v-model.trim="searchData.name" style="width: 300px" @keyup.enter="search()" @input="(val: string) => !val&&search()" clearable></el-input>
        <InputNumberBox
          v-if="searchData.status && [SpeechCraftStatusEnum['已停用'], SpeechCraftStatusEnum['生效中']].includes(searchData.status)"
          v-model:value="searchData.days"
          placeholder="查询【输入天数】未外呼的话术"
          style="width: 300px;"
          class="tw-ml-1"
          :min="0"
          append="天"
          @enter="search()"
        />
        <el-button type="primary" class="tw-ml-1" @click="search()" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>
    <div class="tw-flex tw-justify-between tw-mb-[12px] tw-items-center tw-mr-[12px]">
      <el-radio-group v-model="searchData.status" @change="search()">
        <el-radio-button v-for="item in statusMap" :label="item?.value" :key="item?.value">{{ item?.text }}</el-radio-button>
      </el-radio-group>
      <span class="info-title">
        共 <span class="tw-text-[var(--primary-blue-color)]">{{ tableData?.length || 0 }}</span> 条
      </span>
    </div>
    
    <el-scrollbar class="tw-grow" wrap-class="tw-pr-1">
      <ul class="tw-grid tw-grid-cols-4 2xl:tw-grid-cols-5 tw-gap-4 tw-gap-x-[8px]" v-loading="loading">
        <li v-for="item in tableData" class="item">
          <div class="info-box">
            <div class="tw-w-full tw-flex">
              <span class="tw-flex-grow tw-leading-[18px] tw-font-[600] tw-line-clamp-2 tw-break-all tw-text-[var(--primary-black-color-600)]">{{ item.scriptName }}</span>
              <el-icon v-if="item.smsTriggerNames && item.smsTriggerNames.length > 0 && item.status === SpeechCraftStatusEnum['生效中']" class="tw-flex-shrink-0 tw-ml-[4px]" :size="16" color="var(--primary-blue-color)">
                <SvgIcon name="trigger-sms" />
              </el-icon>
              <el-icon v-if="item.transferHuman && item.status === SpeechCraftStatusEnum['生效中']" class="tw-flex-shrink-0 tw-ml-[4px]" :size="16" color="var(--primary-blue-color)">
                <SvgIcon name="trigger-human" />
              </el-icon>
            </div>
            <div class="tw-text-[13px] tw-leading-[15px] tw-w-full tw-truncate">
              <span class="tw-text-[var(--primary-black-color-400)]">所属行业：</span>
              <span class="tw-text-[var(--primary-black-color-500)]">{{ item.primaryIndustry||'' }}</span>
              <span class="tw-text-[var(--primary-black-color-500)]">-</span>
              <span class="tw-text-[var(--primary-black-color-500)]">{{ secondaryIndustries?.find(v => v.id === item.secondaryIndustryId)?.name || '' }}</span>
            </div>
            <div class="tw-text-[13px] tw-leading-[15px] tw-w-full tw-truncate">
              <span class="tw-text-[var(--primary-black-color-400)]">交互版本：</span>
              <span class="tw-text-[var(--primary-black-color-500)]">{{ !!item.multiContentVersion ? 'v2.0' : 'v1.0' }}</span>
            </div>
            <div class="tw-text-[13px] tw-leading-[15px] tw-w-full tw-truncate">
              <span class="tw-text-[var(--primary-black-color-400)]">所属账号：</span>
              <span class="tw-text-[var(--primary-black-color-500)]">{{ item.ownerAccount || '-' }}</span>
            </div>
            <div class="tw-text-[13px] tw-leading-[15px] tw-w-full tw-truncate">
              <span class="tw-text-[var(--primary-black-color-400)]">可见账号：</span>
              <span class="tw-text-[var(--primary-black-color-500)]">{{ item.watchAccounts?.length ? item.watchAccounts.join('、') || '-' : '-' }}</span>
            </div>
            <div class="tw-text-[13px] tw-leading-[15px] tw-w-full tw-truncate">
              <span class="tw-text-[var(--primary-black-color-400)]">锁定账号：</span>
              <span class="tw-text-[var(--primary-black-color-500)]">{{ item.lockAccount || '-' }}</span>
            </div>
            <div class="tw-text-[13px] tw-leading-[15px] tw-w-full tw-truncate">
              <span class="tw-text-[var(--primary-black-color-400)]">更新时间：</span>
              <span class="tw-text-[var(--primary-black-color-500)]">{{ dayjs(item.updateTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
            </div>
            <div v-if="item.status && [SpeechCraftStatusEnum['生效中'], SpeechCraftStatusEnum['已停用']].includes(item.status)" class="tw-text-[13px] tw-leading-[15px] tw-w-full tw-truncate">
              <span class="tw-text-[var(--primary-black-color-400)]">最近外呼：</span>
              <span
                class="tw-text-[var(--primary-black-color-500)]"
                :class="{'tw-text-[var(--primary-orange-color)]': item.lastUsingDate && dayjs(item.lastUsingDate).isBefore(dayjs().subtract(30, 'day'))}"
              >
                {{ translateLastUsingDate(item.lastUsingDate) }}
              </span>
            </div>
            <div class="tw-text-[13px] tw-leading-[15px] tw-w-full tw-mb-[4px] tw-line-clamp-2">
              <span class="tw-text-[var(--primary-black-color-400)]">话术备注：</span>
              <span class="tw-text-[var(--primary-black-color-500)]">{{ item.remark || '-' }}</span>
            </div>
          </div>

          <div class="btn-box">
            <button @click="check(item)"><el-icon :size="16" color="inherit"><SvgIcon name="check"></SvgIcon></el-icon>查看</button>
            <template v-if="!!item.editPermission">
              <button
                v-if="(permissions.includes(routeMap['话术制作'].permissions['编辑话术']) && item.status && [SpeechCraftStatusEnum['编辑中'], SpeechCraftStatusEnum['已驳回']].includes(item.status))
                  || (item.status === SpeechCraftStatusEnum['已驳回'])"
                @click="edit(item)"
              >
                <el-icon :size="16" color="inherit"><SvgIcon name="edit"></SvgIcon></el-icon>
                编辑
              </button>
              <button
                v-if="permissions.includes(routeMap['话术制作'].permissions['编辑话术']) && item.status==SpeechCraftStatusEnum['生效中']"
                @click="edit(item)"
              >
                <el-icon :size="16" color="inherit"><SvgIcon name="edit"></SvgIcon></el-icon>
                编辑
              </button>
            </template>
            <button v-if="item.status && [SpeechCraftStatusEnum['生效中'], SpeechCraftStatusEnum['已停用']].includes(item.status) && permissions.includes(routeMap['话术制作'].permissions['编辑话术'])" @click="copy(item)"><el-icon :size="16" color="inherit"><SvgIcon name="copy"></SvgIcon></el-icon>复制</button>
            <template v-if="!!item.editPermission">
              <button v-if="(item.status && [SpeechCraftStatusEnum['已停用']].includes(item.status)) && permissions.includes(routeMap['话术制作'].permissions['编辑话术'])" @click="activeScript(item)"><el-icon :size="16" color="inherit"><SvgIcon name="start3"></SvgIcon></el-icon>激活</button>
              <!-- 更多：包括删除、停用、激活、绑定、设置、备注 -->
              <el-popover
                popper-style="width: 80px;min-width: 80px;font-size: 13px;"
                trigger="click"
              >
                <template #reference>
                  <button><el-icon :size="16" color="inherit"><SvgIcon name="more2"></SvgIcon></el-icon>更多</button>
                </template>
                <div class="tw-flex tw-flex-col tw-gap-y-1 tw-items-stretch">
                  <el-button
                    v-if="(item.status && [SpeechCraftStatusEnum['生效中']].includes(item.status)) && permissions.includes(routeMap['话术制作'].permissions['编辑话术'])"
                    type="primary"
                    link
                    @click="bindScript(item)"
                  >
                    <el-icon :size="14" color="inherit"><SvgIcon name="bind"></SvgIcon></el-icon>
                    <span>绑定</span>
                  </el-button>
                  <el-button
                    v-if="(item.status && [SpeechCraftStatusEnum['已停用'], SpeechCraftStatusEnum['编辑中'], SpeechCraftStatusEnum['生效中']].includes(item.status)) && permissions.includes(routeMap['话术制作'].permissions['设置话术'])"
                    type="primary"
                    link
                    @click="setScript(item)"
                  >
                    <el-icon :size="14" color="inherit"><SvgIcon name="setting1"></SvgIcon></el-icon>
                    <span>设置</span>
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    @click="editScriptRemark(item)"
                  >
                    <el-icon :size="14" color="inherit"><SvgIcon name="edit2"></SvgIcon></el-icon>
                    <span>备注</span>
                  </el-button>
                  <el-button
                    v-if="(item.status && [SpeechCraftStatusEnum['编辑中'],SpeechCraftStatusEnum['已驳回']].includes(item.status)) && permissions.includes(routeMap['话术制作'].permissions['编辑话术'])"
                    type="danger"
                    link
                    @click="del(item)"
                  >
                    <el-icon :size="14" color="inherit"><SvgIcon name="delete"></SvgIcon></el-icon>
                    <span>删除</span>
                  </el-button>
                  <el-button
                    v-if="(item.status && [SpeechCraftStatusEnum['生效中']].includes(item.status)) && permissions.includes(routeMap['话术制作'].permissions['编辑话术'])"
                    type="danger"
                    link
                    @click="stopScript(item)"
                  >
                    <el-icon :size="14" color="inherit"><SvgIcon name="stop3"></SvgIcon></el-icon>
                    <span>停用</span>
                  </el-button>
                </div>
              </el-popover>
              
            </template>
          </div>
        </li>
      </ul>
    </el-scrollbar>
  </div>
  <AddDialog
    v-model:visible="addVisible"
    @confirm="confirmAdd"
  ></AddDialog>
  <SettingDialog
    v-model:visible="setVisible"
    :data="setScriptItem"
    @confirm="search"
  ></SettingDialog>
  <BindDialog
    v-model:visible="bindVisible"
    :data="setScriptItem"
  ></BindDialog>
  <EditRemarkDialog
    v-model:visible="editScriptRemarkVisible"
    :data="setScriptItem"
    @confirm="search"
  ></EditRemarkDialog>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, } from '@element-plus/icons-vue'
import { scriptTableModel, scriptIntentionModel, scriptAudioModel, } from '@/api/speech-craft'
import { router } from "@/router";
import { useScriptStore } from '@/store/script'
import { SpeechCraftInfoItem, SpeechCraftStatusEnum, AudioParamsOrigin } from '@/type/speech-craft'
import AddDialog from './AddDialog.vue'
import Confirm from '@/components/message-box'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import dayjs from 'dayjs'
import routeMap from '@/router/asyncRoute/route-map'
import { useUserStore } from "@/store/user";
import to from 'await-to-js'
import SvgIcon from '@/components/SvgIcon.vue';
import { trace } from '@/utils/trace';
import SettingDialog from './SettingDialog.vue'
import BindDialog from './BindDialog.vue'
import EditRemarkDialog from './EditRemarkDialog.vue'
import InputNumberBox from '@/components/InputNumberBox.vue'

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
// 用户权限获取
const userStore = useUserStore();
const permissions = userStore.permissions[routeMap['话术制作'].id]

const tableData = ref<SpeechCraftInfoItem[] | null>([])
const scriptStore = useScriptStore()
const searchData = reactive<{
  name: string,
  status: SpeechCraftStatusEnum,
  days?: number,
}>({
  name: '',
  days: undefined,
  status: permissions.includes(routeMap['话术制作'].permissions['编辑话术']) ? SpeechCraftStatusEnum['编辑中'] : SpeechCraftStatusEnum['已驳回']
})

const addVisible = ref(false)
const statusMap = computed(() => {
  const res:Record<string, any>[] = []
  if (permissions.includes(routeMap['话术制作'].permissions['编辑话术'])) {
    res.push({
      color: 'color: #1890ff',
      text: '编辑中',
      value: SpeechCraftStatusEnum['编辑中'],
    })
    res.push({
      color: 'color: #fda92d',
      text: '审核中',
      value: SpeechCraftStatusEnum['审核中'],
    })
    res.push({
      color: 'color: #1890ff',
      text: '预发布',
      value: SpeechCraftStatusEnum['预发布'],
    })
    res.push( {
      color: 'color: #33cc00',
      text: '生效中',
      value: SpeechCraftStatusEnum['生效中'],
    })
  }
  
  res.push({
    color: 'color: #ff0000',
    text: '已驳回',
    value: SpeechCraftStatusEnum['已驳回'],
  })

  if (permissions.includes(routeMap['话术制作'].permissions['编辑话术'])) {
    res.push({
      color: 'color: #1890ff',
      text: '已停用',
      value: SpeechCraftStatusEnum['已停用'],
    })
  }
  return res
})
const search = async () => {
  loading.value = true
  try {
    const res = await scriptTableModel.findAllScriptInPermission({
      status: searchData.status,
      days: (searchData.status && [SpeechCraftStatusEnum['已停用'], SpeechCraftStatusEnum['生效中']].includes(searchData.status)) ? searchData.days : undefined,
    }) as SpeechCraftInfoItem[]
    tableData.value = res.filter(item => item.scriptName.includes(searchData.name))
    scriptStore.searchData = {
      name: searchData.name,
      status: searchData.status,
    }
  } catch(err) {
    ElMessage({
      message: '获取话术列表失败',
      type: 'error',
    })
  }
  loading.value = false
}

const add = () => {
  addVisible.value = true
}
const confirmAdd = async (data : SpeechCraftInfoItem) => {
  await scriptStore.setSpeechCraft(data, false)
  await scriptIntentionModel.createDefaultIntention({ id: data.id as number })
  await scriptAudioModel.saveAudioParameters(new AudioParamsOrigin(data.id as number))
  globalStore.copyCellList = { nodes: [],edges: [], isMasterCanvas: true}
  router.push({
    name: 'MainProgressManager',
  })
}

// 生效中 - 停用话术
const stopScript = (row: SpeechCraftInfoItem) => {
  Confirm({
    text: `您确定要停用话术【${row.scriptName}】吗?`,
    type: 'danger',
    title: `停用确认`,
    confirmText: '停用'
  }).then(async () => {
    loading.value = true
    await trace({ page: `话术制作${row.id}-停用话术`, params: { scriptId: row.id as number} })
    const [err] = await to(scriptTableModel.stopOneScript({ scriptId: row.id as number }))
    if (!err) {
      ElMessage.success('停用话术成功')
    }
    search()
    loading.value = false
  }).catch(() => {})
}

// 停用中 - 激活话术
const activeScript = (row: SpeechCraftInfoItem) => {
  Confirm({
    text: `您确定要激活话术【${row.scriptName}】吗?激活后话术将进入生效中状态，请知悉！`,
    type: 'warning',
    title: `激活确认`,
    confirmText: '激活'
  }).then(async () => {
    loading.value = true
    await trace({ page: `话术制作-激活成功（${row.id}）`, params: { scriptId: row.id as number} })
    const [err] = await to(scriptTableModel.activeOneScript({ scriptId: row.id as number }))
    if (!err) {
      ElMessage.success('激活成功')
    }
    search()
    loading.value = false
  }).catch(() => {})
}

const del = (row: SpeechCraftInfoItem) => {
  Confirm({
    text: `您确定要删除话术【${row.scriptName}】吗?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(async () => {
    loading.value = true
    await trace({ page: `话术制作${row.id}-删除话术`, params: { scriptId: row.id as number} })
    const [err] = await to(scriptTableModel.deleteScriptTables({ scriptId: row.id as number }))
    search()
    if (!err) {
      ElMessage.success('删除成功')
    }
    loading.value = false
  }).catch(() => {})
}
const check = async (row: SpeechCraftInfoItem) => {
  await scriptStore.setSpeechCraft(row, true)
  globalStore.copyCellList = { nodes: [],edges: [], isMasterCanvas: true}
  router.push({
    name: 'MainProgressManager',
  })
}
const copy = (row: SpeechCraftInfoItem) => {
  if (row.status && [SpeechCraftStatusEnum['生效中'], SpeechCraftStatusEnum['已停用']].includes(row.status)) {
    Confirm({
      text: `<div>您确定要以话术【${row.scriptName}】为模板<span class="tw-text-[#E54B17] tw-font-[600]">复制并新建</span>一个话术吗?</div>
      <div class="tw-mt-[12px] tw-text-[#E54B17] tw-font-[600]">复制话术后，请确保话术配置（如触发事件、触发短信等）在绑定的新账号上可用</div>`,
      type: 'warning',
      countdownTime: 3,
      title: `复制确认`,
      confirmText: '复制'
    }).then(() => copyAction(row)).catch(() => {})
  }
}
const copyAction = async (row: SpeechCraftInfoItem) => {
  loading.value = true
  try {
    const copyRow = await scriptTableModel.copyOneScript({
      scriptId: row.id as number
    }) as SpeechCraftInfoItem
    await trace({ page: `话术制作-复制生效中的话术${row.id}`, params: {
      originId: row.id,
      newId: copyRow.id,
    }})
    const res = await scriptStore.setSpeechCraft(copyRow, false)
    globalStore.copyCellList = { nodes: [],edges: [], isMasterCanvas: true}
    if (copyRow && copyRow.id && !!res) {
      searchData.status = SpeechCraftStatusEnum['编辑中']
      setTimeout(() => {
        router.push({
          name: 'MainProgressManager',
        })
      }, 200)
    }
  } catch(err) {
    ElMessage({
      message: '复制话术失败',
      type: 'error',
    })
  }
  loading.value = false
}
const editAction = async (row: SpeechCraftInfoItem) => {
  try {
    const copyRow = await scriptTableModel.updateOneScriptVersion({
      scriptId: row.id as number
    }) as SpeechCraftInfoItem
    await trace({ page: `话术制作-编辑生效中的话术${row.id}`, params: {
      originId: row.id,
      newId: copyRow.id,
    }})
    const res = await scriptStore.setSpeechCraft(copyRow, false)
    globalStore.copyCellList = { nodes: [],edges: [], isMasterCanvas: true}
    if (copyRow && copyRow.id && !!res) {
      searchData.status = SpeechCraftStatusEnum['编辑中']
      let timer: any = setTimeout(() => {
        router.push({
          name: 'MainProgressManager',
        })
        clearTimeout(timer)
        timer = null
      }, 200)
    }
  } catch(err) {
    ElMessage({
      message: '编辑生效中的话术失败',
      type: 'error',
    })
  }
}
const edit = async (row: SpeechCraftInfoItem) => {
  loading.value = true
  if (row.status === SpeechCraftStatusEnum['生效中']) {
    const [_, data] = await to(scriptTableModel.findTemplateByScript({scriptStringId: row.scriptStringId!}))
    Confirm({
      text: `<div class="tw-text-[#E54B17] tw-font-[600]">您确定要编辑生效中的话术【${row.scriptName}】吗?编辑将影响正在外呼的任务，请和相关人员确认无误后再操作！</div><div class="tw-mt-[12px] tw-text-[#E59000]">${data ? '已绑定外呼模板：' + data : ''}</div>`,
      type: 'warning',
      title: `编辑确认`,
      confirmText: '编辑',
      countdownTime: 3
    }).then(() => editAction(row)).catch(() => {})
  } else {
    const res = await scriptStore.setSpeechCraft(row, false, row?.scriptStringId)
    globalStore.copyCellList = { nodes: [],edges: [], isMasterCanvas: true}
    if (row && row.id && !!res) {
      router.push({
        name: 'MainProgressManager',
      })
    }
  }
  loading.value = false
}

// 设置开放范围
const setScriptItem = ref<SpeechCraftInfoItem | null>(null)
const setVisible = ref(false)
const setScript = (row: SpeechCraftInfoItem) => {
  setScriptItem.value = row
  setVisible.value = true
}

// 设置话术备注
const editScriptRemarkVisible = ref(false)
const editScriptRemark = (row: SpeechCraftInfoItem) => {
  setScriptItem.value = row
  editScriptRemarkVisible.value = true
}

// 绑定话术至主账号
const bindVisible = ref(false)
const bindScript = (row: SpeechCraftInfoItem) => {
  setScriptItem.value = row
  bindVisible.value = true
}

const translateLastUsingDate = (lastUsingDate: string | undefined) => {
  if (!lastUsingDate) return '-'
  const days = dayjs().diff(dayjs(lastUsingDate), 'day')
  return days > 0 ? `${dayjs(lastUsingDate).format('YYYY-MM-DD')} 已空置${days}天` : dayjs(lastUsingDate).format('YYYY-MM-DD')
}

const secondaryIndustries = ref<{name:string, id: number}[] | null>([])
const init = async () => {
  if (!globalStore.allIndustryList || globalStore.allIndustryList.length < 1) {
    await globalStore.getAllIndustryList()
  }
  secondaryIndustries.value = globalStore.allIndustryList?.map(item => item.secondaryIndustries)?.flat() || []
}
// 执行区
onMounted(() => {
  init()
  if (scriptStore.searchData.status) {
    searchData.status = scriptStore.searchData.status as SpeechCraftStatusEnum
  }
  searchData.name = scriptStore.searchData.name || ''
  search()
})

onUnmounted(() => {
  tableData.value = null
  secondaryIndustries.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.script-container {
  padding: 16px 12px 12px 16px;
  width: 100%;
  height: 100%;
  min-width: 1080px;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  :deep(.el-radio-button__inner) {
    border: 0;
  }
.item {
  font-size: 14px;
  display: flex;
  flex-direction: column;
  height: 240px;
  text-align: justify;
  padding: 0;
  background-color: white;
  border-radius: 4px;
  overflow: hidden;
  .info-box {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    gap: 4px;
    flex-grow: 1;
    padding: 12px 12px 0;
    margin: 0;
    text-align: justify;
  }
  .status-box-mini {
    width: 32px;
    font-size: 11px;
  }
  .btn-box {
    width: 100%;
    display: flex;
    flex-shrink: 0;
    justify-content: space-around;
    border-radius: 0 0 4px 4px;
    align-items: center;
    height: 32px;
    box-sizing: border-box;
    :deep(.el-popover.el-popper) {
      min-width: 80px !important;
      width: 80px !important;
    }
    button {
      border-top: 1px solid var(--primary-black-color-200);
      color: var(--primary-blue-color);
      width: 100%;
      height: 32px;
      border-right: 1px solid var(--primary-black-color-200);
      font-size: 13px;
      display: flex;
      align-items: center;
      justify-content: center;
      &:hover {
        background-color: var(--primary-blue-color);
        color: #fff;
      }
      .el-icon {
        margin-right: 4px;
      }
      &:last-child {
        border-right: 0;
      }
    }
    .el-button+.el-button {
      margin-left: 0;
    }
  }
  
}
}
:deep(.el-button+.el-button) {
  margin-left: 0 !important;
  
}
.el-button {
  font-size: 13px;
}

</style>
