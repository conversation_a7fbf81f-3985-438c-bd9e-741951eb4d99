<template>
  <el-dialog
    v-model="dialogVisible"
    width="540px"
    class="dialog-form"
    @close="cancel"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">设置话术备注</div>
    </template>
    <el-form
      :model="addData"
      :rules="rules"
      label-width="77px"
      ref="addFormRef"
    >
      <el-form-item label="话术名称：">
        <span class="info-title">{{ props.data?.scriptName || '' }}</span>
      </el-form-item>
      <el-form-item label="话术备注：" prop="remark">
        <el-input v-model="addData.remark" type="textarea" show-word-limit :maxlength="50" :rows="3" placeholder="请输入话术备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, reactive, watch, onUnmounted, } from 'vue'
import { SpeechCraftInfoItem, SpeechCraftStatusEnum } from '@/type/speech-craft'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { scriptTableModel } from '@/api/speech-craft'
import type { FormInstance, FormRules } from 'element-plus'
import to from 'await-to-js';
import { trace } from '@/utils/trace';

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  data?: SpeechCraftInfoItem | null;
}>();
const loading = ref(false)
const dialogVisible = ref(false)
const addData = reactive<{scriptId: number | undefined, remark?: string}>({
  scriptId: undefined,
  remark: '',
})
const addFormRef = ref<FormInstance | null>(null)
const rules = {
  remark: [
    { max: 50, message: '话术备注最多50个字符', trigger: 'blur' }
  ],
}

const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const confirm = () => {
  if (!addData.scriptId) return ElMessage.warning('获取话术信息异常')
  addFormRef.value && addFormRef.value.validate(async (valid) => {
    if (valid) {
      trace({ page: `话术制作-设置话术备注(${addData.scriptId})`, params: {params:addData} })
      const [err] = await to(scriptTableModel.editScriptRemark(addData))
      if (!err) {
        ElMessage.success('操作成功')
        emits('confirm')
        cancel()
      }
    }
  })
}

watch(() => props.visible, async n => {
  dialogVisible.value = n
  if (n) {
    addData.scriptId = props.data?.id
    addData.remark = props.data?.remark || ''
    addFormRef.value && addFormRef.value.clearValidate()
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
</style>
