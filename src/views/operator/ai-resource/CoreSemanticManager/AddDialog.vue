<template>
  <el-dialog
    v-model="dialogVisible"
    width="450px"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ title }}</div>
    </template>
    <el-form
      ref="addFormRef"
      :model="addData"
      :rules="rules"
      label-width="90px"
    >
      <el-form-item label="核心语义：" prop="semantic">
        <el-input
          v-model="addData.semantic"
          type="text"
          clearable
          maxlength="30"
          show-word-limit
          placeholder="请输入核心语义名称,30汉字以内"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { AiSemantics } from '@/type/core-semantic'
import type { FormInstance } from 'element-plus'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { scriptCoreSemanticModel } from '@/api/speech-craft'
import to from 'await-to-js';
import { addDataOrigin } from './constant'
import { pickAttrFromObj } from '@/utils/utils'

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean
  addData: AiSemantics
}>()
const dialogVisible = ref(props.visible)
const title = computed(() => (props.addData && props.addData.id ? '编辑核心语义' : '新增核心语义'))
const loading = ref(false)
const addData = reactive<AiSemantics>(props.addData)
const addFormRef = ref<FormInstance | null>(null)
const rules = {
  semantic: [
    { required: true, message: '请输入核心语义名称', trigger: 'blur' },
    { min: 1, max: 30, message: '核心语义名称，30汉字以内', trigger: 'blur' },
  ],
}

const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const confirm = async () => {
  addFormRef.value && addFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const params = pickAttrFromObj(addData, [
        'id', 'semantic', 'active', 'secondIndustryId'
      ])
      const [err, res] = await to(scriptCoreSemanticModel.saveSemantic(addData.id ? addData : params))  as [any, AiSemantics]
      loading.value = false
      
      if (!err) {
        ElMessage({
          message: '操作成功',
          type: 'success',
        })
        cancel()
        emits('confirm', res)
      }
    }
  })
}

watch(() => props.visible, n => {
  dialogVisible.value = n
    if (n) {
      Object.assign(addData, props.addData)
      addFormRef.value && addFormRef.value.clearValidate()
    }
})
</script>

<style lang="postcss" type="text/postcss">
.el-input-number .el-input__inner {
  text-align: left;
}
</style>
