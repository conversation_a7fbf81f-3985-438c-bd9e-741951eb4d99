<template>
  <!--模块标题-->
  <HeaderBox title="语义管理" />

  <!--模块主体-->
  <div class="semantic-container">
    <!--左侧行业-->
    <div class="aside-list-box">
      <div class="aside-title tw-mx-[12px] tw-mb-[12px]">
        行业列表
        <el-icon size="12" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="initIndustry()">
          <SvgIcon name="reset" color="inherit" />
        </el-icon>
      </div>
      <div class="tw-mx-[12px]">
        <el-input v-model.trim="searchIndustriesData.secondIndustry" clearable placeholder="请输入二级行业名称" @clear="initIndustry()" @keyup.enter.native="initIndustry()">
          <template #suffix>
            <el-icon :size="16">
              <SvgIcon name="search" />
            </el-icon>
          </template>
        </el-input>
      </div>
      <el-scrollbar wrap-class="tw-pr-[12px]" class="tw-grow tw-pl-[12px] tw-pb-2">
        <!--列表里的单项-->
        <div
          v-for="item in secondIndustries || []"
          :key="item.id"
          class="aside-normal-item"
          :class="[{'aside-active-item':item.id===activeIndustry?.id}]"
        >
          <!--内层容器-->
          <div class="aside-normal-item-inner" @click="handleIndustryChange(item)">
            <!--名称-->
            <div class="tw-flex tw-items-center">
              <span
                class="tw-truncate tw-text-[14px] tw-text-[var(--primary-black-color-600)] tw-font-[600] tw-grow"
                :class="item.id===activeIndustry?.id ? 'tw-text-[var(--primary-blue-color)]' : undefined"
              >{{ item.secondIndustry || '' }}</span>
              <span class="status-box-mini" :class="item.publishStatus ? 'green-status' : 'blue-status'">
                {{ item.publishStatus ? '已发布' : '待发布' }}
              </span>
            </div>
            <!--日期时间-->
            <div class="tw-text-[13px] tw-break-words tw-text-[var(--primary-black-color-400)]">
              一级行业：{{ primaryIndustryIdAndNameMap?.get(item.primaryIndustryId) || '-' }}
            </div>
            <!--日期时间-->
            <div class="tw-text-[13px] tw-break-words tw-text-[var(--primary-black-color-400)]">
              更新时间：{{ item.updateTime ? dayjs(item.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
            </div>
            <!--日期时间-->
            <!-- <div class="tw-text-[13px] tw-break-words tw-text-[var(--primary-black-color-400)]">
              创建时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}
            </div> -->
          </div>
        </div>
        <!--空数据提示-->
        <el-empty v-if="!secondIndustries?.length" class="tw-m-auto" />
      </el-scrollbar>

    </div>

    <!--右侧标签页-->
    <div class="module-main module-vertical">
      <TabsBox
        v-model:active="activeTab"
        :tabList="tabList"
        @update:active="onUpdateTab"
      />

      <!--核心语义-->
      <div v-if="activeTab === '核心语义' && activeIndustry?.id" v-loading="loading" class="module-main module-vertical">
        <!--顶部-->
        <div class="tw-flex tw-justify-between tw-items-center tw-grow-0 tw-border-b-[1px] tw-py-1 tw-bg-white tw-px-[12px]">
          <div class="aside-title">{{ `【${activeIndustry?.secondIndustry || ''}】核心语义` }}</div>
          <div>
            <el-button @click="onClickTestSemantic">
              语义测试
            </el-button>
            <el-button v-if="hasEditPermission && (!activeIndustry || !activeIndustry?.publishStatus)" type="primary" @click="publish()">
              发布核心语义
            </el-button>
            <el-button v-if="activeIndustry && semanticsList && semanticsList.length > 0" @click="exportXls()">
              导出核心语义
            </el-button>
          </div>

        </div>

        <!--内容-->
        <div class="module-main module-main-inner">
          <!--左侧语义列表-->
          <div class="tw-flex tw-flex-col tw-w-[240px]">
            <el-button v-if="hasEditPermission" class="tw-mb-[12px]" type="primary" :icon="Plus" @click="edit()">
              创建核心语义
            </el-button>
            <div class="tw-flex tw-items-center">
              <el-input v-model.trim="searchData.queryStr" clearable :placeholder="`${searchData.type}名称`" style="width: 250px;" @keyup.enter.native="search">
                <template #prepend>
                  <el-select v-model="searchData.type" style="width: 80px;" class="tw-ml-1">
                    <el-option v-for="item in typeList" :key="item" :label="item" :value="item" />
                  </el-select>
                </template>
              </el-input>
            </div>
            <div class="aside-list">
              <el-scrollbar class="tw-pr-[14px]" view-class="tw-min-h-full">
                <!--列表为空-->
                <div
                  v-if="!semanticsList || semanticsList.length < 1"
                  class="tw-bg-white tw-min-h-full tw-flex tw-item-center tw-justify-center"
                >
                  <el-empty description="暂无数据" />
                </div>
                <!--语义单项-->
                <div
                  v-for="item in semanticsList||[]"
                  v-else
                  :key="item.id"
                  class="aside-item"
                  :class="{'aside-item--active': item.id == currentSemantics?.id}"
                >
                  <!--语义名称-->
                  <div
                    class="tw-box-border tw-w-[100%] tw-h-[100%] tw-py-[8px] tw-pr-[32px] tw-pl-[8px] tw-line-clamp-2"
                    @click="handleActive(item.id as number)"
                  >
                    {{ item.semantic }}
                  </div>

                  <!--右上角按钮组-->
                  <div v-if="hasEditPermission" class="btn-box">
                    <!--编辑按钮-->
                    <!--重命名-->
                    <el-button type="primary" :icon="Edit" link @click="edit(item)"></el-button>
                    <!--删除按钮-->
                    <el-button type="primary" :icon="Delete" link @click="del(item)"></el-button>
                  </div>

                </div>
              </el-scrollbar>
            </div>
          </div>

          <!--右侧核心短语列表-->
          <div v-if="currentSemantics && currentSemantics.id" class="module-content" style="margin-right: 0;">
            <CorePhrase
              :corePhraseAllData="corePhraseAllData"
              :currentSemantics="currentSemantics"
              :readonly="!hasEditPermission"
              @copy="copy"
              @renovate="handleActive"
              @updateRecord="updateCorePhraseRecord"
            />
          </div>
        </div>
      </div>

      <!--语义标签-->
      <SemanticLabel v-if="activeTab === '语义标签'" :industry="activeIndustry" @test="onClickTestSemantic" />
    </div>
  </div>

  <!--创建核心语义弹窗-->
  <AddDialog
    v-model:visible="addVisible"
    :addData="addData"
    @confirm="confirmAdd"
  />

  <!--语义测试弹窗-->
  <TestSemanticDialog
    v-model:visible="testSemanticDialogVisible"
    :industry="activeIndustry"
  />
</template>

<script lang="ts" setup>
import { onUnmounted, reactive, ref, } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete, Edit, Plus } from '@element-plus/icons-vue'
import { scriptCoreSemanticModel } from '@/api/speech-craft'
import CorePhrase from './CorePhrase.vue'
import Confirm from '@/components/message-box'
import {
  AiCorePhrase,
  AiCorePhraseRecord,
  AiSemantics,
  AiSemanticsResponse,
  SecondIndustrySemanticItem
} from '@/type/core-semantic'
import AddDialog from './AddDialog.vue'
import { copyText } from '@/utils/utils'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { onBeforeRouteLeave } from 'vue-router'
import to from 'await-to-js'
import dayjs from 'dayjs'
import HeaderBox from '@/components/HeaderBox.vue'
import { generateExcelByAoa } from '@/utils/export'
import { addDataOrigin } from './constant'
import routeMap from '@/router/asyncRoute/route-map'
import { useUserStore } from '@/store/user'
import TabsBox from '@/components/TabsBox.vue'
import SemanticLabel from './SemanticLabel/Index.vue'
import TestSemanticDialog from '@/views/operator/ai-resource/CoreSemanticManager/TestSemanticDialog.vue'
import { trace } from '@/utils/trace'

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)

// 用户权限获取
const userStore = useUserStore()
const hasEditPermission = userStore.permissions[routeMap['语义管理'].id]?.includes(routeMap['语义管理'].permissions['编辑语义'])

// 标签页名称
type TabName = '核心语义' | '语义标签'
// 标签页名称合集
const tabList: TabName[] = ['核心语义', '语义标签']
// 当前选中标签页
const activeTab = ref<TabName>('核心语义')
// const activeTab = ref<TabName>('语义标签')

/**
 * 标签页组件 更新
 * @param {TabName} val 新标签页名称
 */
const onUpdateTab = (val: TabName) => {
  if (val === '核心语义') {
    search()
  }
}

const semanticsList = ref<AiSemantics[] | null>([])
const activeIndustry = ref<SecondIndustrySemanticItem | null>(null)
const corePhraseAllData = reactive<AiSemanticsResponse>({
  aiCorePhrases: [],
  deleteAICorePhrases: [],
  insertAICorePhrases: [],
  updateTime: ''
})

const currentSemantics = ref<AiSemantics | null>(null)
const addData = ref<AiSemantics>(new addDataOrigin(activeIndustry.value?.id || undefined))

const typeList = ['语义', '短语']
const searchData = reactive<{
  queryStr: string,
  type: '语义' | '短语'
}>({
  type: '语义',
  queryStr: ''
})
const addVisible = ref(false)

/**
 * 根据语义ID获取核心短语列表
 * @param id 语义ID
 */
const handleActive = async (id: number) => {
  currentSemantics.value = semanticsList.value?.find(item => item.id === id) || null

  loading.value = true
  // 如果搜索短语，则直接解析语义列表里给出的核心短语列表
  // 如果搜索语义，则还要根据语义ID获取核心短语列表
  if (searchData.type === '短语' && searchData.queryStr) {
    Object.assign(corePhraseAllData, currentSemantics.value ? JSON.parse(JSON.stringify(currentSemantics.value)) : new addDataOrigin(activeIndustry.value?.id || undefined))
  } else {
    try {
      const data = await scriptCoreSemanticModel.findCorePhraseList(id) as AiSemanticsResponse
      Object.assign(corePhraseAllData, JSON.parse(JSON.stringify(data)))
    } catch (err) {
      ElMessage({
        message: '获取核心短语列表失败',
        type: 'error',
      })
    }
  }
  loading.value = false
}

/**
 * 点击左上角语义搜索按钮
 */
const search = async () => {
  if (!activeIndustry.value || !activeIndustry.value.id) return ElMessage({
    type: 'warning', message: '请先选择所属行业'
  })
  loading.value = true
  try {
    // 如果搜索框为空，则获取全部列表
    // 如果搜索框不为空，则继续按搜索逻辑执行
    const str = searchData.queryStr.trim()
    if (str) {
      semanticsList.value = <AiSemantics[]>await scriptCoreSemanticModel.findSemanticList({
        type: searchData.type === '短语' ? 'phrase' : 'semantic',
        queryStr: str,
        secondIndustryId: activeIndustry.value?.id,
      }) || []
    } else {
      semanticsList.value = <AiSemantics[]>await scriptCoreSemanticModel.findSemanticAllList({
        secondIndustryId: activeIndustry.value?.id,
      }) || []
    }
    // 对语义进行 排序
    semanticsList.value = semanticsList.value.sort((a, b) => a.semantic.localeCompare(b.semantic))
    // 列表初始化
    if (semanticsList.value?.length) {
      const hasId = currentSemantics.value && (semanticsList.value.findIndex(item => item.id === currentSemantics.value?.id) >= 0)
      handleActive(hasId ? (currentSemantics.value?.id || semanticsList.value[0].id || 0) : (semanticsList.value[0].id || 0))
    } else {
      currentSemantics.value = null
      Object.assign(corePhraseAllData, {
        aiCorePhrases: [],
        deleteAICorePhrases: [],
        insertAICorePhrases: [],
        updateTime: ''
      })
    }
  } catch (err) {
    ElMessage({
      message: '获取语义列表失败',
      type: 'error',
    })
  }

  loading.value = false
}

const confirmAdd = (data: AiSemantics) => {
  if (data) {
    activeIndustry.value = secondIndustries.value?.find(item => item.id === data?.secondIndustryId) || null
    currentSemantics.value = data
  }
  initIndustry()
}
const del = (row: AiSemantics) => {
  Confirm({
    text: `您确定要删除【${row.semantic}】吗?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(async () => {
    try {
      const res = await scriptCoreSemanticModel.deleteSemantic({
        id: row.id as number
      })
      if (res) {
        ElMessage({
          message: '删除成功',
          type: 'success',
        })
        initIndustry()
      } else {
        ElMessage({
          message: '删除失败',
          type: 'error',
        })
      }
    } catch (err) {
      ElMessage({
        message: '删除失败',
        type: 'error',
      })
    }
  }).catch(() => {
  })
}
const edit = (row?: AiSemantics) => {
  addVisible.value = true
  if (row && row.id) {
    addData.value = row || new addDataOrigin(activeIndustry.value?.id || undefined)
  } else {
    addData.value = new addDataOrigin(activeIndustry.value?.id || undefined)
  }
}

/** 发布核心语义 */
const publish = async () => {
  Confirm({
    text: `您确定要发布【${activeIndustry.value?.secondIndustry}】的核心语义吗?`,
    type: 'warning',
    title: `发布确认`,
    confirmText: '发布'
  }).then(async () => {
    if (!activeIndustry.value?.id) return ElMessage.warning('请先选择所属行业')
    trace({
      page: '语义管理-发布核心语义',
      params: { secondIndustryId: activeIndustry.value?.id },
    })
    const [err] = await to(scriptCoreSemanticModel.publishSemantics({ secondIndustryId: activeIndustry.value?.id }))
    if (!err) {
      ElMessage.success('发布成功')
      initIndustry()
    } else {
      // @ts-ignore
      const errMsg = err?.data || ''
      ElMessage.error(JSON.stringify(errMsg))
    }
  }).catch(() => {
  })
}

const exportXls = () => {
  generateExcelByAoa(
    [['语义名称'], ...(semanticsList.value || [])?.map(item => [(item.semantic || '')])],
    `【${activeIndustry.value?.secondIndustry || ''}】核心语义-${activeIndustry.value?.updateTime ? dayjs(activeIndustry.value?.updateTime).format('YYYY-MM-DD') || '-' : '-'}.xls`
  )
}

/**
 * 复制当前生效短语
 * @param corePhraseList 核心短语列表
 */
const copy = (corePhraseList: AiCorePhrase[]) => {
  if (!corePhraseList || corePhraseList.length < 1) {
    ElMessage({
      message: '核心短语内容为空，没有进行复制',
      type: 'warning',
    })
    return
  }

  let arr = corePhraseList.map(item => item.phraseName)
  let str = arr.join(',')
  copyText(str || '')
}

/**
 * 更新核心短语增删记录
 */
const updateCorePhraseRecord = async (semanticId: number, record: AiCorePhraseRecord) => {
  // 先把底部的短语增删记录提交给接口，然后更新顶部的核心短语列表

  // 提交核心短语的增删记录
  try {
    await scriptCoreSemanticModel.deleteAndSaveCorePhraseList({
      ...record,
      secondIndustryId: activeIndustry.value?.id,
    })
    initIndustry()
    ElMessage({
      message: '核心短语保存成功',
      type: 'success',
    })
  } catch (err) {
    ElMessage({
      message: '核心短语保存失败',
      type: 'error',
    })
  }
}
// 初始化行业信息
const secondIndustries = ref<SecondIndustrySemanticItem[] | null>([])
const primaryIndustryIdAndNameMap = ref<Map<number, string> | null>(new Map([]))
const searchIndustriesData = reactive({
  secondIndustry: '',
})
const initIndustry = async () => {
  const [_, res] = await to(scriptCoreSemanticModel.findAllSecondIndustries())
  const res1 = (res || []).filter(item => !searchIndustriesData.secondIndustry || item.secondIndustry?.includes(searchIndustriesData.secondIndustry))
  // 为了实现排序，又把空的更新时间放最后
  const res2 = res1.filter(item => !!item.updateTime)
  const res3 = res1.filter(item => !item.updateTime)
  secondIndustries.value = [...res2.sort((a, b) => dayjs(a.updateTime).isBefore(dayjs(b.updateTime)) ? 1 : -1), ...res3]
  if (!secondIndustries.value || secondIndustries.value.length < 1) {
    handleIndustryChange(null)
  } else if (!activeIndustry.value || !activeIndustry.value.id || !secondIndustries.value?.find(item => item.id === activeIndustry.value?.id)) {
    secondIndustries.value[0].id && handleIndustryChange(secondIndustries.value[0])
  } else {
    // 重新更新激活行业的数据，防止状态发生变更
    activeIndustry.value = secondIndustries.value?.find(item => item.id === activeIndustry.value?.id) || null
    handleIndustryChange(activeIndustry.value || secondIndustries.value[0])
  }
  if (globalStore.getPrimaryIndustryIdAndNameMap?.size) {
    primaryIndustryIdAndNameMap.value = globalStore.getPrimaryIndustryIdAndNameMap
  } else {
    await globalStore.getAllIndustryList()
    primaryIndustryIdAndNameMap.value = globalStore.getPrimaryIndustryIdAndNameMap
  }
}
// 选中的二级行业
const handleIndustryChange = (secondaryIndustry: SecondIndustrySemanticItem | null) => {
  activeIndustry.value = secondaryIndustry || null
  search()
}
// 执行区
initIndustry()

const testSemanticDialogVisible = ref(false)

const onClickTestSemantic = () => {
  testSemanticDialogVisible.value = true
}

const clearAll = () => {
  secondIndustries.value = null
  semanticsList.value = null
  activeIndustry.value = null
}
onUnmounted(() => {
  clearAll()
})
onBeforeRouteLeave(() => {
  clearAll()
})
</script>

<style scoped lang="postcss">
.semantic-container {
  display: flex;
  width: 100%;
  height: calc(100% - 56px);
  min-width: 1080px;
  .module-main {
    padding: 16px 0;
    &.module-main-inner {
      padding: 0 12px;
    }
    &.module-vertical {
      padding-top: 0;
    }
  }
  .aside-list-box {
    padding: 0;
    width: 250px;
  }
  .aside-title {
    font-size: 16px;
    font-weight: 600;
    margin-top: 6px;
    text-align: left;
  }
  .aside-item {
    position: relative;
    /* padding: 8px 8px 8px 12px; */
    padding: 0;
    .btn-box {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      justify-content: space-around;
      visibility: hidden;
      width: 32px;
      height: 12px;
      .el-button {
        width: 16px;
        height: 16px;
        border-radius: 0;
        font-size: 14px;
        color: #aaa;
      }
    }
    &:hover {
      .btn-box {
        visibility: visible;
        .el-button {
          color: #409eff;
        }
      }
    }
  }
  .industry--active {
    background-color: #409eff;
    color: #fff;
  }
  .module-content {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
  }
  :deep(.el-collapse-item__header) {
    color: var(--primary-black-color-500);
    padding-left: 10px;
    font-weight: 600;
  }
  :deep(.el-collapse-item__content) {
    padding-bottom: 10px;
  }
}
</style>
