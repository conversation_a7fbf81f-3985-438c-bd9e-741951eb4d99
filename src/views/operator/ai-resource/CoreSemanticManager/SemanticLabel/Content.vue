<template>
  <div class="tw-flex-none tw-flex tw-justify-between tw-items-center tw-flex-0 tw-pr-[14px] tw-text-[18px] tw-font-bold">
    <span>{{ currentSemantics.semanticLabel }}</span>
    <div v-if="!readonly">
      <el-button v-if="!isEdit" type="primary" @click="isEdit= true">编辑</el-button>
      <el-button v-if="isEdit" type="primary" @click="save">保存</el-button>
      <el-button v-if="isEdit" @click="cancel">取消</el-button>
    </div>
  </div>

  <div class="tw-flex-none tw-mt-[16px] tw-mb-[8px] tw-p-[8px] tw-bg-white">
    <div v-if="!selectedSemanticList.length">
      暂无数据
    </div>
    <div v-else class="tw-flex tw-flex-wrap">
      <el-tag
        v-for="(item,index) in selectedSemanticList"
        :key="index"
        class="tw-h-auto tw-m-[.5em] tw-cursor-pointer tw-whitespace-break-spaces"
        style="height: auto; padding: 0.4em; line-height: 1.4em; text-align: left; white-space: break-spaces"
        size="large"
        :closable="isEdit"
        @close="handleClose(item)"
      >
        {{ item.semantic }}
      </el-tag>
    </div>
  </div>

  <div v-show="isEdit" class="tw-flex-auto tw-flex tw-flex-col tw-h-auto tw-mt-[8px] tw-mb-[16px] tw-p-[8px] tw-bg-white tw-text-left">
    <div class="tw-py-[4px] tw-text-[16px] tw-font-bold">可关联的语义</div>
    <div class="tw-py-[4px] tw-text-[14px] tw-font-bold">批量添加</div>
    <div>
      <SelectBox
        :selectVal="selectedMultipleList.map(item => item.id)"
        :options="availableSemanticList"
        name="semantic"
        val="id"
        placeholder="请选择语义"
        filterable
        class="tw-w-[400px]"
        :multiple="true"
        @update:select-val="onUpdateSelectVal"
      />
      <el-button link type="primary" class="tw-mx-[8px]" @click="onClickBatchAddSemantics">
        批量添加
      </el-button>
    </div>
    <div class="tw-py-[4px] tw-text-[14px] tw-font-bold">单击添加</div>
    <el-scrollbar class="tw-flex-auto tw-flex tw-flex-wrap">
      <el-tag
        v-for="(item,index) in availableSemanticList"
        :key="index"
        class="tw-h-auto tw-m-[.5em] tw-cursor-pointer tw-whitespace-break-spaces tw-select-none"
        style="height: auto; padding: 0.4em; line-height: 1.4em; text-align: left; white-space: break-spaces"
        size="large"
        @click="onClickAddLabel(item)"
      >
        <span class="tw-flex tw-justify-center tw-items-center">
          <span>
            {{ item.semantic }}
          </span>
          <el-icon :size="12">
            <SvgIcon name="add1" color="inherit" />
          </el-icon>
        </span>
      </el-tag>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { AiSemantics, SemanticsLabelItem } from '@/type/core-semantic'
import { scriptCoreSemanticModel } from '@/api/speech-craft'
import SelectBox from '@/components/SelectBox.vue'

const emits = defineEmits([
  'renovate',
])

const props = defineProps<{
  currentSemantics: SemanticsLabelItem
  relatedSemanticsList: AiSemantics[]
  allList: AiSemantics[]
  readonly?: boolean
}>()

// 核心短语是否编辑模式
const isEdit = ref(false)

/**
 * 编辑模式下点击取消按钮
 */
const cancel = () => {
  // 退出编辑模式
  isEdit.value = false
  selectedSemanticList.value = JSON.parse(JSON.stringify(props.relatedSemanticsList))
  // 通知语义管理父组件，更新核心短语列表，参数是当前语义ID
  emits('renovate', props.currentSemantics)
}
/**
 * 编辑模式下点击保存按钮
 */
const save = async () => {
  // 退出编辑模式
  isEdit.value = false
  const idList: number[] = selectedSemanticList.value.map(item => item.id!) || []
  await scriptCoreSemanticModel.saveSemanticRelation({
    semanticId: idList,
    semanticLabelId: props.currentSemantics.id!,
  })
  // 通知语义管理父组件，更新核心短语列表，参数是当前语义ID
  emits('renovate', props.currentSemantics)
}

const selectedSemanticList = ref<AiSemantics[]>([])

/**
 * 点击生效核心短语或者新增记录的叉按钮——删除核心短语
 * @param row 该短语的一些接口参数
 */
const handleClose = (row: AiSemantics) => {
  // 找到短语内容一样的那个元素并移除
  selectedSemanticList.value = selectedSemanticList.value.filter(item => (item.id !== row.id))
}

const availableSemanticList = computed<AiSemantics[]>(() => {
  // 过滤掉已经关联的语义
  const selectedSemanticIdSet = new Set(selectedSemanticList.value.map(item => item.id))
  return props.allList.filter((item) => {
    return !selectedSemanticIdSet.has(item.id)
  })
})
const selectedMultipleList = ref<AiSemantics[]>([])
const onUpdateSelectVal = (val: number[]) => {
  selectedMultipleList.value = availableSemanticList.value.filter(item => val?.includes(item.id!)) || []
}

const onClickAddLabel = (item: AiSemantics) => {
  selectedSemanticList.value.push(item)
}
const onClickBatchAddSemantics = () => {
  selectedMultipleList.value.forEach(item => {
    onClickAddLabel(item)
  })
  selectedMultipleList.value = []
}

watch(() => props.relatedSemanticsList, (val, oldVal) => {
  selectedSemanticList.value = []
  selectedSemanticList.value.push(...(JSON.parse(JSON.stringify(props.relatedSemanticsList)) || []) as AiSemantics[])
}, {
  deep: true,
})
</script>

<style scoped lang="postcss">
</style>
