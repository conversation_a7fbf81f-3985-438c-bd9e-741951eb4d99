<template>
  <el-dialog
    v-model="dialogVisible"
    class="dialog-form"
    width="450px"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ title }}</div>
    </template>
    <el-form
      ref="addFormRef"
      :model="addData"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item v-if="addData.id" label="核心短语：" prop="phraseName">
        <el-input
          v-model.trim="addData.phraseName"
          clearable
          placeholder="请输入核心短语"
          @keyup.enter.native="confirm"
        />
      </el-form-item>
      <el-form-item v-if="!addData.id" label="核心短语：">
        <div class="tw-flex tw-items-center tw-w-full">
          <!--批量导入按钮改成按回车-->
          <el-input
            v-model.trim="phraseNameStr"
            class="tw-grow"
            clearable
            placeholder="请输入核心短语"
            @keyup.enter="branchOperate"
          />
          <div class="tw-grow-0 tw-shrink-0">
            <el-button link type="primary" class="tw-ml-[6px]" @click="branchOperate">批量导入</el-button>
            <el-button link type="danger" @click="reset">清空</el-button>
          </div>
        </div>
      </el-form-item>
      <el-form-item v-if="!addData.id" label="当前数据：">
        <el-tag
          v-if="phraseDataArr?.length>0"
          v-for="item in phraseDataArr"
          :key="item.phraseName"
          class="tag-item"
          closable
          @close="handleClose(item)"
        >
          {{ item.phraseName }}
        </el-tag>
        <span v-else class="info-title">(空)</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button type="primary" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, watch, computed, } from 'vue'
import { AiCorePhrase } from '@/type/core-semantic'
import { CloseBold, Select } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { ElMessage, } from 'element-plus'

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  addData: AiCorePhrase
}>()
const dialogVisible = ref(props.visible)

const addData = reactive<AiCorePhrase>(props.addData)

const addFormRef = ref<FormInstance | null>(null)
const rules = {
  phraseName: [
    { required: true, message: '请输入核心短语', trigger: 'blur' },
  ],
}
const title = computed(() => addData.id ? '编辑短语' : '新增短语')
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const phraseNameStr = ref<string>('')
const phraseDataArr = ref<AiCorePhrase[]>([])

/**
 * 批量导入
 * 需要去重和移除空白字符（空格、tab制表符、换行符等）
 */
const branchOperate = () => {
  // 输入的字符串，移除首尾多余空白字符
  let str = phraseNameStr.value.trim()

  /* 输入为空，提醒用户输入，不做后续动作 */
  if (!str) {
    ElMessage({
      message: '请输入至少一个核心短语',
      type: 'warning',
    })
    return
  }

  /* 输入不为空，对数据检验并筛选 */
  // 按英文逗号分隔，转成数组
  let arr = str.split(',')
  // 输入的每项不能有纯空白字符，有则剔除
  arr = arr.filter(item => (item.trim()))

  // 和已导入的数组进行比较，移除重复项
  const res = [...new Set([...phraseDataArr.value.map(item => item.phraseName), ...arr])]

  // 更新已导入数组
  phraseDataArr.value = res.map(item => {
    return {
      semanticId: addData.semanticId,
      phraseName: item
    }
  })

  // 清空当前文本框输入
  phraseNameStr.value = ''
}

const reset = () => {
  phraseDataArr.value = []
}
const handleClose = (val: AiCorePhrase) => {
  phraseDataArr.value = phraseDataArr.value.filter(item => val.phraseName != item.phraseName)
}

/**
 * 增改短语弹窗的确认按钮
 */
const confirm = async () => {
  // 调整业务逻辑：
  // 将用户编辑或者新增的短语列表，追加到底部新增记录列表的后面
  // 但此时不会请求接口，只有当用户点击保存按钮（退出编辑模式）后，才会把所有增改的都发送给接口
  addFormRef.value && addFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        let params: AiCorePhrase[] = []
        if (addData.id) {
          params = [addData]
        } else {
          if (!phraseDataArr.value || phraseDataArr.value.length < 1) {
            return ElMessage({
              message: '请输入至少一个核心短语',
              type: 'warning',
            })
          } else {
            params = phraseDataArr.value
          }
        }

        // 注意：仅通知父组件（核心短语）更新列表数据，关闭弹窗，但不要立马请求接口
        // params是新增短语数组
        cancel()
        emits('confirm', params, addData.id)
      } catch (err) {
        ElMessage({
          message: '操作失败',
          type: 'error',
        })
      }
    }
  })
}

watch(() => props.visible, n => {
  dialogVisible.value = n
  if  (n) {
    phraseNameStr.value = ''
    phraseDataArr.value = []
    Object.assign(addData, props.addData)
    addFormRef.value && addFormRef.value.clearValidate()
  }
})
</script>

<style lang="postcss">
.el-input-number .el-input__inner {
  text-align: left;
}
.el-button+.el-button {
  margin-left: 6px;
}
.tag-item {
  height: auto;
  margin: 2px;
  padding: 6px;
  line-height: 14px;
  text-align: left;
  white-space: break-spaces;
}
</style>
