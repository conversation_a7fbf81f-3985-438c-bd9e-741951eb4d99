<template>
  <HeaderBox :title="titleList" />
  <div class="process-container" v-loading="loading">
    <div class="tw-flex tw-justify-between tw-items-center">
      <div class="tw-text-left tw-font-semibold tw-text-[14px] tw-flex tw-items-center">
        <el-icon class="tw-mr-1 tw-cursor-pointer" @click="goBack"><ArrowLeftBold /></el-icon>
        <span>{{ scriptName }}</span>
        <span class="status-box-mini blue-status tw-ml-0.5">
          {{ industryMapList?.find(item => item.id === scriptStore.secondIndustryId)?.name || '' }}
        </span>
        <span class="status-box-mini orange-status tw-ml-0.5">
          {{ multiContentVersion ? 'v2.0' : 'v1.0' }}
        </span>
        <el-icon v-if="!isChecked" class="tw-cursor-pointer tw-ml-0.5" :size="16" color="var(--el-color-primary)" @click="edit"><SvgIcon name="edit2"></SvgIcon></el-icon>
      </div>
      <div v-if="!isChecked" class="tw-flex tw-items-center">
        <span class="tw-text-[var(--primary-black-color-400)] tw-text-[12px]">所有语料音频内容上传完毕后方可提交发布</span>
        <el-button  class="tw-ml-1" type="primary" @click="publish" :icon="Select">
          提交审核
        </el-button>
        <el-button class="tw-ml-1" @click="exitEdit" :icon="CloseBold">
          退出编辑
        </el-button>
      </div>
    </div>
    <div class="script-container">
      <TabsBox v-model:active="active" :tabList="scriptTabs">
        <!--话术编辑时显示-->
        <div v-if="!scriptStore.isChecked" class="tw-flex tw-justify-center tw-items-center tw-ml-auto tw-mr-[8px]">
          <el-button size="small" @click="onClickCheckSemantics">
            语义检查
          </el-button>
        </div>
      </TabsBox>
      <keep-alive :max="4">
        <MainProgress v-if="active == '主动流程'"/>
        <Knowledge v-else-if="active == '知识库'"/>
        <FunctionalWords v-else-if="active == '功能话术'"/>
        <BranchManager v-else-if="active == '分支管理'"/>
        <IntentionManager v-else-if="active == '意向'"/>
        <AudioManager v-else-if="active == '音频'"/>
        <Training v-else-if="active == '话术训练'" />
        <Statistic v-else-if="active == '统计' && status && [SpeechCraftStatusEnum['生效中'], SpeechCraftStatusEnum['已停用']].includes(status)"/>
        <Setting v-else-if="active == '设置'"/>
        <Log v-else-if="active == '日志'"/>
        <el-empty v-else></el-empty>
      </keep-alive>
    </div>
  </div>
  <AddDialog
    v-model:visible="addVisible"
    :editData="editData"
    @confirm="confirmAdd"
  />
  <CheckSemanticsDialog
    v-model:visible="checkSemanticsDialogVisible"
    :secondIndustryId="scriptStore.secondIndustryId"
    :scriptId="scriptStore.id"
  />
</template>

<script lang="ts" setup>
import { computed, defineAsyncComponent, onUnmounted, ref, } from 'vue'
import { scriptTableModel, } from '@/api/speech-craft'
import { ArrowLeftBold, CloseBold, Select, } from '@element-plus/icons-vue'
import { useScriptStore } from '@/store/script'
import { ElMessage } from 'element-plus'
import { router } from '@/router'
import { SpeechCraftInfoItem, SpeechCraftStatusEnum } from '@/type/speech-craft'
import AddDialog from '@/views/operator/ai-resource/SpeechCraftManager/AddDialog.vue'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import Confirm from '@/components/message-box'
import HeaderBox from '@/components/HeaderBox.vue'
import TabsBox from '@/components/TabsBox.vue'
import { traceApi } from '@/utils/trace';

const MainProgress = defineAsyncComponent({loader: () => import('./MainProcess/Index.vue')})
const IntentionManager = defineAsyncComponent({loader: () => import('./IntentionManager/Index.vue')})
const Knowledge = defineAsyncComponent({loader: () => import('./Knowledge/Index.vue')})
const AudioManager = defineAsyncComponent({loader: () => import('./AudioManager/Index.vue')})
const BranchManager = defineAsyncComponent({loader: () => import('./Branch/Index.vue')})
const FunctionalWords = defineAsyncComponent({loader: () => import('./FunctionalWords/Index.vue')})
const Training = defineAsyncComponent({loader: () => import('@/views/operator/ai-resource/MainProgressManager/Train/Index.vue')})
const Statistic = defineAsyncComponent({loader: () => import('./Statistic/Index.vue')})
const Log = defineAsyncComponent({loader: () => import('./Log/Index.vue')})
const Setting = defineAsyncComponent({loader: () => import('./Setting/Index.vue')})
const CheckSemanticsDialog = defineAsyncComponent({loader: () => import('./CheckSemanticsDialog.vue')})
const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
// const loading = ref(false)
const scriptStore = useScriptStore()
const id = scriptStore.id
const scriptName = ref(scriptStore.name)
const isChecked = scriptStore.isChecked
const multiContentVersion = scriptStore.multiContentVersion
const status = scriptStore.status
const scriptTabs = ref<string[]>([])
if (status && [SpeechCraftStatusEnum['生效中'], SpeechCraftStatusEnum['已停用']].includes(status)) {
  scriptTabs.value = ['主动流程', '知识库', '功能话术', '分支管理', '意向', '音频', '话术训练', '统计', '设置', '日志']
} else {
  scriptTabs.value = ['主动流程', '知识库', '功能话术', '分支管理', '意向', '音频', '话术训练', '设置', '日志']
}
const active = ref(scriptTabs.value[0])
const addVisible = ref(false)
const titleList = computed(() => [{title: '话术制作',}, {title: '话术编辑'},])

const industryMapList = globalStore.getIndustryIdAndNameMapList

const editData = ref<SpeechCraftInfoItem | null>(null)
const confirmFunc = async () => {
  loading.value = true
  try {
    await scriptStore.saveGraph(false, true)
    const params = {
      scriptId: id,
      scriptName: scriptName.value,
    }
    const err = await traceApi(
      `话术编辑(${id})-提交审核`,
      params,
      scriptTableModel.publishScriptTable
    )
    if (!err) {
      ElMessage.success('发布成功')
      scriptStore.clearScriptData()
      router.push({
        name: 'SpeechCraftCheck',
      })
    }
  } catch(err) {
    ElMessage.error('发布失败')
  }
  loading.value = false
}
const publish = () => {
  Confirm({
    text: `<p>您确定要审核【${scriptName.value}】吗?</p>
    <p style="margin-top:6px;color:#E54B17;font-weight:600;">话术提交审核并通过审核后，会先进入预发布状态，等待音频全部同步完成后状态会变为生效中。</p>`,
    type: 'warning',
    title: '发布确认',
    confirmText: '我已知悉'
  }).then(() => {
    confirmFunc()
  }).catch(() => {})
}
const edit = async () => {
  const data = await scriptTableModel.findOneScriptById({id}) as SpeechCraftInfoItem
  editData.value  = data || null
  addVisible.value = true
}
const confirmAdd = async (data: SpeechCraftInfoItem) => {
  addVisible.value = false
  await scriptStore.setSpeechCraft(data, false)
  scriptName.value = data.scriptName
  scriptStore.getSemanticOptions(true)
}

// 语义检查弹窗，是否显示
const checkSemanticsDialogVisible = ref(false)
/**
 * 点击语义检查按钮
 */
const onClickCheckSemantics = () => {
  // 显示语义检查弹窗
  checkSemanticsDialogVisible.value = true
}
// 执行区

// 返回话术列表
const goBack = () => {
  router.push({ name: 'SpeechCraftManager'})
}
// 退出编辑
const exitEdit = async () => {
  if (!scriptStore.isChecked) {
    await scriptStore.unLockSpeechCraft(scriptStore.id)
  }
  goBack()
}

onUnmounted(() => {
  scriptStore.clearScriptData()
})

</script>

<style scoped lang="postcss" type="text/postcss">
.process-container {
  margin: 16px;
  min-width: 1040px;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  /* background-color: #fff; */
  display: flex;
  flex-direction: column;
  .status-box-mini {
    width: auto;
    min-width: 40px;
    padding: 0 4px;
  }
}
.script-container {
  margin-top: 12px;
  flex-grow: 1;
}
</style>
