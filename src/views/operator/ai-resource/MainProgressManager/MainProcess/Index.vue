<template>
  <div class="tw-flex master-container">
    <div class="left">
      <el-button :disabled="isChecked" type="primary" :icon="Plus" @click="addProcess()" class="tw-w-[166px]">新增流程</el-button>
      <el-scrollbar class="process-container">
        <!-- <template > -->
          <li v-if="processList && processList.length>=1" :label="processList[0]?.id" :key="(processList[0]?.id || 0) + '' + (processList[0].weight || 0)" class="process tw-list-none" :class="{'process-active': processId === processList[0].id}" @click="handleProcessChange(processList[0].id as number)">
            <div class="process-name">
              {{ processList[0]?.name }}
            </div>
            <div class="btn-box" >
              <button @click.stop="addProcess(processList[0])"><el-icon :size="16" color="inherit"><SvgIcon name="edit"></SvgIcon></el-icon></button>
              <button v-if="!isChecked" @click.stop="delProcess(processList[0])"><el-icon :size="16" color="inherit"><SvgIcon name="delete"></SvgIcon></el-icon></button>
            </div>
          </li>
          <div v-show="processList && processList.length > 1" class="tw-flex tw-flex-col" id="process-draggable-boxes">
            <li
              v-for="item in processList?.slice(1)"
              :label="item.id"
              :key="(item.id || 0) + '' + (item.weight || 0)"
              class="process tw-list-none tw-cursor-grab"
              :class="{'process-active': processId === item.id}"
              @click="handleProcessChange(item.id as number)"
            >
              <div class="process-name" >{{ item.name }}</div>
              <div v-if="!isChecked" class="btn-box" >
                <button @click.stop="addProcess(item)"><el-icon :size="16" color="inherit"><SvgIcon name="edit"></SvgIcon></el-icon></button>
                <button @click.stop="delProcess(item)"><el-icon :size="16" color="inherit"><SvgIcon name="delete"></SvgIcon></el-icon></button>
              </div>
              <div v-if="!isChecked" class="handle">
                <el-icon v-if="processId === item.id" :size="16"><SvgIcon name="drag" color="var(--el-color-primary)"></SvgIcon></el-icon>
                <el-icon v-else :size="16"><SvgIcon name="drag2" color="var(--primary-black-color-500)"></SvgIcon></el-icon>
              </div>
            </li>
          </div>
        <!-- </template> -->
      </el-scrollbar>
    </div>
    <div class="right" v-loading="loadingGraph">
      <AntvX6GraphBox
        v-if="curCanvas && curCanvas.id"
        v-model:needRefresh="needRefresh"
        :nodeList="nodeList"
        :edgeList="edgeList"
        :curCanvas="curCanvas"
        @add-normal="addNormalCorpus"
        @add-connect="addConnectionCorpus"
        @paste="pasteAction"
        @edit="editCorpus"
        @delete="deleteAction"
        @save="saveGraph"
        @update="updateGraph"
      >
        <CanvasCorpusDrawer v-model:visible="corpusDialogVisible" :corpusData="corpusData" @update:data="updateNodes"/>
      </AntvX6GraphBox>
    </div>
  </div>
  <CanvasEditDialog :readonly="isChecked" class="tw-z-[100]" v-model:visible="canvasDialogVisible" :canvasData="canvasData" @confirm="confirmCanvas"/>
</template>

<script setup lang="ts">
import { ref, reactive, onActivated, onDeactivated, onUnmounted, h } from 'vue';
import { ElMessage, } from 'element-plus'
import AntvX6GraphBox from '@/components/x6/AntvX6GraphBox.vue'
import { Plus, } from '@element-plus/icons-vue'
import CanvasCorpusDrawer from '@/components/corpus/CanvasCorpusDrawer.vue'
import CanvasEditDialog from './CanvasEditDialog.vue'
import { ScriptCorpusItem, CorpusTypeEnum, ScriptInfo, ScriptBaseInfo, corpusTypeOption, ScriptBranch, ConnectTypeEnum, CanvasCorpus, CanvasBranch, CorpusDataOrigin, EventValueItem, } from '@/type/speech-craft'
import { scriptCanvasModel, scriptCorpusModel, } from '@/api/speech-craft'
import { Node, } from '@antv/x6'
import { EdgeItem, PortItem } from '@/type/common'
import { useScriptStore } from '@/store/script'
import Confirm from '@/components/message-box'
import Sortable from "sortablejs";
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { pickAttrFromObj } from '@/utils/utils'
import { filterNodeDom, pasteCorpus, X6ColorEnum, deleteNodes } from '@/components/x6/config'
import { onBeforeRouteLeave } from 'vue-router'
import { CanvasDataOrigin, updateCanvasInfoById, updateCanvasInfoByNewCorpus, CurCanvasOrigin } from './constant'
import to from 'await-to-js'
import { trace } from '@/utils/trace';

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const scriptStore = useScriptStore()
const editId = scriptStore.id // 话术ID
const isChecked = scriptStore.isChecked // 话术进入状态：true-查看，false-编辑
const needUpdateMaster = ref(false) // 是否需要更新主流程，用于更新语料连接到后选择的主流程选项列表
const nodeList = ref<Node.Metadata[]>([]) // 画布语料列表（node）
const edgeList = ref<EdgeItem[]>([]) // 画布分支列表（edge）
const processList = ref<ScriptInfo[] | null>([]) // 主流程列表
const processId = ref<number>() // 当前选中主流程ID
const corpusDialogVisible = ref(false) // 语料编辑弹窗Visible
const canvasDialogVisible = ref(false) // 主流程编辑弹窗Visible
const curCanvas = reactive<ScriptInfo>(new CurCanvasOrigin(editId)) // 当前选中主流程信息
const corpusData = reactive<ScriptCorpusItem>(new CorpusDataOrigin(
  editId,  CorpusTypeEnum['主动流程-普通语料'], undefined, !curCanvas.headCorpusId, nodeList.value && nodeList.value.length === 0 && (!processList.value || processList.value.length === 0 || curCanvas.id == processList.value[0].id)
)) // 当前编辑语料信息


const canvasData = reactive<ScriptBaseInfo>(new CanvasDataOrigin(editId, (processList.value!.length + 1) || 1)) // 当前编辑主流程信息（修改主流程名称）
const corpusAllMap = ref<{
  [propName: string]: CanvasCorpus;
} | null>({})
// 全部当前流程分支map
const branchAllMap = ref<{
  [propName: string]: CanvasBranch;
} | null>({})

const needRefresh = ref(false)

/** 左侧主流程函数 开始 */
// 初始化页面，获取事件触发，意向分类、标签等选项，获取左侧列表等
const initScriptInfo = async () => {
  loading.value = true
  await scriptStore.initScriptOption()
  await getScriptInfo()
  loading.value = false
}
// 左侧列表拖拽初始化
const processSortable = ref()
const initDraggableTable = () => {
  const dom = document.querySelector('#process-draggable-boxes')
  if (!dom) {
    return
  }
  processSortable.value =  Sortable.create(
    dom as HTMLElement, {
    animation: 300,
    draggable: ".process",
    ghostClass: 'ghost-item',
    filter: ".ignore-item",
    preventOnFilter: true,
    onStart: async () => {
      await scriptStore.saveGraph()
    },
    onEnd: async (evt) => {
      const newIndex = (evt.newIndex as number) + 1
      const oldIndex = (evt.oldIndex as number) + 1
      const currRow = processList.value!.splice(oldIndex, 1)[0];
      processList.value!.splice(newIndex, 0, currRow);
      const list = processList.value!.map(item => item.id!) || []
      loading.value = true
      await scriptCanvasModel.saveMasterCanvasOrder({
        canvasIds: list,
        scriptId: editId,
      })
      await getScriptInfo()
      loading.value = false
    },
  }) || null
}
// 选中某个流程后执行函数
const loadingGraph = ref(false)
const handleProcessChange = async (id: number, needUpdate: boolean = true) => {
  loadingGraph.value = true
  if (processId.value && processId.value !== id && needUpdate) {
    await scriptStore.saveGraph(false, true)
  }
  // 如果点击的画布id相同，为保证画布刷新（防止在非画布页面更新语料文字内容等信息），先清空画布数据
  if (processId.value && processId.value === id) {
    nodeList.value = []
    edgeList.value = []
  }
  const data = await updateCanvasInfoById(id, editId)
  nodeList.value = data.nodeList
  edgeList.value = data.edgeList
  Object.assign(curCanvas, data.curCanvas)
  processId.value = id
  scriptStore.updateScript(nodeList.value, edgeList.value, curCanvas)
  loadingGraph.value = false
}
// 获取左侧主流程列表
const getScriptInfo = async (idc?: number) => {
  loading.value = true
  const [err, headCorpusList] = await to(scriptCanvasModel.findAllMasterCanvas({
    scriptId: editId
  }))
  processList.value = []
  if (err) return
  processList.value = headCorpusList || []
  scriptStore.masterProcessOptions = headCorpusList?.flatMap(item => !!item.headCorpusId ? [{
    id: item.id!,
    name: item.name!,
    headCorpusId: item.headCorpusId
  }] : []) || []
  if (processList.value.length > 0) {
    if (idc) {
      handleProcessChange(idc, true)
    } else {
      const inProcess = !!processId.value && !!processList.value.find(item => item.id === processId.value)
      processId.value = inProcess ? processId.value : (headCorpusList[0].id || -1) as number
      handleProcessChange(processId.value as number, inProcess)
    }
  }
  loading.value = false
}
// 新增编辑主流程
const addProcess = (item?: ScriptInfo) => {
  canvasDialogVisible.value = true
  if (item) {
    Object.assign(canvasData, item ? pickAttrFromObj(item, [
      'groupOpenScope', 'isOpenContext', 'name', 'openScopeType', 'id', 'scriptId'
    ]) : new CanvasDataOrigin(editId, (processList.value!.length + 1) || 1))
  } else {
    Object.assign(canvasData, new CanvasDataOrigin(editId, (processList.value!.length + 1) || 1))
  }
}

// 主流程编辑弹窗-确认，刷新画布，更新缓存当前主流程信息
const confirmCanvas = (canvas: ScriptInfo) => {
  if (canvas.id === curCanvas.id) {
    curCanvas.name = canvas.name
    scriptStore.currentCanvas && (scriptStore.currentCanvas.name = canvas.name)
  }
  getScriptInfo(canvas.id)
}
// 删除主流程
const delProcess = (item: ScriptInfo) => {
  if (item.id === processList.value![0].id) {
    return ElMessage.error('请勿删除主动流程的第一个流程！')
  }
  Confirm({ 
    text: `您确定要删除流程【${item.name}】吗?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(async () => {
    loading.value = true
    trace({ page: `话术编辑-删除主流程(${editId})`, params: { canvasId: item.id as number} })
    const [err] = await to(scriptCanvasModel.deleteMasterCanvas({
      canvasId: item.id as number
    }))
    if (!err) {
      ElMessage.success('操作成功')
      getScriptInfo()
    }
    loading.value = false
  }).catch(() => {})
}
/** 左侧主流程函数 结束 */

/** 画布执行函数 开始 */
// 新增画布 普通语料，获取画布当前定位，定义新增语料位置
const addNormalCorpus = (pos: {x: number, y: number}) => {
  Object.assign(corpusData, new CorpusDataOrigin(
    editId,  CorpusTypeEnum['主动流程-普通语料'], undefined, !curCanvas.headCorpusId, nodeList.value && nodeList.value.length === 0 && (!processList.value || processList.value.length === 0 || curCanvas.id == processList.value[0].id)
  ), {
    corX: pos.x, 
    corY: pos.y
  })
  corpusData.connectType = undefined
  corpusData.corpusType = CorpusTypeEnum['主动流程-普通语料']
  corpusData.isOpenContext = true
  corpusData.branchList = [{name: '统一回复'}]
  corpusDialogVisible.value = true
}
// 编辑语料
const editCorpus = (corpus: ScriptCorpusItem) => {
  Object.assign(corpusData, corpus)
  corpusDialogVisible.value = true
}
// 删除画布中语料
const deleteAction = async (corpusIds: number[]) => {
  if (corpusIds && corpusIds.length > 0) {
    await deleteNodes(corpusIds)
  }
}
// 新增连接语料
const addConnectionCorpus = (pos: {x: number, y: number}) => {
  Object.assign(corpusData, new CorpusDataOrigin(
    editId,  CorpusTypeEnum['主动流程-连接语料'],
  ), {
    corX: pos.x, 
    corY: pos.y
  })
  corpusDialogVisible.value = true
}

// 粘贴操作
const pasteAction = async (obj: {
  nodes: {
    id: number, x?: number, y?: number, canvasId: number | null, corpusType: CorpusTypeEnum
  }[],
  edges: {
    id: number, pre?: number, next?: number,
  }[]
}, posCavas: {x: number, y: number}) => {
  loading.value = true
  const { nodes, edges, headCorpusId } = await pasteCorpus(obj, curCanvas, posCavas)
  
  nodeList.value.push(...nodes)
  edgeList.value.push(...edges)
  headCorpusId && (curCanvas.headCorpusId = headCorpusId)
  scriptStore.updateScript(nodeList.value, edgeList.value, curCanvas)
  await scriptStore.saveGraph(true, false, !!headCorpusId)
  globalStore.copyCellList.nodes = []
  globalStore.copyCellList.edges = []
  needRefresh.value = true
  loading.value = false
}
// 新增、编辑画布中语料后执行函数，并更新整个画布
const updateNodes = async (node: ScriptCorpusItem) => {
  loading.value = true
  // 历史记录添加
  scriptStore.x6History?.push({
    data: node.id,
    type: corpusData.id !== node.id ? '画布-新增语料' : '画布-编辑语料',
  })
  if (node.isHead) {
    needUpdateMaster.value = true
    curCanvas.headCorpusId = node.id
  }
  const data = updateCanvasInfoByNewCorpus(node, nodeList.value, edgeList.value, curCanvas)
  nodeList.value = data.nodeList || []
  edgeList.value = data.edgeList || []
  scriptStore.updateScript(nodeList.value, edgeList.value, curCanvas)
  await scriptStore.saveGraph(true, false, needUpdateMaster.value)
  loading.value = false
  needUpdateMaster.value = false
  if (node.id === corpusData.id) {
    needRefresh.value = true
  }
}
// 保存画布
const saveGraph = async (nodes: Node.Metadata[], edges: EdgeItem[], currentCanvas: ScriptInfo ) => {
  loading.value = true
  const { id, name, scriptId, headCorpusId } = currentCanvas
  const params: ScriptInfo = {
    id,
    scriptId,
    headCorpusId,
    canvasCorpusDataMap: {},
    canvasBranchDataMap: {},
  }
  nodes.map(item => {
    const {  x, y, data, } = item
    params.canvasCorpusDataMap[data.corpusId] = {
      corpusId: data.corpusId,
      corX: x as number,
      corY: y as number,
      name: data.name,
      content: data.content,
      branches: data.branches,
      aiIntentionType: data.aiIntentionType,
      corpusType: data.corpusType,
      connectCorpusId: data.connectCorpusId || null,
      connectType: data.connectType,
      listenInOrTakeOver: data.listenInOrTakeOver,
      smsTriggerName: data.smsTriggerName,
      eventTriggerValueIds: data.eventTriggerValueIds || null,
    }
  })
  edges.map(item => {
    const {  source, target, } = item
    const id = item.id?.split('--')[1] as string
    params.canvasBranchDataMap[id] = {
      branchId: parseInt(id),
      // @ts-ignore
      preCorpusId: parseInt(source.cell.split('--')[1]),
      // @ts-ignore
      nextCorpusId: parseInt(target.cell.split('--')[1]),
      name: '',
      branchContent: ''
    }
  })
  const [err, data1] = await to(scriptCanvasModel.saveOneMasterCanvas(params)) as [Error | null, ScriptInfo | null]
  if (data1 && data1.id) {
    ElMessage({
      message: `流程【${name}】画布保存成功`,
      type: 'success',
    })
    const data = await scriptCanvasModel.findOneMasterCanvas({
      canvasId: data1.id
    }) as ScriptInfo
    Object.assign(curCanvas, data)
    curCanvas.canvasBranchDataMap = data.canvasBranchDataMap
    curCanvas.canvasCorpusDataMap = data.canvasCorpusDataMap
  } else {
    ElMessage.error(`流程【${name}】画布保存失败`)
  }
  loading.value = false
}
// 更新画布节点和分支，用于画布组件暴露方法，数据更新
const updateGraph = (nodes: Node.Metadata[], edges: EdgeItem[]) => {
  nodeList.value = nodes
  edgeList.value = edges
}
/** 画布执行函数 结束 */

onActivated(() => {
  initScriptInfo()
  !isChecked && initDraggableTable()
});
const clearAll = () => {
  processSortable.value?.destroy()
  processSortable.value = null
  corpusAllMap.value = null
  branchAllMap.value = null
  // @ts-ignore
  nodeList.value = null
  // @ts-ignore
  edgeList.value = null
  processList.value = null
  Object.assign(curCanvas, new CurCanvasOrigin(editId))
}
onDeactivated(() => {
  scriptStore.saveGraph(false, true)
})
onBeforeRouteLeave(() => {
  clearAll()
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.master-container {
  .left {
    width: 192px;
    overflow: hidden;
    height: calc(100vh - 180px);
    padding: 16px 0;
    position: relative;
    background-color: #fff;
    border-right: 2px solid #e5e7eb;
    .process-container {
      display: flex;
      height: calc(100vh - 275px);
      width: 190px;
      margin-top: 16px;
      align-items: center;
      flex-direction: column;
      overflow-y: auto;
      .process {
        margin: 0 12px 8px;
        color: var(--primary-black-color-400);
        border: 1px solid var(--primary-black-color-300);
        border-radius: 4px;
        position: relative;
        box-sizing: border-box;
        flex-shrink: 0;
        flex-grow: 0;
        width: 166px;
        height: 60px;
        line-height: 20px;
        font-size: 14px;
        background-color: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .process-name {
          width: 124px;
          flex-shrink: 0;
          flex-grow: 0;
          text-align: left;
          word-break: break-all;
          text-overflow: ellipsis;
          display: -webkit-box;
          line-clamp: 2;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          margin: 8px 0 8px 8px;
          line-height: 22px;
        }
        .handle {
          visibility: hidden;
          position: absolute;
          cursor: pointer;
          left: -15px;
          top: 50%;
          transform: translateY(-50%);
        }
        .btn-box {
          visibility: hidden;
          height: 100%;
          display: flex;
          flex-direction: column;
          flex-shrink: 0;
          flex-grow: 0;
          justify-content: space-around;
          border-radius: 0 4px 4px 0;
          align-items: center;
          width: 20px;
          box-sizing: border-box;
          background-color: var(--primary-black-color-200);
          color: #fff;
          button {
            width: 20px;
            font-size: 16px;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            &:hover {
              background-color: var(--primary-blue-color);
            }
            .el-icon {
              margin-right: 2px;
            }
            &:last-child {
              border-right: 0;
            }
          }
          .el-button+.el-button {
            margin-left: 0;
          }
        }
        &:hover{
          background-color: #F5F7FA;
          color: var(--primary-black-color-500);
          .btn-box{
            visibility: visible;
            color: #fff;
          }
          .handle {
            visibility: visible;
          }
        } 
      }
      
      .process-active {
        background-color: #fff;
        font-weight: 600;
        border: 1px solid var(--primary-blue-color);
        color: var(--primary-blue-color);
        &:hover {
          background-color: #fff;
          border: 1px solid var(--primary-blue-color);
          color: var(--primary-blue-color);
        }
        .handle {
          visibility: visible;
        }
        .btn-box {
          visibility: visible;
          .el-button {
            color: var(--primary-blue-color);
          }
        }
      }
      .ghost-item {
        border-style: dashed;
        border-width: 2px;
        opacity: 0.4;
      }
    }
  }
  .right {
    position: relative;
    width: calc(100% - 190px);
    height: calc(100vh - 180px);
    .btn-box {
      position: absolute;
      left: 20px;
      top: 10px;
      display: flex;
      flex-direction: column;
      border: 0;
      .el-button:nth-child(n+2) {
        margin-left: 0;
        margin-top: 10px;
      }
    }
  }
}
</style>