<template>
  <el-dialog
    v-model="dialogVisible"
    width="480px"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ title }}</div>
    </template>
    <el-form
      :model="addData"
      :rules="rules"
      label-width="80px"
      ref="editRef"
      @submit.native.prevent
    >
      <el-form-item label="分组名称：" prop="groupName">
        <el-input
          v-model="addData.groupName"
          clearable
          placeholder="请输入分组名称（20字以内）"
          maxlength="20"
          ref="inputRef"
          @keyup.enter="confirm"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, reactive, watch, nextTick } from 'vue'
import { KnowledgeGroupItem } from '@/type/speech-craft'
import { ElMessage, } from 'element-plus'
import { scriptCorpusModel } from '@/api/speech-craft'
import type { FormInstance, FormRules } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { useScriptStore } from '@/store/script'
import to from 'await-to-js'
import { pickAttrFromObj } from '@/utils/utils'
import { trace } from '@/utils/trace'

const emits = defineEmits(['update:visible', 'confirm'])
const loading = ref(false)
const props = defineProps<{
  visible: boolean;
  data: KnowledgeGroupItem | null
}>();

const scriptStore = useScriptStore()
const scriptId = scriptStore.id

class KnowledgeGroupOrigin implements KnowledgeGroupItem {
  id = undefined
  groupName = ''
  scriptId = scriptId
  priority = undefined
}

const addData = reactive<KnowledgeGroupItem>(new KnowledgeGroupOrigin())
const dialogVisible = ref(props.visible)
const editRef = ref<FormInstance  | null>(null)
const cancel = () => {
  dialogVisible.value = false
  loading.value = false
  emits('update:visible', false)
}
const rules = {
  groupName: [
    { required: true, message: '请输入分组名称', trigger: 'blur' },
  ],
}
const title = computed(() => {
  return addData.id ? '编辑分组' : '新增分组'
})
const confirm = async () => {  
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const params = pickAttrFromObj(addData, ['id', 'scriptId', 'groupName', 'priority'])
      trace({ page: `话术编辑-${addData.id ? '编辑' : '新增'}知识库分组(${scriptId})`, params })
      const [err, res] = await to(scriptCorpusModel.saveKnowledgeGroup(params))
      loading.value = false
      if (!err) {
        ElMessage.success('操作成功')
        emits('confirm', res?.id)
        cancel()
      }
    }
  })
}
const inputRef = ref<null | HTMLElement>(null)
nextTick(() => inputRef.value && inputRef.value.focus())

watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n) {
    Object.assign(addData, props.data || new KnowledgeGroupOrigin())
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
.el-form {
  color: var(--primary-black-color-600);
  .el-form-item {
    margin-bottom: 14px;
    width: 100%;
    padding: 0 12px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>