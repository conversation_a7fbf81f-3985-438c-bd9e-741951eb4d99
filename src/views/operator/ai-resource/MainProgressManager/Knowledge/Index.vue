<template>
  <div class="knowledge-container">
      <!--左半部分，列表-->
    <div class="aside-list-box">
      <div class="tw-pb-[8px] tw-border-b-[1px]">
        <el-button
          :disabled="isChecked"
          class="tw-w-[calc(100%-24px)] tw-mx-[12px] tw-box-border"
          type="primary"
          :icon="Plus"
          @click="editLeftItemAction()"
        >
          新增分组
        </el-button>
      </div>
      <!--符合搜索条件的列表-->
      <el-scrollbar class="tw-grow" wrap-class="tw-pr-[14px]" view-class="left-draggable-boxes">
        <!--列表里的单项-->
        <div
          v-for="item in leftList"
          :key="item.id"
          class="aside-normal-item"
          :class="[{'aside-active-item':item.id===currentItem?.id}]"
        >
          <div v-if="!isChecked" class="handle">
            <el-icon v-if="item.id===currentItem?.id" :size="16"><SvgIcon name="drag" color="var(--el-color-primary)"></SvgIcon></el-icon>
            <el-icon v-else :size="16"><SvgIcon name="drag2" color="var(--primary-black-color-500)"></SvgIcon></el-icon>
          </div>
          <!--内层容器-->
          <div class="aside-normal-item-inner" @click="clickLeftItem(item)">
            <!--名称-->
            <div
              class="tw-text-justify tw-break-words tw-truncate tw-text-[14px] tw-text-[var(--primary-black-color-600)] tw-font-[700]"
              :class="item.id===currentItem?.id ? 'tw-text-[var(--primary-blue-color)]' : undefined"
            >
              {{ item.groupName || '' }}
            </div>
            <!--日期时间-->
            <div class="tw-text-[13px] tw-break-words tw-text-[var(--primary-black-color-400)]">
              创建时间：{{ dayjs(item.createTime).format('MM-DD HH:mm') }}
            </div>
          </div>
          <!--按钮-->
          <div v-if="!isChecked" class="aside-item-btn-box">
            <el-tooltip content="编辑" placement="right" :show-after="500">
              <span class="tw-cursor-pointer" @click.stop="editLeftItemAction(item)"><el-icon :size="16" color="#fff"><SvgIcon name="edit" color="#fff"></SvgIcon></el-icon></span>
            </el-tooltip>
            <el-tooltip content="删除" placement="right" :show-after="500">
              <span class="tw-cursor-pointer" @click.stop="delLeftItem(item)"><el-icon :size="16" color="#fff"><SvgIcon name="delete" color="#fff"></SvgIcon></el-icon></span>
            </el-tooltip>
          </div>
        </div>
        <!--空数据提示-->
        <el-empty v-if="!leftList || leftList.length < 1" class="tw-m-auto"/>
      </el-scrollbar>
    </div>
    <div class="tw-grow tw-overflow-hidden tw-relative" v-if="currentItem?.id">
      <el-tabs v-model="active">
        <el-tab-pane label="基本问答" :name="1"></el-tab-pane>
        <el-tab-pane label="深层沟通" :name="2"></el-tab-pane>
        <el-tab-pane label="知识权重" :name="3"></el-tab-pane>
      </el-tabs>
      <p class="tw-absolute tw-right-1 tw-top-1 info-title">{{ activeTips[active-1] }}</p>
      <keep-alive>
        <BasicQA v-if="active===1" :groupName="currentItem?.groupName" :groupId="currentItem?.id"/>
        <DeepProcess v-else-if="active===2" :groupName="currentItem?.groupName" :groupId="currentItem?.id"/>
        <Weight v-else :groupName="currentItem?.groupName" :groupId="currentItem?.id"/>
      </keep-alive>
    </div>
    <el-empty v-else class="tw-m-auto"/>
  </div>
  <GroupEditDialog v-model:visible="editLeftVisible" :data="editLeftItem" @confirm="searchLeftAction" />
</template>

<script lang="ts" setup>
import { ref, onActivated, defineAsyncComponent, onDeactivated } from 'vue'
import { useScriptStore } from '@/store/script'
import { KnowledgeGroupItem } from '@/type/speech-craft'
import { scriptCorpusModel } from '@/api/speech-craft'
import dayjs from 'dayjs'
import to from 'await-to-js'
import { Plus, } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import Confirm from '@/components/message-box'
import GroupEditDialog from './GroupEditDialog.vue'
import Sortable from "sortablejs";
import { trace } from '@/utils/trace'

const BasicQA = defineAsyncComponent({loader: () => import('./BasicQA/Index.vue')})
const Weight = defineAsyncComponent({loader: () => import('./Weight/Index.vue')})
const DeepProcess = defineAsyncComponent({loader: () => import('./DeepProcess/Index.vue')})

const scriptStore = useScriptStore()
const scriptId = scriptStore.id
const isChecked = scriptStore.isChecked

/** 左侧知识库分组 */
// loading信息
const loadingLeft = ref(false)
// 当前选中左侧元素
const currentItem = ref<KnowledgeGroupItem | null>(null)
// 左侧列表
const leftList = ref<KnowledgeGroupItem[] | null>([])

/** 知识库分组搜索 */
const searchLeftAction = async (id?: number) => {
  loadingLeft.value = true
  const [_, res] = await to(scriptCorpusModel.findKnowledgeGroupList({scriptId})) as [any, KnowledgeGroupItem[]]
  leftList.value = (res || []).sort((a,b) => (a.priority || 0) - (b.priority || 0));
  scriptStore.knowledgeGroupOptions = leftList.value?.map(item => ({name: item.groupName, id: item.id!})) || []
  const cur = id ? leftList.value?.find(item => item.id === id) : leftList.value?.find(item => item.id === currentItem.value?.id)
  if (cur) {
    clickLeftItem(cur)
  } else {
    leftList.value?.length > 0 ? clickLeftItem(leftList.value[0]) : (currentItem.value = null)
  }
  loadingLeft.value = false
}

/** 点击左侧知识库分组 */
const clickLeftItem = (item: KnowledgeGroupItem) => {
  currentItem.value = item
}

// 新增左侧
const editLeftVisible = ref(false)
const editLeftItem = ref<KnowledgeGroupItem | null>(null)
const editLeftItemAction = (item?: KnowledgeGroupItem) => {
  editLeftItem.value = item || null
  editLeftVisible.value = true
}
// 删除知识库分组
const delLeftItem = async (row?: KnowledgeGroupItem) => {
  if (!row || !row.id) return ElMessage.warning('获取分组失败')
  if (leftList.value?.length === 1) return ElMessage.warning('至少保留一个知识库分组')
  Confirm({
    text: `<p>确定要删除知识库分组【${row.groupName}】嘛？</p>
    <p style="margin-top:6px;color:#E54B17;font-weight:600;">删除后，涉及该分组的开放范围均会将该分组剔除，请确认无误后操作！<p>
    `,
    type: 'danger',
    title: `删除知识库分组`,
    confirmText: '删除'
  }).then(async () => {
    loadingLeft.value = true
    await trace({ page: `话术编辑-删除知识库分组(${scriptId})`, params: {id: row?.id!} })
    const [err] = await to(scriptCorpusModel.deleteKnowledgeGroup({id: row?.id!}))
    !err && ElMessage.success('删除成功')
    searchLeftAction()
  }).catch(() => {}).finally(() => {
    loadingLeft.value = false
  })
}

const active = ref(1)
const activeTips = [
  '基本问答：适用于“一问一答”式的知识库',
  '深层沟通：适用于“多轮对话”式的知识库',
  '知识权重：对“基本问答”和“深层沟通”进行优先级排序',
]

/** 初始化知识库分组排序 */
const sortableDom = ref()
const initDraggableTable = () => {
  const dom = document.querySelector('.left-draggable-boxes')
  if (!dom) {
    return
  }
  if (sortableDom.value) {
    sortableDom.value.destroy()
  }
  sortableDom.value =  Sortable.create(
    dom as HTMLElement, {
    animation: 300,
    draggable: ".aside-normal-item",
    ghostClass: 'ghost-item',
    preventOnFilter: true,
    onEnd: async (evt) => {
      const newIndex = (evt.newIndex as number)
      const oldIndex = (evt.oldIndex as number)
      const currRow = leftList.value!.splice(oldIndex, 1)[0];
      leftList.value!.splice(newIndex, 0, currRow);
      const list = leftList.value!.map(item => item.id!) || []
      loadingLeft.value = true
      trace({ page: `话术编辑-知识库分组排序(${scriptId})`, params: list })
      
      await to(scriptCorpusModel.saveKnowledgeGroupOrders(list))
      await searchLeftAction()
      loadingLeft.value = false
    },
  }) || null
}
// 执行区
onActivated(async () => {
  await scriptStore.getIntentionTagOptions(true)
  await scriptStore.getEventOptions(true)
  searchLeftAction()
  !isChecked && initDraggableTable()
});
onDeactivated(() => {
  sortableDom.value?.destroy()
  sortableDom.value = null
  leftList.value = null
})

</script>

<style scoped lang="postcss" type="text/postcss">
.knowledge-container {
  width: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  background-color: #fff;
  height: calc(100vh - 170px);
  display: flex;
  .aside-list-box {
    width: 200px;
    border-right: 1px solid #e5e7eb;
    .aside-normal-item {
      width: 175px;
      margin-left: 14px;
      .handle {
        visibility: hidden;
        position: absolute;
        cursor: pointer;
        left: -15px;
        top: 50%;
        transform: translateY(-50%);
      }
      &:hover{
        .handle {
          visibility: visible;
        }
      } 
    }
    .ghost-item {
      border-style: dashed;
      border-width: 2px;
      opacity: 0.4;
    }
  }
  .el-tabs :deep(.el-tabs__item){
    padding: 10px auto 0;
    height: 40px;
    line-height: 40px;
    width: 100px;
  }
  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
    border: none;
    background-color: #e5e7eb;
  }
  :deep(.el-tabs__nav-prev), :deep(.el-tabs__nav-next) {
    line-height: 60px;
    font-size: 16px;
  }
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
}
.script-container {
  margin: 10px;
  border: 2px solid #e5e7eb;
  background-color: #f2f2f2;
  :deep(.el-tabs__content) {
    height: 600px;
  }
}

</style>