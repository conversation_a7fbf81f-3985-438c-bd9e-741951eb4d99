<template>
  <el-dialog
    v-model="dialogVisible"
    width="640px"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">批量{{ nameObj[props.type] || '操作' }}</div>
    </template>
    <el-form
      :model="addData"
      :rules="rules"
      label-width="75px"
      ref="editRef"
      @submit.native.prevent
    >
      <div class="info-title tw-text-left tw-mt-[14px] tw-mb-[6px] tw-ml-[16px]">
        已选择
        <span class="tw-text-[var(--primary-blue-color)]">【{{addData.corpusLit?.length || 0}}】</span>
        条基本问答
      </div>
      <el-form-item label="选择分组：" prop="groupId">
        <el-select v-model="addData.groupId" clearable placeholder="请选择" class="tw-w-full">
          <el-option v-for="item in groupList" :key="item.id" :label="item.groupName" :value="item.id"/>
        </el-select>
      </el-form-item>
      <!-- 仅复制的时候需要重命名 -->
      <el-form-item v-if="props.type === 'copy'" :label-width="1" prop="corpusLit">
        <div class="tw-flex tw-justify-between">
          <span class="tw-w-[75px] tw-text-right tw-text-[13px]">重新命名：</span>
          <div>
            <el-button type="primary" link @click="addCorpusGroupName">+分组名称</el-button>
          </div>
        </div>
        <el-table
          :data="addData.corpusLit"
          style="width: 100%"
          class="tw-grow"
          :header-cell-style="tableHeaderStyle"
          ref="tableRef"
          stripe
        >
          <el-table-column property="name" align="left" label="当前名称" min-width="200"></el-table-column>
          <el-table-column property="name" align="left" label="复制后名称" min-width="200">
            <template #default="{ row }">
              <el-input
                v-model="row.newName"
                placeholder="请输入"
                clearable
                maxlength="20"
                class="tw-w-full"
              ></el-input>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">{{ nameObj[props.type] || '确定' }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, } from 'vue'
import { ScriptCorpusItem, KnowledgeGroupItem } from '@/type/speech-craft'
import { ElMessage, } from 'element-plus'
import { scriptCorpusModel } from '@/api/speech-craft'
import type { FormInstance, FormRules } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { useScriptStore } from '@/store/script'
import to from 'await-to-js'
import { tableHeaderStyle } from '@/assets/js/constant'

const emits = defineEmits(['update:visible', 'confirm'])
const loading = ref(false)
const props = defineProps<{
  visible: boolean;
  type: 'move' | 'copy';
  data: {
    id?: number,
    name?: string,
  }[] | null;
}>();

const nameObj = {
  move: '移动',
  copy: '复制',
}

const scriptStore = useScriptStore()
const scriptId = scriptStore.id

const addData = reactive<{
  groupId?: number;
  corpusLit: {
    id: number,
    name: string,
    newName?: string
  }[];
  scriptId: number;
}>({
  groupId: undefined,
  corpusLit: [],
  scriptId: scriptId,
})
const groupList = ref<KnowledgeGroupItem[]>([])
const dialogVisible = ref(props.visible)
const editRef = ref<FormInstance | null>(null)
const cancel = () => {
  dialogVisible.value = false
  loading.value = false
  emits('update:visible', false)
}
const rules = {
  groupId: [
    { required: true, message: '请选择分组', trigger: 'change' },
  ],
  corpusLit: [
    { required: true, message: '请填写重新命名', trigger: 'blur' },
    { validator: (rule: any, value: any, callback: any) => {
      const reg = /[$#_?&\/]/
      const errMsg: string[] = []
      value.forEach((item: {
        id: number,
        name: string,
        newName?: string
      }) => {
        if (!item.newName) {
          errMsg.push(`【${item.name}】复制后的名称不能为空`)
        } else if (item.newName.length > 20 || item.newName.length < 2) {
          errMsg.push(`【${item.name}】复制后的名称长度必须在2-20个字符`)
        } else if (reg.test(item.newName)) {
          errMsg.push(`【${item.name}】复制后的名称不可出现$#_?&/等特殊字符`)
        } else if (item.newName === item.name) {
          errMsg.push(`【${item.name}】复制后的名称不能和原名称相同`)
        }
      })
      if (errMsg.length) {
        return callback(new Error(errMsg[0]))
      }
      return callback()
    }, trigger: 'blur'},
  ],
}

const confirm = async () => {  
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const [err,] = await to(props.type === 'copy'
      ? scriptCorpusModel.copyKnowledgeBaseQAList({
        gid: addData.groupId!,
        pairs: Object.fromEntries(addData.corpusLit.map((item) => [item.id, item.newName!])),
      }) : scriptCorpusModel.moveKnowledgeBaseQAList({
        gid: addData.groupId!,
        ids: addData.corpusLit.map(item => item.id!),
      }))
      loading.value = false
      if (!err) {
        ElMessage.success('操作成功')
        emits('confirm')
        cancel()
      }
    }
  })
}

// 在所有语料重命名的名称后加分组名称
const addCorpusGroupName = () => {
  if (!addData.groupId) {
    return ElMessage.warning('请先选择分组')
  }
  const groupName = groupList.value?.find(item => item.id === addData.groupId)?.groupName || ''
  if (!groupName) return
  addData.corpusLit?.map((item, index) => {
    addData.corpusLit[index].newName = addData.corpusLit[index].newName + '-' + groupName
  })
}

const init = async () => {
  const [err, res] = await to(scriptCorpusModel.findKnowledgeGroupList({scriptId: scriptId}))
  groupList.value = res || []
}

watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n) {
    addData.corpusLit = props.data?.map(item => ({ id: item.id!, name: item.name!, newName: item.name })) || []
    addData.groupId = undefined
    init()
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
.el-form {
  color: var(--primary-black-color-600);
  .el-form-item {
    margin-bottom: 14px;
    width: 100%;
    padding: 0 12px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>