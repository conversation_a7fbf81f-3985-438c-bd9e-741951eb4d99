<template>
  <el-dialog
    v-model="dialogVisible"
    class="dialog-form"
    width="600px"
    @close="cancel"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">导入基本问答</div>
    </template>
    <el-form
      :loading="loading"
      :model="editData"
      :rules="rules"
      :disabled="isChecked"
      label-width="90px"
      ref="editRef"
    >
      <el-form-item label="选择话术：" prop="sourceScriptId">
        <el-select
          v-model="editData.sourceScriptId"
          filterable
          class="tw-grow"
          placeholder="请选择话术"
          @change="handleScriptIdChange"
        >
          <el-option
            v-for="item in scriptOptions"
            :label="item.scriptName"
            :key="item.id"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="!!editData.sourceScriptId" label="知识库分组：" prop="sourceGroupId">
        <el-select
          v-model="editData.sourceGroupId"
          filterable
          class="tw-grow"
          placeholder="请选择知识库分组"
          @change="handleGroupIdChange"
        >
          <el-option
            v-for="item in groupOptions"
            :label="item.groupName"
            :key="item.id"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="!!editData.sourceGroupId" label="基本问答：" prop="corpusIds">
        <SelectBox 
          v-model:selectVal="editData.corpusIds"
          :options="basewQAOption||[]"
          name="name"
          val="id"
          placeholder="请选择基本问答"
          class="tw-grow"
          suffix="suffix"
          canCopy canSelectAll filterable
          copyName="ruleIds"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer" v-if="!isChecked">
        <el-button @click="dialogVisible=false">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, onUnmounted,} from 'vue'
import { SpeechCraftStatusEnum, SpeechCraftInfoItem, KnowledgeGroupItem, QueryTypeEnum } from '@/type/speech-craft'
import { ElMessage, } from 'element-plus'
import type { FormInstance, } from 'element-plus'
import { scriptTableModel, scriptCorpusModel } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import to from 'await-to-js';
import { findValueInEnum } from '@/utils/utils'
import { trace } from '@/utils/trace'

const scriptStore = useScriptStore()
const editId = scriptStore.id
const isChecked = scriptStore.isChecked

const loading = ref(false)
const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  groupId: number,
}>();
const dialogVisible = ref(props.visible)
const editData = reactive<{
  sourceScriptId?: number,
  sourceGroupId?: number,
  corpusIds: number[], // 分组ID：语料ID的list
}>({
  sourceScriptId: undefined, // 导入问答所属话术id
  sourceGroupId: undefined, // 当前话术分组id
  corpusIds: [], // 分组ID：语料ID的list
})
const scriptOptions = ref<SpeechCraftInfoItem[] | null>([])
const groupOptions = ref<KnowledgeGroupItem[]>([])
const basewQAOption = ref<{
  id: number,
  name: string,
  suffix: string,
}[] | null>(null)
const handleScriptIdChange = async () => {
  if (!editData.sourceScriptId) return
  const [_, data] = await to(scriptCorpusModel.findKnowledgeGroupList({
    scriptId: editData.sourceScriptId
  }))
  groupOptions.value = data || []
  editData.sourceGroupId = data?.length ? data[0]?.id || undefined : undefined
  editData.corpusIds = []
  handleGroupIdChange()
}
const handleGroupIdChange = async () => {
  if (!editData.sourceGroupId) return
  loading.value = true
  const [_, data] = await to(scriptCorpusModel.findBaseQAListByGroupId({
    groupId: editData.sourceGroupId
  }))
  basewQAOption.value = (data || []).map(item => ({
    id: item.id!,
    name: item.name!,
    suffix: findValueInEnum(item.queryType, QueryTypeEnum) || ''
  }))
  loading.value = false
}
const rules = {
  sourceScriptId: [
    { required: true, message: '请选择话术', trigger: 'change' },
  ],
  sourceGroupId: [
    { required: true, message: '请选择知识库分组', trigger: 'change' },
  ],
  corpusIds: [
    { required: true, message: '请选择基本问答', trigger: 'change' },
  ]
}

const editRef = ref<FormInstance>()
const confirm = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const params = {
        scriptId: editId,
        targetGid: props.groupId,
        baseQACorpusPairs: {
          [editData.sourceGroupId!]: editData.corpusIds,
        }
      }
      await trace({ page: `话术编辑-导入问答(${editId})`, params: params })
      const [err, data] = await to(scriptCorpusModel.loadBaseQAFromOtherScript(params))
      loading.value = false
      if (!err) {
        // 根据后端返回成功的数量判断展示成功失败
        if (data?.length === editData.corpusIds?.length) {
          ElMessage.success('操作成功')
        } else {
          ElMessage.error(`操作完成，成功：${data?.length || 0}个，失败：${editData.corpusIds?.length - (data?.length || 0)}个`)
        }
        dialogVisible.value = false
        emits('confirm')
      }
    }
  })
}
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  dialogVisible.value = false
  emits('update:visible', false)
}


const init = async () => {
  loading.value = true
  const [_, data] = await to(scriptTableModel.findScriptListByStatusAndIndustry({
    status: SpeechCraftStatusEnum['生效中'],
    secondaryIndustryId: scriptStore.secondIndustryId,
  }))
  scriptOptions.value = data || []
  loading.value = false
}

onUnmounted(() => {
  scriptOptions.value = null
})

watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    init()
    editData.corpusIds = []
    editData.sourceGroupId = undefined
    Object.assign(editData, {
      sourceScriptId: undefined,
      rules: [],
    })
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.requirement-dialog {
  .el-input-number .el-input__inner {
    text-align: left;
  }
  .text {
    font-size: 14px;
    line-height: 24px;
    margin-right: 10px;
    text-align: left;
  }
  .el-form {
    color: var(--primary-black-color-600);
    width: 100%;
    padding: 0 12px;
    .el-form-item {
      margin-bottom: 14px;
      &:first-child {
        margin-top: 14px;
      }
    }
    :deep(.el-form-item__label) {
      padding-right: 0;
    }
    :deep(.el-form-item__content) {
      font-size: var(--el-font-size-base);
    }
  }
}

</style>