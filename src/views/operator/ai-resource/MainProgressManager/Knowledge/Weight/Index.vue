<template>
  <div class="weight-container MiniClass" :class="{'weightClass': isFullScream,}">
    <div class="tw-flex tw-justify-between tw-grow-0">
      <h5 class="tw-text-[18px] tw-font-semibold tw-leading-[32px]">知识权重</h5>
      <div class="tw-flex">
        <el-button v-if="!isChecked && !isEdit" class="tw-mx-1" type="primary" @click="openEdit">编辑</el-button>
        <div class="tw-mx-1 tw-flex tw-items-center" @click="isFullScream = !isFullScream">
          <el-icon v-if="!isFullScream" :size="30" ><FullScreen /></el-icon>
          <el-icon v-else :size="30"><Close /></el-icon>
        </div>
        <el-button v-if="!isChecked && isEdit" type="primary" @click="save">保存</el-button>
        <el-button v-if="!isChecked && isEdit" @click="closeEdit">取消</el-button>
      </div>
    </div>
    <el-scrollbar class="tw-grow tw-mt-1" wrap-class="tw-px-1">
      <ul
        v-show="tableData && tableData?.length>0"
        id="weight-draggable-boxes"
        class="tw-mt-[10px] tw-grid tw-grid-cols-7 2xl:tw-grid-cols-10 tw-gap-4"
      >
        <li v-for="item in tableData" class="item" :class="{'moved-item': item.isMoved}" :key="item.id">
          <div class="tw-grow-0 tw-text-[12px] tw-shrink-0 tw-leading-[15px] tw-w-[20px] tw-text-center tw-text-[var(--primary-black-color-400)] tw-border-[var(--primary-black-color-300)] tw-border-r-[1px] ">
            {{ item.weight || '' }}
          </div>
          <div class="tw-line-clamp-2 tw-text-left tw-leading-[16px] tw-shrink tw-grow tw-pl-[8px] tw-text-[var(--primary-black-color-600)]">
            {{ item.name }}
          </div>
        </li>
      </ul>
      <el-empty v-show="!tableData || tableData.length===0" />
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { ScriptCorpusItem, } from '@/type/speech-craft'
import { scriptCorpusModel } from '@/api/speech-craft'
import { ref, onActivated, onDeactivated, watch, toRaw, nextTick } from 'vue'
import to from 'await-to-js'
import { ElMessage, } from 'element-plus'
import Sortable from "sortablejs";
import { useScriptStore } from '@/store/script'
import { FullScreen, Close, } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import Confirm from '@/components/message-box'
import { trace } from '@/utils/trace'

const props = defineProps<{
  groupName?: string;
  groupId?: number;
}>();

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const scriptStore = useScriptStore()
const scriptId = scriptStore.id as number
const isChecked = scriptStore.isChecked
const isEdit = ref(false)
const sortableDom = ref()
const isFullScream = ref(false)

// 表格区
const tableData = ref<ScriptCorpusItem[] | null>([])
// 搜索区
const search = async () => {
  if (!props.groupId) return
  loading.value = true
  const [_, data] = await to(scriptCorpusModel.findAllKnowledgeCorpusByGroupId({
    groupId: props.groupId,
  }))
  tableData.value = (data || []).sort((a, b) => (a.weight || 0) - (b.weight || 0))
  loading.value = false
}

// 操作区
const save = () => {
  Confirm({ 
    text: `您确定要保存当前知识权重吗?`,
    type: 'warning',
    title: `保存确定`,
    confirmText: '保存'
  }).then(async () => {
    loading.value = true
    const params = {
      corpusIdList: tableData.value?.map(item => item.id as number) || [],
      scriptId: scriptId,
    }
    trace({ page: `话术编辑-知识库-修改知识权重(${scriptId}-${props.groupName})`, params })
    const [err] = await to(scriptCorpusModel.saveKnowledgeCorpus(params))
    if (!err) {
      ElMessage.success('保存成功')
      closeEdit()
    }
    loading.value = false
  }).catch(() => {})
}

const initDraggableTable = () => {
  if (sortableDom.value) {
    sortableDom.value.destroy()
    sortableDom.value = null
  }
  sortableDom.value = new Sortable(
    document.querySelector('#weight-draggable-boxes') as HTMLElement, {
    animation: 300,
    draggable: "li",
    ghostClass: 'ghost-item',
    forceFallback: true,
    onEnd: async (evt) => {
      if (!tableData.value) return
      const newIndex = evt.newIndex as number
      const oldIndex = evt.oldIndex as number
      const data = toRaw(tableData.value)
      const currRow = data?.splice(oldIndex, 1)[0];
      currRow.isMoved = true
      data.splice(newIndex, 0, currRow);
      tableData.value = []
      await nextTick()
      tableData.value = data
    },
  });
}
const openEdit = () => {
  isEdit.value = true
  initDraggableTable()
}
const closeEdit = () => {
  isEdit.value = false
  isFullScream.value = false
  sortableDom.value?.destroy()
  sortableDom.value = null
  search()
}
const init = () => {
  isEdit.value = false
  isFullScream.value = false
  search()
  initDraggableTable()
}
onActivated(() => {
  init()
})
onDeactivated(() => {
  sortableDom.value?.destroy()
  sortableDom.value = null
  tableData.value = null
})
watch(() => props.groupId, n => {
  n && init()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.weight-container {
  width: 100%;
  position: relative;
  box-sizing: border-box;
  padding: 10px 20px 0;
  height: calc(100vh - 220px);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  .item {
    flex-direction: row;
    font-size: 13px;
    height: 40px;
    line-height: 24px;
    padding: 0;
    border-radius: 8px;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    display: flex;
    box-sizing: border-box;
    background-color: #fff;
    border: 2px solid #409eff;
    font-size: var(--el-font-size-base);
    overflow: hidden;
  }
  .moved-item{
    background-color: #f5dcc8;
    border: 2px solid #f7b077;
    
  }
  &.weightClass {
    background-color: #fff;
    width: 100vw;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 99;
    ul {
      grid-template-columns: repeat(9, minmax(0, 1fr));
      @media (min-width: 1536px) {
        grid-template-columns: repeat(13, minmax(0, 1fr));
      }
    }
    #weight-draggable-boxes {
      max-height: calc(100vh - 80px);
    }
  }
  .ghost-item {
    border-style: dashed;
    opacity: 0.2;
  }
}

</style>