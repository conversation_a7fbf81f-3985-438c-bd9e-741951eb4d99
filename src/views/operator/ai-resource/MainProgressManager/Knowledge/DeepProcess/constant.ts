
import { OpenScopeTypeEnum } from '@/type/speech-craft'

export class  CanvasData<PERSON>rigin {
  constructor(scriptId: number, groupId?: number) {
    this.scriptId = scriptId
    this.knowledgeGroupId = groupId
  }
  id = undefined
  name =  ''
  scriptId
  isMasterCanvas = false
  headCorpusId = null
  isOpenContext = true
  groupOpenScope = undefined
  openScopeType = OpenScopeTypeEnum['全部']
  knowledgeGroupId
}