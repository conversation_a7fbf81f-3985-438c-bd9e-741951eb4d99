<template>
  <div class="deep-process tw-flex">
    <div class="left">
      <el-button :disabled="isChecked" type="primary" :icon="Plus" @click="addProcess()" class="tw-w-[166px]">新增流程</el-button>
      <el-scrollbar class="process-container">
        <template v-for="item in processList" :label="item.id" :key="item.id">
          <div class="process tw-list-none" :class="{'process-active': processId === item.id}" @click="handleProcessChange(item.id as number)">
            <div class="process-name">{{ item.name }}</div>
            <div v-if="!isChecked" class="btn-box" >
              <button @click.stop="addProcess(item)"><el-icon :size="16" color="inherit"><SvgIcon name="edit"></SvgIcon></el-icon></button>
              <button @click.stop="delProcess(item)"><el-icon :size="16" color="inherit"><SvgIcon name="delete"></SvgIcon></el-icon></button>
            </div>
          </div>
        </template>
      </el-scrollbar>
    </div>
    <div class="right" v-loading="loadingGraph">
      <AntvX6GraphBox
        v-if="curCanvas && curCanvas.id"
        v-model:needRefresh="needRefresh"
        :nodeList="nodeList || []"
        :edgeList="edgeList || []"
        :curCanvas="curCanvas"
        @paste="pasteAction"
        @update="updateGraph"
        @add-normal="addNormalCorpus"
        @add-connect="addConnectionCorpus"
        @edit="editCorpus"
        @delete="deleteAction"
        @save="saveGraph"
      >
        <CanvasCorpusDrawer v-model:visible="corpusDialogVisible" :corpusData="corpusData" @update:data="updateNodes"/>
      </AntvX6GraphBox>
    </div>
  </div>
  <CanvasEditDialog
    class="tw-z-[100]"
    v-model:visible="canvasDialogVisible"
    :canvasData="canvasData"
    :groupId="props.groupId!"
    @confirm="confirmCanvas"
  />
</template>

<script setup lang="ts">
import { ref, watch, reactive, onActivated, onDeactivated} from 'vue'
import { ElMessage, } from 'element-plus'
import AntvX6GraphBox from '@/components/x6/AntvX6GraphBox.vue'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import CanvasCorpusDrawer from '@/components/corpus/CanvasCorpusDrawer.vue'
import CanvasEditDialog from './CanvasEditDialog.vue'
import { ScriptCorpusItem, CorpusTypeEnum, ConnectTypeEnum, ScriptInfo, CorpusDataOrigin, ScriptBaseInfo, corpusTypeOption, ScriptBranch, CanvasCorpus, CanvasBranch, EventItem, EventValueItem, } from '@/type/speech-craft'
import { scriptCanvasModel, scriptCorpusModel, } from '@/api/speech-craft'
import { Node, } from '@antv/x6'
import { EdgeItem, PortItem } from '@/type/common'
import { useScriptStore } from '@/store/script'
import Confirm from '@/components/message-box'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { pickAttrFromObj } from '@/utils/utils'
import dayjs from 'dayjs'
import { updateCanvasInfoById, CurCanvasOrigin, updateCanvasInfoByNewCorpus } from '@/views/operator/ai-resource/MainProgressManager/MainProcess/constant'
import {  filterNodeDom, pasteCorpus, X6ColorEnum, deleteNodes } from '@/components/x6/config'
import to from 'await-to-js'
import { trace } from '@/utils/trace'

const props = defineProps<{
  groupName?: string;
  groupId?: number;
}>();


const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const loadingGraph = ref(false)
const scriptStore = useScriptStore()
const editId = scriptStore.id
const isChecked = scriptStore.isChecked
const nodeList = ref<Node.Metadata[] | null>([])
const edgeList = ref<EdgeItem[] | null>([])
const curCanvas = reactive<ScriptInfo>(new CurCanvasOrigin(editId,props.groupId))

const corpusData = reactive<ScriptCorpusItem>(new CorpusDataOrigin(
  editId, CorpusTypeEnum['深层沟通-普通语料'], props.groupId, !curCanvas.headCorpusId
))

class CanvasDataOrigin {
  id = undefined
  name =  ''
  scriptId = editId
  isMasterCanvas = false
  headCorpusId = null
  knowledgeGroupId = props.groupId
}
const canvasData = reactive<ScriptBaseInfo>(new CanvasDataOrigin())
const processList = ref<ScriptInfo[] | null>([])
const processId = ref<number>()
const corpusDialogVisible = ref(false)
const canvasDialogVisible = ref(false)
const corpusAllMap = ref<{
  [propName: string]: CanvasCorpus;
} | null>({})
const branchAllMap = ref<{
  [propName: string]: CanvasBranch;
} | null>({})

const needRefresh = ref(false)

const pasteAction = async (obj: {
  nodes: {
    id: number, x?: number, y?: number, canvasId: number | null, corpusType: CorpusTypeEnum
  }[],
  edges: {
    id: number, pre?: number, next?: number,
  }[],
}, posCavas: {x: number, y: number}) => {
  loading.value = true
  const { nodes, edges, headCorpusId } = await pasteCorpus(obj, curCanvas, posCavas)
  nodeList.value?.push(...nodes)
  edgeList.value?.push(...edges)
  headCorpusId && (curCanvas.headCorpusId = headCorpusId)
  scriptStore.updateScript(nodeList.value!, edgeList.value!, curCanvas)
  await scriptStore.saveGraph(true)
  globalStore.copyCellList.nodes = []
  globalStore.copyCellList.edges = []
  needRefresh.value = true
  loading.value = false
}
const addProcess = (item?: ScriptInfo) => {
  canvasDialogVisible.value = true
  if (item) {
    Object.assign(canvasData, item ? pickAttrFromObj(item, [
      'groupOpenScope', 'isOpenContext', 'name', 'openScopeType', 'id', 'knowledgeGroupId', 'scriptId'
    ]) : new CanvasDataOrigin())
  } else {
    Object.assign(canvasData, new CanvasDataOrigin())
  }
}

const delProcess = (item: ScriptInfo) => {
  Confirm({ 
    text: `您确定要删除深层沟通【${item.name}】吗?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(async () => {
    loading.value = true
    trace({ page: `话术编辑-删除深层沟通(${editId})`, params: { canvasId: item.id as number} })
    const [err] = await to(scriptCanvasModel.deleteDeepCanvas({
      canvasId: item.id as number
    }))
    if (!err) {
      ElMessage.success('操作成功')
      getScriptInfo()
    }
    loading.value = false
  }).catch(() => {})
}
const addNormalCorpus = (pos: {x: number, y: number}) => {
  Object.assign(corpusData, new CorpusDataOrigin(editId, CorpusTypeEnum['深层沟通-普通语料'], props.groupId, !curCanvas.headCorpusId), {
    corX: pos.x, 
    corY: pos.y
  })
  corpusData.corpusType = CorpusTypeEnum['深层沟通-普通语料']
  corpusData.isOpenContext = true
  corpusData.branchList = [{name: '统一回复'}]
  corpusDialogVisible.value = true
}
const editCorpus = (corpus: ScriptCorpusItem) => {
  Object.assign(corpusData, corpus)
  corpusDialogVisible.value = true
}
const deleteAction = (corpusIds: number[]) => {
  deleteNodes(corpusIds)
}
const addConnectionCorpus = (pos: {x: number, y: number}) => {
  Object.assign(corpusData, new CorpusDataOrigin(editId, CorpusTypeEnum['深层沟通-连接语料'], props.groupId), {
    corX: pos.x, 
    corY: pos.y
  })
  corpusDialogVisible.value = true
}
const findOneCanvas = async (id: number) => {
  const data = await scriptCanvasModel.findOneDeepCanvas({
    canvasId: id
  }) as ScriptInfo
  Object.assign(curCanvas, data)
  curCanvas.canvasBranchDataMap = data.canvasBranchDataMap
  curCanvas.canvasCorpusDataMap = data.canvasCorpusDataMap
}
const handleProcessChange = async (id: number, needUpdate: boolean = true) => {
  loadingGraph.value = true
  if (processId.value && processId.value !== id && needUpdate) {
    await scriptStore.saveGraph(false, true) // 更新并埋点操作记录
  }
  // 如果点击的画布id相同，为保证画布刷新（防止在非画布页面更新语料文字内容等信息），先清空画布数据
  if (processId.value && processId.value === id) {
    nodeList.value = []
    edgeList.value = []
  }
  const data = await updateCanvasInfoById(id, editId, props.groupId)
  nodeList.value = data.nodeList
  edgeList.value = data.edgeList
  Object.assign(curCanvas, data.curCanvas)
  processId.value = id
  scriptStore.updateScript(nodeList.value, edgeList.value, curCanvas)
  loadingGraph.value = false
}
const getScriptInfo = async (idc?: number) => {
  if (!props.groupId) return
  try {
    const headCorpusList = await scriptCanvasModel.findAllDeepCanvas({
      groupId: props.groupId,
    }) as ScriptInfo[]
    processList.value = headCorpusList && headCorpusList.length>0 ? headCorpusList.sort((a, b) => dayjs(a.createTime).isAfter(dayjs(b.createTime)) ? 1 : -1) : []
    if (processList.value.length > 0) {
      if (idc) {
        handleProcessChange(idc, true)
      } else {
        const inProcess = !!processId.value && !!processList.value.find(item => item.id === processId.value)
        processId.value = inProcess ? processId.value : (headCorpusList[0].id || -1) as number
        handleProcessChange(processId.value as number, inProcess)
      }
    } else {
      nodeList.value = []
      edgeList.value = []
      Object.assign(curCanvas, new CurCanvasOrigin(editId, props.groupId))
      scriptStore.updateScript(nodeList.value, edgeList.value, curCanvas)
    }
  } catch(err) {
    ElMessage({
      message: '流程列表获取错误',
      type: 'error',
    })
  }
}
const confirmCanvas = (canvas: ScriptInfo) => {
  if (canvas.id === curCanvas.id) {
    curCanvas.name = canvas.name
    scriptStore.currentCanvas && (scriptStore.currentCanvas.name = canvas.name)
  }
  getScriptInfo(canvas.id)
}
const updateNodes = async (node: ScriptCorpusItem) => {
  loading.value = true
  const {id, isHead} = node
  const corpusId = id as number
  // 是编辑并不是新增语料时，清空回滚历史
  if (corpusData.id === id) {
    scriptStore.x6History?.push({
      data: id,
      type: '画布-编辑语料',
    })
  }
  if (isHead) {
    curCanvas.headCorpusId = corpusId
  }
  const data = updateCanvasInfoByNewCorpus(node, nodeList.value, edgeList.value, curCanvas)
  nodeList.value = data.nodeList || []
  edgeList.value = data.edgeList || []
  scriptStore.updateScript(nodeList.value, edgeList.value, curCanvas)
  await scriptStore.saveGraph(true)
  loading.value = false
  if (corpusId === corpusData.id) {
    needRefresh.value = true
  }
}
const saveGraph = async (nodes: Node.Metadata[], edges: EdgeItem[], currentCanvas: ScriptInfo ) => {
  const { id, name, scriptId, headCorpusId } = currentCanvas
  const params: ScriptInfo = {
    id,
    scriptId,
    headCorpusId,
    canvasCorpusDataMap: {},
    canvasBranchDataMap: {},
  }
  nodes.map(item => {
    const {  x, y, data, } = item
    params.canvasCorpusDataMap[data.corpusId] = {
      corpusId: data.corpusId,
      corX: x as number,
      corY: y as number,
      name: data.name,
      content: data.content,
      branches: data.branches,
      corpusType: data.corpusType,
      aiIntentionType: data.aiIntentionType,
      connectCorpusId: data.connectCorpusId || null,
      connectType: data.connectType,
      listenInOrTakeOver: data.listenInOrTakeOver,
      smsTriggerName: data.smsTriggerName,
      eventTriggerValueIds: data.eventTriggerValueIds || null,
    }
  })
  edges.map(item => {
    const {  source, target, } = item
    const id = item.id?.split('--')[1] as string
    params.canvasBranchDataMap[id] = {
      branchId: parseInt(id),
      // @ts-ignore
      preCorpusId: parseInt(source.cell.split('--')[1]),
      // @ts-ignore
      nextCorpusId: parseInt(target.cell.split('--')[1]),
      name: '',
      branchContent: ''
    }
  })
  try {
    const data = await scriptCanvasModel.saveOneDeepCanvas(params) as ScriptInfo 
    if (data.id) {
      ElMessage({
        message: `流程【${name}】画布保存成功`,
        type: 'success',
      })
      findOneCanvas(data.id)
    } else {
      ElMessage({
        message: `流程【${name}】画布保存失败`,
        type: 'error',
      })
    }
  } catch(err) {
    ElMessage({
      message: `流程【${name}】画布保存失败`,
      type: 'error',
    })
  }
}
const updateGraph = (nodes: Node.Metadata[], edges: EdgeItem[]) => {
  nodeList.value = nodes
  edgeList.value = edges
}

onActivated(async () => {
  getScriptInfo()
})
onDeactivated(() => {
  scriptStore.saveGraph(false, true)
  Object.assign(curCanvas, new CurCanvasOrigin(editId, props.groupId))
  corpusAllMap.value = null
  branchAllMap.value = null
  nodeList.value = null
  edgeList.value = null
  processList.value = null
})
watch(() => props.groupId, async n => {
  await scriptStore.saveGraph(false, true)
  n && getScriptInfo()
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.deep-process {
  .left {
    width: 192px;
    padding: 16px 0;
    position: relative;
    background-color: #fff;
    border-right: 2px solid #e5e7eb;
    height: calc(100vh - 230px);
    .process-container {
      display: flex;
      height: calc(100vh - 310px);
      width: 190px;
      margin-top: 16px;
      align-items: center;
      flex-direction: column;
      overflow-y: auto;
      .process {
        color: var(--primary-black-color-400);
        border: 1px solid var(--primary-black-color-300);
        border-radius: 4px;
        margin: 0 12px 8px;
        position: relative;
        box-sizing: border-box;
        flex-shrink: 0;
        flex-grow: 0;
        width: 166px;
        height: 60px;
        line-height: 20px;
        font-size: 14px;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .process-name {
          width: 124px;
          flex-shrink: 0;
          flex-grow: 0;
          text-align: left;
          word-break: break-all;
          text-overflow: ellipsis;
          display: -webkit-box;
          line-clamp: 2;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          margin: 8px 0 8px 8px;
          line-height: 22px;
        }
        .handle {
          visibility: hidden;
          position: absolute;
          cursor: pointer;
          left: -15px;
          top: 50%;
          transform: translateY(-50%);
        }
        .btn-box {
          visibility: hidden;
          height: 100%;
          display: flex;
          flex-direction: column;
          flex-shrink: 0;
          justify-content: space-around;
          border-radius: 0 4px 4px 0;
          align-items: center;
          width: 20px;
          box-sizing: border-box;
          background-color: var(--primary-black-color-200);
          color: #fff;
          button {
            width: 20px;
            font-size: 16px;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            &:hover {
              background-color: var(--primary-blue-color);
            }
            .el-icon {
              margin-right: 2px;
            }
            &:last-child {
              border-right: 0;
            }
          }
          .el-button+.el-button {
            margin-left: 0;
          }
        }
        &:hover{
          background-color: #F5F7FA;
          color: var(--primary-black-color-500);
          .btn-box{
            visibility: visible;
            color: #fff;
          }
        } 
      }
      
      .process-active {
        background-color: #fff;
        font-weight: 600;
        border: 1px solid var(--primary-blue-color);
        color: var(--primary-blue-color);
        &:hover {
          background-color: #fff;
          border: 1px solid var(--primary-blue-color);
          color: var(--primary-blue-color);
        }
        .btn-box {
          visibility: visible;
          .el-button {
            color: var(--primary-blue-color);
          }
        }
      }
      .ghost-item {
        border-style: dashed;
        border-width: 2px;
        opacity: 0.4;
      }
    }
  }
  .right {
    position: relative;
    width: calc(100% - 190px);
    height: calc(100vh - 230px);
    .btn-box {
      position: absolute;
      left: 20px;
      top: 10px;
      display: flex;
      flex-direction: column;
      .el-button:nth-child(n+2) {
        margin-left: 0;
        margin-top: 10px;
      }
    }
  }
}

</style>