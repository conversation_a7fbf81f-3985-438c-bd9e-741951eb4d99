<template>
  <div class="info-set-container">
    <div class="tw-flex tw-flex-col tw-grow-0 tw-mt-[16px] tw-my-[8px] tw-mx-[16px]">
      <div class="tw-flex tw-items-center">
        <el-input
          v-model="searchForm.name"
          style="width:250px"
          placeholder="请输入事件触发名称（20字以内）"
          maxlength="20"
          clearable
          @keyup.enter="search"
        >
        </el-input>
        <el-button type="primary" link class="tw-ml-1" @click="search">
          <el-icon :size="16" class="tw-mr-0.5">
            <SvgIcon name="filter" color="none"></SvgIcon>
          </el-icon>
          筛选
        </el-button>
      </div>
      <div class="tw-flex tw-items-end tw-justify-between">
        <span class="info-title">用于触发外部事件，需业务侧和研发侧均支持后才能使用</span>
        <el-button v-if="!isChecked" type="primary" :icon="Plus" @click="edit()">新增事件</el-button>
      </div>
    </div>
   
    <el-table
      :data="tableData"
      style="width: 100%"
      :header-cell-style="{background:'#F7F8FA', color: '#646566', padding: 'auto 8px'}"
      class="tw-grow"
      stripe
    >
      <el-table-column type="expand" width="36">
        <template #default="props">
          <el-table 
            v-if="props.row.eventValuemap" :data="Object.values(props.row.eventValuemap)"
            :header-cell-style="{background:'#F7F8FA', color: '#646566', padding: 'auto 8px', borderBottom: 'none'}"
          >
            <el-table-column label="字段值" prop="name" align="left" min-width="120"/>
            <el-table-column label="释义" prop="explanation" align="left" min-width="120"/>
            <el-table-column label="备注" prop="note" align="left" min-width="120"/>
            <template #empty>
              <el-empty v-if="props.row.eventValuemap || Object.values(props.row.eventValuemap).length === 0" description="暂无数据" />
            </template>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column property="eventName" label="事件名" align="left" min-width="400"></el-table-column>
      <el-table-column property="note" align="left" label="备注" min-width="400"></el-table-column>
      <el-table-column align="right" label="操作" fixed="right" min-width="120">
        <template #default="{ row }">
          <el-button type="primary" link @click="edit(row)">编辑</el-button>
          <el-button :disabled="!!isChecked" :type="!isChecked ? 'primary': 'default'" link @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
  <EventDialog v-if="infoDialogVisible" :visible="infoDialogVisible" :eventData="eventData" @update="search" @close="closeInfoQueryDialog"/>
</template>

<script lang="ts" setup>
import { EventItem, EventValueItem } from '@/type/speech-craft'
import { scriptEventModel } from '@/api/speech-craft'
import { Search, Plus, CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { reactive, computed, ref, onActivated, onDeactivated , } from 'vue'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import PaginationBox from '@/components/PaginationBox.vue'
import { pickAttrFromObj } from '@/utils/utils'
import EventDialog from './EventDialog.vue'
import { useScriptStore } from '@/store/script'
import Confirm from '@/components/message-box'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { trace } from '@/utils/trace'
const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const scriptStore = useScriptStore()
const scriptId = scriptStore.id
const isChecked = scriptStore.isChecked
const searchForm = reactive<{
  name: string
}>({
  name: '',
})
// 表格区
const pageSizeList = [20, 50, 100, 200]
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(pageSizeList[0])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  search()
}
const tableData = computed(() => {
  if (!tableDataOrigin.value) return []
  const name = searchForm.name.trim()
  const data: EventItem[] = name ? tableDataOrigin.value.filter(item => item.eventName?.includes(name)) : tableDataOrigin.value
  total.value = data?.length || 0
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})
const tableDataOrigin = ref<EventItem[]|null>([])
// 搜索区
const search = async () => {
  loading.value = true
  try {
    const data = await scriptStore.getEventOptions(true) as EventItem[] || []
    tableDataOrigin.value = data || []
  } catch(err) {
    ElMessage({
      message: '获取事件触发数据失败',
      type: 'error',
    })
  }
  loading.value = false
}
// 操作区
const infoDialogVisible = ref(false)
class eventDataOrigin {
  id = undefined
  scriptLongId = scriptId
  eventName = undefined
  note = ''
  eventValuemap = undefined
}
const eventData = reactive<EventItem>(new eventDataOrigin())
const edit = (row?: EventItem) => {
  if (row && row.id) {
    Object.assign(eventData, pickAttrFromObj(row, ['id', 'scriptLongId', 'eventName', 'note', 'eventValuemap']))
  } else {
    Object.assign(eventData, new eventDataOrigin())
  }
  infoDialogVisible.value = true
}
const closeInfoQueryDialog = () => {
  infoDialogVisible.value = false
}
const delAction = async (triggerKeyId: number) => {
  loading.value = true
  trace({
    page: `话术编辑-事件触发设置-删除事件(${scriptId})`,
    params: {id: triggerKeyId},
  })
  await scriptEventModel.deleteEvent({triggerKeyId}) as boolean
  ElMessage({
    message: '删除成功',
    type: 'success',
  })
  search()
  loading.value = false
}
const del = (row: EventItem) => {
  Confirm({ 
    text: `您确定要删除事件触发【${row.eventName}】吗?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(() => {
    delAction(row.id as number)
  }).catch(() => {})
}


onActivated(() => {
  search()
})
onDeactivated(() => {
  tableDataOrigin.value = null
  Object.assign(eventData, new eventDataOrigin())
})
</script>

<style scoped lang="postcss" type="text/postcss">
.info-set-container {
  width: 100%;
  position: relative;
  box-sizing: border-box;
  overflow-y: auto;
  height: calc(100vh - 230px);
  display: flex;
  flex-direction: column;
  .el-table {
    font-size: var(--el-font-size-base);
    color: var(--primary-black-color-500);
    :deep(td.el-table__expanded-cell) {
      margin: 0;
      padding: 0;
      .el-table {
        border-left: 36px solid #f7f8fa;
      }
    }
  }
}
.item {
  font-size: 14px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 10px;
  span {
    width: 90px
  }
}
:deep(.el-table .caret-wrapper) {
  display: none;
}
</style>