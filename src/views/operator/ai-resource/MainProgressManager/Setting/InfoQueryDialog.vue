<template>
  <el-dialog
    v-model="dialogVisible"
    align-center
    width="720px"
    @close="cancel"
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">信息字段</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
    >
      <!-- <pre>{{addData.infoQueryValues}}</pre> -->
      <el-form
        :model="addData"
        :rules="rules"
        label-width="90px"
        ref="editRef"
        :disabled="isChecked"
      >
        <div class="tw-w-[80px] tw-font-[600] tw-text-[var(--primary-black-color-600)] tw-text-left tw-mt-[16px] tw-mb-[4px]">基础配置</div>
        <el-form-item label="信息字段名：" prop="infoFieldName">
          <el-input v-model.trim="addData.infoFieldName" clearable placeholder="请输入信息字段名（60字以内）" maxlength="60"/>
        </el-form-item>
        <el-form-item label="显示名：" prop="fieldDefinition">
          <el-input v-model.trim="addData.fieldDefinition" clearable placeholder="请输入显示名（30字以内）" maxlength="30"/>
        </el-form-item>
        <div class="tw-flex tw-items-center">
          <span class="tw-w-[80px] tw-font-[600] tw-text-[var(--primary-black-color-600)]">信息字段值</span>
          <span class="tw-text-[var(--primary-black-color-400)]">（“字段值”“释义”为必填项）</span>
        </div>
        <el-form-item prop="infoQueryValues" label-width="1px">
          <div ref="fieldValuesRef" class="tw-mb-1 tw-w-full">
            <el-row
              v-for="(item, index) in infoQueryValues"
              class="tw-flex tw-justify-around tw-items-center tw-w-full tw-overflow-hidden tw-mb-[2px]"
              :class="{'group-values': item && item.length > 1}"
              :key="index"
            >
              <el-col :span="1" class="tw-flex tw-items-center">
                <el-icon class="handle tw-cursor-pointer" :size="16"><SvgIcon name="drag" color="var(--primary-black-color-400)"/></el-icon>
              </el-col>
              <el-col :span="23">
                <el-row v-for="(row, index2) in item" :key="index2" class="tw-h-[32px] tw-flex tw-justify-center tw-items-center tw-my-[4px]">
                  <el-col :span="1">
                    <div v-if="item.length === 1">
                      <el-popover
                        v-if="item.length === 1 && infoQueryValues.length > 1"
                        placement="right"
                        :width="50"
                        trigger="click"
                      >
                        <template #reference>
                          <el-icon :size="14" color="var(--el-color-primary)" class="tw-cursor-pointer">
                            <SvgIcon name="lock"/>
                          </el-icon>
                        </template>
                        <div class="tw-flex tw-flex-col tw-items-center tw-text-[13px] tw-justify-around">
                          <button v-if="index >= 1" @click="lock(index, -1)" class="tw-w-full tw-cursor-pointer hover:tw-text-[--el-color-primary] tw-my-[3px]">向上合并</button>
                          <button v-if="index < infoQueryValues.length - 1" @click="lock(index, 1)" class="tw-w-full tw-cursor-pointer hover:tw-text-[--el-color-primary] tw-my-[3px]">向下合并</button>
                        </div>
                      </el-popover>
                      
                    </div>
                    <el-icon v-else="item.length >= 1" color="var(--el-color-danger)" class="tw-cursor-pointer" :size="14" @click="unlock(index, index2)">
                      <SvgIcon name="unlock"/>
                    </el-icon>
                  </el-col>
                  <el-col :span="7" class="tw-flex tw-justify-center tw-items-center">
                    <span class="tw-w-[80px] tw-shrink-0 tw-text-right">字段值：</span>
                    <el-input v-model="row.value" clearable placeholder="信息字段值（60字以内）" maxlength="60" style="width:140px"/>
                  </el-col>
                  <el-col :span="7" class="tw-flex tw-justify-center tw-items-center">
                    <span class="tw-w-[60px] tw-shrink-0 tw-text-right">释义：</span>
                    <el-input v-model="row.definition" clearable placeholder="释义（30字以内）" maxlength="30" style="width:150px"/>
                  </el-col>
                  <el-col :span="8" class="tw-flex tw-justify-center tw-items-center">
                    <span class="tw-w-[60px] tw-shrink-0 tw-text-right">备注：</span>
                    <el-input v-model="row.note" clearable placeholder="备注" maxlength="60" style="width:150px"/>
                  </el-col>
                  <el-col :span="1" class="tw-flex tw-justify-center tw-items-center">
                    <el-button :disabled="!(addData.infoQueryValues && addData.infoQueryValues.length > 1)" link :type="(addData.infoQueryValues && addData.infoQueryValues.length > 1) ? 'primary': 'default'" @click="delQuery(index, index2)">
                      删除
                    </el-button>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
            <el-row>
              <el-button link type="primary" @click="addQuery" style="height: 32px"><el-icon><SvgIcon name="add1"></SvgIcon></el-icon>新增事件字段</el-button>
            </el-row>
          </div>
        </el-form-item>
        
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer" v-if="!isChecked">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, toRaw, nextTick } from 'vue'
import { InfoQueryItem, InfoQueryValueItem } from '@/type/speech-craft'
import { Switch, CloseBold, Select } from '@element-plus/icons-vue'
import Sortable from "sortablejs";
import { ElMessage, } from 'element-plus'
import { pickAttrFromObj } from '@/utils/utils'
import type { FormInstance, FormRules } from 'element-plus'
import { scriptInfoModel } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import { trace } from '@/utils/trace'

const scriptStore = useScriptStore()
const editId = scriptStore.id
const isChecked = scriptStore.isChecked
const loading = ref(false)
const emits = defineEmits(['close', 'update',])
const props = defineProps<{
  visible: boolean;
  infoQueryData: InfoQueryItem;
}>();

const dialogVisible = ref(props.visible)
const addData = reactive<InfoQueryItem>(props.infoQueryData)
const editRef = ref<FormInstance  | null>(null)
const infoQueryValues = ref<InfoQueryValueItem[][]>([])
const addQuery = () => {
  infoQueryValues.value.push([{
    definition: '', value: '', note: '', weight: addData.infoQueryValues && addData.infoQueryValues.length >= 1 ? (addData.infoQueryValues!.at(-1)!.weight || 100) + 1 || 1 : 1, infoQueryKeyId: addData.id || undefined
  }])
}
const delArr = ref<number[]>([])
const delQuery = (index: number, index2: number) => {
  if (infoQueryValues.value[index][index2].id) {
    delArr.value.push(infoQueryValues.value[index][index2].id!)
  }
  infoQueryValues.value[index].splice(index2, 1)
  infoQueryValues.value = infoQueryValues.value.filter(item => item && item.length > 0)
}
const lock = (index: number, flag: -1 | 1) => {
  if (flag === -1) {
    const row = infoQueryValues.value.splice(index, 1)[0][0]
    infoQueryValues.value[index - 1].push(row)
  } else {
    const row = infoQueryValues.value.splice(index, 1)[0][0]
    infoQueryValues.value[index].push(row)
  }
  
}
const unlock = (index1: number, index2: number) => {
  const row = infoQueryValues.value[index1].splice(index2, 1)
  infoQueryValues.value.splice(index1, 0, row)
}
const refreshWeight = (arr: InfoQueryValueItem[][])=> {
  return arr.map((item, index) => {
    return item.map(row => {
      row.weight = index + 1
      return row
    })
  })
}
watch(infoQueryValues, n => {
  addData.infoQueryValues = refreshWeight(n).flat()
  if (n && n.length > 1) {
    sortableDom.value && (sortableDom.value.options.disabled = false)
  } else {
    sortableDom.value && (sortableDom.value.options.disabled = true)
  }
}, {
  deep: true
})
const checkQueryField = (rule: any, value: any, callback: any) => {
  if (!addData.infoQueryValues || addData.infoQueryValues.length < 1) {
    return callback(new Error('请输入信息字段值'))
  }
  addData.infoQueryValues.map(item => {
    if (!item.value) {
      return callback(new Error('请输入信息字段值'))
    }
    if (!item.definition) {
      return callback(new Error('请输入信息字段释义'))
    }
  })
  return callback()
}
const rules = {
  infoFieldName: [
    { required: true, message: '请输入信息字段名', trigger: 'blur' },
  ],
  fieldDefinition: [
    { required: true, message: '请输入信息字段显示名', trigger: 'blur' },
  ],
  infoQueryValues: [
    { validator: checkQueryField, trigger: ['change']},
  ],
}
// 底部确认、取消
const confirm = async () => {  
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const params: InfoQueryItem = pickAttrFromObj(addData, 
          ['id', 'scriptLongId', 'infoFieldName', 'fieldDefinition', 'infoQueryValues']
        )
        trace({ page: `话术编辑-信息查询设置-${addData.id ? '编辑' : '新增'}信息查询(${editId})`, params: params })
        delArr.value?.length > 0 && trace({ page: `话术编辑-信息查询设置-编辑信息查询-删除信息查询值(${editId})`, params: delArr.value })
        const data = await scriptInfoModel.saveInfoQuery(params) as InfoQueryItem
        await Promise.all(
          delArr.value.map(async item => {
            await scriptInfoModel.deleteInfoQueryValue({
              id: item, scriptLongId: addData.scriptLongId,
            }) as InfoQueryItem
          })
        )
        ElMessage({
          message: '操作成功',
          type: 'success',
        })
        loading.value = false
        dialogVisible.value =false
        emits('update', data)
      } catch(err) {
        loading.value = false
        ElMessage({
          message: '操作失败',
          type: 'error',
        })
      }
    }
  })
}
const cancel = () => {
  dialogVisible.value = false
  emits('close')
}
const sortableDom = ref()
const fieldValuesRef = ref<null | HTMLElement>(null)
const init = async () => {
  if (addData.infoQueryValues && addData.infoQueryValues.length > 0) {
    const obj: {
      [key: string]: InfoQueryValueItem[]
    } = {}
    addData.infoQueryValues.map(item => {
      if (item.weight) {
        obj[item.weight] ? obj[item.weight].push(item) : (obj[item.weight] = [item])
      }
    })
    infoQueryValues.value = Object.values(obj)
  } else {
    infoQueryValues.value = [[{ value: '', definition: '', note: '', weight: 1, infoQueryKeyId: addData.id || undefined}]]
  }
  
  delArr.value = []
  await nextTick()
  sortableDom.value = fieldValuesRef.value && Sortable.create(
    fieldValuesRef.value, {
    animation: 300,
    handle: ".handle",
    disabled: !infoQueryValues.value || infoQueryValues.value.length < 1,
    forceFallback: true,
    onEnd: async (evt) => {
      const newIndex = evt.newIndex as number
      const oldIndex = evt.oldIndex as number
      if (oldIndex !== newIndex && infoQueryValues.value) {
        const currRow = infoQueryValues.value[oldIndex]
        infoQueryValues.value.splice(oldIndex, 1);
        infoQueryValues.value.splice(newIndex, 0, currRow);
        const data = toRaw(infoQueryValues.value)
        infoQueryValues.value = []
        await nextTick()
        infoQueryValues.value = data
      }
    },
  });
}
watch(() => props.visible, n => {
  n && init()
}, {
  immediate: true
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
.group-values {
  border: 1px solid var(--el-color-primary);
  border-radius: 5px;
}
.text {
  font-size: 13px;
  line-height: 24px;
  margin-right: 10px;
  text-align: left;
}

.el-dialog .el-form {
  color: var(--primary-black-color-600);
  .el-form-item {
    margin-bottom: 16px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
  :deep(.el-form-item__content) {
    font-size: var(--el-font-size-base);
  }
}
</style>