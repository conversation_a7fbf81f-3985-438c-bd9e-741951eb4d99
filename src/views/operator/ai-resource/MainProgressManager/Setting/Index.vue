<template>
    <div class="setting-container">
      <el-tabs v-model="active">
        <el-tab-pane label="声学设置" :name="1"></el-tab-pane>
        <el-tab-pane label="信息查询设置" :name="2"></el-tab-pane>
        <el-tab-pane label="事件触发设置" :name="3"></el-tab-pane>
      </el-tabs>
      <keep-alive>
        <VoiceSetting v-if="active===1"/>
        <InfoQuery v-else-if="active===2"/>
        <EventSetting v-else/>
      </keep-alive>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineAsyncComponent } from 'vue'

const InfoQuery = defineAsyncComponent({loader: () => import('./InfoQuery.vue')})
const VoiceSetting = defineAsyncComponent({loader: () => import('./VoiceSetting.vue')})
const EventSetting = defineAsyncComponent({loader: () => import('./EventSetting.vue')})

const active = ref(1)
// 执行区
onMounted(() => {
  
});

</script>

<style scoped lang="postcss" type="text/postcss">
.setting-container {
  width: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  position: relative;
  background-color: #fff;
  height: calc(100vh - 180px);
  .el-tabs {
    
    &:deep(.el-tabs__item){
      height: 40px;
      line-height: 22px;
      width: 100px;
      color: var(--primary-black-color-400);
      font-size: 14px;
      &.is-active {
        color: var(--el-color-primary);
      }
      &:nth-child(2) {
        padding-left: 16px;
      }
    }
  }
 
  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
    border: none;
    background-color: #e5e7eb;
  }
  :deep(.el-tabs__nav-prev), :deep(.el-tabs__nav-next) {
    line-height: 60px;
    font-size: 16px;
  }
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
}
</style>
