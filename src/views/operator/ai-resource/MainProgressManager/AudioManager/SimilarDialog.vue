<template>
  <el-dialog
    :model-value="props.visible"
    width="600px"
    align-center
    class="script-audio-dialog"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        音文相似度详情
      </div>
    </template>

    <div class="form-dialog-main">
      <!--相似度-->
      <div class="form-dialog-main-inner">
        <span>
          相似度：
        </span>
        <span
          :class="typeof props.data.asrResult !== 'number' ? 'gray-status' : props.data.asrResult >= 95 ? 'green-status' : props.data.asrResult >= 90 ? 'orange-status' : 'red-status'"
          style="background-color: transparent;"
        >
          {{ typeof props.data.asrResult === 'number' ? (props.data.asrResult + '%') : '-' }}
        </span>
        <!--文本对比-->
        <!--标题-->
        <div class="tw-grid tw-grid-cols-2 tw-gap-[8px] tw-my-[8px] tw-text-[13px] tw-font-bold">
          <div>
            原始文字内容
          </div>
          <div>
            音频识别结果
          </div>
        </div>
        <!--正文-->
        <el-scrollbar view-class="tw-grid tw-grid-cols-2 tw-gap-[8px] tw-text-[13px]" max-height="50vh">
          <!--原文-->
          <div v-html="props.data.content || ''" />
          <!--ASR识别结果-->
          <div v-html="formattedAsrTxt" />
        </el-scrollbar>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button type="primary" :icon="CloseBold" @click="onClickCancel">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { CloseBold } from '@element-plus/icons-vue'
import { AudioItem } from '@/type/speech-craft'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
  data: AudioItem,
}>(), {
  visible: false,
  data: () => ({
    name: '',
    content: '',
    audioPath: '',
  }),
})
const emits = defineEmits([
  'update:visible',
])

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- ASR 开始 ----------------------------------------

const formattedAsrTxt = computed(() => {
  return props.data.asrTxt?.replaceAll('[', `<span style="color: var(--primary-red-color);">`).replaceAll(']', `</span>`) || ''
})

// ---------------------------------------- ASR 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  // 每次显示弹窗时
  if (val) {
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
