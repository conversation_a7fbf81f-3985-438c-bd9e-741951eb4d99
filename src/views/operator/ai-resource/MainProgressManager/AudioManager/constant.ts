import { CorpusTypeEnum } from '@/type/speech-craft'

// 用于音频组件的数据类型，区别于列表数据，注意转换
export interface AudioInfoItem {
  id?: number,
  name?: string | null // 语料名称
  content?: string | null// 文字内容
  audioPath?: string | null // 音频文件URL
  isPlayed?: boolean
}

export class TempAudioOrigin implements AudioInfoItem {
  id = -1
  contentName = ''
  content = ''
  corpusId = -1
  corpusType = CorpusTypeEnum['主动流程-普通语料']
  audioPath = ''
  isPlayed = false
}

export const corpusTypeMixOption = [
  {
    name: '主动流程',
    value: [CorpusTypeEnum['主动流程-普通语料'], CorpusTypeEnum['主动流程-连接语料']].join(',')
  },
  {
    name: '知识库',
    value: [CorpusTypeEnum['深层沟通-普通语料'], CorpusTypeEnum['深层沟通-连接语料'], CorpusTypeEnum['基本问答']].join(',')
  },
  {
    name: '功能话术',
    value: [
      CorpusTypeEnum['最高优先'], CorpusTypeEnum['重复语料'], CorpusTypeEnum['沉默语料'],
      CorpusTypeEnum['打断垫句'], CorpusTypeEnum['续播垫句'], CorpusTypeEnum['承接语料'],
    ].join(',')
  },
]
