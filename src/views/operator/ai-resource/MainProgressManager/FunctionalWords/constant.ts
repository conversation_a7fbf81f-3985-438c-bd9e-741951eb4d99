
import { CorpusConditionItem } from '@/type/corpus'
export type RequirementData = {
  scriptId: number,
  semCombineEntity?: {
    excludeSemConditions: CorpusConditionItem[],
    satisfySemConditions: CorpusConditionItem[],
  } // 满足排除核心语义和补充短语条件
}

export class RequirementOriginData implements RequirementData {
  constructor(scriptId: number) {
    this.scriptId = scriptId
  }
  scriptId: number
  semCombineEntity = {
    excludeSemConditions: [],
    satisfySemConditions: [],
  }
}
