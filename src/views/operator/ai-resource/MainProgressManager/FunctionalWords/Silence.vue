<template>
  <div class="silence-container">
    <div class="tw-mb-[8px] tw-flex tw-items-center tw-justify-between">
      <div class="tw-flex tw-items-center info-title">
        <span>连续触发限制：{{ silenceData.maxSilenceCount }}；</span>
        <span>触发标签：{{ silenceData.intentionTagName??'-' }}</span>
        <el-button v-if="!isChecked" class="tw-ml-[4px]" type="primary" link @click="handleSilence">
          修改
        </el-button>
      </div>
      <div class="tw-flex tw-items-center">
        <div class="info-title tw-mr-[8px]">沉默语料最后一个默认执行挂机</div>
        <el-button v-if="!isChecked" type="primary" :icon="Plus" @click="edit">新增</el-button>
      </div>
    </div>
    <el-table
      :data="tableData || []"
      style="width: 100%"
      id="silence-draggable-boxes"
      class="tw-grow"
      :header-cell-style="tableHeaderStyle"
      stripe
    >
      <el-table-column v-if="!isChecked" label=" " align="left"  width="80">
        <div class="handle tw-cursor-pointer"><el-icon ><Switch /></el-icon></div>
      </el-table-column>
      <el-table-column property="name" label="语料名称" align="left"  width="120">
        <template #default="{ row }">
          <div class="tw-w-full tw-flex tw-items-center">
            <span class="tw-line-clamp-2">{{ row.name ?? '-' }}</span>
            <el-icon v-if="row.smsTriggerName" :size="14" color="var(--el-color-primary)" class="tw-ml-[4px]"><SvgIcon name="trigger-sms"></SvgIcon></el-icon>
            <el-icon v-if="row.listenInOrTakeOver" :size="14" color="var(--el-color-primary)" class="tw-ml-[4px]"><SvgIcon name="trigger-human"></SvgIcon></el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="content" align="left" label="文字内容" min-width="200">
        <template #default="{ row }">
          <el-tooltip placement="top" trigger="click">
            <template #content><div class="tw-max-w-[20vw]">{{ row.content }}</div></template>
            <div class="tw-truncate">
              {{ row.content }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column property="eventTriggerValueIds" align="left" label="触发事件" min-width="120">
        <template #default="{ row }">
          <el-tooltip placement="top" trigger="click">
            <template #content><div class="tw-max-w-[20vw]">{{ filterEventValueIds(row.eventTriggerValueIds) }}</div></template>
            <div class=" tw-truncate">
              {{ filterEventValueIds(row.eventTriggerValueIds)|| '-' }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column property="updateTime" align="center" label="最后更新时间" width="160" sortable>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="right" width="160">
        <template #default="{ row }">
          <el-button type="primary" link @click="edit(row)">{{!isChecked ? '编辑' : '查看'}}</el-button>
          <el-button type="primary" link @click="editContent(row)">
            打断设置
          </el-button>
          <el-button v-if="!isChecked" type="danger" link @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
  </div>
  <SilenceDialog
    v-model:visible="silenceDialogVisible"
    :silenceData="silenceData"
    @update:visible="updateSilenceCount"
  ></SilenceDialog>
  <BaseCorpusDrawer v-model:visible="corpusDialogVisible" :corpusData="corpusData"  @update:data="search"/>
  <CorpusContentSettingDrawer v-model:visible="corpusContentVisible" :corpusId="corpusData?.id!"/>
</template>

<script lang="ts" setup>
import { ScriptCorpusItem, corpusTypeOption, CorpusTypeEnum, EventValueItem, CorpusDataOrigin } from '@/type/speech-craft'
import { scriptCorpusModel } from '@/api/speech-craft'
import { CaretTop, CaretBottom, Plus, Switch, } from '@element-plus/icons-vue'
import Sortable from "sortablejs";
import { reactive, computed, ref, watch, onActivated, nextTick, onDeactivated, } from 'vue';
import { ElMessage } from 'element-plus'
import BaseCorpusDrawer from '@/components/corpus/BaseCorpusDrawer.vue'
import { useScriptStore } from '@/store/script'
import { storeToRefs } from 'pinia'
import Confirm from '@/components/message-box'
import { useGlobalStore } from '@/store/globalInfo'
import { pickAttrFromObj } from "@/utils/utils";
import SilenceDialog from './SilenceDialog.vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from 'await-to-js';
import CorpusContentSettingDrawer from '@/components/corpus/CorpusContentSettingDrawer.vue'
import { trace } from '@/utils/trace'

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const scriptStore = useScriptStore()
const scriptId = scriptStore.id
const isChecked = scriptStore.isChecked

// 表格区
const tableData = ref<ScriptCorpusItem[]>([])
// 搜索区
// 搜索沉默语料连续触发限制
const updateSilenceCount = async () => {
  loading.value = true
  try {
    if (!silenceDialogVisible.value) {
      const data = await scriptCorpusModel.findSilenceRequirement({scriptId})
      silenceData.maxSilenceCount = data.maxSilenceCount
      silenceData.intentionTagId = data.aiLabel?.id || undefined
      silenceData.intentionTagName = data.aiLabel?.labelName || undefined
    }
  }catch(err) {
    ElMessage({
      message: '获取沉默语料触发限制失败',
      type: 'error',
    })
  }
  loading.value = false
}
// 搜索列表
const search = async () => {
  loading.value = true
  const data = await to(scriptCorpusModel.findSilenceList({
    scriptId: scriptId
  }))
  tableData.value = (data[1] || []).map(item => {
    const scriptMultiContents = item.scriptMultiContents || []
    const content = (scriptMultiContents && scriptMultiContents.length) ? scriptMultiContents[0].scriptUnitContents?.reduce((a, b) => a + b.content, '') || '' : ''
    return {
      ...item,
      content: content
    }
  })
  loading.value = false
}

// 操作区
const corpusDialogVisible = ref(false)

const corpusData = reactive<ScriptCorpusItem>(new CorpusDataOrigin(scriptId, CorpusTypeEnum['沉默语料']))
const edit = (row?: ScriptCorpusItem) => {
  if (row && row.id) {
    Object.assign(corpusData, pickAttrFromObj(row, corpusTypeOption[corpusData.corpusType!].keys))
  } else {
    Object.assign(corpusData, new CorpusDataOrigin(scriptId, CorpusTypeEnum['沉默语料']))
  }
  corpusDialogVisible.value = true
}

/**
 * 修改沉默连续触发限制
 * */
// 修改沉默连续触发弹窗Visible
const silenceDialogVisible = ref(false)
// 修改沉默连续触发弹窗数据
const silenceData = reactive<{
  maxSilenceCount: number, scriptId: number, intentionTagId?: number, intentionTagName?: string
}>({
  maxSilenceCount: 3,
  scriptId: scriptId,
  intentionTagId: undefined,
  intentionTagName: undefined,
})
const handleSilence = () => {
  silenceDialogVisible.value = true
}

const delAction = async (id: number) => {
  loading.value = true
  trace({
    page: `话术编辑-功能语料-沉默语料：删除(${scriptId})`,
    params: { id },
  })
  const [err] = await to(scriptCorpusModel.deleteFuncCorpus({corpusId: id}))
  !err && ElMessage.success('删除成功')
  search()
  loading.value = false
}
const del = (row: ScriptCorpusItem) => {
  Confirm({ 
    text: `您确定要删除语料【${row.name}】吗?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(() => {
    delAction(row.id as number)
  }).catch(() => {})
}

/** 语句打断设置 */
const corpusContentVisible = ref(false)
const editContent = (row: ScriptCorpusItem) => {
  if (row && row.id) {
    Object.assign(corpusData, pickAttrFromObj(row,
      corpusTypeOption[corpusData.corpusType!].keys
    ))
  } else {
    Object.assign(corpusData, new CorpusDataOrigin(scriptId, CorpusTypeEnum['最高优先']))
  }
  corpusContentVisible.value = true
}

const sortableDom = ref<Sortable | null>(null)
const initDraggableTable = () => {
  sortableDom.value = Sortable.create(
    document.querySelector('#silence-draggable-boxes .el-table__body tbody') as HTMLElement, {
    animation: 300,
    sort: !isChecked,
    handle: ".handle",
    onEnd: async (evt) => {
      const newIndex = evt.newIndex as number
      const oldIndex = evt.oldIndex as number
      const currRow = tableData.value.splice(oldIndex, 1)[0];
      tableData.value.splice(newIndex, 0, currRow);
      const list = tableData.value.map(item => item.id!) || []
      loading.value = true
      const params = {
        corpusIdList: list,
        scriptId: scriptId,
      }
      trace({ page: `话术编辑-功能语料-沉默语料-排序(${scriptId})`, params: params })
      await scriptCorpusModel.saveSilenceByOrder(params)
      tableData.value = []
      await nextTick()
      search()
      await nextTick()
      loading.value = false
    },
  });
}
const eventValuesOptions = reactive<{[key: string]: EventValueItem}>({})
const filterEventValueIds = (ids?: number[]) => {
  if (ids && ids.length > 0) {
    const eventTriggerValueArr: string[] = []
    ids && ids?.length > 0 && ids?.map(item => {
      eventValuesOptions[item] && eventTriggerValueArr.push(`${(eventValuesOptions[item].name || '')}(${(eventValuesOptions[item].explanation || '')})`)
    })
    return eventTriggerValueArr.join(',') || ''
  } else {
    return ''
  }
}
const init = async() => {
  const res = await scriptStore.getEventOptions()
  if (res && res.length > 0) {
    res.map(item => {
      item.eventValuemap && Object.assign(eventValuesOptions, item.eventValuemap)
    })
  }
  updateSilenceCount()
  search()
}
onActivated(() => {
  init()
  initDraggableTable()
})
onDeactivated(() => {
  sortableDom.value?.destroy()
  sortableDom.value = null
  // @ts-ignore
  tableData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.silence-container {
  width: 100%;
  height: calc(100vh - 240px);
  padding: 12px;
  position: relative;
  font-size: var(--el-font-size-base);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .el-table {
    font-size: 13px;
  }
  :deep(.el-table .caret-wrapper) {
    display: none;
  }
}
.item {
  font-size: 14px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 10px;
  span {
    width: 90px
  }
}
</style>