<template>
  <div class="priority-container">
    <div class="tw-mb-[8px] tw-flex tw-flex-col">
      <div class="tw-flex tw-items-center tw-justify-between tw-mb-[8px]">
        <div class="tw-flex tw-items-center">
          <el-input
            v-model="searchForm.name"
            style="width:250px"
            placeholder="请输入语料名称（20字以内）"
            maxlength="20"
            clearable
            @keyup.enter="search"
            @input="(val: string) => !val && search()" 
          >
          </el-input>
          <el-button type="primary" class="tw-ml-1" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
        <CardListRadioBox v-model:active="listType"/>
      </div>
      <div class="tw-flex tw-items-center tw-justify-between">
        <span class="info-title">AI回答后为“挂机”的语料，在相同的满足条件下会优先命中</span>
        <el-button v-if="!isChecked" type="primary" :icon="Plus" @click="edit" class="tw-ml-1">新建语料</el-button>
      </div>
    </div>
    <el-table
      v-if="listType === 'list'"
      :data="tableData"
      style="width: 100%"
      class="tw-flex-grow"
      v-loading="loading"
      :header-cell-style="tableHeaderStyle"
      stripe
    >
      <el-table-column property="weight" align="left" label="权重" width="80"></el-table-column>
      <el-table-column property="name" label="语料名称" align="left" width="200">
        <template #default="{ row }">
          <div class="tw-w-full tw-flex tw-items-center">
            <span>{{ row.name ?? '-' }}</span>
            <el-icon v-if="row.smsTriggerName" :size="14" color="var(--el-color-primary)" class="tw-ml-[4px]"><SvgIcon name="trigger-sms"></SvgIcon></el-icon>
            <el-icon v-if="row.listenInOrTakeOver" :size="14" color="var(--el-color-primary)" class="tw-ml-[4px]"><SvgIcon name="trigger-human"></SvgIcon></el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="matchText" align="left" label="满足条件" min-width="240">
        <template #default="{ row, $index }">
          <div v-if="row.matchText && row.matchText.length" v-for="(item, index) in row.matchText||[]" :key="`${$index}-${index}`">
            <el-divider v-if="index" border-style="dashed">or</el-divider>
            <li v-for="(item2, index2) in item" :key="`${$index}-${index}-${index2}`">
              {{ item2 || '-' }}
            </li>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column property="excludeText" align="left" label="排除条件" min-width="240">
        <template #default="{ row, $index }">
          <div v-if="row.excludeText && row.excludeText.length" v-for="(item, index) in row.excludeText||[]" :key="`${$index}-${index}`">
            <el-divider v-if="index" border-style="dashed">or</el-divider>
            <li v-for="(item2, index2) in item" :key="`${$index}-${index}-${index2}`">
              {{ item2 || '-' }}
            </li>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column property="connectType" align="center" label="AI回答后" width="120">
        <template #default="{ row }">
          {{ findValueInEnum(row.connectType, ConnectTypeEnum) || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="eventTriggerValueIds" align="left" label="触发事件" width="120">
        <template #default="{ row }">
          {{ filterEventValueIds(row.eventTriggerValueIds)|| '-' }}
        </template>
      </el-table-column>
      <el-table-column property="aiIntentionType" align="center" label="分类" width="120">
        <template #default="{ row }">
          {{ row.aiIntentionType ? row.aiIntentionType.intentionType + '-' + row.aiIntentionType.intentionName : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="标签" align="left" min-width="160">
        <template #default="{ row }">
          {{ row.aiLabels?.map((item: LabelItem) =>item.labelName).join(',') || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="content" align="left" label="文字内容" min-width="300">
        <template #default="{ row }">
          <li v-if="row.content && row.content.length > 0" v-for="item in row.content || []">
            <span>{{ item || '-' }}</span>
          </li>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column property="updateTime" label="最后更新时间" width="160" sortable>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" :min-width="isChecked ? 120 : 160" align="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="edit(row)">
            {{!isChecked ? '编辑' : '查看'}}
          </el-button>
          <el-button type="primary" link @click="editContent(row)">
            打断设置
          </el-button>
          <el-button v-if="!isChecked" type="danger" link @click="del(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <el-scrollbar v-show="listType === 'card'" class="tw-grow">
      <ul id="prior-draggable-boxes" class="tw-mt-[10px] tw-grid tw-grid-cols-7 2xl:tw-grid-cols-10 tw-gap-4">
        <li v-for="(item, index) in tableData" class="box handle" :class="{'moved-box': item.isMoved}" :key="item.id">
          <div class="tw-grow-0 tw-shrink-0 tw-leading-[40px] tw-mr-[3px]">
            {{ item.weight || '未知'}}
          </div>
          <el-tooltip placement="top" trigger="click">
            <template #content> {{ item.name }}</template>
            <div class="tw-line-clamp-2 tw-text-left tw-leading-[20px] tw-shrink-0 tw-max-w-[82%]">
              {{ item.name }}
            </div>
          </el-tooltip>
        </li>
      </ul>
    </el-scrollbar>
  </div>
  <BaseCorpusDrawer v-model:visible="corpusDialogVisible" :corpusData="corpusData" @update:data="search"/>
  <CorpusContentSettingDrawer v-model:visible="corpusContentVisible" :corpusId="corpusData?.id!"/>
</template>

<script lang="ts" setup>
import { CorpusTypeEnum, corpusTypeOption, CorpusDataOrigin, ScriptCorpusItem, ConnectTypeEnum, ScriptBaseInfo, EventValueItem  } from '@/type/speech-craft'
import { scriptCorpusModel, scriptCanvasModel } from '@/api/speech-craft'
import { LabelItem, IntentionType } from "@/type/IntentionType";
import { CaretTop, CaretBottom, Plus } from '@element-plus/icons-vue'
import Sortable from "sortablejs";
import { reactive, ref, watch, onActivated, nextTick, onDeactivated, } from 'vue'
import { ElMessage } from 'element-plus'
import BaseCorpusDrawer from '@/components/corpus/BaseCorpusDrawer.vue'
import CardListRadioBox from '@/components/CardListRadioBox.vue'
import Confirm from '@/components/message-box'
import { tableHeaderStyle } from '@/assets/js/constant'
import CorpusContentSettingDrawer from '@/components/corpus/CorpusContentSettingDrawer.vue'
import { useScriptStore } from '@/store/script'
import { findValueInEnum, pickAttrFromObj } from "@/utils/utils";
import to from 'await-to-js'
import { useUserStore } from "@/store/user";
import routeMap from '@/router/asyncRoute/route-map'
import { translateCorpusRules } from "@/components/corpus/constant";
import { trace } from '@/utils/trace';

const loading = ref(false)
const scriptStore = useScriptStore()
const scriptId = scriptStore.id
const isChecked = scriptStore.isChecked

// 用户权限获取
const userStore = useUserStore();

const listType = ref<'card'|'list'>('list')
const searchForm = reactive<{
  name: string
}>({
  name: '',
})

// 表格区
const tableData = ref<ScriptCorpusItem[]>([])
// 搜索区
const search = async () => {
  loading.value = true
  const [err, data] = await to(searchForm.name
    ? scriptCorpusModel.findPriorByName({
      name: searchForm.name,
      scriptId,
    }) : scriptCorpusModel.findPriorList({
      scriptId,
    }))
  tableData.value = (data || []).sort((a, b) => (a.weight || 0) - (b.weight || 0)).map(item => {
    const scriptMultiContents = item.scriptMultiContents || []
    const content = (scriptMultiContents && scriptMultiContents.length) ? scriptMultiContents[0].scriptUnitContents?.reduce((a, b) => ([...a, b.content || '']), [] as string[]) || undefined : undefined
    return {
      ...item,
      content: content,
      matchText: translateCorpusRules(item.semCombineEntity?.satisfySemConditions || []),
      excludeText: translateCorpusRules(item.semCombineEntity?.excludeSemConditions || []),
    }
  })
  loading.value = false
}

// 操作区
const corpusDialogVisible = ref(false)

const corpusData = reactive<ScriptCorpusItem>(new CorpusDataOrigin(scriptId, CorpusTypeEnum['最高优先']))
const edit = (row?: ScriptCorpusItem) => {
  if (row && row.id) {
    Object.assign(corpusData, pickAttrFromObj(row,
      [...corpusTypeOption[corpusData.corpusType!].keys]
    ))
  } else {
    Object.assign(corpusData, new CorpusDataOrigin(scriptId, CorpusTypeEnum['最高优先']))
  }
  corpusDialogVisible.value = true
}

/** 语句打断设置 */
const corpusContentVisible = ref(false)
const editContent = (row: ScriptCorpusItem) => {
  if (row && row.id) {
    Object.assign(corpusData, pickAttrFromObj(row,
      corpusTypeOption[corpusData.corpusType!].keys
    ))
  } else {
    Object.assign(corpusData, new CorpusDataOrigin(scriptId, CorpusTypeEnum['最高优先']))
  }
  corpusContentVisible.value = true
}

/** 删除 */
const delAction = async (id: number) => {
  loading.value = true
  trace({
    page: `话术编辑-功能语料-最高优先语料：删除(${scriptId})`,
    params: { id },
  })
  const [err] = await to(scriptCorpusModel.deleteFuncCorpus({
    corpusId: id
  }))
  !err && ElMessage.success('删除成功')
  search()
  loading.value = false
}
const del = (row: ScriptCorpusItem) => {
  Confirm({ 
    text: `您确定要删除语料【${row.name}】吗?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(() => {
    delAction(row.id as number)
  }).catch(() => {})
}

const sortableDom = ref<Sortable | null>(null)
const initDraggableTable = () => {
  sortableDom.value = Sortable.create(
    document.querySelector('#prior-draggable-boxes') as HTMLElement, {
    animation: 300,
    sort: !isChecked,
    ghostClass: 'ghost-item',
    onEnd: async (evt) => {
      const newIndex = evt.newIndex as number
      const oldIndex = evt.oldIndex as number
      const currRow = tableData.value.splice(oldIndex, 1)[0];
      tableData.value.splice(newIndex, 0, currRow);
      const list = tableData.value.map(item => item.id!) || []
      loading.value = true
      const params = {
        corpusIdList: list,
        scriptId: scriptId,
      }
      trace({
        page: `话术编辑-功能语料-最高优先语料：排序(${scriptId})`,
        params,
      })
      await to(scriptCorpusModel.savePriorByOrder(params))
      tableData.value = []
      await nextTick()
      search()
      await nextTick()
      loading.value = false
    },
  });
}
const masterProcessOptions = ref<{
  id: number,
  name: string,
  headCorpusId: number,
}[]>([])
const intentionTagOptions = ref<LabelItem[]>([])
const intentionLevelOptions = ref<IntentionType[]>([])
const eventValuesOptions = reactive<{[key: string]: EventValueItem}>({})
const init = async () => {
  await scriptStore.getSemanticOptions()
  intentionLevelOptions.value = await scriptStore.getIntentionLevelOptions()
  intentionTagOptions.value = await scriptStore.getIntentionTagOptions()
  masterProcessOptions.value = await scriptStore.getProcessOptions()
  const res = await scriptStore.getEventOptions()
  if (res && res.length > 0) {
    res.map(item => {
      item.eventValuemap && Object.assign(eventValuesOptions, item.eventValuemap)
    })
  }
}
watch(listType, () => {
  listType.value === 'card' && initDraggableTable()
})
const filterEventValueIds = (ids?: number[]) => {
  if (ids && ids.length > 0) {
    const eventTriggerValueArr: string[] = []
    ids && ids?.length > 0 && ids?.map(item => {
      eventValuesOptions[item] && eventTriggerValueArr.push(`${(eventValuesOptions[item].name || '')}(${(eventValuesOptions[item].explanation || '')})`)
    })
    return eventTriggerValueArr.join(',') || ''
  } else {
    return ''
  }
}

onActivated(() => {
  init()
  search()
  listType.value = 'list'
})

onDeactivated(() => {
  sortableDom.value?.destroy()
  sortableDom.value = null
  // @ts-ignore
  masterProcessOptions.value = null
  // @ts-ignore
  intentionTagOptions.value = null
  // @ts-ignore
  intentionLevelOptions.value = null
  // @ts-ignore
  tableData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.priority-container {
  width: 100%;
  padding: 16px 12px 0;
  height: calc(100vh - 240px);
  display: flex;
  flex-direction: column;
  position: relative;
  box-sizing: border-box;
  font-size: 13px;
  :deep(.el-table .caret-wrapper) {
    display: none;
  }
  .span-btn {
    width: 50%;
    height: 100%;
    background-color: inherit;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 2px;
  }
  .active-btn {
    background-color: var(--el-color-primary);
  }
  .el-table {
    font-size: 13px;
    :deep(.cell) {
      padding: 0 8px;
      line-height: 20px;
    }
  }
  .box {
    font-size: 14px;
    line-height: 28px;
    border-radius: 10px;
    padding: 0;
    padding-left: 6px;
    position: relative;
    box-sizing: border-box;
    background-color: #fff;
    border: 2px solid #409eff;
    font-size: var(--el-font-size-base);
    overflow: hidden;
    cursor: pointer;
    height: 40px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .moved-box{
    background-color: #f5dcc8;
    border: 2px solid #f7b077;
  }
  .ghost-item {
    border-style: dashed;
    opacity: 0.2;
  }
}
.item {
  font-size: 14px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 10px;
  span {
    width: 90px
  }
}
</style>
