<template>
  <el-dialog
    v-model="dialogVisible"
    class="silence-dialog tw-max-h-full tw-overflow-y-auto"
    width="480px"
    @close="cancel"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">沉默语料设置</div>
    </template>
    <el-form
      :model="silenceData"
      :rules="rules"
      :disabled="isChecked"
      label-width="110px"
      ref="branchEditRef"
    >
      <el-form-item prop="maxSilenceCount" label="连续触发限制：">
        <InputNumberBox
          v-model:value="silenceData.maxSilenceCount"
          placeholder="连续触发该次数后，执行挂机（1-10次）"
          :min="1"
          :max="10"
          append="次"
        />
      </el-form-item>
      <el-form-item prop="intentionTagId" label="触发标签：">
        <el-select
          v-model="silenceData.intentionTagId"
          clearable
          filterable
          placeholder="请选择触发标签"
          style="width: 100%;"
        >
          <el-option
            v-for="item in scriptStore.intentionTagOptions"
            :key="item.id"
            :label="item.labelName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer" v-if="!isChecked">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive,} from 'vue'
import { ElMessage, } from 'element-plus'
import type { FormInstance, } from 'element-plus'
import { scriptCorpusModel, } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import InputNumberBox from '@/components/InputNumberBox.vue'
import { trace } from '@/utils/trace'
import to from 'await-to-js';

const scriptStore = useScriptStore()
const isChecked = scriptStore.isChecked
const loading = ref(false)
const emits = defineEmits(['update:visible', ])
const props = defineProps<{
  visible: boolean;
  silenceData: { maxSilenceCount: number, scriptId: number, intentionTagId?: number };
}>();
const dialogVisible = ref(false)
const silenceData = reactive(props.silenceData)
watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    Object.assign(silenceData, props.silenceData)
    silenceData.maxSilenceCount = silenceData.maxSilenceCount || 3
  }
})
const rules = {
  maxSilenceCount: { required: true, message: '请输入连续触发限制次数', trigger: 'blur' },
  intentionTagId: { required: true, message: '请选择触发标签', trigger: 'change' },
}
const branchEditRef = ref<FormInstance  | null>(null)
const confirm = async () => {
  branchEditRef.value && branchEditRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const params = {
        intentionTagId: silenceData.intentionTagId,
        maxSilenceCount: silenceData.maxSilenceCount,
        scriptId: silenceData.scriptId,
      }
      await trace({ page: `话术编辑-沉默语料-修改连续触发限制(${scriptStore.id})`, params: params })
      const [err] = await to(scriptCorpusModel.saveSilenceRequirement(params))
      loading.value = false
      if (!err) {
        ElMessage.success('操作成功')
        dialogVisible.value = false
      }
      
    }
  })
}
const cancel = () => {
  branchEditRef.value && branchEditRef.value.clearValidate()
  emits('update:visible', false)
}
</script>

<style lang="postcss" type="text/postcss" scoped>
.silence-dialog {
  .el-input-number .el-input__inner {
    text-align: left;
  }
  .text {
    font-size: 14px;
    line-height: 24px;
    margin-right: 10px;
    text-align: left;
  }
  .el-form {
    color: var(--primary-black-color-600);
    width: 100%;
    padding: 0 12px;
    .el-form-item {
      margin-bottom: 14px;
      &:first-child {
        margin-top: 14px;
      }
    }
    :deep(.el-form-item__label) {
      padding-right: 0;
    }
    :deep(.el-form-item__content) {
      font-size: var(--el-font-size-base);
    }
  }
}
</style>
