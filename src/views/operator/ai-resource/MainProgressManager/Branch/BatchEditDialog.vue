<template>
  <el-dialog
    :model-value="!!dialogVisible"
    width="800px"
    class="dialog-form"
    @close="cancel"
    :close-on-click-modal="false"
    align-center
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">
        {{ props.visible === 'single' ? `编辑【${props.branchData[0]?.branchName || '-'}】分支` : '批量编辑' }}
      </div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="branchData"
        :rules="rules"
        label-width="90px"
        ref="branchEditRef"
      >
        <div v-if="props.visible === 'batch'" class="info-title tw-text-left tw-mt-[14px] tw-mb-[6px] tw-ml-[26px]">
          已选择
          <span class="tw-text-[var(--primary-blue-color)]">【{{props.branchData?.length || 0}}】</span>
          个
          <span class="tw-text-[var(--primary-blue-color)]">【{{ props.branchData[0]?.branchName || '-' }}】</span>
          分支
        </div>
        <el-form-item v-if="props.visible !== 'single'" label="编辑类型：" class="info-title">
          <el-radio-group v-model="branchData.type" class="tw-ml-[6px]">
            <el-radio :label="0">重置条件</el-radio>
            <el-radio :label="1">增加条件</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label-width="0" prop="satisfySemConditions">
          <MultCorpusRuleBox
             v-if="dialogVisible && branchData.semCombineEntity?.satisfySemConditions"
            v-model:data="branchData.semCombineEntity.satisfySemConditions"
            :readonly="isChecked"
            :required="branchData.type === 0"
            title="满足条件"
          />
        </el-form-item>
        <el-form-item label-width="0" prop="excludeSemConditions">
          <MultCorpusRuleBox
            v-if="dialogVisible && branchData.semCombineEntity?.excludeSemConditions"
            v-model:data="branchData.semCombineEntity.excludeSemConditions"
            :readonly="isChecked"
            title="排除条件"
          />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer" v-if="!isChecked">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button type="primary" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, defineAsyncComponent, } from 'vue'
import { CorpusConditionItem } from '@/type/corpus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import type { FormInstance, } from 'element-plus'
import { useScriptStore } from '@/store/script'
import { checkCorpusRules }  from '@/components/corpus/constant'
import { scriptBranchModel, } from '@/api/speech-craft'
import to from 'await-to-js';
import Confirm from '@/components/message-box'
import { ElMessage } from 'element-plus'
import { traceApi } from '@/utils/trace'

const MultCorpusRuleBox = defineAsyncComponent({ loader:() => { return import('@/components/corpus/MultCorpusConditionBox.vue')}})

const scriptStore = useScriptStore()
const isChecked = scriptStore.isChecked
const emits = defineEmits(['confirm', 'update:visible',])
const props = defineProps<{
  visible: 'single' | 'batch' | null;
  branchData: {
    branchId: number,
    corpusId: number,
    branchName: string,
    semCombineEntity?: {
      excludeSemConditions: CorpusConditionItem[],
      satisfySemConditions: CorpusConditionItem[],
    }
  }[];
}>();

const branchData = reactive<{
  type: 0 | 1
  semCombineEntity: {
    excludeSemConditions: CorpusConditionItem[],
    satisfySemConditions: CorpusConditionItem[],
  }
}>({
  type: 0,
  semCombineEntity: {
    excludeSemConditions: [],
    satisfySemConditions: [],
  }
})
const dialogVisible = ref(props.visible)

const rules = {
  satisfySemConditions: [
    { validator: (rule: any, value: any, callback: any) => {
      const errMsg = checkCorpusRules(branchData.semCombineEntity?.satisfySemConditions, branchData.type !== 1)
      return errMsg ? callback(new Error(errMsg)) : callback()
    }, trigger: ['change', 'blur']},
  ],
  excludeSemConditions: [
    { validator: (rule: any, value: any, callback: any) => {
      const errMsg = checkCorpusRules(branchData.semCombineEntity?.excludeSemConditions, false)
      return errMsg ? callback(new Error(errMsg)) : callback()
    }, trigger: ['change', 'blur']},
  ],
}
const branchEditRef = ref<FormInstance  | null>(null)
const loading = ref(false)
const confirm = async () => {
  branchEditRef.value && branchEditRef.value.validate(async (valid) => {
    if (valid) {
      const params = {
        branchCorpusMap: Object.fromEntries(props.branchData.map(item => [item.branchId, item.corpusId])),
        semCombineEntity: branchData.semCombineEntity,
        scriptId: scriptStore.id
      }
      if (branchData.type === 1 && !branchData.semCombineEntity.satisfySemConditions?.length && !branchData.semCombineEntity.excludeSemConditions?.length) {
        return ElMessage.warning('请填写满足条件或排除条件')
      }
      // 批量操作需要二次确认，单个不需要
      if (props.visible === 'batch') {
        const [err1] = await to(Confirm({
          text: `批量修改后无法撤回，您确认修改对象和修改内容准确无误`,
          type: 'warning',
          confirmText: '确认并提交',
          title: '批量编辑确认'
        }))
        if (err1) return
      }
      loading.value = true
      const err = await traceApi(
        `话术编辑-分支管理-${branchData.type === 0 ? '重置条件' : '增加条件'}(${scriptStore.id})`,
        params,
        branchData.type === 0 ? scriptBranchModel.resetBranchesCondition : scriptBranchModel.addBranchesCondition
      )
      loading.value = false
      if (!err) {
        cancel()
        ElMessage.success('操作成功')
        emits('confirm')
      }
    }
  })
}
const cancel = () => {
  branchEditRef.value && branchEditRef.value.clearValidate()
  emits('update:visible', null)
}
watch(() => props.visible, n => {
  dialogVisible.value  = props.visible
  if (n) {
    if (n === 'single') {
      branchData.type = 0
      branchData.semCombineEntity = props.branchData[0].semCombineEntity || {
        excludeSemConditions: [],
        satisfySemConditions: [],
      }
    } else {
      branchData.semCombineEntity = {
        excludeSemConditions: [],
        satisfySemConditions: [],
      }
    }
  } else {
    loading.value = false
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
  :deep(.el-form-item__content) {
    font-size: var(--el-font-size-base);
  }
  .triangle {
    background-color: #fff;
    width: 15px;
    height: 15px;
    position: absolute;
    right: 1px;
    top: 1px;
    -webkit-clip-path: polygon(0 0, 100% 100%, 100% 8%, 92% 0);
    clip-path: polygon(0 0, 100% 100%, 100% 8%, 92% 0);
    .el-icon {
      position: absolute;
      right: -2px;
      top: -2px;
      z-index: 8;
    }
  }
}
</style>
