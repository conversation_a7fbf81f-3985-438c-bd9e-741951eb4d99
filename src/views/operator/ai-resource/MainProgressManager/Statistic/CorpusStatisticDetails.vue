<template>
  <el-drawer v-model="drawerVisible" :with-header="false" @close="close" size="80%" class="corpus-details-drawer">
    <div class="corpus-details-container" v-loading="loading">
      <div class="tw-absolute tw-top-0 tw-left-0 tw-z-10 tw-w-[25vw] tw-rounded-br-[4px]">
        <PercentBox
          v-if="isTableExtend"
          class="tw-bg-white tw-py-[12px] tw-pl-[12px]"
          :title="title"
          canShowDetails
          :tableList="dataList"
          :currentItem="currentItem"
          @show-details="showDetails"
        ></PercentBox>
        <PercentBox
          v-else
          :title="title"
          canShowDetails
          class="tw-bg-white tw-py-[12px] tw-pl-[12px]"
          :tableList="currentItem ? [currentItem] : []"
          :currentItem="currentItem"
          @show-details="showDetails"
        ></PercentBox>
        <div class="tw-flex tw-justify-center tw-bg-transparent">
          <div class="trapezoid" @click="isTableExtend=!isTableExtend">
            <el-icon v-if="isTableExtend" :size="13"><ArrowUpBold/>  </el-icon>
            <el-icon v-else :size="13"><ArrowDownBold/>  </el-icon>
          </div>
        </div>
      </div>
      <div v-if="drawerVisible" class="tw-w-full tw-h-full tw-overflow-hidden tw-relative">
        <AntvX6GraphStatic
          :nodeList="nodeList || []"
          :edgeList="edgeList || []"
          :canvasId="processId"
          @select="selectNode"
        ></AntvX6GraphStatic>
      </div>

      <div v-if="selectedNode && !!selectedNode.corpusId" v-loading="loadingDetails" class="tw-flex tw-flex-col tw-absolute tw-top-0 tw-right-0 tw-z-10 tw-w-[500px] tw-rounded-bl-[4px]">
        <div class="tw-leading-[20px] tw-pt-[16px] tw-pr-[12px] tw-bg-white tw-pl-[16px]">
          <h5 class="tw-text-[14px] tw-font-[600] tw-text-left">{{ selectedNode?.corpusName || '' }}</h5>
          <el-row class="tw-py-[8px]">
            <template v-if="title.includes('挂机')">
              <el-col :span="8" class="tw-text-[13px] tw-text-left">
                <span class="tw-text-[--primary-black-color-400]">语料挂机数：</span>
                <span>{{ selectedNode?.numerator || '-' }}</span>
              </el-col>
              <el-col :span="8" class="tw-text-[13px] tw-text-left">
                <span class="tw-text-[--primary-black-color-400]">节点挂机数：</span>
                <span>{{ selectedNode?.denominator || '-' }}</span>
              </el-col>
              <el-col :span="8" class="tw-text-[13px] tw-text-left">
                <span class="tw-text-[--primary-black-color-400]">挂机分布：</span>
                <span>{{ selectedNode?.denominator ? formatNumber(selectedNode?.numerator/selectedNode?.denominator*100, 2)+'%' || '-' : '0' }}</span>
              </el-col>
            </template>
            <template v-if="title.includes('命中')">
              <el-col :span="8" class="tw-text-[13px] tw-text-left">
                <span class="tw-text-[--primary-black-color-400]">语料命中数：</span>
                <span>{{ selectedNode?.numerator || '-' }}</span>
              </el-col>
              <el-col :span="8" class="tw-text-[13px] tw-text-left">
                <span v-if="selectedNode.corpusType?.includes('MASTER_')" class="tw-text-[--primary-black-color-400]">接通数：</span>
                <span v-else class="tw-text-[--primary-black-color-400]">节点命中数：</span>
                <span>{{ selectedNode?.denominator || '-' }}</span>
              </el-col>
              <el-col :span="8" class="tw-text-[13px] tw-text-left">
                <span class="tw-text-[--primary-black-color-400]">命中分布：</span>
                <span>{{ selectedNode?.denominator ? formatNumber(selectedNode?.numerator/selectedNode?.denominator*100, 2)+'%' || '-' : '0' }}</span>
              </el-col>
            </template>
          </el-row>
        </div>
        <el-table
          v-if="isDetailExtend"
          :data="nodeDetailList"
          class="tw-w-full tw-pb-[8px]"
          max-height="300px"
          row-key="id"
          :header-cell-style="{background:'#f7f8fa', 'border-top': '1px solid #EBEEF5', color: 'var(--primary-black-color-500)'}"
        >
          <el-table-column property="corpusName" label="来源" align="left"  min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
          <el-table-column v-if="title.includes('挂机')" property="hitNum" label="通话数" align="left" sortable min-width="80" :formatter="formatterEmptyData" show-overflow-tooltip>
            <template #header="{ column }">
            <div class="tw-flex tw-items-center tw-justify-start">
              <span>{{column.label}}</span>
              <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
                <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
                <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
              </div>
            </div>
          </template>
          </el-table-column>
          <el-table-column v-if="title.includes('挂机')" property="hangupNum" label="挂机数" align="left" sortable min-width="80" :formatter="formatterEmptyData" show-overflow-tooltip>
            <template #header="{ column }">
              <div class="tw-flex tw-items-center tw-justify-start">
                <span>{{column.label}}</span>
                <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
                  <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
                  <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="title.includes('挂机')" property="corpusName" label="挂机率" align="left"  min-width="80" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.hitNum ? formatNumber(row.hangupNum/row.hitNum*100, 2)+'%' || '-' : '0' }}
            </template>
          </el-table-column>
          <el-table-column v-if="title.includes('挂机')" property="corpusName" label="挂机分布" align="left" min-width="80" show-overflow-tooltip>
            <template #default="{ row }">
              {{ selectedNode?.numerator ? formatNumber(row.hangupNum/selectedNode?.numerator*100, 2)+'%' || '-' : '0' }}
            </template>
          </el-table-column>
          <el-table-column v-if="title.includes('命中')" property="hitNum" label="命中数" align="left" sortable min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip>
            <template #header="{ column }">
              <div class="tw-flex tw-items-center tw-justify-start">
                <span>{{column.label}}</span>
                <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
                  <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
                  <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="title.includes('命中')" label="命中分布" align="left" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              {{ selectedNode?.numerator ? formatNumber(row.hitNum/selectedNode?.numerator*100, 2)+'%' || '-' : '0' }}
            </template>
          </el-table-column>
        </el-table>
        <div class="tw-flex tw-justify-center">
          <div class="trapezoid" @click="isDetailExtend=!isDetailExtend">
            <el-icon v-if="isDetailExtend" :size="13"><ArrowUpBold/>  </el-icon>
            <el-icon v-else :size="13"><ArrowDownBold/>  </el-icon>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import AntvX6GraphStatic from '@/components/x6/AntvX6GraphStatic.vue'
import { ref, computed, reactive, onMounted, watch} from 'vue'
import { CorpusTypeEnum, ScriptInfo, EventValueItem, CorpusStatisticDetails, ScriptBranch, CanvasCorpus, CanvasBranch, ScriptStatisticParams, ScriptStatisticItem} from '@/type/speech-craft'
import { scriptCanvasModel, scriptEventModel, } from '@/api/speech-craft'
import { Node, Edge, Cell  } from '@antv/x6'
import PercentBox from './PercentBox.vue'
import { CaretTop, CaretBottom, ArrowUpBold, ArrowDownBold, } from '@element-plus/icons-vue'
import { EdgeItem, PortItem } from '@/type/common'
import { useScriptStore } from '@/store/script'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { scriptStatisticsModel } from '@/api/speech-craft'
import { updateCanvasInfoById } from '@/views/operator/ai-resource/MainProgressManager/MainProcess/constant'
import { formatterEmptyData, formatNumber } from '@/utils/utils'
const globalStore = useGlobalStore()
const loading = ref(false)

const scriptStore = useScriptStore()
const editId = scriptStore.id

const props = defineProps<{
  title: string,
  dataList: ScriptStatisticItem[],
  currentItem: ScriptStatisticItem,
  visible: boolean,
  scriptId: number,
  hangupType?: string
}>();
const scriptId = computed(() => { return props.scriptId })
const dataList = computed(() => { return props.dataList })
const title = computed(() => { return props.title })
const currentItem = ref<ScriptStatisticItem | null>(null)
const emits = defineEmits(['update:visible'])
const nodeList = ref<Node.Metadata[] | null>([])
const edgeList = ref<EdgeItem[] | null>([])
const statisticList = ref<ScriptStatisticItem[]>([])
const processId = ref<number>()
const drawerVisible = ref(props.visible || false)
const isTableExtend = ref(true) // 是否折叠列表，默认不折叠（true表示不折叠）
const isDetailExtend = ref(true) // 是否折叠node详情，默认不折叠（true表示不折叠）

const handleProcessChange = async (row: ScriptStatisticItem) => {
  selectedNode.value = {
    canvasId: null, canvasName: '', corpusId: -1,
    corpusType: CorpusTypeEnum['主动流程-普通语料'],
    corpusName: '', denominator: 0, numerator: 0
  }
  nodeDetailList.value = []
  loading.value = true
  try {
    switch(props.title) {
      case '命中分布': {
        statisticList.value = await scriptStatisticsModel.getHitDistributionDetails({
          scriptId: scriptId.value,
          headCorpusId: row.corpusId,
          type: row.corpusType
        }) || [];
        break;
      };
      case '挂机率': {
        statisticList.value = await scriptStatisticsModel.getHangupRateDetails({
          scriptId: scriptId.value,
          hangupType: props.hangupType,
          headCorpusId: row.corpusId,
          type: row.corpusType
        }) || [];
        break;
      };
      case '挂机分布': {
        statisticList.value = await scriptStatisticsModel.getHangupDistributionDetails({
          scriptId: scriptId.value,
          headCorpusId: row.corpusId,
          type: row.corpusType,
          hangupType: props.hangupType,
        }) || [];
        break;
      };
    }
    const statisticObj: {[key: string]: any} = {}
    statisticList.value.length>0 && statisticList.value.map(item => {
      if (!item.corpusId) return
      statisticObj[item.corpusId] = item.numerator*item.denominator>0 ? formatNumber(item.numerator/item.denominator * 100, 2) + '%' : '0'
    })
    const data = await updateCanvasInfoById(row.canvasId!, editId, undefined, statisticObj)
    nodeList.value = data.nodeList
    edgeList.value = data.edgeList
    processId.value = row.corpusId
  } catch (err) {
    console.log(err)
  } finally {
    loading.value = false
  }
}
    
const selectedNode = ref<ScriptStatisticItem | undefined>(undefined)
const nodeDetailList = ref<CorpusStatisticDetails[]>([])

const loadingDetails = ref(false)
const selectNode = async (node: Node.Metadata) => {
  loadingDetails.value = true
  selectedNode.value = statisticList.value.find(item => item.corpusId === node.data.corpusId) || {
    canvasId: null, canvasName: '', corpusId: -1,
    corpusType: CorpusTypeEnum['主动流程-普通语料'],
    corpusName: '', denominator: 0, numerator: 0
  }
  const params: ScriptStatisticParams = {
    corpusId: node.data.corpusId,
    type: props.hangupType,
    scriptId: scriptId.value,
  }
  nodeDetailList.value = title.value === '命中分布' ? await scriptStatisticsModel.getCorpusHitDetail(params)
  : await scriptStatisticsModel.getCorpusHangupDetail(params)
  loadingDetails.value = false
}
const showDetails = (item: ScriptStatisticItem) => {
  currentItem.value = item
  currentItem.value && handleProcessChange(currentItem.value)
}
const close = () => {
  drawerVisible.value = false
  emits('update:visible', false)
}
// 执行区
const eventValuesOptions = reactive<{[key: string]: EventValueItem}>({})

watch(() => props.visible, async n => {
  drawerVisible.value = n
  if(n) {
    currentItem.value = props.currentItem
    const res = await scriptStore.getEventOptions()
    if (res && res.length > 0) {
      res.map(item => {
        item.eventValuemap && Object.assign(eventValuesOptions, item.eventValuemap)
      })
    }
    !!currentItem.value && handleProcessChange(currentItem.value)
  }
})
</script>

<style scoped lang="postcss" type="text/postcss">
.corpus-details-container {
  width: 100%;
  box-sizing: border-box;
  color: var(--primary-black-color-600);
  height: 100%;
  position: relative;
  .el-table {
    font-size: 13px;
    color: var(--primary-black-color-500);
    :deep(.el-table__cell) {
      padding: 4px 0;
    }
    :deep(.caret-wrapper) {
      display: none;
    }
  }
  .trapezoid {
    -webkit-clip-path: polygon(0 0, 100% 0, 82% 100%, 18% 100%);
    clip-path: polygon(0 0, 100% 0, 82% 100%, 18% 100%);
    background-color: #fff;
    width: 92px;
    height: 15px;
    cursor: pointer;
    padding-bottom: 3px;
    display: flex;
    justify-content: center;
    align-items: center;
    align-self: center;
  }
}
</style>