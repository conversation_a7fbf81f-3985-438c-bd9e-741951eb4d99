<template>
  <el-scrollbar class="intention-statistic-container">
      <div class="content">
        <div v-loading="loadingCategory">
          <PieChart :data="categoryChartData" title="意向分类" yName="分类占比"/>
        </div>
        <div v-loading="loadingBar">
          <BarCategoryChart
            v-if="!loadingBar"
            :data="corpusChartData"
            :legends="['A', 'B', 'C']"
            xName="语料"
            yName="占比"
            title="意向分类分布"
            :scrollSpan="5"
          />
        </div>
      </div>

      <div class="title">意向详情</div>
      <div class="tw-grid tw-grid-cols-4 tw-gap-1 tw-mb-[12px]">
        <el-input
          v-model="searchFormData.name"
          clearable
          placeholder="请输入语料名称（20字以内）"
          maxlength="20"
        >
        </el-input>
        <el-select v-model.trim="searchFormData.corpusType" placeholder="请选语料类型" clearable>
          <el-option
            v-for="item in corpusTypeOption"
            :key="item.name"
            :value="item.value"
            :label="item.name"
          />
        </el-select>
        <el-select v-model="searchFormData.intentionType" clearable placeholder="请选择客户意向分类">
          <el-option
            v-for="item in intentionLevelOptions"
            :key="item.intentionType"
            :label="item.intentionType + ' - ' + item.intentionName"
            :value="item.intentionType"
          />
        </el-select>
        <div class="tw-flex">
          <el-button type="primary" @click="clearSearchForm" link><el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>重置</el-button>
          <el-button type="primary" @click="searchDetailsAction" link><el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>筛选</el-button>
        </div>
      </div>
      
      <el-table
        :data="tableTempData"
        style="width: 100%"
        class="tw-flex-grow"
        height="100%"
        row-key="id"
        :header-cell-style="tableHeaderStyle"
      >
        <el-table-column property="name" label="语料名称" align="left" :formatter="formatterEmptyData" min-width="200"></el-table-column>
        <el-table-column property="corpusType" label="语料类型" align="left" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.corpusType ? corpusTypeOption[row.corpusType as CorpusTypeEnum]?.name : '-' }}
          </template>
        </el-table-column>
        <el-table-column property="numerator" align="left" label="个数" min-width="120">
          <template #default="{ row, }">
            {{ formatNumber(row.numerator) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="占比" min-width="120">
          <template #default="{ row, }">
            {{ (row.denominator && row.numerator)? row.numerator / row.denominator * 100 + '%' : 0 }}
          </template>
        </el-table-column>
        <template #empty>
          <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
        </template>
      </el-table>
      <PaginationBox
        :pageSize="pageSize"
        :currentPage="currentPage"
        :total="total"
        @search="searchDetailsAction"
        @update="updatePage"
      >
      </PaginationBox>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, watch, computed } from 'vue'
import PaginationBox from '@/components/PaginationBox.vue'
import { ElMessage, } from 'element-plus'
import { formatNumber, formatterEmptyData, } from '@/utils/utils'
import { router } from "@/router";
import { IntentionType, } from '@/type/IntentionType'
import BarCategoryChart from '@/components/charts/BarCategoryChart.vue'
import PieChart from '@/components/charts/PieChart.vue'
import { scriptStatisticsModel } from '@/api/speech-craft'
import { corpusTypeOption, ScriptStatisticItem, CorpusTypeEnum, } from '@/type/speech-craft'
import { tableHeaderStyle } from '@/assets/js/constant'
import { useScriptStore } from '@/store/script'
/** 入参props */
const props = defineProps<{
  scriptId: number,
}>()
/** 全局变量 */
const scriptStore = useScriptStore() // 话术信息

/** 意向分类 饼图 开始 */
const loadingCategory = ref(false)
// 意向分类，图表数据
const categoryChartData = ref<{
  name: string,
  value: number,
  num: number
}[]>([])
// 获取意向分类饼图
const searchCategoryAction = async () => {
  try {
    loadingCategory.value = true
    // categoryChartData.value = await scriptStatisticsModel.getIntentionCategory({
    //   scriptId: props.scriptId!
    // }) as { name: string, value: number, num: number}[] || []
    categoryChartData.value = [
      {name: 'A', value: 12, num: 120},
      {name: 'C', value: 22, num: 220},
      {name: 'B', value: 42, num: 420},
      {name: 'E', value: 24, num: 240},
    ]
  } catch (err) {
    ElMessage({
      message: '获取意向分类失败',
      type: 'error',
    })
  } finally {
    loadingCategory.value = false
  }
}
/** 意向分类 饼图 结束 */

/** 意向分类 柱状图 开始 */
const loadingBar = ref(false)
// 意向分类，图表数据
const corpusChartData = ref<{
  name: string,
  value: {[key: string]: number},
}[]>([])
// 获取意向分类饼图
const searchIntentionBarAction = async () => {
  try {
    loadingBar.value = true
    // corpusChartData.value = await scriptStatisticsModel.getIntentionCategory({
    //   scriptId: props.scriptId!
    // }) as { name: string, value: number[]}[] || []
    corpusChartData.value = [
      {name: 'sfs', value: {A: 12, B: 13, C: 14}},
      {name: '1243242', value: {A: 1, B: 13, C: 34}},
      {name: '士大夫但是', value: {A: 22, B: 43, C: 44}},
      {name: 's1fs', value: {A: 14, B: 6, C: 24}},
      {name: 's的四个地方士2大夫但是士2大夫但是士2大夫但是士2大夫但是', value:{A: 42, B: 3, C: 4}},
      {name: '士2大夫但是', value: {A: 7, B: 43, C: 21}},
      {name: '士2大夫dfg但是', value: {A: 32, B: 28, C: 16}},
    ]
  } catch (err) {
    ElMessage({
      message: '获取柱状图失败',
      type: 'error',
    })
  } finally {
    loadingBar.value = false
  }
}
/** 意向分类 柱状图 结束 */

/** 意向详情列表 开始 */ 
// 变量
type SearchForm = {
  name: string
  corpusType?: CorpusTypeEnum
  intentionType?: string
}
class SearchFormOrigin {
  name = ''
  corpusType = undefined
  intentionType = undefined
}
const searchFormData = reactive<SearchForm>(new SearchFormOrigin()) // 搜索内容
const loadingDetails = ref(false)
const tableData = ref<ScriptStatisticItem[]>([]) // 列表全部内容
const intentionLevelOptions = ref<IntentionType[]>([]) // 意向分类 选项
// 列表当前内容，分页后内容
const tableTempData = computed(() => tableData.value?.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value) || [])
// 分页
const pageSizeList = [20, 100, 200]
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(pageSizeList[0])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  searchDetailsAction()
}
// 重置意向详情筛选选项
const clearSearchForm = () => {
  Object.assign(searchFormData, new SearchFormOrigin())
  ElMessage({
    type: 'success',
    message: '重置成功'
  })
}
// 获取意向详情列表
const searchDetailsAction = async () => {
  try {
    loadingDetails.value = true
    tableData.value = await scriptStatisticsModel.getIntentionDetail(searchFormData) || []
    total.value = tableData.value?.length || 0
  } catch (err) {
    ElMessage({
      message: '获取意向详情列表失败',
      type: 'error',
    })
  } finally {
    loadingDetails.value = false
  }
}
/** 意向详情列表 结束 */

// 执行区
const init = async () => {
  searchCategoryAction()
  searchIntentionBarAction()
  // searchDetailsAction()
  intentionLevelOptions.value = await scriptStore.getIntentionLevelOptions()
}
// watch
watch([() => props.scriptId], () => {
  init()
}, { deep: true, immediate: true })


</script>

<style scoped lang="postcss" type="text/postcss">
.intention-statistic-container {
  width: 100%;
  position: relative;
  box-sizing: border-box;
  overflow-y: auto;
  height: calc(100vh - 230px);
  display: flex;
  padding: 16px;
  font-size: var(--el-font-size-base);
  flex-direction: column;
  .el-tabs :deep(.el-tabs__item){
    padding: 10px auto;
    height: 60px;
    line-height: 60px;
    width: 100px;
  }
  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
    border: none;
    background-color: #e5e7eb;
  }
  :deep(.el-tabs__nav-prev), :deep(.el-tabs__nav-next) {
    line-height: 60px;
    font-size: 16px;
  }
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
  .title {
    display: flex;
    font-size: 16px;
    font-weight: 600;
    line-height: 40px;
    margin-bottom: 12px;
    flex-grow: 0;
    flex-shrink: 0;
  }
  .content {
    flex-grow: 1;
    flex-shrink: 0;
    height: 550px;
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    column-gap: 16px;
    overflow: hidden;
    width: 100%;
    background-color: #fff;
    .el-scrollbar {
      border-radius: 4px;
      background-color: #F9FAFC;
      border: 1px solid #F9FAFC;
      padding: 16px;
      :deep(.el-scrollbar__bar.is-vertical) {
        top: 18px;
      }
      &:hover {
        border-color: var(--primary-black-color-300);
      }
    }
    
  }
}

</style>