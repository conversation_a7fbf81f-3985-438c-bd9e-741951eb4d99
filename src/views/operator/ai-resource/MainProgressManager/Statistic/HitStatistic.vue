<template>
  <div class="hit-statistic-container">
    <!-- 主动流程 -->
    <div class="content">
      <el-scrollbar>
        <PercentBox 
          title="主动流程命中分布" 
          :tableList="masterTableList"
          canShowDetails 
          tips="命中分布：以第一个主动流程的第一个语料触发次数作为分母（即接通电话数），每一个主流程节点的第一个语料触发次数作为分子。以触发漏斗的形式，统计不同主动流程节点实际被命中的概率；"
          @show-details="row => showDetails(row, masterTableList)"
        ></PercentBox>
      </el-scrollbar>
      <el-scrollbar>
        <PercentBox 
          title="核心语义命中"
          showNumber
          :tableList="semanticTableList" 
        >
          <template v-slot:default>
            <el-radio-group v-model="isUsedSemantic" size="small" class="tw-mx-1" @change="searchSemantic">
              <el-radio-button :label="true">话术内</el-radio-button>
              <el-radio-button :label="false">话术外</el-radio-button>
            </el-radio-group>
          </template>
        </PercentBox>
      </el-scrollbar>
    </div>

    <!-- 知识库和功能话术 -->
    <div class="title">知识库和功能话术</div>
    <div class="tw-flex tw-justify-between tw-mb-[8px]">
      <span class="tw-flex tw-items-center">
        <span class="tw-w-[60px]">筛选：</span>
        <el-checkbox-group v-model="corpusTypes" class="tw-flex tw-justify-start">
          <el-checkbox v-for="item in corpusTypeList" :key="item.value" :label="item.value">{{ item.name }}</el-checkbox>
        </el-checkbox-group>
      </span>
      <span class="tw-flex  tw-self-start">
        <el-input
          v-model="name"
          style="width:250px"
          placeholder="请输入语料名称（20字以内）"
          maxlength="20"
          clearable
          @keyup.enter="searchOther"
          @input="(val: string) => !val && searchOther()"
        >
        </el-input>
        <el-button type="primary" link class="tw-ml-1" @click="searchOther">
          <el-icon :size="16" class="tw-mr-[4px]"><SvgIcon name="search"></SvgIcon></el-icon>
            搜索
        </el-button>
      </span>
    </div>
    <div class="content">
      <el-scrollbar>
        <PercentBox
          title="命中分布"
          tips="命中分布：该节点命中次数/接通电话数"
          :tableList="otherDistributionTableList"
          needType
          canShowDetails
          @show-details="row => showDetails(row, otherDistributionTableList)"
        ></PercentBox>
      </el-scrollbar>
      <el-scrollbar>
        <PercentBox title="命中占比" :tableList="otherRateTableList" tips="命中占比：该节点命中次数/知识库和功能话术的命中之和"></PercentBox>
      </el-scrollbar>
    </div>

    <!-- 标签和高级规则 -->
    <div class="title">标签和高级规则</div>
    <div class="tw-flex tw-justify-between tw-mb-1">
      <span class="tw-flex">
        <el-input
          v-model="ruleName"
          style="width:250px"
          placeholder="请输入名称（20字以内）"
          maxlength="20"
          clearable
          @keyup.enter="searchRulesAndLabels()"
          @clear="searchRulesAndLabels()"
        >
        </el-input>
        <el-button type="primary" link class="tw-ml-1" @click="searchRulesAndLabels">
          <el-icon :size="16" class="tw-mr-[4px]"><SvgIcon name="search"></SvgIcon></el-icon>
            搜索
        </el-button>
      </span>
    </div>
    <div class="content">
      <el-scrollbar>
        <PercentBox
          title="高级规则命中分布"
          tips="命中分布：该节点命中次数/接通电话数"
          :tableList="advanceRulesDistributionTableList"
        ></PercentBox>
      </el-scrollbar>
      <el-scrollbar>
        <PercentBox
          title="标签命中分布"
          tips="命中分布：该节点命中次数/接通电话数"
          :tableList="labelsDistributionTableList"
        ></PercentBox>
      </el-scrollbar>
    </div>

    <CorpusStatisticDetails
      v-model:visible="detailVisible[0]"
      :dataList="currentList"
      :scriptId="props.scriptId"
      title="命中分布"
      :currentItem="currentItem"
    >
    </CorpusStatisticDetails>
    <SourceStatisticDetails
      v-model:visible="detailVisible[1]"
      :scriptId="props.scriptId"
      :currentItem="currentItem"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, reactive, computed } from 'vue'
import CorpusStatisticDetails from './CorpusStatisticDetails.vue'
import PercentBox from './PercentBox.vue'
import SourceStatisticDetails from './SourceStatisticDetails.vue'
import { scriptStatisticsModel } from '@/api/speech-craft'
import { ScriptStatisticParams, ScriptStatisticItem, CorpusTypeEnum, } from '@/type/speech-craft'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const props = defineProps<{
  scriptId: number,
}>()
// 主动流程-命中
const masterTableList = ref<ScriptStatisticItem[]>([])
const searchMaster = async () => {
  loading.value = true
  const params: ScriptStatisticParams = { scriptId: props.scriptId, type: 'master'}
  const data = await scriptStatisticsModel.getHitDistributionList(params) || []
  masterTableList.value = data.sort((a, b) => (a.weight || 0) - (b.weight || 0))
  loading.value = false
}

// 核心语义
const isUsedSemantic = ref(true)
const semanticTableList = ref<ScriptStatisticItem[]>([])
const searchSemantic = async () => {
  loading.value = true
  const data = await scriptStatisticsModel.getHitSemanticDistributionList({scriptId: props.scriptId,}) || []
  let maxHitNum = 1
  semanticTableList.value = (data || []).sort((a, b) => (a.hitNum || 0) - (b.hitNum || 0)).flatMap((item, index) => {
    if (index === 0) {
      maxHitNum = item.hitNum
    }
    if (item.isUsedSemantic === isUsedSemantic.value) {
      return {
        name: item.semanticName,
        denominator: maxHitNum,
        numerator: item.hitNum,
      }
    } else {
      return []
    }
  })
  loading.value = false
}

// 非主动流程-命中分布、命中率
// 筛选参数
const corpusTypeList = ref([
  {name: '基本问答', value: CorpusTypeEnum['基本问答']},
  {name: '深层沟通', value: CorpusTypeEnum['深层沟通-普通语料']},
  {name: '沉默', value: CorpusTypeEnum['沉默语料']},
  {name: '重复', value: CorpusTypeEnum['重复语料']},
  {name: '最高优先', value: CorpusTypeEnum['最高优先']},
  {name: '打断垫句', value: CorpusTypeEnum['打断垫句']},
  {name: '续播垫句', value: CorpusTypeEnum['续播垫句']},
  {name: '承接语料', value: CorpusTypeEnum['承接语料']},
])
const corpusTypes = ref<CorpusTypeEnum[]>(corpusTypeList.value.map(item => item.value))
// 列表数据
const name = ref('')
const otherDistributionTableList = ref<ScriptStatisticItem[]>([])
const otherRateTableList = ref<ScriptStatisticItem[]>([])
const searchOther = async () => {
  loading.value = true
  const params: ScriptStatisticParams = {
    scriptId: props.scriptId,
    corpusTypes: corpusTypes.value,
    type: 'other',
  }
  const data1 = await scriptStatisticsModel.getHitDistributionList(params) || []
  otherDistributionTableList.value = data1?.filter(item => !!item.numerator && (!name.value.trim() || (item.corpusName?.includes(name.value) || item.canvasName?.includes(name.value)))).sort((a, b) => (a.weight || 0) - (b.weight || 0)) || []
  otherRateTableList.value = data1?.filter(item => !!item.numerator && (!name.value.trim() || (item.corpusName?.includes(name.value) || item.canvasName?.includes(name.value)))).sort((a, b) => (a.weight || 0) - (b.weight || 0)) || []
  loading.value = false
}

// 高级规则-命中分布
const ruleName = ref('')
const advanceRulesDistributionTableList = ref<ScriptStatisticItem[]>([])
const searchAdvanceRules = async () => {
  loading.value = true
  const data = await scriptStatisticsModel.getHitAdvanceDistributionList({scriptId: props.scriptId,}) || []
  advanceRulesDistributionTableList.value = data?.filter(item => !!item.numerator && (!ruleName.value.trim() || item.name?.includes(ruleName.value))) || []
  loading.value = false
}

// 标签-命中分布
const labelsDistributionTableList = ref<ScriptStatisticItem[]>([])
const searchLabels = async () => {
  loading.value = true
  const data = await scriptStatisticsModel.getHitIntentionLabelDistributionList({scriptId: props.scriptId,}) || []
  labelsDistributionTableList.value = data?.filter(item => !!item.numerator && (!ruleName.value.trim() || item.name?.includes(ruleName.value))) || []
  loading.value = false
}
const searchRulesAndLabels = () => {
  searchAdvanceRules()
  searchLabels()
}

// 当前选中进入详情
class OriginStatisticItem {
  canvasId = null
  canvasName = ''
  corpusId = -1
  corpusType = CorpusTypeEnum['主动流程-普通语料']
  corpusName = ''
  denominator = 0
  numerator = 0
}
const currentItem = reactive<ScriptStatisticItem>(new OriginStatisticItem())
const currentList = ref<ScriptStatisticItem[]>([])
const detailVisible = ref([false, false])
const showDetails = (row: ScriptStatisticItem, dataList: ScriptStatisticItem[]) => {
  Object.assign(currentItem, row)
  detailVisible.value = [false, false]
  if (row.canvasId) {
    detailVisible.value[0] = true
  } else {
    detailVisible.value[1] = true
  }
  // 只将存在画布且画布存在头节点的列表带入
  currentList.value = dataList?.filter(item => item.canvasId && item.corpusId) || []
}

// 执行区
watch(() => props.scriptId, () => {
  searchMaster()
  searchOther()
  searchRulesAndLabels()
  searchSemantic()
}, {deep: true, immediate: true})
watch(corpusTypes, () => {
  searchOther()
}, {deep: true,})
</script>

<style scoped lang="postcss" type="text/postcss">
.hit-statistic-container {
  width: 100%;
  position: relative;
  box-sizing: border-box;
  overflow-y: auto;
  height: calc(100vh - 230px);
  padding: 16px;
  display: flex;
  font-size: var(--el-font-size-base);
  flex-direction: column;
  .el-tabs :deep(.el-tabs__item){
    padding: 10px auto;
    height: 60px;
    line-height: 60px;
    width: 100px;
  }
  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
    border: none;
    background-color: #e5e7eb;
  }
  :deep(.el-tabs__nav-prev), :deep(.el-tabs__nav-next) {
    line-height: 60px;
    font-size: 16px;
  }
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
  .title {
    display: flex;
    font-size: 16px;
    font-weight: 600;
    line-height: 40px;
    flex-grow: 0;
    flex-shrink: 0;
  }
  .content {
    flex-grow: 1;
    flex-shrink: 0;
    height: 270px;
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    column-gap: 16px;
    width: 100%;
    background-color: #fff;
    .el-scrollbar {
      border-radius: 4px;
      background-color: #F9FAFC;
      border: 1px solid #F9FAFC;
      padding: 16px;
      :deep(.el-scrollbar__bar.is-vertical) {
        top: 18px;
      }
      &:hover {
        border-color: var(--primary-black-color-300);
      }
    }
    
  }
}

</style>