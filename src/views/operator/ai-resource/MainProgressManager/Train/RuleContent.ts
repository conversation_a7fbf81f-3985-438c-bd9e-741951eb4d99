import { AdvancedRulesItem } from '@/type/IntentionType'
import { scriptCorpusModel, scriptIntentionModel } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import { storeToRefs } from 'pinia'
import { ScriptCorpusItem } from '@/type/speech-craft'

/**
 * 解析高级规则内容
 */
export const parseRule = async (str: string) => {
  const store = useScriptStore()
  const { id: scriptId } = storeToRefs(store)

  // 将CSV格式的规则ID字符串转成列表
  const tempIdList: number[] = !str ? [] : str.split(',').map(item => parseInt(item))
  // 去重
  const tempIdSet = new Set(tempIdList)
  // 去除非数字
  tempIdSet.delete(NaN)
  // 还原成数组
  const ruleIdList: number[] = Array.from(tempIdSet)
  // console.log('ruleIdList', ruleIdList)

  // 获取当前话术的高级规则列表
  const scriptRuleList = <AdvancedRulesItem[]>await scriptIntentionModel.findRulesList({
    scriptId: scriptId.value
  })
  // 可能存在旧规则，无法找到，则展示id(未知规则)
  const resultList = ruleIdList.map(item => {
    const row = scriptRuleList.find(item1 => item == item1.id)
    return row ? {
      order: row?.order,
      ruleName: row?.ruleName,
    } : {
      order: item,
      ruleName: item + '(未知规则)',
    }
  })

  // 返回结果列表
  // console.log('resultList', resultList)
  return resultList ?? []
}
