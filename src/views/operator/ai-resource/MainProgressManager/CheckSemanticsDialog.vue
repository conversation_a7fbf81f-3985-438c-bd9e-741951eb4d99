<template>
  <el-dialog
    :model-value="props.visible"
    width="600px"
    class="semantic-dialog"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        语义检查
      </div>
    </template>

    <div class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="top"
          label-width="90px"
        >
          <el-form-item :label="`已使用语义（${selectedSemanticsList?.length || 0}）：`">
            <el-scrollbar max-height="30vh">
              {{ selectedSemanticsStr }}
            </el-scrollbar>
          </el-form-item>

          <el-form-item :label="`未使用语义（${unusedSemanticsList?.length || 0}）：`">
            <el-scrollbar max-height="30vh">
              {{ unusedSemanticsStr }}
            </el-scrollbar>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button type="primary" :icon="CloseBold" @click="onClickCancel">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { CloseBold } from '@element-plus/icons-vue'
import { ElMessage, FormRules } from 'element-plus'
import { Throttle } from '@/utils/utils'
import { scriptCoreSemanticModel } from '@/api/speech-craft'
import to from 'await-to-js'
import { AiSemantics, SemanticsCheckItem, SemanticsCheckParam } from '@/type/core-semantic'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
  secondIndustryId: number,
  scriptId: number,
}>(), {
  visible: false,
  secondIndustryId: 0,
  scriptId: 0,
})
const emits = defineEmits([
  'update:visible',
])

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = () => ({
  text: '',
})
// 表单数据
const form = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({})

/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 语义列表 开始 ----------------------------------------

// 语义列表，正在加载
const loadingSemanticsList = ref<boolean>(false)
// 语义列表，加载节流锁
const throttleSemanticsList = new Throttle(loadingSemanticsList)
// 已使用语义列表
const selectedSemanticsList = ref<AiSemantics[]>([])
// 已使用语义列表，格式化展示文本
const selectedSemanticsStr = computed<string>(() => {
  if (selectedSemanticsList.value.length) {
    return selectedSemanticsList.value.map((item) => item.semantic).join('、')
  }
  return '暂无数据'
})
// 未使用语义列表
const unusedSemanticsList = ref<AiSemantics[]>([])
// 未使用语义列表，格式化展示文本
const unusedSemanticsStr = computed<string>(() => {
  if (unusedSemanticsList.value.length) {
    return unusedSemanticsList.value.map((item) => item.semantic).join('、')
  }
  return '暂无数据'
})

/**
 * 更新语义列表
 */
const updateSemanticsList = async () => {
  // 提交节流锁上锁
  if (throttleSemanticsList.check()) {
    return
  }
  throttleSemanticsList.lock()

  // 处理参数
  const params: SemanticsCheckParam = {
    secondIndustryId: props.secondIndustryId,
    scriptId: props.scriptId,
  }

  // 请求接口
  const [err, res] = <[any, SemanticsCheckItem]>await to(scriptCoreSemanticModel.checkSemantic(params))

  // 返回失败结果
  if (err) {
    selectedSemanticsList.value = []
    unusedSemanticsList.value = []
    ElMessage({
      message: '检查语义失败',
      type: 'error',
    })
  }

  // 返回成功结果
  if (res) {
    selectedSemanticsList.value = res?.usedSemanticList?.length ? res?.usedSemanticList : []
    unusedSemanticsList.value = res?.unUsedSemanticList?.length ? res?.unUsedSemanticList : []
  }
  // 节流锁解锁
  throttleSemanticsList.unlock()
  return
}

// ---------------------------------------- 语义列表 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  // 每次显示弹窗时
  if (val) {
    // 重置表单
    resetForm()
    // 更新语义列表
    await updateSemanticsList()
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
