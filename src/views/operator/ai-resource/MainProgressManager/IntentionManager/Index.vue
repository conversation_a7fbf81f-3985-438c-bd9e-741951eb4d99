<template>
    <div class="intention-container">
      <el-tabs v-model="active">
        <el-tab-pane label="分类" :name="1"></el-tab-pane>
        <el-tab-pane label="标签" :name="2"></el-tab-pane>
        <el-tab-pane label="高级规则" :name="3"></el-tab-pane>
        <el-tab-pane label="最终意向" :name="4"></el-tab-pane>
      </el-tabs>
      <keep-alive>
        <Category v-if="active===1"/>
        <LabelManager v-else-if="active===2"/>
        <AdvancedRules v-else-if="active===3"/>
        <FinalIntentionRules v-else/>
      </keep-alive>
    </div>
</template>

<script lang="ts" setup>
import { ref, onActivated, defineAsyncComponent } from 'vue'
import { useScriptStore } from '@/store/script'

const AdvancedRules = defineAsyncComponent({loader: () => import('./AdvancedRules/Index.vue')})
const LabelManager = defineAsyncComponent({loader: () => import('./LabelManager.vue')})
const Category = defineAsyncComponent({loader: () => import('./Category.vue')})
const FinalIntentionRules = defineAsyncComponent({loader: () => import('./FinalIntentionRules/Index.vue')})

const active = ref(1)
// 执行区
onActivated(async () => {
  // 更新高级规则、最终意向需要使用的数据
  const scriptStore = useScriptStore()
  await scriptStore.getQaAndPriorOptions()
  await scriptStore.getIntentionLevelOptions(true)
  await scriptStore.getIntentionTagOptions(true)
  await scriptStore.getProcessOptions(true)
  await scriptStore.getSemanticOptions(true)
  await scriptStore.getSemanticLabelOptions(true)
  await scriptStore.getEventOptions()
  await scriptStore.getKnowledgeGroupOptions()
  await scriptStore.getAllDeepCorpusOptions()
});

</script>

<style scoped lang="postcss" type="text/postcss">
.intention-container {
  width: 100%;
  height: calc(100vh - 180px);
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  background-color: #fff;
  .el-tabs :deep(.el-tabs__item){
    padding: 10px auto 0;
    height: 40px;
    line-height: 40px;
    width: 100px;
  }
  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
    border: none;
    background-color: #e5e7eb;
  }
  :deep(.el-tabs__nav-prev), :deep(.el-tabs__nav-next) {
    line-height: 60px;
    font-size: 16px;
  }
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
}
.script-container {
  margin: 10px;
  border: 2px solid #e5e7eb;
  background-color: #f2f2f2;
  :deep(.el-tabs__content) {
    height: 600px;
  }
}

</style>