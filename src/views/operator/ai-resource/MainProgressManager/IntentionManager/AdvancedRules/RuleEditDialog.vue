<template>
  <el-dialog
    v-model="dialogVisible"
    class="dialog-form"
    width="800px"
    @close="cancel"
    :close-on-click-modal="false"
    align-center
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ title }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 180px)'"
      wrap-class="tw-px-[12px] tw-w-full"
      view-class="tw-flex tw-flex-col"
    >
      <el-form
        :model="editData"
        :rules="rules"
        :disabled="readonly"
        scroll-to-error
        :scroll-into-view-options="{ behavior: 'smooth', block: 'center' }"
        label-width="90px"
        class="tw-grow"
        ref="ruleEditRef"
      >
        <el-form-item label="规则名称：" prop="ruleName">
          <el-input v-model.trim="editData.ruleName" clearable placeholder="请填写规则名称" :maxlength="20" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="备注：" prop="comment">
          <el-input
            v-model.trim="editData.comment"
            clearable placeholder="请填写规则备注"
            :maxlength="200"
            show-word-limit
            type="textarea"
            :autosize="{minRows: 3, maxRows: 6}"
          ></el-input>
        </el-form-item>
        <!-- 满足条件，必填 -->
        <el-form-item label-width="0" prop="matchConditionList">
          <MultRuleBox
            v-if="dialogVisible"
            v-model:data="editData.matchConditionList"
            :readonly="readonly"
            :scriptId="editId"
            required
            :options="enum2Options(RuleTypeEnum)"
            title="满足条件"
          />
        </el-form-item>
        <el-form-item label-width="0" prop="excludeConditionList">
          <MultRuleBox
            v-if="dialogVisible"
            v-model:data="editData.excludeConditionList"
            :readonly="readonly"
            :scriptId="editId"
            :options="enum2Options(RuleTypeEnum)"
            title="排除条件"
          />
        </el-form-item>
        <div class="tw-text-left tw-mt-[30px] tw-mb-[12px] info-title-deep tw-font-[600]">规则生效</div>
        <el-form-item label="设置标签：" prop="intentionTagId">
          <SelectBox 
            v-model:selectValStr="editData.intentionTagId"
            :options="scriptStore.intentionTagOptions"
            class="tw-w-full"
            name="labelName"
            val="id"
            placeholder="请选择意向标签"
            multiple filterable isString canSelectAll
          >
          </SelectBox>
        </el-form-item>
        <el-form-item label="意向分类：" prop="intentionLevelId" >
          <el-select v-model="editData.intentionLevelId" clearable :style="'width:100%'"  placeholder="请选择客户意向分类">
            <el-option
              v-for="item in scriptStore.intentionLevelOptions"
              :key="item.id"
              :label="`${item.intentionType} - ${item.intentionName}`"
              :value="item.id+''"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer" v-if="!readonly">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, defineAsyncComponent, computed,} from 'vue'
import { AdvancedRulesItem, AdvancedRulesOrigin, RuleTypeEnum, } from '@/type/IntentionType'
import { ElMessage, } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { scriptIntentionModel, } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import { CloseBold, Select } from '@element-plus/icons-vue'
import to from 'await-to-js';
import { enum2Options } from '@/utils/utils'
import { checkMultRules } from '@/components/script-rules/constant'
import MultRuleBox from '@/components/script-rules/MultRuleBox.vue'
import { trace } from '@/utils/trace'

// 组件性能消耗较大，动态引入
const SelectBox = defineAsyncComponent({
  loader:() => {
    return import('@/components/SelectBox.vue')
  }
})
const scriptStore = useScriptStore()
const editId = scriptStore.id
const readonly = scriptStore.isChecked
const loading = ref(false)
const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean,
  editData: AdvancedRulesItem | null,
}>();
const title = computed(() => (props.editData?.id ? '编辑高级规则' : '新增高级规则'))

const dialogVisible = ref(props.visible)
const editData = reactive<AdvancedRulesItem>(props.editData || new AdvancedRulesOrigin(editId))

const checkIntentions =  (rule: any, value: any, callback: any) => {
  if ((!editData.intentionLevelId && !editData.intentionTagId)) {
    return callback(new Error('意向分类和意向标签至少选择一个'))
  }
  return callback()
}
const rules:FormRules = {
  ruleName: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 20, message: '规则名称长度限制2-20字符', trigger: 'blur' },
  ],
  comment: [
    // { required: true, message: '请输入规则备注', trigger: 'blur' },
    { max: 200, message: '备注长度限制200字符', trigger: 'blur' },
  ],
  intentionTagId: [
    { validator: checkIntentions, trigger: ['change']},
  ],
  intentionLevelId: [
    { validator: checkIntentions, trigger: ['change']},
  ],
  
  matchConditionList: [
    { validator: (rule: any, value: any, callback: any) => {
      const errMsg = checkMultRules(editData.matchConditionList, true)
      return errMsg ? callback(new Error(errMsg)) : callback()
    }, trigger: ['change', 'blur']},
  ],
  excludeConditionList: [
    { validator: (rule: any, value: any, callback: any) => {
      const errMsg = checkMultRules(editData.excludeConditionList, false)
      return errMsg ? callback(new Error(errMsg)) : callback()
    }, trigger: ['change', 'blur']},
  ],
}

const ruleEditRef = ref<FormInstance | null>(null)
const confirm = async () => {
  ruleEditRef.value && ruleEditRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      editData.intentionLevelName = scriptStore.intentionLevelOptions?.find(item => item.id == editData.intentionLevelId)?.intentionType
      editData.intentionTagName = scriptStore.intentionTagOptions?.filter(item => item.id &&  editData.intentionTagId?.split(',')?.map(Number)?.includes(item.id))?.map(item => item.labelName).join(',') || undefined
      const params = {
        id: editData.id || undefined,
        ruleName: editData.ruleName,
        comment: editData.comment,
        intentionTagId: editData.intentionTagId,
        intentionTagName: editData.intentionTagName,
        intentionLevelId: editData.intentionLevelId,
        intentionLevelName: editData.intentionLevelName,
        scriptId: editId,
        matchConditionList: editData.matchConditionList,
        excludeConditionList: editData.excludeConditionList,
      }
      trace({
        page: `话术编辑-高级规则-${editData.id ? '编辑' : '新增'}高级规则(${editId})`,
        params,
      })
      const [err] = await to(scriptIntentionModel.saveOneRule(params))
      loading.value = false
      if (!err) {
        dialogVisible.value = false
        ElMessage.success('操作成功')
        emits('confirm')
      }
    }
  })
}
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

watch(() => props.visible, async n => {
  dialogVisible.value = n
  if (!n) {
    Object.assign(editData, new AdvancedRulesOrigin(editId))
    return
  }
  Object.assign(editData, props.editData ? JSON.parse(JSON.stringify(props.editData)) : new AdvancedRulesOrigin(editId))
  ruleEditRef.value && ruleEditRef.value.clearValidate()
})
</script>

<style lang="postcss" type="text/postcss">
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
  :deep(.el-form-item__content) {
    font-size: var(--el-font-size-base);
  }
}
</style>
