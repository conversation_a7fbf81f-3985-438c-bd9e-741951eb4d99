<template>
  <el-dialog
    v-model="dialogVisible"
    class="dialog-form"
    width="600px"
    @close="cancel"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">导入高级规则</div>
    </template>
    <el-form
      :model="editData"
      :rules="rules"
      :disabled="isChecked"
      label-width="80px"
      ref="editRef"
    >
      <el-form-item label="选择话术：" prop="scriptId">
        <el-select
          v-model="editData.sourceScriptId"
          filterable
          class="tw-grow"
          placeholder="请选择话术"
          @change="handleScriptIdChange"
        >
          <el-option
            v-for="item in scriptOptions"
            :label="item.scriptName"
            :key="item.id"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="选择规则：" prop="ruleIdList">
        <SelectBox 
          v-model:selectVal="editData.ruleIdList"
          :options="ruleList||[]"
          name="ruleName"
          val="id"
          placeholder="请选择规则"
          class="tw-grow"
          canCopy canSelectAll filterable
          copyName="ruleIds"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer" v-if="!isChecked">
        <el-button @click="dialogVisible=false">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
  <ProcessDialog
    v-model:visible="resultProcessVisible"
    title="批量启动结果"
    :successNumber="successNumber"
    :totalNumber="editData.ruleIdList?.length||0"
    :processInfo="resultProcessInfo"
  >
  </ProcessDialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, defineAsyncComponent, onUnmounted,} from 'vue'
import { SpeechCraftStatusEnum, SpeechCraftInfoItem } from '@/type/speech-craft'
import { AdvancedRulesItem } from '@/type/IntentionType'
import { ElMessage, } from 'element-plus'
import type { FormInstance, } from 'element-plus'
import { scriptTableModel, scriptIntentionModel } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import to from 'await-to-js';
import ProcessDialog from '@/components/ProcessDialog.vue'
import { trace } from '@/utils/trace'

// 组件性能消耗较大，动态引入
const SelectBox = defineAsyncComponent({
  loader:() => {
    return import('@/components/SelectBox.vue')
  }
})
const scriptStore = useScriptStore()
const editId = scriptStore.id
const isChecked = scriptStore.isChecked

const loading = ref(false)
const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
}>();
const dialogVisible = ref(props.visible)
const editData = reactive<{
  scriptId: number,
  ruleIdList: number[],
  sourceScriptId?: number
}>({
  scriptId: editId, // 当前话术id
  sourceScriptId: undefined, // 复制话术id
  ruleIdList: [], // 复制话术选择的高级规则
})
const ruleList = ref<AdvancedRulesItem[]>([])
  
const handleScriptIdChange = async () => {
  if (!editData.sourceScriptId) return
  const [_, data] = await to(scriptIntentionModel.findRulesList({
    scriptId: editData.sourceScriptId
  })) as [any, AdvancedRulesItem[]]
  ruleList.value = data || []
}

const rules = {
  sourceScriptId: [
    { required: true, message: '请选择话术', trigger: 'change' },
  ],
  ruleIdList: [
    { required: true, message: '请选择高级规则', trigger: 'change' },
    { validator:(rule: any, value: any, callback: any) => {
      if (value.length > 20) {
        return callback(new Error('高级规则最多选择20个'))
      }
      return callback()
    }, trigger: 'change' },
  ]
}

const editRef = ref<FormInstance>()
const resultProcessVisible = ref(false)
const resultProcessInfo = ref<{ name: string, remark?: string }[]>([])
const successNumber = ref(0)
const confirm = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      await trace({ page: `话术编辑-导入高级规则(${editId})`, params: editData })
      const [err, data] = await to(scriptIntentionModel.copyRulesList(editData))
      loading.value = false
      if (data) {
        if (!data?.length) {
          ElMessage.success('全部导入成功')
        } else {
          resultProcessVisible.value = true
          resultProcessInfo.value = data?.map(item => ({ name: item.ruleName, remark: item.failReason })) || []
          successNumber.value = editData.ruleIdList?.length - (data?.length || 0)
        }
        
        dialogVisible.value = false
        emits('confirm')
      }
    }
  })
}
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  dialogVisible.value = false
  emits('update:visible', false)
}

const scriptOptions = ref<SpeechCraftInfoItem[] | null>([])
const init = async () => {
  const [_, data] = await to(scriptTableModel.findScriptListByStatusAndIndustry({
    status: SpeechCraftStatusEnum['生效中'],
    secondaryIndustryId: scriptStore.secondIndustryId,
  }))
  scriptOptions.value = data || []
}

onUnmounted(() => {
  scriptOptions.value = null
})

watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    init()
    Object.assign(editData, {
      sourceScriptId: undefined,
      ruleIdList: [],
    })
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.requirement-dialog {
  .el-input-number .el-input__inner {
    text-align: left;
  }
  .text {
    font-size: 14px;
    line-height: 24px;
    margin-right: 10px;
    text-align: left;
  }
  .el-form {
    color: var(--primary-black-color-600);
    width: 100%;
    padding: 0 12px;
    .el-form-item {
      margin-bottom: 14px;
      &:first-child {
        margin-top: 14px;
      }
    }
    :deep(.el-form-item__label) {
      padding-right: 0;
    }
    :deep(.el-form-item__content) {
      font-size: var(--el-font-size-base);
    }
  }
}

</style>