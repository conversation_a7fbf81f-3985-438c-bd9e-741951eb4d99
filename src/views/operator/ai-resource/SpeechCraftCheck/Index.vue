<template>
  <HeaderBox title="话术审核" class="tw-grow-0 tw-shrink-0"/>
  <div class="line-container">
    <TabsBox v-model:active="activeTab" :tabList="tabList"></TabsBox>
    <keep-alive>
      <SpeechCraftCheckList v-if="activeTab == '话术审核'"/>
      <SpeechCraftCheckRecord v-else-if="activeTab == '审核记录'"/>
      <el-empty v-else/>
    </keep-alive>
  </div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, ref, } from 'vue'
import HeaderBox from '@/components/HeaderBox.vue'
import TabsBox from '@/components/TabsBox.vue'
const SpeechCraftCheckList = defineAsyncComponent({loader: () => import('./SpeechCraftCheckList.vue')})
const SpeechCraftCheckRecord = defineAsyncComponent({loader: () => import('./SpeechCraftCheckRecord.vue')})

const tabList = ['话术审核', '审核记录']
const activeTab = ref(tabList[0] || '')

</script>

<style scoped lang="postcss" type="text/postcss">
.line-container {
  width: 100%;
  box-sizing: border-box;
  min-width: 1080px;
  padding: 16px;
  height: calc(100vh - 55px);
  position: relative;
  font-size: 13px;
}
</style>