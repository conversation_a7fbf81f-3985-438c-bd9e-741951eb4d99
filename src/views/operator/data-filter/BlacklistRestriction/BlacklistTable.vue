<template>
  <div v-if="props.type!=='全局限制'"  class="search-box tw-w-full">
    <div class="tw-grid tw-grid-cols-3 tw-gap-[12px]">
      <div v-if="props.type==='行业限制'" class="item-col">
        <span class="label">行业·：</span>
        <el-cascader
          v-model="searchForm.productIndustryId"
          placeholder="请选择行业"
          :options="allIndustryList"
          :props="cascaderProps"
          class="tw-w-full"
          clearable
        />
      </div>
      <div v-if="props.type==='产品限制'" class="item-col">
        <span class="label">产品：</span>
        <el-select
          v-model="searchForm.productIndustryId"
          filterable
          clearable
          placeholder="请选择产品"
          class="tw-w-full"
        >
          <el-option
            v-for="item in productOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </div>
      <div class="item-btn">
        <el-button type="primary" @click="search" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>
    <div  class="tw-w-full tw-flex tw-justify-end">
      <el-button type="primary" @click="edit()">{{ '新增' + props.type }}</el-button>
    </div>
  </div>

  <el-table
    :data="tableDataTemp"
    v-loading="loading"
    class="tw-grow"
    row-key="id"
    :header-cell-style="tableHeaderStyle"
    stripe
  >
    <template v-if="!tableData || tableData.length < 1" #empty>
      <el-empty  description="暂无数据" />
    </template>
    <el-table-column v-if="props.type==='全局限制'" label="范围" align="left" width="120">
      <template #default="{ row }">
        全局限制
      </template>
    </el-table-column>
    <el-table-column v-else-if="props.type==='行业限制'" label="行业" align="left" prop="productIndustryName" width="120" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
    <template v-else>
      <el-table-column label="产品ID" align="left" prop="productIndustryId" width="80"></el-table-column>
      <el-table-column label="产品名称" align="left" prop="productIndustryName" min-width="160" show-overflow-tooltip :formatter="formatterEmptyData">
        <template #default="{ row }">
          {{ productOptions?.find(item => item.id == row.productIndustryId)?.name || '-' }}
        </template>
      </el-table-column>
    </template>
    <el-table-column label="挂载黑名单" align="left" min-width="240" show-overflow-tooltip>
      <template #default="{ row }">
        {{ row.blackListGroupList?.map((item:any) => item.groupName)?.join('、') || '-' }}
      </template>
    </el-table-column>
    <el-table-column label="操作" :width="props.type === '全局限制' ? 80 : 120" align="right" fixed="right">
      <template #default="{ row }">
        <el-button type="primary" link @click="edit(row)">编辑</el-button>
        <el-button v-if="props.type !== '全局限制'" type="danger" link @click="del(row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <PaginationBox
    v-if="props.type!=='全局限制'"
    :pageSize="pageSize"
    :currentPage="currentPage"
    :total="total || 0"
    @search="search()"
    @update="updatePage"
  >
  </PaginationBox>
  <EditDialog
    v-model:visible="visible"
    :disabledList="tableData||[]"
    :editData="addData"
    :type="props.type"
    @confirm="search"
  />
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onActivated, watch } from 'vue'
import { BlackRestrictionItem, BlackRestrictionTypeEnum } from '@/type/dataFilter'
import to from 'await-to-js'
import { formatNumber, formatterEmptyData, pickAttrFromObj, Throttle, } from '@/utils/utils'
import { ElMessage, CascaderProps } from 'element-plus'
import { tableHeaderStyle } from '@/assets/js/constant'
import { blacklistRestrictionModel } from '@/api/data-filter'
import { useGlobalStore } from '@/store/globalInfo'
import EditDialog from './EditDialog.vue'
import Confirm from '@/components/message-box'
import PaginationBox from '@/components/PaginationBox.vue'

const props = withDefaults(defineProps<{
  type: '全局限制'|'行业限制'|'产品限制', // 
}>(), {
  type: '全局限制'
})

const globalStore = useGlobalStore()
const productOptions = computed(() => globalStore.productList)
const allIndustryList = computed(() => globalStore.getIndustryOption)
const cascaderProps: CascaderProps = {
  value: 'id',
  label: 'name',
  children: 'secondaryIndustries',
  emitPath: false,
}

const tableData = ref<BlackRestrictionItem[]>([])

const searchForm = reactive<{
  productIndustryId?: number,
}>({
  productIndustryId: undefined,
})
const loading = ref(false)
const throttleLoading = new Throttle(loading)
const search = async () => {
  if (throttleLoading.check()) {
    return
  }
  throttleLoading.lock()
  let res: [any, BlackRestrictionItem[] | null | undefined] = [null, []]
  res = await to(blacklistRestrictionModel.findList({
    groupType: BlackRestrictionTypeEnum[props.type],
    productIndustryId: searchForm.productIndustryId || undefined,
  }))
  if (props.type == '全局限制' && (!res[1] || res[1].length === 0)) {
    tableData.value = [
      { 
        id: undefined,
        groupType: BlackRestrictionTypeEnum['全局限制'],
        productIndustryId: undefined,
        productIndustryName: undefined,
        blackListGroupList: []
      }
    ]
  } else {
    tableData.value = res[1] || []
  }
  
  total.value = tableData.value?.length || 0
  throttleLoading.unlock()
}

const visible = ref(false)
const addData = ref<null | BlackRestrictionItem>(null)
const edit = (row?: BlackRestrictionItem) => {
  visible.value = true
  addData.value = row || null
}
const del = (row: BlackRestrictionItem) => {
  Confirm({ 
    text: `您确定要删除【${row.productIndustryName}】?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(async () => {
    const params = pickAttrFromObj(row, [
      'id', 'groupType', 'productIndustryId', 'productIndustryName', 'blackListGroupList'
    ])
    const res = await to(blacklistRestrictionModel.deleteGroups(params))
    !res[0] && ElMessage.success('删除成功')
    search()
  }).catch(() => {})
}
search()

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}
const tableDataTemp = computed(() => {
  return tableData.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

watch(() => props.type, () => {
  searchForm.productIndustryId = undefined
  search()
  if (props.type === '产品限制') {
    globalStore.updateProductList(true)
  }
})
</script>

<style scoped lang="postcss" type="text/postcss">
.search-box {
  display: grid;
}
.el-table {
  font-size: var(--el-font-size-base);
  :deep(.cell) {
    padding: 0 8px;
  }
  :deep(.caret-wrapper) {
    display: none;
  }
}
</style>
