<template>
  <el-dialog
    :model-value.sync="visible"
    class="dialog-form"
    :width="tableData.length ? '800px' : '500px'"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">{{ title }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="addData"
        label-width="80px"
        :rules="rules"
        ref="editRef"
      >
        <el-form-item v-if="props.type === '行业限制'" label="行业：" prop="productIndustryId">
          <el-cascader
            v-model="addData.productIndustryId"
            placeholder="请选择行业"
            :options="allIndustryList"
            :props="cascaderProps"
            :disabled="!!props.editData?.productIndustryId"
            class="tw-w-[400px]"
            clearable
          />
        </el-form-item>
        <el-form-item v-if="props.type === '产品限制'" label="产品：" prop="productIndustryId">
          <el-select
            v-model="addData.productIndustryId"
            filterable
            :disabled="!!props.editData?.productIndustryId"
            class="tw-w-[400px]"
            clearable
            placeholder="请选择产品"
          >
            <el-option
              v-for="item in productOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选择黑名单：" prop="blackList">
          <SelectBox 
            :selectVal="blacklistAdd.map(item => item.id)"
            :options="blacklistUnselected"
            name="groupName"
            val="id"
            placeholder="请选择黑名单"
            filterable
            class="tw-w-[400px]"
            :multiple="true"
            @update:select-val="handleBlacklistAdd"
          >
          </SelectBox>
        </el-form-item>
      </el-form>
      <el-table
        v-show="tableData.length > 0"
        :data="tableData"
        class="tw-grow"
        row-key="id"
        :header-cell-style="tableHeaderStyle"
        stripe
      >
        <template v-if="!tableData || tableData.length < 1" #empty>
          <el-empty  description="暂无数据" />
        </template>
        <el-table-column label="分组名称" align="left" min-width="200" fixed="left" prop="groupName" :formatter="formatterEmptyData"></el-table-column>
        <el-table-column label="人群类别" align="left" width="120" prop="targetType" :formatter="formatterEmptyData" show-overflow-tooltip>
          <template #default="{ row }">
            {{ findValueInEnum(row?.targetType, BlackPersonTypeEnum) || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="人群等级" align="left" min-width="80" prop="targetLevel" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
        <el-table-column label="分组规则" align="left" min-width="200" prop="targetComment" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
        <el-table-column label="影响接通" align="left" min-width="200" prop="putThroughComment" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
        <el-table-column label="影响收益" align="left" min-width="200" prop="costBenefitComment" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
        <el-table-column label="影响本收" align="left" min-width="200" prop="costBenefitComment" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
        <el-table-column label="备注" align="left" min-width="200" prop="comment" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
        
        <el-table-column label="操作" width="80" align="right" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.id && groupIds?.includes(row.id)"
              type="danger"
              @click="remove(row)"
              link
            >
              移除
            </el-button>
            <el-button
              v-else
              type="primary"
              @click="cancelBlacklistAdd(row)"
              link
            >
              取消新增
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div  class="tw-w-full tw-text-[13px]">
        <div v-if="blacklistAdd.length" class="tw-flex tw-items-center">
          <span>新增：</span>
          <div>
            <el-tag
              v-for="tag in blacklistAdd"
              :key="tag.id"
              class="tw-mx-0.5 tw-my-0.5"
              closable
              @close="cancelBlacklistAdd(tag)"
            >
              {{ tag.groupName }}
            </el-tag>
          </div>
        </div>
        <div v-if="blacklistRemove.length" class="tw-flex tw-items-center tw-mt-[6px]">
          <span>删除：</span>
          <div>
            <el-tag
              v-for="tag in blacklistRemove"
              :key="tag.id"
              class="tw-mx-0.5 tw-my-0.5"
              closable
              @close="cancelRemove(tag)"
            >
              {{ tag.groupName }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, reactive, watch, onMounted, } from 'vue'
import { ElMessage, CascaderProps, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { BlackListGroupItem, BlackRestrictionItem, BlackPersonTypeEnum, BlackListTypeEnum, } from '@/type/dataFilter'
import to from 'await-to-js'
import { blacklistModel, blacklistRestrictionModel } from '@/api/data-filter'
import type { FormInstance } from 'element-plus'
import { BlackRestrictionItemOrigin } from './constant'
import { tableHeaderStyle } from '@/assets/js/constant'
import { Throttle, formatterEmptyData, findValueInEnum } from '@/utils/utils'
import SelectBox from '@/components/SelectBox.vue'
import { useGlobalStore } from '@/store/globalInfo'

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  editData: BlackRestrictionItem | null;
  disabledList: BlackRestrictionItem[];
  type: '全局限制' | '行业限制' | '产品限制'
}>();

const globalStore = useGlobalStore()

const loading = ref(false)
const throttleLoading = new Throttle(loading)
const dialogVisible = ref(props.visible)
const addData = reactive<BlackRestrictionItem>(new BlackRestrictionItemOrigin(props.type))

const title = computed(() => ((props.type === '全局限制' || props.editData?.productIndustryId) ? '编辑' : '新增') + props.type)

const groupIds = computed(() => addData.blackListGroupList?.map(item => +item.groupId) || [])

const rules = {
  productIndustryId: [
    { required: true, message: `请填写${props.type}名称`, trigger: 'blur' },
  ],
  blackList: {
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if ((!blacklistAdd.value || blacklistAdd.value.length < 1) && (!props.editData?.productIndustryId)) {
        callback(new Error('黑名单分组不能为空'))
      } else {
        callback()
      }
    }
  }
}

const blacklistAll = ref<BlackListGroupItem[]>([])
const blacklistAdd = ref<BlackListGroupItem[]>([])
const blacklistRemove = ref<BlackListGroupItem[]>([])
const blacklistSelected = ref<BlackListGroupItem[]>([])
const blacklistUnselected = ref<BlackListGroupItem[]>([])
// 列表显示已选择+ 新增的分组
const tableData = computed(() => {
  const list = [...blacklistSelected.value, ...blacklistAdd.value]
  const ids = blacklistRemove.value.map(item => item.id)
  return list.filter(item => !ids?.includes(item.id))
})

const handleBlacklistAdd = (val: number[]) => {
  blacklistAdd.value = blacklistAll.value.filter(item => val?.includes(item.id!)) || []
}

const cancelBlacklistAdd = (row: BlackListGroupItem) => {
  blacklistAdd.value = blacklistAdd.value.filter(item => item.id !== row.id) || []
}

const remove = (row: BlackListGroupItem) => {
  if (!row.id) return
  blacklistRemove.value.push(row)
}

const cancelRemove = (row: BlackListGroupItem) => {
  if (!row.id) return
  blacklistRemove.value = blacklistRemove.value.filter(item => item.id !== row.id)
}

const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

const editRef = ref<FormInstance  | null>(null)
const confirm = async () => {
  if (throttleLoading.check()) { return }
  throttleLoading.lock()
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      if (props.type === '产品限制') {
        addData.productIndustryName = productOptions.value.find(item => item.id == +addData.productIndustryId!)?.name || ''
      }
      if (props.type === '行业限制') {
        addData.productIndustryName = globalStore.getSecondIndustryList.find(item => item.id == +addData.productIndustryId!)?.name || ''
      }
      let res1: [any, any] = [null, null]
      let res2: [any, any] = [null, null]
      if (blacklistAdd.value && blacklistAdd.value.length > 0) {
        res1 = await to(blacklistRestrictionModel.addGroups({
          groupType: addData.groupType,
          productIndustryId: addData.productIndustryId,
          productIndustryName: addData.productIndustryName,
          blackListGroupList: blacklistAdd.value.map(item => {
            return {
              groupId: item.id!,
              groupName: item.groupName!,
            }
          }),
        }))
      }
      if (blacklistRemove.value && blacklistRemove.value.length > 0) {
        res2 = await to(blacklistRestrictionModel.deleteGroups({
          groupType: addData.groupType,
          productIndustryId: addData.productIndustryId,
          productIndustryName: addData.productIndustryName,
          blackListGroupList: blacklistRemove.value.map(item => {
            return {
              groupId: item.id!,
              groupName: item.groupName!,
            }
          }),
        }))
      }
      if (!res1[0] && !res2[0]) {
        ElMessage.success('操作成功')
        cancel()
        emits('confirm')
      } 
      throttleLoading.unlock()
    } else {
      throttleLoading.unlock()
    }
  })
}


const cascaderProps: CascaderProps = {
  value: 'id',
  label: 'name',
  children: 'secondaryIndustries',
  emitPath: false,
}
const allIndustryList = ref(globalStore.getIndustryOption)
const productOptions = ref(globalStore.productList)
const init = async () => {
  const [_, res] = await to(blacklistModel.getGroupList({
    groupType: BlackListTypeEnum['普通']
  })) as [any, BlackListGroupItem[]]
  blacklistAll.value = res || []
  productOptions.value = globalStore.productList
  allIndustryList.value = globalStore.getIndustryOption
}

onMounted(() => {
  init()
})

watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n) {
    blacklistAdd.value = []
    blacklistRemove.value = []
    Object.assign(addData, props.editData || new BlackRestrictionItemOrigin(props.type))
    addData.productIndustryId = props.editData?.productIndustryId ? +props.editData?.productIndustryId : undefined
    addData.blackListGroupList = addData.blackListGroupList || []
    blacklistAdd.value = []
    blacklistSelected.value = []
    blacklistUnselected.value = []
    const ids = addData.blackListGroupList?.map(item => +item.groupId) || []
    blacklistAll.value.forEach(item => {
      if (item.id && ids?.includes(item.id)) {
        blacklistSelected.value.push(item)
      } else {
        blacklistUnselected.value.push(item)
      }
    });
    // 当新增行业产品限制时，更新选项，剔除列表已有
    if (!props.editData?.productIndustryId) {
      const list = props.disabledList?.map(item => item.productIndustryId + '')
      if (props.type === '产品限制') {
        productOptions.value = globalStore.productList.filter(item => {
          return !list.includes(item.id + '')
        })
      }
      if (props.type === '行业限制') {
        allIndustryList.value = globalStore.getIndustryOption.flatMap(item => {
          const secondaryIndustries = item.secondaryIndustries?.filter(subItem => !list.includes(subItem.id + '')) || []
          return secondaryIndustries?.length > 0 ? [
            {...item, secondaryIndustries,}
          ] : []
        })
      }
    }
    editRef.value?.clearValidate()
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.el-table {
  font-size: var(--el-font-size-base);
  :deep(.cell) {
    padding: 0 8px;
  }
  :deep(.caret-wrapper) {
    display: none;
  }
}
</style>