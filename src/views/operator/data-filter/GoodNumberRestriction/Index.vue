<template>
  <!--模块标题-->
  <HeaderBox title="靓号限制" />

  <!--模块主体-->
  <div class="module-container tw-min-w-[1080px]">
    <el-scrollbar class="tw-px-[16px]" view-class="module-container-inner tab-container">

      <div class="tab-header">
        <div class="tw-flex tw-justify-end">
          <el-button type="primary" link @click="onClickTestGoodNumber">
            测试规则
          </el-button>
          <el-button type="primary" @click="onClickAddGoodNumber">
            新增限制规则
          </el-button>
        </div>
      </div>

      <!--表格-->
      <el-table
        srtipe
        :data="goodNumberCurrentList"
        :header-cell-style="tableHeaderStyle"
        class="tw-mt-[12px]"
      >
        <template #empty>
          暂无数据
        </template>

        <el-table-column align="left" prop="regexName" label="靓号名称" min-width="100" show-overflow-tooltip>
          <template #default="{row}">
            {{ row.regexName || '-' }}
          </template>
        </el-table-column>
        <el-table-column align="left" prop="phoneRegex" label="靓号规则" min-width="200" show-overflow-tooltip />
        <el-table-column align="left" prop="rank" label="规则等级" min-width="100" show-overflow-tooltip>
          <template #default="{row}">
            {{ row.rank || '-' }}
          </template>
        </el-table-column>
        <el-table-column align="left" prop="comment" label="备注" min-width="100" show-overflow-tooltip>
          <template #default="{row}">
            {{ row.comment || '-' }}
          </template>
        </el-table-column>
        <el-table-column align="right" fixed="right" width="100" label="操作">
          <template #default="{row}">
            <el-button v-show="row" type="primary" link @click="onClickEditGoodNumber(row)">
              编辑
            </el-button>
            <el-button type="danger" link @click="onClickDeleteGoodNumber(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!--分页条-->
      <PaginationBox
        :pageSize="goodNumberPageSize"
        :pageSizeList="goodNumberSizeList"
        :currentPage="goodNumberPageNum"
        :total="goodNumberPageTotal"
        @search="updateGoodNumberAllList"
        @update="updateGoodNumberCurrentList"
      />

    </el-scrollbar>
  </div>

  <!--新增靓号规则弹窗-->
  <GoodNumberEditDialog
    :visible="dialogGoodNumberAddVisible"
    :isEdit="dialogGoodNumberEdit"
    :data="dialogGoodNumberAddData"
    @close="onDialogGoodNumberAddClose"
    @update="onDialogGoodNumberAddUpdate"
  />

  <!--靓号测试弹窗-->
  <GoodNumberTestDialog
    :visible="dialogGoodNumberTestVisible"
    :data="dialogGoodNumberTestData"
    @close="onDialogGoodNumberTestClose"
    @update="onDialogGoodNumberTestUpdate"
  />
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, ref } from 'vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import { Throttle, updateCurrentPageList } from '@/utils/utils'
import to from 'await-to-js'
import { ElMessage } from 'element-plus'
import Confirm from '@/components/message-box'
import { goodNumberRestrictionModel } from '@/api/data-filter'
import { GoodNumberRestrictionInfo } from '@/type/dataFilter'

// 动态引入组件
const HeaderBox = defineAsyncComponent(() => import('@/components/HeaderBox.vue'))
const PaginationBox = defineAsyncComponent(() => import('@/components/PaginationBox.vue'))
const GoodNumberEditDialog = defineAsyncComponent(() => import('./GoodNumberEditDialog.vue'))
const GoodNumberTestDialog = defineAsyncComponent(() => import('@/components/GoodNumberTestDialog.vue'))

// ---------------------------------------- 靓号限制 开始 ----------------------------------------

// 靓号限制，全部，正在加载
const loadingGoodNumber = ref<boolean>(false)
// 靓号限制，全部，正在加载节流锁
const throttleGoodNumber = new Throttle(loadingGoodNumber)

// 靓号限制，全部，接口数据
const goodNumberAllList = ref<GoodNumberRestrictionInfo[]>([])
// 靓号限制，总数
const goodNumberPageTotal = computed(() => {
  return goodNumberAllList.value.length ?? 0
})

// 靓号限制，当前页，全部的子集
const goodNumberCurrentList = ref<GoodNumberRestrictionInfo[]>([])
// 靓号限制，当前页，页码
const goodNumberPageNum = ref(1)
// 靓号限制，当前页，每页大小
const goodNumberPageSize = ref(20)
// 靓号限制，当前页，每页大小可选数值
const goodNumberSizeList: number[] = [10, 20, 50, 100]

/**
 * 靓号限制，更新列表，全部
 */
const updateGoodNumberAllList = async () => {
  // 节流锁上锁
  if (throttleGoodNumber.check()) {
    return
  }
  throttleGoodNumber.lock()

  // 请求接口
  const [err, res] = <[any, GoodNumberRestrictionInfo[]]>await to(goodNumberRestrictionModel.getList())
  if (err) {
    ElMessage.error('无法获取靓号列表')
    goodNumberAllList.value = []
    // 节流锁解锁
    throttleGoodNumber.unlock()
    return
  }
  // 更新全部列表
  goodNumberAllList.value = res?.length ? res : []
  // 更新当前页列表
  updateGoodNumberCurrentList(goodNumberPageNum.value, goodNumberPageSize.value)

  // 节流锁解锁
  throttleGoodNumber.unlock()
}
/**
 * 靓号限制，更新列表，当前页
 * @param {number} p 新页码
 * @param {number} s 新分页大小
 */
const updateGoodNumberCurrentList = (p?: number, s?: number) => {
  // 如果参数指定了页码或/和每页大小，则按参数更新列表；
  // 否则保持当前页码和大小不变直接更新。
  // 这里的判断条件相当于排除了undefined, null, 0等逻辑假值，
  // 另外，页码为0或者分页大小为0，本身就是逻辑错误的，不应该更新分页。
  if (p || s) {
    goodNumberPageNum.value = p!
    goodNumberPageSize.value = s!
  }
  // 更新当前页码
  goodNumberCurrentList.value = updateCurrentPageList(goodNumberAllList.value, goodNumberPageNum.value, goodNumberPageSize.value)
}
/**
 * 靓号限制 点击新增按钮
 */
const onClickAddGoodNumber = () => {
  // 新增模式
  dialogGoodNumberEdit.value = false
  // 更新表单数据
  dialogGoodNumberAddData.value = {}
  // 显示弹窗
  dialogGoodNumberAddVisible.value = true
}
/**
 * 靓号限制 点击测试按钮
 * @param {GoodNumberRestrictionInfo} row 靓号限制信息
 */
const onClickTestGoodNumber = (row: GoodNumberRestrictionInfo) => {
  // 更新表单数据
  dialogGoodNumberTestData.value = row
  // 显示弹窗
  dialogGoodNumberTestVisible.value = true
}
/**
 * 靓号限制 点击编辑按钮
 * @param {GoodNumberRestrictionInfo} row 当前靓号限制信息
 */
const onClickEditGoodNumber = (row: GoodNumberRestrictionInfo) => {
  // 编辑模式
  dialogGoodNumberEdit.value = true
  // 更新表单数据
  dialogGoodNumberAddData.value = row
  // 显示弹窗
  dialogGoodNumberAddVisible.value = true
}
/**
 * 靓号限制 点击删除按钮
 * @param row 当前行数据
 */
const onClickDeleteGoodNumber = async (row: GoodNumberRestrictionInfo) => {
  // 判断当前靓号规则是否绑定了线路供应商
  // 绑定线路供应商了不允许删除
  // 反之允许
  const [err, res] = <[any, boolean]>await to(goodNumberRestrictionModel.getBindMerchant({
    id: row?.id ?? -1
  }))
  if (err) {
    ElMessage({
      type: 'error',
      message: '无法获取靓号规则绑定的线路供应商'
    })
    return
  }
  if (res) {
    ElMessage({
      type: 'warning',
      message: '当前靓号规则已绑定线路供应商，无法删除'
    })
    return
  }

  const text = row?.regexName
    ? `您确定要删除靓号规则【${row.regexName ?? ''}】吗？<br>${row.phoneRegex ?? ''}`
    : `您确定要删除靓号规则【${row.phoneRegex ?? ''}】吗？`
  // 显示确认弹窗
  Confirm({
    title: '删除确认',
    text,
    type: 'danger',
    confirmText: '确定',
    cancelText: '取消',
  }).then(async () => {
    // 处理参数
    const params: GoodNumberRestrictionInfo = {
      id: row.id ?? undefined,
    }
    // 请求接口
    const [err, res] = <[any, GoodNumberRestrictionInfo]>await to(goodNumberRestrictionModel.delete(params))
    if (err) {
      ElMessage({
        type: 'error',
        message: '删除失败'
      })
      return
    }
    ElMessage({
      message: '删除成功',
      type: 'success',
    })
    // 更新列表
    await updateGoodNumberAllList()
  }).catch(() => {
  })
}

// ---------------------------------------- 靓号限制 结束 ----------------------------------------

// ---------------------------------------- 靓号规则编辑弹窗 开始 ----------------------------------------

// 靓号规则编辑弹窗 显示
const dialogGoodNumberAddVisible = ref<boolean>(false)
// 靓号规则编辑弹窗 数据
const dialogGoodNumberAddData = ref<GoodNumberRestrictionInfo>({})
// 靓号规则编辑弹窗 是否编辑
const dialogGoodNumberEdit = ref<boolean>(false)

/**
 * 靓号规则编辑弹窗 关闭
 */
const onDialogGoodNumberAddClose = () => {
  dialogGoodNumberAddVisible.value = false
}
/**
 * 靓号规则编辑弹窗 更新
 * @param {GoodNumberRestrictionInfo} data 新数据
 */
const onDialogGoodNumberAddUpdate = (data: GoodNumberRestrictionInfo) => {
  // 更新父组件表单数据
  dialogGoodNumberAddData.value = data
  // 更新父组件列表
  updateGoodNumberAllList()
}

// ---------------------------------------- 靓号规则编辑弹窗 结束 ----------------------------------------

// ---------------------------------------- 靓号测试弹窗 开始 ----------------------------------------

// 靓号测试弹窗 显示
const dialogGoodNumberTestVisible = ref<boolean>(false)
// 靓号测试弹窗 数据
const dialogGoodNumberTestData = ref<GoodNumberRestrictionInfo>({})

/**
 * 靓号测试弹窗 关闭
 */
const onDialogGoodNumberTestClose = () => {
  dialogGoodNumberTestVisible.value = false
}
/**
 * 靓号测试弹窗 更新
 * @param {GoodNumberRestrictionInfo} data 新数据
 */
const onDialogGoodNumberTestUpdate = (data: GoodNumberRestrictionInfo) => {
  // 更新父组件表单数据
  dialogGoodNumberTestData.value = data
  // 更新父组件列表
  updateGoodNumberAllList()
}

// ---------------------------------------- 靓号测试弹窗 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

updateGoodNumberAllList()

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.module-container {
  padding: 16px 0;
  text-align: left;
}
/*文本框内的标签*/
:deep(.el-cascader__tags .el-tag) {
  margin: 0 0 0 4px;
}
/* 标签卡模块 标签卡 */
.tab-container {
  padding: 0;
  text-align: left;
  /* 标签卡顶部 */
  .tab-header {
    padding: 12px 16px 0;
  }
  /* 标签卡顶部，表单项 */
  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
