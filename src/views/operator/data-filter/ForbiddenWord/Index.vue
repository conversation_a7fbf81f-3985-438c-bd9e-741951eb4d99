<template>
  <!--模块标题-->
  <HeaderBox title="违禁词管理" />

  <!--模块主体-->
  <div class="module-container tw-min-w-[1080px]">
    <!--标签卡-->
    <TabsBox
      v-model:active="activeTab"
      :tabList="ForbiddenWordTabList"
      class="forbidden-word-tab"
    />

    <!--表格-->
    <Table :tab="activeTab" />
  </div>

</template>

<script setup lang="ts">
import { ref } from 'vue'
import HeaderBox from '@/components/HeaderBox.vue'
import TabsBox from '@/components/TabsBox.vue'
import Table from './Table.vue'
import { ForbiddenWordTabEnum, ForbiddenWordTabList } from '@/type/dataFilter'

// ---------------------------------------- 通用 开始 ----------------------------------------

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 标签卡 开始 ----------------------------------------

// 当前选中标签页
const activeTab = ref<ForbiddenWordTabEnum>(ForbiddenWordTabEnum.VARIABLE)

// ---------------------------------------- 标签卡 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
/* 标签卡 */
.forbidden-word-tab {
  /*noinspection CssUnusedSymbol*/
  :deep(.normal-tab) {
    width: 6em;
  }
}
</style>
