<template>
  <el-dialog
    :model-value="props.visible"
    width="600px"
    class="restriction-dialog"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        {{ props.isEdit ? '编辑' : '添加' }}{{ props.tab }}
      </div>
    </template>

    <div class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="90px"
        >
          <el-form-item :label="props.tab+'：'" prop="forbiddenWord">
            <el-input
              v-model="form.forbiddenWord"
              type="text"
              :placeholder="'请输入'+props.tab"
              clearable
              show-word-limit
              maxlength="200"
              style="width: 100%;"
            />
          </el-form-item>
          <el-form-item label="类型：" prop="isRegex">
            <el-radio-group v-model="form.isRegex" class="tw-ml-[6px]">
              <el-radio :label="false">普通文本</el-radio>
              <el-radio :label="true">正则表达式</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注：" prop="remarks">
            <el-input
              v-model="form.remarks"
              type="text"
              placeholder="请输入备注"
              clearable
              show-word-limit
              maxlength="200"
              style="width: 100%;"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="handleCancel">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref, watch } from 'vue'
import { ForbiddenWordItem, ForbiddenWordParam, ForbiddenWordTabEnum } from '@/type/dataFilter'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { Throttle } from '@/utils/utils'
import { ElMessage, FormRules } from 'element-plus'
import to from 'await-to-js'
import { forbiddenWordUrlModel, forbiddenWordVariableModel } from '@/api/data-filter'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
  data: ForbiddenWordItem,
  isEdit: boolean,
  allList: ForbiddenWordItem[],
  tab: ForbiddenWordTabEnum,
}>(), {
  visible: false,
  data: () => ({}),
  isEdit: false,
  allList: () => ([]),
  tab: ForbiddenWordTabEnum.VARIABLE,
})
const emits = defineEmits([
  'update:visible',
  'confirm'
])

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): ForbiddenWordItem => {
  return {
    id: undefined,
    forbiddenWord: '',
    isRegex: false,
  }
}
// 表单数据
const form: ForbiddenWordItem = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  forbiddenWord: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error(props.tab + '不能为空'))
      } else {
        // 判断是否重复添加，排除当前违禁词
        const isExist = props.allList.some((item: ForbiddenWordItem) => {
          return item.forbiddenWord === value && item.forbiddenWord !== props.data?.forbiddenWord
        })
        if (isExist) {
          // 校验不通过
          callback(new Error(props.tab + '已存在'))
        } else {
          // 校验通过
          callback()
        }
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
      })
    }
  })
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 处理参数
  const params: ForbiddenWordParam = {
    id: form.id ?? undefined,
    forbiddenWord: form.forbiddenWord ?? '',
    remarks: form.remarks ?? '',
    isRegex: form.isRegex ?? false,
  }

  // 请求接口
  let err: any = null
  if (props.tab === ForbiddenWordTabEnum.VARIABLE) {
    // 变量违禁词
    // 是否存在ID
    if (form.id !== undefined) {
      // 编辑
      const [e, _] = <[any, ForbiddenWordItem]>await to(forbiddenWordVariableModel.edit(params))
      err = e
    } else {
      // 新建
      const [e, _] = <[any, ForbiddenWordItem]>await to(forbiddenWordVariableModel.add(params))
      err = e
    }
  } else if (props.tab === ForbiddenWordTabEnum.URL) {
    // 短链违禁词
    // 是否存在ID
    if (form.id !== undefined) {
      // 编辑
      const [e, _] = <[any, ForbiddenWordItem]>await to(forbiddenWordUrlModel.edit(params))
      err = e
    } else {
      // 新建
      const [e, _] = <[any, ForbiddenWordItem]>await to(forbiddenWordUrlModel.add(params))
      err = e
    }
  }

  // 返回失败结果
  if (err) {
    ElMessage({
      type: 'error',
      message: '保存失败'
    })
    // 节流锁解锁
    throttleConfirm.unlock()
    return
  }

  // 返回成功结果
  ElMessage({
    message: '保存成功',
    type: 'success',
  })
  emits('confirm')
  // 关闭弹窗
  closeDialog()

  // 节流锁解锁
  throttleConfirm.unlock()
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击确定按钮
 */
const handleConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const handleCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  // 每次显示弹窗时
  if (val) {
    // 更新表单数据
    await nextTick()
    resetForm()
    Object.assign(form, props.data)
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
