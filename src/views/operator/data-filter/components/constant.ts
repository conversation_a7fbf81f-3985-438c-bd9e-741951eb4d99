import dayjs from 'dayjs'
import { BlackListTypeEnum, BlackPersonTypeEnum, ImportTypeEnum } from '@/type/dataFilter'
import { phoneReg } from '@/utils/constant'

export const shortcuts = [
  {
    text: '明天',
    value: dayjs().add(1, 'd').endOf('d').format('YYYY-MM-DD'),
  },
  {
    text: '7天',
    value: dayjs().add(7, 'd').endOf('d').format('YYYY-MM-DD'),
  },
  {
    text: '30天',
    value: dayjs().add(30, 'd').endOf('d').format('YYYY-MM-DD'),
  },
  {
    text: '1年',
    value: dayjs().add(1, 'y').endOf('d').format('YYYY-MM-DD'),
  },
]

const validateFiles = (rule: any, value: any, callback: any) => {
  if (!value || value.length === 0) {
    callback(new Error('请选择需要上传的文件!'))
  } else {
    if (value.length === 1 && value[0]!.size! > 1024 * 1024 * 80) {
      callback(new Error('文件大小不得超过80M!'))
    }
    const type = value[0].name?.split('.')?.at(-1)
    if (!type || !['csv', 'xls', 'xlsx'].includes(type)) {
      callback(new Error('文件类型必须为csv、xlsx、xls!'))
    }
  }
  callback()
}

const validatePhone = (rule: any, value: any, callback: any) => {
  // 兼容11位常规手机号和+68开头的特殊16位手机号
  if (!value.match(phoneReg) && !value.startsWith('+')) {
    callback(new Error('手机号不合规'))
  } else {
    callback()
  }
}

export const phoneRules = {
  phone: [
    { required: true, message: '请输入号码', trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' },
  ],
  expireDate: [
    { required: true, message: '请选择过期时间', trigger: 'change' },
  ],
  uploadFiles: [
    { validator: validateFiles, trigger: ['change', 'blur'] },
  ]
}

export const groupRules = {
  groupName: [
    { required: true, message: '请填写黑名单分组名称', trigger: 'blur' },
    { min: 2, max: 20, message: '黑名单分组长度必须在2-20个字符', trigger: 'blur' }
  ],
  groupType: [
    { required: true, message: '请选择分组类型', trigger: 'change' },
  ],
  targetType: [
    { required: true, message: '请选择人群类别', trigger: 'change' },
  ],
  targetLevel: [
    { required: true, message: '请填写人群等级', trigger: 'blur' },
  ],
  limitDuration: [
    { required: true, message: '请填写限制时长', trigger: 'blur' },
  ],
  targetComment: [
    { max: 50, message: '分组规则长度必须在50个字符以内', trigger: 'blur' }
  ],
  putThroughComment: [
    { max: 50, message: '影响接通长度必须在50个字符以内', trigger: 'blur' }
  ],
  benefitComment: [
    { max: 50, message: '影响收益长度必须在50个字符以内', trigger: 'blur' }
  ],
  costBenefitComment: [
    { max: 50, message: '影响本收长度必须在50个字符以内', trigger: 'blur' }
  ],

  comment: [
    { max: 200, message: '备注不得超过200个字符', trigger: 'blur' }
  ],
}

export class BlackGroupOrigin {
  id = undefined
  groupType = BlackListTypeEnum['普通']
  targetType = BlackPersonTypeEnum['低价值人群']
  groupName = ''
  targetLevel = undefined
  targetComment =  ''
  limitDuration = 30
  putThroughComment =  ''
  benefitComment =  ''
  costBenefitComment =  ''
  comment =  ''
}

export class WhiteListItemOrigin {
  id = undefined
  phone = ''
  expirationTime = undefined
  groupId = undefined
  groupName = undefined
}

export class WhiteListImportItemOrigin {
  id = undefined
  phone = ''
  expirationTime = undefined
  importType = ImportTypeEnum['批量导入']
  groupId = undefined
  groupName = undefined
  uploadFiles = undefined
}