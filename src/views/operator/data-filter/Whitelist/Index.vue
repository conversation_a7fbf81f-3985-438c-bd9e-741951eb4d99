<template>
  <!--模块标题-->
  <HeaderBox title="白名单" />

  <!--模块主体-->
  <div v-loading="loadingLeft" class="tw-flex tw-min-w-[1080px] tw-h-[calc(100%-56px)]">
    <!--右半部分，内容-->
    <el-scrollbar class="tw-grow tw-p-[16px]" view-class="tw-flex tw-flex-col tw-h-full">
        <!--白名单详情模块-->
      <template v-if="(currentItem?.id??-1)>=0">
          <!--按钮容器-->
          <div class="tw-mb-[12px] tw-flex tw-justify-end tw-w-full">
          <el-dropdown class="tw-mx-[12px]" @command="handleImportOperator">
            <el-button type="primary">
              <el-icon :size="16"><SvgIcon name="upload"></SvgIcon></el-icon>
              <span>导入号码</span>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="file">文件导入</el-dropdown-item>
                <el-dropdown-item command="single">单个导入</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button type="primary" @click="updatePhoneCount">
            <el-icon :size="16"><SvgIcon name="reset3"></SvgIcon></el-icon>
            <span>更新数量</span>
          </el-button>
        </div>
          <!--白名单基本信息-->
        <div class="card-box tw-flex tw-flex-col">
          <div class="tw-flex tw-items-between tw-w-full">
            <h5 class="tw-text-[14px] tw-text-[var(--primary-black-color-600)] tw-font-semibold">
              {{ currentItem?.groupName ?? '' }}
            </h5>
          </div>
          <div class="tw-flex tw-flex-col tw-w-full tw-mt-[12px]">
            <div class="tw-flex tw-items-center tw-w-full tw-pb-[4px]">
              <p class="tw-text-left tw-break-all">
                <span class="info-title">号码数量：</span>
                <span class="info-content tw-border-r-[1px] tw-pr-[16px]">{{ currentItem?.phoneCount || '-' }}</span>
              </p>
              <p class="tw-text-left tw-break-all tw-pl-[16px]">
                <span class="info-title">创建时间：</span>
                <span class="info-content  tw-border-r-[1px] tw-pr-[16px]">{{ dayjs(currentItem?.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
              </p>
              <p class="tw-text-left tw-break-all tw-pl-[16px]">
                <span class="info-title">更新时间：</span>
                <span class="info-content">
                  {{dayjs(currentItem?.updateTime).format('YYYY-MM-DD HH:mm:ss') }}
                </span>
              </p>
            </div>
          </div>
        </div>

        <div class="tw-rounded-[4px] tw-bg-white tw-flex tw-flex-col tw-grow tw-mt-[12px]">
          <TabsBox v-model:active="activeTab" :tabList="tabList" class="tw-w-full" @update:active="handleTabChange"></TabsBox>
          <div class="tw-flex tw-flex-col tw-px-[12px] tw-py-[16px] tw-grow">
            <template v-if="activeTab==='号码'">
              <SearchPhoneList
                :tableData="rightPhoneList"
                :loading="loadingRight"
                @search="searchPhoneAction"
                @edit="editRight"
                @del="delPhoneAction"
              />
            </template>
            <!--导入记录-->
            <template v-else>
              <div class="tw-flex tw-w-[540px] tw-mb-1 tw-items-center tw-gap-x-[6px]">
                <div class="item">
                  <span class="label">导入时间：</span>
                  <TimePickerBox
                    v-model:start="searchRightForm.startTime"
                    v-model:end="searchRightForm.endTime"
                    placeholder="导入时间"
                    separator="-"
                    :clearable="false"
                    format="YYYY-MM-DD HH:mm:ss"
                    @change="searchRecordAction()"
                  />
                </div>
                <div class="item-btn">
                  <el-button type="primary" @click="searchRecordAction" link>
                    <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
                    <span>查询</span>
                  </el-button>
                </div>
              </div>
              <el-table
                :data="rightRecordListTemp"
                v-loading="loadingRight"
                class="tw-grow"
                row-key="id"
                :header-cell-style="tableHeaderStyle"
                stripe
              >
                <el-table-column label="文件名" property="fileName" align="left" :formatter="formatterEmptyData" min-width="160"></el-table-column>
                <el-table-column property="importType" label="导入类型" align="center" min-width="160" :formatter="formatterEmptyData">
                  <template #default="{ row }">
                    {{ findValueInEnum(row.importType, ImportTypeEnum) }}
                  </template>
                </el-table-column>
                <el-table-column property="importCount" label="号码数量" align="left" min-width="160" :formatter="formatterEmptyData">
                  <template #default="{ row }">
                    {{ row.importCount ? formatNumber(row.importCount) : '-' }}
                  </template>
                </el-table-column>
                <el-table-column property="importTime" label="导入时间" align="center" min-width="160" :formatter="formatterEmptyData">
                  <template #default="{ row }">
                    {{ row.importTime ? dayjs(row.importTime).format('YYYY-MM-DD') : '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" min-width="60" align="right" fixed="right">
                  <template #default="{ row }">
                    <el-button v-if="row.status === '1'" type="primary" link @click="cancelRecordAction(row)">撤销导入</el-button>
                    <span v-else class="tw-text-[var(--primary-red-color)]">已撤销</span>
                  </template>
                </el-table-column>
                <!--空数据提示-->
                <template #empty>
                  <el-empty v-if="!rightRecordList || rightRecordList.length < 1" description="暂无数据" />
                </template>
              </el-table>
              <PaginationBox
                :pageSize="pageSize"
                :currentPage="currentPage"
                :total="total || 0"
                @search="searchRecordAction"
                @update="updatePage"
              >
              </PaginationBox>
            </template>
          </div>
        </div>

      </template>
      <template v-else>
        <el-empty class="tw-m-auto"/>
      </template>
    </el-scrollbar>
  </div>
  <EditPhoneDialog
    v-model:visible="rightPhoneVisible"
    :editData="currentRightPhone!"
    groupType="white"
    @confirm="handleConfirm()"
  />
  <AddPhoneDialog
    v-model:visible="rightAddPhoneVisible"
    :groupId="(currentItem?.id||'') + ''"
    groupType="white"
    @confirm="handleConfirm"
  />
  <ImportPhoneDialog
    v-model:visible="rightImportVisible"
    :groupId="currentItem?.id || -1"
    groupType="white"
    @confirm="handleConfirm"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, computed, defineAsyncComponent } from 'vue'
import dayjs from 'dayjs'
import { findValueInEnum, formatterEmptyData, formatNumber, Throttle } from '@/utils/utils'
import { tableHeaderStyle } from '@/assets/js/constant'
import Confirm from '@/components/message-box'
import HeaderBox from '@/components/HeaderBox.vue'
import { useUserStore } from "@/store/user";
import routeMap from '@/router/asyncRoute/route-map'
import { ElMessage } from 'element-plus'
import to from 'await-to-js';
import { whitelistModel } from '@/api/data-filter'
import EditPhoneDialog from '../components/EditPhoneDialog.vue'
import ImportPhoneDialog from '../components/ImportPhoneDialog.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import { WhiteListGroupItem, WhiteBlackListItem, WhiteBlackListRecordItem, ImportTypeEnum } from '@/type/dataFilter'
import TabsBox from '@/components/TabsBox.vue'
import AddPhoneDialog from '../components/AddPhoneDialog.vue'
import PaginationBox from '@/components/PaginationBox.vue'

const SearchPhoneList = defineAsyncComponent({loader: () => import('@/components/blacklist/SearchPhoneList.vue')})

// 用户权限获取
const userStore = useUserStore();
const permissions = userStore.permissions[routeMap['白名单'].id]

// loading信息
const loadingLeft = ref(false)
const loadingRight = ref(false)
const throttleLeft = new Throttle(loadingLeft)
const throttleRight = new Throttle(loadingRight)

/**
 * 左侧模块
 */
// 当前选中左侧元素
const currentItem = ref<WhiteListGroupItem | null>(null)
// 左侧列表
const leftList = ref<WhiteListGroupItem[]>([])

// 搜索左侧
const searchLeftAction = async (id?: number) => {
  if (throttleLeft.check()) {
    return
  }
  throttleLeft.lock()
  const [_, res] = await to(whitelistModel.getGroupList()) as [any, WhiteListGroupItem[]]
  leftList.value = res || [];
  const cur = id ? leftList.value?.find(item => item.id === id) : leftList.value?.find(item => item.id === currentItem.value?.id)
  if (cur) {
    clickLeftItem(cur)
  } else {
    leftList.value?.length > 0 && clickLeftItem(leftList.value[0])
  }
  throttleLeft.unlock()
}
const clickLeftItem = (item: WhiteListGroupItem) => {
  currentItem.value = item
  activeTab.value === tabList[1] && searchRecordAction()
}

// 更新号码数
const updatePhoneCount = async () => {
  if (throttleLeft.check()) {
    return
  }
  if (currentItem.value && currentItem.value?.id) {
    throttleLeft.lock()
    const [err, data] = await to(whitelistModel.updateCount())
    currentItem.value.phoneCount = data ?? currentItem.value.phoneCount
    const index = leftList.value.findIndex(item => item.id === currentItem.value!.id)
    leftList.value[index].phoneCount = data ?? currentItem.value.phoneCount
    !err && ElMessage.success('更新号码数量成功')
    throttleLeft.unlock()
  }
}

/**
 * 右侧模块
 */
const tabList = ['号码', '导入记录']
const activeTab = ref(tabList[0])

/** 右侧列表 - 搜索号码 */ 

const rightPhoneList = ref<WhiteBlackListItem[]>([])
const currentSearchInfo = ref('')
const searchPhoneAction = async (phone: string) => {
  if (throttleRight.check()) {
    return
  }
  throttleRight.lock()
  currentSearchInfo.value = phone || ''
  const [_, res] = await to(whitelistModel.findPhoneList({
    phoneList: (phone || '').split(','),
  })) as [any, WhiteBlackListItem[]]
  rightPhoneList.value = res || [];
  throttleRight.unlock()
}
const delPhoneAction = (row: WhiteBlackListItem) => {
  Confirm({ 
    text: `您确定要删除【${row?.phone||''}】?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(async () => {
    const [err, _] = await to(whitelistModel.deletePhone({
      id: row.id!
    }))
    !err && ElMessage.success('删除成功')
    searchPhoneAction(currentSearchInfo.value)
  }).catch(() => {})
}

 // 右侧弹窗-单个导入\编辑弹窗
const currentRightPhone = ref<WhiteBlackListItem | null>(null)
const rightPhoneVisible = ref(false)
const editRight = (row: WhiteBlackListItem) => {
  currentRightPhone.value = row
  rightPhoneVisible.value = true
}
const handleImportOperator = (type?: string) => {
  if (type === 'single') {
    importSingleRight()
  }
  if (type === 'file') {
    importBatchRight()
  }
}
const rightAddPhoneVisible = ref(false)
const importSingleRight = () => {
  if (!currentItem.value?.id) return
  rightAddPhoneVisible.value = true
}
// 右侧弹窗-导入弹窗
const rightImportVisible = ref(false)
const importBatchRight = () => {
  rightImportVisible.value = true
}
const handleConfirm = (num?: number) => {
  if (activeTab.value === tabList[1]) {
    searchRecordAction()
  } else {
    searchPhoneAction(currentSearchInfo.value||'')
  }
  // 更新号码
  if (num && currentItem.value) {
    currentItem.value.phoneCount = num ?? currentItem.value.phoneCount
    const index = leftList.value.findIndex(item => item.id === currentItem.value!.id)
    leftList.value[index].phoneCount = num ?? currentItem.value.phoneCount
  }
}
/** 右侧列表 - 搜索号码 结束*/ 

// 右侧列表： 导入记录
const searchRightForm = reactive<{
  startTime?: string,
  endTime?: string,
}>({
  startTime: dayjs().add(-7, 'd').format('YYYY-MM-DD HH:mm:ss'),
  endTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
})
const rightRecordList = ref<WhiteBlackListRecordItem[]>([])
const searchRecordAction = async () => {
  if (throttleRight.check()) {
    return
  }
  throttleRight.lock()
  const [_, res] = await to(whitelistModel.getRecordList(searchRightForm)) as [any, WhiteBlackListRecordItem[]]
  rightRecordList.value = res || [];
  total.value = res?.length || 0
  throttleRight.unlock()
}
const cancelRecordAction = async (row: WhiteBlackListRecordItem) => {
  let warningInfo = ''
  if (row.importCount && row.importCount >= 1000000) {
    warningInfo = `<div class="tw-text-[#E54B17] tw-font-[600]">注意：您撤销的数据量为【${row.importCount}】，可能会处理较长时间，请谨慎操作!</div>`
  } else if (row.importCount && row.importCount >= 100000) {
    warningInfo = `<div class="tw-text-[#FFCC00] tw-font-[600]">注意：您撤销的数据量为【${row.importCount}】，可能会处理较长时间，请谨慎操作!</div>`
  }
  Confirm({ 
    text: `您确定要撤销导入${row?.fileName ? '【' + row?.fileName + '】': ''}吗?` + warningInfo,
    type: 'warning',
    title: `撤销导入确认`,
    confirmText: '撤销'
  }).then(async () => {
    loadingRight.value = true
    const [err, _] = await to(whitelistModel.cancelRecord({
      importHistoryId: row.id!
    }))
    if (!err) ElMessage.success('撤销导入成功')
    loadingRight.value = false
  }).catch(() => {}).finally(() => {
    searchRecordAction()
  })
}
// // 对于导入记录，每次切换tab后刷新
const handleTabChange = () => {
  if (activeTab.value == tabList[1]) {
    searchRecordAction()
  }
}

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}
const rightRecordListTemp = computed(() => {
  return rightRecordList.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

// 初始化
const init = async () => {
  searchLeftAction()
}
init()
</script>

<style scoped lang="postcss">
.form-dialog-header {
  font-size: 14px;
}
.el-table {
  font-size: 13px;
  color: var(--primary-black-color-600);
}
.search-box {
  padding: 0 16px 16px;
  display: grid;
}
.item {
    height: 32px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
}
.el-tabs :deep(.el-tabs__item){
  padding-bottom: 10px;
  font-size: 16px;
  height: 30px;
  line-height: 20px;
}
:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
  border: none;
  background-color: #E6E6E6;
}
:deep(.el-tabs__nav-prev), :deep(.el-tabs__nav-next) {
  line-height: 60px;
  font-size: 16px;
}
:deep(.el-tabs__header) {
  margin-bottom: 0;
}
</style>
