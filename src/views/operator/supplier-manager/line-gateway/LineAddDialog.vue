<template>
  <el-dialog
    :model-value.sync="visible"
    width="900px"
    align-center
    :close-on-click-modal="false"
    :show-close="props.closable"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">添加网关线路组成</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="80px"
        ref="editRef"
      >
        <el-form-item label="添加线路：" prop="addLines">
          <SelectBox 
            v-model:selectVal="editData.addLines"
            :options="lineList||[]"
            copyName="supplyLineNumbers"
            name="lineName"
            val="lineNumber"
            placeholder="请选择需要添加的线路（多选）"
            :filterable="true" 
            class="tw-w-[400px]"
            :multiple="true"
          >
          </SelectBox>
        </el-form-item>
      </el-form>
      <SupplyLineTable v-if="lineAllSelectedList.length > 0" :tableData="lineAllSelectedList" :needLeftCol="true">
        <template v-slot:left-col="{ row }">
          <span v-if="!lineSelectedList?.find(item => item.lineNumber === row.lineNumber)" class="status-box blue-status" style="width:32px">
            {{ lineSelectedList?.find(item => item.lineNumber === row.lineNumber) ? '' : '新' }}
          </span>
        </template>
        <template v-slot:operate="{ row }">
          <el-button 
            :disabled="!!lineSelectedList?.find(item => item.lineNumber === row.lineNumber)"
            :type="lineSelectedList?.find(item => item.lineNumber === row.lineNumber) ? 'default' : 'danger'"
            @click="delLineAction(row)"
            link
          >
            删除
          </el-button>
        </template>
      </SupplyLineTable>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="confirm">添加</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, watch, reactive, onUnmounted,} from 'vue'
import { LineInfo } from '@/type/gateway'
import { useGlobalStore } from '@/store/globalInfo'
import type { FormInstance, } from 'element-plus'
import SelectBox from '@/components/SelectBox.vue'
import SupplyLineTable from '@/components/SupplyLineTable.vue'
import { onBeforeRouteLeave } from 'vue-router'

// props和emits
const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean,
  closable?: boolean,
  lineSelectedList: LineInfo[]
  lineUnselectedList: LineInfo[]
}>();

// 全局信息，用于存储复制、粘贴的数据
const globalStore = useGlobalStore()

/** 变量 */
const visible = ref(false)
const lineList = ref<LineInfo[] | null>(props.lineUnselectedList || []) // 线路选项,即未选择的线路（外部传入）
const lineSelectedList = ref<LineInfo[] | null>(props.lineSelectedList || []) // 已选择的线路（外部传入）
const lineAllSelectedList = computed(() => [
  ...(lineList.value?.filter(item => item.lineNumber && editData.addLines.includes(item.lineNumber))||[]),
  ...(lineSelectedList.value || []),
]) // 选择的线路（外部传入）+ 当前选择
class OriginData {
  addLines = []
  delLines = []
}
const editData = reactive<{addLines: string[], delLines: string[] }>(new OriginData()) // 表格数据
const editRef = ref<FormInstance  | null>(null) // 表格ref
const delLineAction = (row: LineInfo) => {
  const index = (lineSelectedList.value||[]).findIndex(item => item.lineNumber === row.lineNumber) || -1
  if (index >= 0) {
    const line = lineSelectedList.value?.splice(index, 1) || []
    line[0] && lineList.value?.push(line[0])
    line[0]?.lineNumber && editData.delLines.push(line[0].lineNumber)
  } else {
    editData.addLines = editData.addLines?.filter(item => item !== row.lineNumber) || []
  }
}

// 表格校验
const rules = {
  // 线路组成
  addLines: [{
    trigger: ['blur'], validator: (rule: any, value: string[], callback: any) => {
      if (lineList.value && lineList.value.length > 0) {
        callback()
      } else {
        callback(new Error('请添加线路组成！'))
      }
    }
  }],
}
// 弹窗-取消
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  emits('update:visible', false)
}
// 弹窗-确认操作
const confirm = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      emits('confirm', editData, )
      emits('update:visible', false)
    }
  })
}

/** watch */ 
// 监听visible等组件入参
watch(() => props.visible, () => {
  visible.value = props.visible
  if (props.visible) {
    lineList.value = props.lineUnselectedList || []
    lineSelectedList.value = props.lineSelectedList || []
    Object.assign(editData, new OriginData())
  }
})

const clearAllData = () => {
  editRef.value = null
  Object.assign(editData, new OriginData())
  lineSelectedList.value = null
  lineList.value = null
}
onUnmounted(() => {
  clearAllData()
})
onBeforeRouteLeave(() => {
  clearAllData()
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {

  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    .el-form-item__content {
      align-items: flex-start;
      justify-content: flex-start;
    }
    &:first-child {
      margin-top: 14px;
    }
    &.muti-items {
      margin-top: 0;
      width: 320px;
      &:last-child {
        margin-left: 20px;
      }
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
.el-table {
  font-size: var(--el-font-size-base);
}
</style>
