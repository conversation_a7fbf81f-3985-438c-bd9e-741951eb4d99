<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    class="merchant-dialog"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        新增靓号规则
      </div>
    </template>

    <div class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="110px"
        >
          <el-form-item prop="supplyLineNumbers" label="生效范围：" required>
            <el-radio-group v-model="allLineSelected" @change="onChangeScopeIsAll">
              <el-radio :label="true">
                全部
              </el-radio>
              <el-radio :label="false">
                部分
              </el-radio>
              <SelectBox
                v-show="!allLineSelected"
                v-model:selectVal="selectedLineNumberList"
                :options="lineAllList||[]"
                name="lineName"
                val="lineNumber"
                placeholder="供应线路"
                filterable
                class="tw-flex-grow"
                multiple
                canSelectAll
                @change="onChangeSelectedLine"
              />
            </el-radio-group>
          </el-form-item>

          <el-form-item prop="lightPhoneBatchParamMap" label="选择靓号规则：">
            <SelectBox
              v-model:selectVal="selectedIdList"
              :options="goodNumberAvailableList||[]"
              name="regexName"
              val="id"
              placeholder="靓号规则"
              filterable
              class="tw-flex-grow"
              multiple
              canSelectAll
              @update:selectVal="onSelectedLineChange"
            >
              <template v-slot:option-tips="{ option }">
                <div class="tw-ml-auto tw-text-[#969799]">{{ option.rank }}</div>
              </template>
            </SelectBox>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" :disabled="loadingConfirm" @click="handleCancel">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" :loading="loadingConfirm" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, watch } from 'vue'
import { Throttle } from '@/utils/utils'
import { ElMessage, FormRules } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { supplierModel } from '@/api/supplier'
import { GoodNumberRestrictionInfo } from '@/type/dataFilter'
import to from 'await-to-js'
import { goodNumberRestrictionModel } from '@/api/data-filter'
import { useSupplierStore } from '@/store/supplier'
import {
  SupplierGoodNumberInfo,
  SupplierGoodNumberMultipleParam,
  SupplierGoodNumberOneParam,
  SupplierLineInfo
} from '@/type/supplier'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = defineProps<{
  visible: boolean,
  // 当前供应商已配置的靓号限制规则列表
  list: SupplierGoodNumberInfo[],
}>()
const emits = defineEmits([
  'close',
  'update'
])

const supplierStore = useSupplierStore()

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)

watch(() => props.visible, async (val) => {
  dialogVisible.value = val
  // 每次显示弹窗时
  if (val) {
    await nextTick()
    // 重置表单
    resetForm()
    // 更新其他内容
    await updateLineAllList()
    await updateGoodNumberAllList()
    updateGoodNumberAvailableList()
    // 默认选中全部
    onChangeScopeIsAll(true)
    onChangeSelectedLine(selectedLineNumberList.value)
    selectedLineNumberList.value = JSON.parse(JSON.stringify(lineNumberAllList.value))
  }
})

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): SupplierGoodNumberMultipleParam => {
  return {
    callLineSupplierId: supplierStore.currentSupplier.id ?? undefined,
    lightPhoneBatchParamMap: {},
  }
}
// 表单数据
const form: SupplierGoodNumberMultipleParam = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  supplyLineNumbers: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!allLineSelected.value && !selectedLineNumberList.value.length) {
        // 选择部分时，已选列表不能为空
        callback(new Error('生效范围不能为空'))
      } else {
        callback()
      }
    }
  },
  lightPhoneBatchParamMap: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!Object.values(value)?.length) {
        callback(new Error('靓号规则不能为空'))
      } else {
        callback()
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
      })
    }
  })
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  let err
  const len = selectedIdList.value.length
  const params = {
    callLineSupplierId: form.callLineSupplierId ?? supplierStore.currentSupplier.id ?? undefined,
  }

  if (len === 1) {
    // 单个
    // 处理参数
    Object.assign(params, {
      lightPhoneId: selectedIdList.value.at(0) ?? undefined,
      allLinesActive: allLineSelected.value,
      supplyLineNumbers: selectedLineNumberList.value,
    } as SupplierGoodNumberOneParam)
    // 请求接口
    const [e,] = <[any, any]>await to(supplierModel.saveOneGoodNumber(params))
    err = e
  } else if (len > 1) {
    // 多个
    // 处理参数
    Object.assign(params, {
      lightPhoneBatchParamMap: form.lightPhoneBatchParamMap ?? undefined,
    } as SupplierGoodNumberMultipleParam)
    // 请求接口
    const [e,] = <[any, any]>await to(supplierModel.saveMultipleGoodNumber(params))
    err = e
  } else {
    err = true
    ElMessage.warning('没有设置正确的生效范围')
  }
  // console.log('params', JSON.parse(JSON.stringify(params)))
  if (err) {
    ElMessage({
      type: 'error',
      message: '保存失败'
    })
    // 节流锁解锁
    throttleConfirm.unlock()
    return
  }
  ElMessage({
    message: '保存成功',
    type: 'success',
  })
  emits('update', params)
  // 关闭弹窗
  closeDialog()

  // 节流锁解锁
  throttleConfirm.unlock()
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
  allLineSelected.value = true
  selectedLineNumberList.value = []
  selectedIdList.value = []
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  dialogVisible.value = false
  emits('close')
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击确定按钮
 */
const handleConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const handleCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 生效范围 开始 ----------------------------------------

// 供应线路列表，全部
const lineAllList = ref<SupplierLineInfo[]>([])
// 供应线路列表，线路编号，全部
const lineNumberAllList = ref<string[]>([])

// 是否全选生效范围
const allLineSelected = ref(true)
// 选中的供应线路编号列表
const selectedLineNumberList = ref<string[]>([])

/**
 * 更新当前线路供应商的全部供应线路
 */
const updateLineAllList = async () => {
  // 获取当前供应商的全部供应线路
  const [, supplierLineRes] = <[any, SupplierLineInfo[]]>await to(supplierModel.getLineList({
    callLineSupplierId: supplierStore.currentSupplier.id ?? undefined
  }))
  lineAllList.value = supplierLineRes?.length ? supplierLineRes : []
  lineNumberAllList.value = lineAllList.value.map((line: SupplierLineInfo) => {
    return line?.lineNumber ?? ''
  })
}
/**
 * 更改生效范围是否全部
 * @param {boolean} val
 */
const onChangeScopeIsAll = (val: boolean) => {
  // console.log('onChangeScopeIsAll', val)
  allLineSelected.value = val
  selectedLineNumberList.value = JSON.parse(JSON.stringify(lineNumberAllList.value))
}
/**
 * 生效范围发生改变
 * @param {string[]} val 已选择的供应线路编号列表
 */
const onChangeSelectedLine = (val: string[]) => {
  // console.log('onChangeSelectedLine', val)
  // 每个选中的靓号规则都添加当前配置的生效范围
  selectedIdList.value.forEach((currentId: number) => {
    // @ts-ignore
    form.lightPhoneBatchParamMap[currentId] = {
      allLinesActive: allLineSelected.value,
      supplyLineNumbers: val
    }
  })
}

// ---------------------------------------- 生效范围 结束 ----------------------------------------

// ---------------------------------------- 靓号规则 开始 ----------------------------------------

// 靓号规则列表，全部
const goodNumberAllList = ref<GoodNumberRestrictionInfo[]>([])
// 靓号规则列表，接口数据
const goodNumberAvailableList = ref<GoodNumberRestrictionInfo[]>([])
// 已选中的靓号规则
const selectedIdList = ref<number[]>([])

/**
 * 靓号规则列表，更新全部列表
 */
const updateGoodNumberAllList = async () => {
  // 请求接口
  const [err, res] = <[any, GoodNumberRestrictionInfo[]]>await to(goodNumberRestrictionModel.getList())
  if (err) {
    ElMessage.error('无法获取靓号规则列表')
    goodNumberAllList.value = []
    return
  }
  // 更新全部列表
  goodNumberAllList.value = res?.length ? res : []
  // 展示名字，如果没有名字，就展示规则内容，都没有就空字符串
  goodNumberAllList.value.forEach((item: SupplierGoodNumberInfo) => {
    item.regexName = item.regexName ?? item.phoneRegex ?? ''
  })
}
/**
 * 靓号规则列表，更新可选列表
 */
const updateGoodNumberAvailableList = () => {
  // 从全部列表里剔除已选，剩下的就是可选
  goodNumberAvailableList.value = goodNumberAllList.value.filter((allItem: GoodNumberRestrictionInfo) => {
    return !props.list.find((item: SupplierGoodNumberInfo) => {
      return item?.lightPhoneId === allItem?.id
    })
  })
}
/**
 * 靓号规则列表，下拉选择框内容变化
 * @param {number[]} val 新内容
 */
const onSelectedLineChange = async (val: number[]) => {
  // 如果清空
  if (!val.length) {
    // 清空相关表单数据
    form.lightPhoneBatchParamMap = {}
    return
  }

  // 如果选中
  form.lightPhoneBatchParamMap = {}
  // 每个选中的靓号规则都添加当前配置的生效范围
  selectedIdList.value.forEach((currentId: number) => {
    // @ts-ignore
    form.lightPhoneBatchParamMap[currentId] = {
      allLinesActive: allLineSelected.value,
      supplyLineNumbers: selectedLineNumberList.value
    }
  })
}

// ---------------------------------------- 靓号规则 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// 如果是每次显示弹窗时都要进行操作，应该放在 dialogVisible 变量的 watch 函数里执行
// 这里是在 vue 组件创建时执行（setup 相当于生命周期的 beforeCreate 和 created），路由不变或者页面不刷新，只会执行一次

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style lang="postcss" scoped>
</style>
