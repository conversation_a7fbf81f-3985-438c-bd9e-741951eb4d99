<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    class="merchant-dialog"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        编辑生效范围
      </div>
    </template>

    <div class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="110px"
        >
          <el-form-item prop="supplyLineNumber" label="生效范围：">
            <el-radio-group v-model="form.allLinesActive" @change="onChangeScopeIsAll">
              <el-radio :label="true">
                全部
              </el-radio>
              <el-radio :label="false">
                部分
              </el-radio>
              <SelectBox
                v-show="!form.allLinesActive"
                v-model:selectVal="form.supplyLineNumbers"
                :options="lineAllList||[]"
                name="lineName"
                val="lineNumber"
                placeholder="供应线路"
                filterable
                class="tw-flex-grow"
                multiple
                canSelectAll
              />
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" :disabled="loadingConfirm" @click="handleCancel">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" :loading="loadingConfirm" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, nextTick, reactive, ref, watch } from 'vue'
import { Throttle } from '@/utils/utils'
import { ElMessage, FormRules } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { supplierModel } from '@/api/supplier'
import to from 'await-to-js'
import { useSupplierStore } from '@/store/supplier'
import { SupplierGoodNumberInfo, SupplierGoodNumberOneParam, SupplierLineInfo } from '@/type/supplier'

// 动态引入组件
const SelectBox = defineAsyncComponent(() => import('@/components/SelectBox.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = defineProps<{
  visible: boolean,
  data: SupplierGoodNumberInfo,
}>()
const emits = defineEmits([
  'close',
  'update'
])

const supplierStore = useSupplierStore()

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)

watch(() => props.visible, async (val) => {
  dialogVisible.value = val
  // 每次显示弹窗时
  if (val) {
    await nextTick()
    resetForm()
    // 更新表单数据
    Object.assign(form, props.data)
    // 生效范围
    if (typeof props.data.isAllLinesActive === 'boolean') {
      form.allLinesActive = props.data.isAllLinesActive
    } else {
      form.allLinesActive = false
    }
    form.supplyLineNumbers = (props?.data?.supplyLines ?? []).map((line: SupplierLineInfo) => {
      return line?.lineNumber ?? ''
    })
    // 更新其他内容
    await updateLineAllList()
  }
})

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): SupplierGoodNumberOneParam => {
  return {
    callLineSupplierId: supplierStore.currentSupplier.id ?? undefined,
    lightPhoneId: undefined,
    allLinesActive: true,
    supplyLineNumbers: [],
  }
}
// 表单数据
const form: SupplierGoodNumberOneParam = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  supplyLineNumbers: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!form.allLinesActive && !value?.length) {
        // 选择部分时，已选列表不能为空
        callback(new Error('生效范围不能为空'))
      } else {
        callback()
      }
    }
  },
  lightPhoneId: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (value === undefined || value === null || value === '') {
        callback(new Error('靓号规则不能为空'))
      } else {
        callback()
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
      })
    }
  })
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 处理参数
  const params: SupplierGoodNumberOneParam = {
    callLineSupplierId: form.callLineSupplierId ?? supplierStore.currentSupplier.id ?? undefined,
    lightPhoneId: form.lightPhoneId ?? undefined,
    allLinesActive: !!form.allLinesActive ?? false,
    supplyLineNumbers: form.supplyLineNumbers ?? [],
  }
  // 请求接口
  const [err,] = <[any, SupplierGoodNumberInfo]>await to(supplierModel.saveOneGoodNumber(params))
  if (err) {
    ElMessage({
      type: 'error',
      message: '保存失败'
    })
    // 节流锁解锁
    throttleConfirm.unlock()
    return
  }
  ElMessage({
    message: '保存成功',
    type: 'success',
  })
  emits('update', params)
  // 关闭弹窗
  closeDialog()

  // 节流锁解锁
  throttleConfirm.unlock()
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
  allLineSelected.value = true
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  dialogVisible.value = false
  emits('close')
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击确定按钮
 */
const handleConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const handleCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 供应线路列表 开始 ----------------------------------------

// 生效范围，选中全部
const allLineSelected = ref(true)

// 供应线路列表，全部
const lineAllList = ref<SupplierLineInfo[]>([])
// 供应线路列表，线路编号，全部
const lineNumberAllList = ref<string[]>([])

/**
 * 更新当前线路供应商的全部供应线路
 */
const updateLineAllList = async () => {
  // 获取当前供应商的全部供应线路
  const [, supplierLineRes] = <[any, SupplierLineInfo[]]>await to(supplierModel.getLineList({
    callLineSupplierId: supplierStore.currentSupplier.id ?? undefined
  }))
  lineAllList.value = supplierLineRes?.length ? supplierLineRes : []
  lineNumberAllList.value = lineAllList.value.map((line: SupplierLineInfo) => {
    return line?.lineNumber ?? ''
  })
}
/**
 * 更改生效范围是否全部
 * @param {boolean} val
 */
const onChangeScopeIsAll = (val: boolean) => {
  allLineSelected.value = val
  // 如果是全部，自动选中所有供应线路
  if (val) {
    form.supplyLineNumbers = JSON.parse(JSON.stringify(lineNumberAllList.value))
  }
}

// ---------------------------------------- 供应线路列表 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// 如果是每次显示弹窗时都要进行操作，应该放在 dialogVisible 变量的 watch 函数里执行
// 这里是在 vue 组件创建时执行（setup 相当于生命周期的 beforeCreate 和 created），路由不变或者页面不刷新，只会执行一次

updateLineAllList()

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style lang="postcss" scoped>
</style>
