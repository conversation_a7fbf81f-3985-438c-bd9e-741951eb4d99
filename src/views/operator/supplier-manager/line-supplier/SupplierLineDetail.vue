<template>
  <!--详情标题-->
  <HeaderBox :title="[{title: '线路供应商'}, {title: form.id!>-1?'编辑线路':'创建线路'}]" />

  <!--详情主体-->
  <el-scrollbar class="submodule-detail" wrap-class="detail-main">
    <!--表单宽度，表单本体最大宽度是外呼支持范围组件的910，再加上左右两侧内边距各24，总共910+48=958-->
    <el-form
      ref="formRef"
      label-width="90px"
      label-position="right"
      inline
      class="tw-max-w-[958px] tw-mx-auto"
      :model="form"
      :rules="rules"
    >
      <div class="form-block tw-w-full">
        <div class="form-block-title">
          线路归属
        </div>
        <el-form-item label="供应商ID：">
          <span style="width: 190px; word-break: break-all;">{{ supplier.supplierNumber }}</span>
        </el-form-item>
        <el-form-item label="供应商名称：">
          <span style="width: 190px; word-break: break-all;">{{ supplier.supplierName }}</span>
        </el-form-item>
        <el-form-item label="供应商简称：" style="margin-right: 0;">
          <span style="width: 190px; word-break: break-all;">{{ supplier.supplierProfile }}</span>
        </el-form-item>

        <!--分割线-->
        <hr class="tw-my-[8px]">

        <!--基本信息-->
        <div class="form-block-title">
          基本信息
        </div>
        <el-form-item label="线路编号：">
          <el-input v-model.trim="form.lineNumber" placeholder="创建成功后自动生成" clearable disabled style="width: 240px;" />
        </el-form-item>
        <el-form-item label="线路类型：" prop="lineType">
          <!--新建线路，允许修改，启用-->
          <!--复制线路，允许修改，启用-->
          <!--编辑线路，禁止修改，禁用-->
          <el-select
            v-model.trim="form.lineType"
            clearable
            placeholder="请选择线路类型"
            style="width: 240px;"
            :disabled="line.id! > -1"
            @change="onChangeLineType"
          >
            <el-option
              v-for="lineTypeItem in Object.values(supplierLineTypeList)"
              :key="lineTypeItem.value"
              :value="lineTypeItem.value"
              :label="lineTypeItem.name"
            />
          </el-select>
        </el-form-item>
        <br>
        <el-form-item label="线路名称：" prop="lineName">
          <el-input
            v-model.trim="form.lineName"
            placeholder="请输入线路名称"
            clearable
            maxlength="50"
            show-word-limit
            style="width: 240px;"
          />
        </el-form-item>
        <el-form-item label="数据传输：" prop="lineName">
          <el-select v-model.trim="form.isForEncryptionPhones" placeholder="请选择数据传输方式" style="width: 240px;">
            <el-option
              v-for="item in dataEncryptionMethodOption"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            />
          </el-select>
        </el-form-item>
        <br>
        <el-form-item label="启用状态：" prop="enableStatus">
          <el-select v-model.trim="form.enableStatus" placeholder="请选择状态" style="width: 240px;">
            <el-option
              v-for="lineStatusItem in Object.values(supplierLineStatusList)"
              :key="lineStatusItem.name"
              :value="lineStatusItem.val"
              :label="lineStatusItem.text"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="挂起状态：" prop="pending">
          <el-select v-model="form.pending" style="width: 240px;">
            <el-option :value="true" label="已挂起" />
            <el-option :value="false" label="未挂起" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注：">
          <el-input
            v-model.trim="form.notes"
            type="textarea"
            placeholder="请输入备注，不超过250字。挂起原因备注请填入【】内。"
            clearable
            maxlength="250"
            show-word-limit
            autosize
            resize="none"
            style="width: 600px;"
          />
        </el-form-item>

        <!--通讯要素-->
        <div class="form-block-title">
          通讯要素
        </div>
        <el-form-item label="主叫号码：" prop="masterCallNumber">
          <el-input
            v-model.trim="form.masterCallNumber"
            placeholder="请输入主叫号码"
            clearable
            style="width: 240px;"
          />
        </el-form-item>
        <el-form-item label="前缀：">
          <!--被禁用并且没有内容时，不显示占位符-->
          <el-input
            v-model.trim="form.prefix"
            :placeholder="readonly ? '' : '请输入前缀'"
            clearable
            style="width: 210px;"
          />
          <!--信息提示图标-->
          <div class="tw-flex tw-justify-center tw-items-center tw-ml-[8px]">
            <el-tooltip content="请确认该供应线路是否采用前缀方式进行外呼" placement="bottom" effect="dark">
              <el-icon size="20">
                <SvgIcon name="warning" />
              </el-icon>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item label="注册IP：" prop="registerIp">
          <el-input
            v-model.trim="form.registerIp"
            placeholder="请输入注册IP"
            clearable
            style="width: 240px;"
            @input="handleRegisterIpInput"
          />
        </el-form-item>
        <el-form-item label="注册端口：" prop="registerPort">
          <el-input v-model.trim="form.registerPort" placeholder="请输入注册端口" clearable style="width: 240px;" />
        </el-form-item>

        <!--业务限制-->
        <div class="form-block-title">
          业务限制
        </div>
        <el-form-item label="并发上限：" prop="concurrentLimit">
          <el-input
            v-model.trim="form.concurrentLimit"
            placeholder="请输入SIP线路并发上限"
            clearable
            style="width: 240px;"
          />
        </el-form-item>

        <!--AI外呼显示速率限制，人工直呼不显示速率限制-->
        <el-form-item v-if="form.lineType===supplierLineTypeList.AI_OUTBOUND_CALL.value" label="速率限制：" prop="caps">
          <el-input-number
            v-model.trim="form.caps"
            placeholder="无"
            clearable
            :controls="true"
            controls-position="right"
            :precision="0"
            :min="1"
            :max="1000"
            style="width: 170px;"
          />
          <span class="tw-ml-[8px]">次/秒</span>
          <!--信息提示图标-->
          <div class="tw-flex tw-justify-center tw-items-center tw-ml-[8px]">
            <el-tooltip content="CAPS限制：每秒钟该线路外呼的次数上限，支持输入1~1000的正整数。" placement="bottom" effect="dark">
              <el-icon size="20">
                <SvgIcon name="warning" />
              </el-icon>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item v-else>
          <div style="width: 240px;"></div>
        </el-form-item>

        <el-form-item label="外呼类型：" prop="outboundTypes">
          <el-select
            v-model.trim="form.outboundTypes"
            placeholder="请选择外呼类型，可多选"
            clearable
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="2"
            style="width: 240px;"
          >
            <el-option
              v-for="outboundTypeItem in Object.values(supplierLineOutboundTypeList)"
              :key="outboundTypeItem.name"
              :value="outboundTypeItem.val"
              :label="outboundTypeItem.text"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="适用行业：" prop="secondIndustries">
          <el-cascader
            v-model.trim="form.secondIndustries"
            :options="industryOptions"
            :props="industryProps"
            :show-all-levels="false"
            collapse-tags
            collapse-tags-tooltip
            placeholder="请选择行业"
            clearable
            style="width: 240px;"
          />
        </el-form-item>
        <el-form-item label="所属运营商：" prop="serviceProviders">
          <el-select
            v-model.trim="form.serviceProviders"
            placeholder="请选择所属运营商"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="2"
            style="width: 240px;"
            @change="handleProvidersSelectValChange"
          >
            <el-option
              v-for="operatorItem in Object.values(supplierOperatorList)"
              :key="operatorItem.name"
              :value="operatorItem.val"
              :label="operatorItem.text"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="接入类型：" prop="lineAccessType">
          <el-select
            v-model.trim="form.lineAccessType"
            placeholder="请选择接入类型"
            style="width: 240px;"
          >
            <el-option
              v-for="lineAccessTypeItem in supplierLineAccessTypeList"
              :key="lineAccessTypeItem.name"
              :value="lineAccessTypeItem.val"
              :label="lineAccessTypeItem.text"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="外显号码：" prop="displayCallNumber">
          <el-input
            v-model.trim="form.displayCallNumber"
            placeholder="请输入外显号码"
            clearable
            style="width: 240px;"
          />
        </el-form-item>

        <!--频率限制-->
        <div class="form-block-title">
          线路频率限制
        </div>
        <el-form-item :ref="restrictionsRef['callingRestrictions']" label="线路拨打限制：" :label-width="120" prop="callingRestrictions" class="muti-items">
          <div class="tw-w-[240px] tw-flex tw-flex-col tw-items-left">
            <div>
              <el-button type="primary" link :icon="Plus" @click="addRestriction('callingRestrictions')">添加限制</el-button>
            </div>
            <div
              v-for="(callingRestriction, index) in form.callingRestrictions"
              v-if="form.callingRestrictions && form.callingRestrictions.length > 0"
              :key="callingRestriction + index"
              class="tw-flex tw-justify-between tw-mt-[8px]"
            >
              <el-input-number
                :model-value="parseInt(callingRestriction.split('-')[0]) || undefined"
                placeholder="请输入"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 70px;"
                @change="(v: string) => updateRestriction(v, callingRestriction.split('-')[1], index, 'callingRestrictions')"
              />
              <span>次</span>
              <el-input-number
                :model-value="parseInt(callingRestriction.split('-')[1]) || undefined"
                placeholder="请输入"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 70px;"
                @change="(v: string) => updateRestriction(callingRestriction.split('-')[0], v, index, 'callingRestrictions')"
              />
              <span>小时</span>
              <el-button type="primary" link :icon="CloseBold" @click="delRestriction('callingRestrictions', callingRestriction)"></el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item :ref="restrictionsRef['dialingRestrictions']" label="线路拨通限制：" :label-width="120" prop="dialingRestrictions" class="muti-items">
          <div class="tw-w-[240px] tw-flex tw-flex-col tw-items-left">
            <div>
              <el-button type="primary" link :icon="Plus" @click="addRestriction('dialingRestrictions')">添加限制</el-button>
            </div>
            <div
              v-for="(dialingRestriction, index) in form.dialingRestrictions"
              v-if="form.dialingRestrictions && form.dialingRestrictions.length > 0"
              :key="dialingRestriction + index"
              class="tw-flex tw-justify-between tw-mt-[8px]"
            >
              <el-input-number
                :model-value="parseInt(dialingRestriction.split('-')[0]) || undefined"
                placeholder="请输入"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 70px;"
                @change="(v: string) => updateRestriction(v, dialingRestriction.split('-')[1], index, 'dialingRestrictions')"
              />
              <span>次</span>
              <el-input-number
                :model-value="parseInt(dialingRestriction.split('-')[1]) || undefined"
                placeholder="请输入"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 70px;"
                @change="(v: string) => updateRestriction(dialingRestriction.split('-')[0], v, index, 'dialingRestrictions')"
              />
              <span>小时</span>
              <el-button type="primary" link :icon="CloseBold" @click="delRestriction('dialingRestrictions', dialingRestriction)"></el-button>
            </div>
          </div>
        </el-form-item>

        <!--频率限制-->
        <div class="form-block-title">
          平台频率限制
        </div>
        <el-form-item :ref="restrictionsRef['dialingRestrictionsGlobal']" label="平台拨打限制：" :label-width="120" prop="callingRestrictionsGlobal" class="muti-items">
          <div class="tw-w-[240px] tw-flex tw-flex-col tw-items-left">
            <div>
              <el-button type="primary" link :icon="Plus" @click="addRestriction('callingRestrictionsGlobal')">添加限制</el-button>
            </div>
            <div
              v-for="(callingRestriction, index) in form.callingRestrictionsGlobal"
              v-if="form.callingRestrictionsGlobal && form.callingRestrictionsGlobal.length > 0"
              :key="callingRestriction + index"
              class="tw-flex tw-justify-between tw-mt-[8px]"
            >
              <el-input-number
                :model-value="parseInt(callingRestriction.split('-')[0]) || undefined"
                placeholder="请输入"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 70px;"
                @change="(v: string) => updateRestriction(v, callingRestriction.split('-')[1], index, 'callingRestrictionsGlobal')"
              />
              <span>次</span>
              <el-input-number
                :model-value="parseInt(callingRestriction.split('-')[1]) || undefined"
                placeholder="请输入"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 70px;"
                @change="(v: string) => updateRestriction(callingRestriction.split('-')[0], v, index, 'callingRestrictionsGlobal')"
              />
              <span>小时</span>
              <el-button type="primary" link :icon="CloseBold" @click="delRestriction('callingRestrictionsGlobal', callingRestriction)"></el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item :ref="restrictionsRef['dialingRestrictionsGlobal']" label="平台拨通限制：" :label-width="120" prop="dialingRestrictionsGlobal" class="muti-items">
          <div class="tw-w-[240px] tw-flex tw-flex-col tw-items-left">
            <div>
              <el-button type="primary" link :icon="Plus" @click="addRestriction('dialingRestrictionsGlobal')">添加限制</el-button>
            </div>
            <div
              v-for="(dialingRestriction, index) in form.dialingRestrictionsGlobal"
              v-if="form.dialingRestrictionsGlobal && form.dialingRestrictionsGlobal.length > 0"
              :key="dialingRestriction + index"
              class="tw-flex tw-justify-between tw-mt-[8px]"
            >
              <el-input-number
                :model-value="parseInt(dialingRestriction.split('-')[0]) || undefined"
                placeholder="请输入"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 70px;"
                @change="(v: string) => updateRestriction(v, dialingRestriction.split('-')[1], index, 'dialingRestrictionsGlobal')"
              />
              <span>次</span>
              <el-input-number
                :model-value="parseInt(dialingRestriction.split('-')[1]) || undefined"
                placeholder="请输入"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 70px;"
                @change="(v: string) => updateRestriction(dialingRestriction.split('-')[0], v, index, 'dialingRestrictionsGlobal')"
              />
              <span>小时</span>
              <el-button type="primary" link :icon="CloseBold" @click="delRestriction('dialingRestrictionsGlobal', dialingRestriction)"></el-button>
            </div>
          </div>
        </el-form-item>

        <div class="form-block-title">
          时间限制
        </div>
        <el-form-item label="时间限制：" prop="disableTimeSlots">
          <el-button size="small" @click="onClickTimeSlot">
            选择时间段
          </el-button>
        </el-form-item>
        <br>
        <el-form-item label="选中时间段：">
          <TagsBox :tagsArr="timeSlotStrList" tagsName="选中时间段" />
        </el-form-item>

        <div class="form-block-title">
          线路计费
        </div>
        <el-form-item label="计费周期：" prop="billingCycle" required>
          <div class="tw-flex tw-flex-row tw-justify-start tw-items-center">
            <el-select v-model.trim="form.billingCycle" placeholder="请选择计费周期" style="width: 100px;">
              <el-option
                v-for="lineStatusItem in [60,6]"
                :key="lineStatusItem"
                :value="lineStatusItem"
                :label="lineStatusItem"
              />
            </el-select>
            <span class="tw-ml-[8px]">秒</span>
          </div>
        </el-form-item>
        <el-form-item label="线路单价：" prop="unitPrice" required>
          <div class="tw-flex tw-flex-row tw-justify-start tw-items-center">
            <el-input-number
              v-model.trim="form.unitPrice"
              placeholder="请输入"
              :precision="3"
              :step="0.001"
              :min="0.000"
              :max="10.000"
              controls-position="right"
              clearable
              style="width: 120px;"
            />
            <span class="tw-ml-[8px]">元</span>
          </div>
          <div v-show="unitPriceHintVisible" class="tw-ml-[8px] tw-text-red-500">
            输入金额>=0.1元，请保证金额正确!
          </div>
        </el-form-item>

        <!--新建线路时不显示-->
        <!--线路类型为人工直呼时禁用-->
        <div v-if="form.id!==-1" class="form-block-title">
          线路网关
        </div>
        <el-form-item v-if="form.id !== -1" label="所属网关：" prop="lineGatewayIds">
          <SelectBox
            v-model:selectVal="form.lineGatewayIds"
            :options="gatewayList"
            name="name"
            val="id"
            :disabled="form.lineType===supplierLineTypeList.MANUAL_DIRECT_CALL.value"
            :placeholder="form.lineType===supplierLineTypeList.MANUAL_DIRECT_CALL.value?'人工直呼线路不能配置线路网关':'请选择线路网关'"
            filterable
            class="tw-w-[300px]"
            multiple
          >
          </SelectBox>
        </el-form-item>

        <!--外呼范围-->
        <div class="form-block-title">
          支持外呼范围
        </div>

        <el-form-item ref="scopeRef" v-model.trim="form.cityCodeGroups" prop="cityCodeGroups">
          <!--地区组件-->
          <CitySettingBox
            ref="restrictComponentRef"
            :taskRestrictData="taskRestrictData"
            :selectedOperatorList="selectedOperatorList"
            :readonly="readonly"
            loadByCity
            @update:data="handleCityUpdate"
          />
        </el-form-item>
      </div>
    </el-form>
  </el-scrollbar>

  <!--详情底部-->
  <div class="submodule-detail">
    <div class="detail-footer">
      <div class="detail-footer-inner">
        <el-button
          :icon="CloseBold"
          :disabled="loadingConfirm"
          @click="handleCancel"
        >
          {{ dialogText.cancelButtonText }}
        </el-button>
        <el-button
          type="primary"
          :icon="Select"
          :loading="loadingConfirm"
          :disabled="loadingConfirm"
          @click="handleConfirm"
        >
          {{ dialogText.confirmButtonText }}
        </el-button>
      </div>
    </div>
  </div>

  <!--时间限制弹窗-->
  <TimeRangePickerDialog
    v-model:visible="dialogTimePickerVisible"
    format="HH:mm"
    defaultStart="00:00"
    defaultEnd="24:00"
    :edit-data="{startWorkTimeList, endWorkTimeList}"
    @confirm="onUpdateDialogTimePicker"
  />
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, nextTick, onMounted, reactive, ref, toRaw } from 'vue'
import { useSupplierStore } from '@/store/supplier'
import {
  dataEncryptionMethodOption,
  supplierLineAccessTypeList,
  supplierLineOutboundTypeList,
  supplierLineStatusList,
  supplierOperatorEnum,
  supplierOperatorList,
  supplierOperatorMap,
  supplierOperatorTabNameMap,
} from '@/assets/js/map-supplier'
import { CloseBold, Plus, Select } from '@element-plus/icons-vue'
import { SupplierInfo, SupplierLineInfo, supplierLineTypeList, SupplierMapItemType } from '@/type/supplier'
import { OperatorEnum, RestrictModal } from '@/type/common'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { ElMessage, FormRules } from 'element-plus'
import { dialogLineFormRules } from './DialogLineFormRules'
import router from '@/router'
import { supplierModel } from '@/api/supplier'
import { gatewayModel } from '@/api/gateway'
import { GatewayItem } from '@/type/gateway'
import { MerchantLineConstituteParams, } from '@/type/merchant'
import {
  convertTimeSlotApiToComponent,
  convertTimeSlotComponentToApi,
  convertTimeSlotComponentToDisplay
} from '@/utils/utils'
import SelectBox from '@/components/SelectBox.vue'
import { FormInstance } from 'ant-design-vue'
import { trace } from '@/utils/trace'

// 动态引入组件
const TimeRangePickerDialog = defineAsyncComponent(() => import('@/components/TimeRangePickerDialog.vue'))
const HeaderBox = defineAsyncComponent(() => import('@/components/HeaderBox.vue'))
const TagsBox = defineAsyncComponent(() => import('@/components/TagsBox.vue'))
const CitySettingBox = defineAsyncComponent(() => import('@/components/CitySettingBox.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

// 全局变量
const globalStore = useGlobalStore()
const { allIndustryList } = storeToRefs(globalStore)
const supplierStore = useSupplierStore()

// 当前编辑的线路的相关信息，只读，不应该修改
const supplier = reactive<SupplierInfo>(supplierStore.currentSupplier)
const line = reactive<SupplierLineInfo>(supplierStore.editingSupplierLine)
const readonly = ref<boolean>(supplierStore.readonly)

onMounted(() => {
  updateAllIndustryList()
  updateGatewayList()

  Object.assign(line, JSON.parse(JSON.stringify(supplierStore.editingSupplierLine)))
  Object.assign(supplier, JSON.parse(JSON.stringify(supplierStore.currentSupplier)))
  Object.assign(readonly, JSON.parse(JSON.stringify(supplierStore.readonly)))
  Object.assign(form, JSON.parse(JSON.stringify(line)))

  // 将接口数据转换为组件数据
  // 呼叫时间段限制
  convertApiToComponentTimeList()
  // 呼叫时间限制
  updateTimeSlot()
  // 外呼支持范围
  scopeToData()
})

// 默认开始时间
const defaultStartTime = '00:00'
// 默认结束时间
const defaultEndTime = '24:00'

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 表单DOM
const formRef = ref()
// 表单默认值
const formDefault: SupplierLineInfo = {
  // 线路ID
  id: -1,

  // 供应商ID
  callLineSupplierId: supplier.id!,
  // 供应商编号
  callLineSupplierNumber: supplier.supplierNumber,

  // 线路编号
  lineNumber: '',
  // 线路类型
  lineType: '',
  // 线路名称
  lineName: '',
  // 数据传输方式
  isForEncryptionPhones: false,
  // 启用状态
  enableStatus: supplierLineStatusList.enabled.val,
  // 挂起状态
  pending: false,
  // 备注
  notes: '',

  // 主叫号码
  masterCallNumber: '',
  // 前缀
  prefix: '',
  // 注册IP
  registerIp: '',
  // 注册端口
  registerPort: '',

  // 并发上限
  concurrentLimit: '',
  // 速率限制
  caps: null,
  // 外呼类型
  outboundTypes: [],
  // 适用行业 二级行业
  secondIndustries: [],
  // 所属运营商
  serviceProviders: [supplierOperatorList.all.val],
  // 接入类型
  lineAccessType: supplierLineAccessTypeList.direct.val,
  // 外显号码
  displayCallNumber: '',

  // 线路拨打限制
  callingRestrictions: [],
  // 线路拨通限制
  dialingRestrictions: [],
  // 平台拨打限制
  callingRestrictionsGlobal: [],
  // 平台拨通限制
  dialingRestrictionsGlobal: [],

  // 时间限制
  disableTimeSlots: convertTimeSlotComponentToApi([defaultStartTime], [defaultEndTime]),

  // 计费周期
  billingCycle: 60,
  // 线路单价
  unitPrice: 0.000,

  // 所属网关
  lineGatewayIds: [],

  // 外呼支持范围
  cityCodeGroups: [],
}
// 表单内容
const form = reactive<SupplierLineInfo>({ ...formDefault })

// 正在提交信息
const loadingConfirm = ref<boolean>(false)
// 表单校验规则
const rules = reactive<FormRules>(dialogLineFormRules)
// 弹窗文本
const dialogText = {
  // 编辑实体时的弹窗标题
  editingTitle: '查看供应线路',
  // 新建实体时的弹窗标题
  creatingTitle: '创建供应线路',
  // 取消按钮文本
  cancelButtonText: '取消',
  // 确定按钮文本
  confirmButtonText: '确定',
  // 编辑成功消息
  msgEditSuccessfully: '供应商线路编辑成功',
  // 创建成功消息
  msgCreateSuccessfully: '供应商线路创建成功',
  // 编辑失败消息
  msgEditFailed: '供应商线路编辑失败',
  // 创建失败消息
  msgCreateFailed: '供应商线路创建失败',
}

/**
 * 表单提交的具体实现
 */
const submitCallback = async (result: SupplierLineInfo) => {
  // 将表单数据转换成接口参数
  const {
    id,

    lineNumber,
    lineType,
    lineName,
    isForEncryptionPhones,
    enableStatus,
    pending,
    notes,

    masterCallNumber,
    prefix,
    registerIp,
    registerPort,

    concurrentLimit,
    caps,
    outboundTypes,
    secondIndustries,
    serviceProviders,
    lineAccessType,
    displayCallNumber,

    callingRestrictions,
    dialingRestrictions,
    callingRestrictionsGlobal,
    dialingRestrictionsGlobal,

    disableTimeSlots,

    billingCycle,
    unitPrice,

    lineGatewayIds,

    cityCodeGroups,
  } = result

  // 根据之前备份的外呼范围ID映射表，按运营商读取ID
  cityCodeGroups?.forEach((item) => {
    const operator = supplierOperatorMap.get(<supplierOperatorEnum>item?.serviceProvider)
    const componentProvince = operator?.province
    const componentCity = operator?.city
    item.id = scopeIdList[<string>(componentProvince ?? componentCity)] ?? undefined
  })

  // 处理参数
  const params: SupplierLineInfo = {
    id,

    callLineSupplierId: supplier.id ?? -1,
    callLineSupplierNumber: supplier.supplierNumber ?? '',

    lineNumber,
    lineType,
    lineName,
    isForEncryptionPhones,
    enableStatus,
    pending,
    notes,

    masterCallNumber,
    prefix,
    registerIp,
    registerPort,

    concurrentLimit,
    caps,
    outboundTypes,
    secondIndustries,
    serviceProviders,
    lineAccessType,
    displayCallNumber,

    callingRestrictions,
    dialingRestrictions,
    callingRestrictionsGlobal,
    dialingRestrictionsGlobal,

    disableTimeSlots,

    billingCycle,
    unitPrice,

    lineGatewayIds,

    cityCodeGroups,
  }

  if (params.id === -1) {
    // 新增
    params.id = undefined
    trace({ page: '线路供应商-创建供应线路', params })
    await supplierModel.addLine(params)
  } else {
    // 编辑
    trace({ page: '线路供应商-编辑供应线路', params })
    await supplierModel.updateLine(params)
  }
}
/**
 * 提交
 */
const submit = async () => {
  try {
    // 表单显示成正在提交的加载状态
    loadingConfirm.value = true

    // 请求接口
    await submitCallback(toRaw(form))

    ElMessage({
      message: form.id && form.id > -1 ? dialogText.msgEditSuccessfully : dialogText.msgCreateSuccessfully,
      duration: 3000,
      type: 'success',
    })

    // 如果是新建供应线路并且线路类型是人工直呼，弹窗提示挂在黑名单
    if (form.id === -1 && form.lineType === supplierLineTypeList.MANUAL_DIRECT_CALL.value) {
      supplierStore.needRemindBlackGroup = true
    }

    // 关闭弹窗
    closeDialog()
  } catch (e) {
    ElMessage({
      message: form.id && form.id > -1 ? dialogText.msgEditFailed : dialogText.msgCreateFailed,
      duration: 3000,
      type: 'error',
    })
  } finally {
    setTimeout(() => {
      // 取消加载状态
      loadingConfirm.value = false
    }, 200)
  }
}
/**
 * 表单校验
 */
const validForm = () => {
  // 表单DOM不存在
  if (!formRef.value) {
    return
  }

  updateTaskRestrictData()
  // 表单DOM
  // 回调函数里的参数valid是指表单校验是否通过，是UI组件传来的
  formRef.value.validate(async (valid: boolean) => {
    // 校验通过
    if (valid) {
      // 发送修改后的数据
      await submit()
    } else {
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        duration: 3000,
        type: 'warning',
      })
    }
  })
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  // 重置表单数据
  resetForm()
  Object.assign(taskRestrictData, new RestrictModalOrigin())
  // 切换路由
  router.back()
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 重置表单数据
  Object.assign(form, JSON.parse(JSON.stringify(formDefault)))
  // 清除表单的校验结果
  formRef.value.resetFields()
}
/**
 * 点击取消按钮
 */
const handleCancel = () => {
  // 回到供应商列表
  closeDialog()
}
/**
 * 点击确定按钮
 */
const handleConfirm = () => {
  // 表单校验
  validForm()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 基本信息 开始 ----------------------------------------

/**
 * 修改线路类型
 * @param {string} val 新值
 */
const onChangeLineType = (val: string) => {
  // 如果从AI外呼变成人工直呼，则需要重置速率限制
  if (val === supplierLineTypeList.MANUAL_DIRECT_CALL.value) {
    form.caps = null
  }
}

// ---------------------------------------- 基本信息 结束 ----------------------------------------

// ---------------------------------------- 通讯要素 开始 ----------------------------------------

/**
 * 注册IP的文本内容变化
 */
const handleRegisterIpInput = () => {
  // 不是数字和半角句号，直接移除
  form.registerIp = form.registerIp?.replace(/[^0-9.]/g, '')
}

// ---------------------------------------- 通讯要素 结束 ----------------------------------------

// ---------------------------------------- 业务限制 开始 ----------------------------------------

// 适用行业，级联选择器，数据内容
const industryOptions = ref<{
  label: string,
  value: string,
  children: {
    label: string,
    value: string
  }[]
}[]>([])
// 适用行业，级联选择器，配置信息
const industryProps = { multiple: true, emitPath: false }

/**
 * 更新全部二级行业列表
 */
const updateAllIndustryList = async () => {
  await globalStore.getAllIndustryList()
  industryOptions.value = allIndustryList.value.map((primaryItem) => {
    return {
      label: primaryItem?.primaryIndustry ?? '',
      value: primaryItem?.primaryIndustry ?? '',
      children: (primaryItem?.secondaryIndustries ?? []).map((secondItem) => {
        return {
          label: secondItem?.name ?? '',
          value: secondItem?.name ?? '',
        }
      })
    }
  })
}

// ---------------------------------------- 业务限制 结束 ----------------------------------------

// ---------------------------------------- 频率限制 开始 ----------------------------------------

// 线路/平台限制DOM
const restrictionsRef = ref<Record<'callingRestrictions' | 'callingRestrictionsGlobal' | 'dialingRestrictions' | 'dialingRestrictionsGlobal', (null | FormInstance)>>({
  'callingRestrictions': null,
  'callingRestrictionsGlobal': null,
  'dialingRestrictions': null,
  'dialingRestrictionsGlobal': null
})
/**
 * 频率限制新增规则
 * type: 1: 拨打限制; 2: 拨通限制 3： 平台拨打限制; 2: 平台拨通限制；
 */
const addRestriction = ( type: 'callingRestrictions' | 'callingRestrictionsGlobal' | 'dialingRestrictions' | 'dialingRestrictionsGlobal') => {
  if (!form[type]) {
    form[type] = []
  } else {
    let msg = ''
    form[type].map(item => {
      const arr = item.split('-')
      if (!arr[0] || !arr[1]) {
        msg = '请确保上一条限制规则填写完整'
      }
    })
    if (msg) {
      return ElMessage({
        type: 'warning',
        message: msg
      })
    }
  }
  form[type].push('-')
}
/**
 * 频率限制删除规则
 * @param {number} type 1: 线路拨打限制; 2: 线路拨通限制； 3： 平台拨打限制; 2: 平台拨通限制；
 * @param {string} row 要删除的项
 */
const delRestriction = async (
  type: 'callingRestrictions' | 'callingRestrictionsGlobal' | 'dialingRestrictions' | 'dialingRestrictionsGlobal',
  row: string
) => {
  form[type] = form[type]?.filter(item => item !== row) || []
  // 更新表单校验结果
  restrictionsRef.value[type]?.validate()
}

/**
 * 更新拨打限制
 * @param val1 次数
 * @param val2 小时
 * @param index 更新对应的拨打限制，转换为val1-val2
 * @param key 'callingRestrictions' | 'callingRestrictionsGlobal' | 'dialingRestrictions' | 'dialingRestrictionsGlobal'
 */
const updateRestriction = async (
  val1: string,
  val2: string,
  index: number, 
  key: 'callingRestrictions' | 'callingRestrictionsGlobal' | 'dialingRestrictions' | 'dialingRestrictionsGlobal'
) => {
  if (form[key]) {
    form[key][index] = `${val1 || ''}-${val2 || ''}`
  }
  // 更新表单校验结果
  restrictionsRef.value[key]?.validate()
}

// ---------------------------------------- 频率限制 结束 ----------------------------------------

// ---------------------------------------- 时间限制 开始 ----------------------------------------

// 显示时间限制弹窗
const dialogTimePickerVisible = ref(false)
// 允许时间段开始位置 组件数据
const startWorkTimeList = ref<string[]>([defaultStartTime])
// 禁止时间段开始位置 组件数据
const endWorkTimeList = ref<string[]>([defaultEndTime])
// 允许时间段的文本 页面展示
const timeSlotStrList = ref<string[]>([])

/**
 * 点击选择时间段按钮
 */
const onClickTimeSlot = () => {
  dialogTimePickerVisible.value = true
}
/**
 * 编辑页面 选中时间段 接口数据转换成组件数据
 */
const convertApiToComponentTimeList = () => {
  const result = convertTimeSlotApiToComponent(form.disableTimeSlots ?? [])
  startWorkTimeList.value = result.startWorkTimeList
  endWorkTimeList.value = result.endWorkTimeList
}
/**
 * 编辑页面 选中时间段 组件数据转换成接口数据
 */
const updateTimeSlot = () => {
  form.disableTimeSlots = convertTimeSlotComponentToApi(startWorkTimeList.value, endWorkTimeList.value)
  timeSlotStrList.value = convertTimeSlotComponentToDisplay(startWorkTimeList.value, endWorkTimeList.value)
}
/**
 * 时间限制弹窗通知编辑页面父组件更新 选中时间段 组件数据转换成接口数据
 * @param data 禁止时间段 组件数据
 */
const onUpdateDialogTimePicker = (data: {
  startWorkTimeList: string[],
  endWorkTimeList: string[],
}) => {
  startWorkTimeList.value = data.startWorkTimeList
  endWorkTimeList.value = data.endWorkTimeList
  updateTimeSlot()
}

// ---------------------------------------- 时间限制 结束 ----------------------------------------

// ---------------------------------------- 线路计费 开始 ----------------------------------------

// 线路单价超过一定金额显示提示
const unitPriceHintVisible = computed(() => {
// 监听表单线路单价，超过一定金额就显示提示
  return form.unitPrice && +form.unitPrice >= 0.1
})

// ---------------------------------------- 线路计费 结束 ----------------------------------------

// ---------------------------------------- 线路网关 开始 ----------------------------------------

// 所有线路网关信息列表
const gatewayList = ref<GatewayItem[]>([])

/**
 * 更新所有线路网关信息列表
 */
const updateGatewayList = async () => {
  try {
    const res = <GatewayItem[]>await gatewayModel.getGatewayList()
    gatewayList.value = Array.isArray(res) ? res : []
  } catch (e) {
    ElMessage({
      message: '无法获取网关信息：' + e,
      type: 'error'
    })
  }
}

// ---------------------------------------- 线路网关 结束 ----------------------------------------

// ---------------------------------------- 外呼范围 开始 ----------------------------------------

// 外呼范围组件缓存数据 按运营商和省市分类
class RestrictModalOrigin {
  allRestrictProvince = null
  allRestrictCity = null
  ydRestrictProvince = null
  ydRestrictCity = null
  ltRestrictProvince = null
  ltRestrictCity = null
  dxRestrictCity = null
  dxRestrictProvince = null
  virtualRestrictCity = null
  virtualRestrictProvince = null
  unknownRestrictCity = null
  unknownRestrictProvince = null
}

// 外呼范围DOM
const scopeRef = ref()
// 外呼范围子组件引用
const restrictComponentRef = ref()
// 外呼范围ID映射表 按运营商分类
const scopeIdList = reactive<{ [propName: string]: number | null }>({
  allRestrictProvince: null,
  allRestrictCity: null,
  ydRestrictProvince: null,
  ydRestrictCity: null,
  ltRestrictProvince: null,
  ltRestrictCity: null,
  dxRestrictCity: null,
  dxRestrictProvince: null,
  virtualRestrictCity: null,
  virtualRestrictProvince: null,
  unknownRestrictCity: null,
  unknownRestrictProvince: null,
})
// 已选中范围
const taskRestrictData = reactive<RestrictModal>(new RestrictModalOrigin())
// 运营商，备份的旧值，用于监听变化
let providersOld: string[] = []

/**
 * 更新外呼支持范围，接口数据转换成组件数据
 */
const scopeToData = () => {
  // 遍历对象里包含的运营商，存放到组件对应数据里
  (form.cityCodeGroups ?? []).forEach((operator: MerchantLineConstituteParams) => {
    const operatorName = <supplierOperatorEnum>operator.serviceProvider

    // 获得当前运营商的一些配置信息，这个是前端自己维护的
    const operatorInfo = supplierOperatorMap.get(operatorName)
    if (operatorInfo) {
      // 这家运营商的ID，用于编辑线路的外呼范围时，接口数据能和组件数据对应起来
      scopeIdList[<keyof typeof taskRestrictData>operatorInfo.province] = operator?.id ?? null
      scopeIdList[<keyof typeof taskRestrictData>operatorInfo.city] = operator?.id ?? null

      // 省
      const provinceList = (operator.cityCodes ?? []).map((item: string) => {
        return item.slice(0, 2) + '0000'
      })
      const provinceCodes = new Set<string>(provinceList)
      taskRestrictData[<keyof typeof taskRestrictData>operatorInfo.province] = (Array.from(provinceCodes) ?? []).join(',')

      // 市
      taskRestrictData[<keyof typeof taskRestrictData>operatorInfo.city] = (operator.cityCodes ?? []).join(',')
    }
    // console.log('taskRestrictData', taskRestrictData)
    // console.log('scopeIdList', scopeIdList)
  })
}

const selectedOperatorList = ref<('全部' | OperatorEnum)[]>([])
// 组件暴露函数，更新运营商、外呼支持范围
const handleCityUpdate = (data: RestrictModal, operators: ('全部' | OperatorEnum)[]) => {
  Object.assign(taskRestrictData, data)
  selectedOperatorList.value = operators
  updateBlockOperatorList(operators)
}

/**
 * 更新外呼支持范围，组件数据转换成接口数据
 */
const updateTaskRestrictData = async () => {
  // 重置接口数据
  form.cityCodeGroups = []

  // 外呼范围表单数据整理，按运营商分类
  Object.values(supplierOperatorList).forEach((item: SupplierMapItemType) => {
    // taskRestrictData是按运营商分类的，所以这里遍历的是选中的运营商

    // 临时缓存，按运营商分类
    // const group = {}

    // 运营商名称
    const serviceProvider = supplierOperatorList[item.name].val
    // 供应线路名称
    const supplyLineNumber = form.lineNumber ?? ''

    // 组件数据，省市参数名
    // const province = <keyof RestrictModal>item.province
    const city = <keyof RestrictModal>item.city

    // 排除null和空字符串''
    if (taskRestrictData[city]) {
      // 如果有城市数据，一定有省份数据，所以此处使用城市判断是否有数据

      // 组件数据，当前运营商省份合集，字符串拼接形式
      // const provinceStr = taskRestrictData[province]
      // 组件数据，当前运营商城市合集，字符串拼接形式
      const cityStr = taskRestrictData[city]
      // 将数据转换格式，放进当前组件的表单对象里
      form.cityCodeGroups!.push({
        cityCodes: cityStr?.split(',') ?? [],
        serviceProvider,
        supplyLineNumber,
      })
    }

    // console.log('form.cityCodeGroups', form.cityCodeGroups)
  })

  // 更新外呼范围的表单校验结果
  await nextTick()
  scopeRef.value.clearValidate()
}

/**
 * 更新当前已添加的运营商，组件数据转换成接口数据
 */
const updateBlockOperatorList = (val: string[] = []) => {
  // 更新所属运营商
  let tempServiceProviders: string[] = []
  // 遍历组件里已添加的标签卡，得到已添加运营商列表
  val?.forEach((item: string) => {
    const info = supplierOperatorTabNameMap.get(item)
    if (info) {
      tempServiceProviders.push(info.val)
    }
  })
  // 更新表单数据（接口数据）
  if (!tempServiceProviders.length) {
    // 子组件初始化时，是空值
    // 但是表单校验不允许所属运营商为空，所以临时使用一个值
    // 之后会被子组件替换成正确的
    form.serviceProviders = [supplierOperatorList.all.val]
  } else {
    form.serviceProviders = tempServiceProviders
  }

  // 备份运营商
  providersOld = [...(form.serviceProviders ?? [])]
}
/**
 * 处理运营商内容变化
 */
const handleProvidersSelectValChange = () => {
  // 如果被清空，则使用默认值，因为表单不允许空数组
  if (!(line?.serviceProviders?.length)) {
    const list = restrictComponentRef?.value?.selectedOperatorList
    if (list && list.length) {
      updateBlockOperatorList(list)
    } else {
      form.serviceProviders = [supplierOperatorList.all.val]
    }
    // console.log('如果被清空', form.serviceProviders)
    return
  }

  // 和旧值比较，多出的是新增，少掉的是删除
  // console.log('和旧值比较', form.serviceProviders)

  // 两者全集，但是这是没去重的列表
  const allList = [...(form.serviceProviders ?? []), ...providersOld]
  // 新增的
  const addSet = new Set(allList)
  providersOld.forEach((item) => {
    addSet.delete(item)
  })
  // 删除的
  const deleteSet = new Set(allList)
  form.serviceProviders?.forEach((item) => {
    deleteSet.delete(item)
  })

  // console.log('addSet', addSet, 'deleteSet', deleteSet)
  // console.log('和旧值比较 更新后', form.serviceProviders)

  // 更新外呼支持范围
  // 添加运营商
  addSet.forEach((provider: string) => {
    const providerName = <supplierOperatorEnum>provider

    // 根据运营商名称找到该运营商的前端配置信息
    const operatorInfo = supplierOperatorMap.get(providerName)
    if (operatorInfo && restrictComponentRef.value) {
      // 添加标签卡
      restrictComponentRef.value.addTabs(<OperatorEnum>operatorInfo.tabName)
      // console.log('添加标签卡', operatorInfo.tabName)

      // 切换到新标签卡
      // restrictComponentRef.value.activeOperator = operatorInfo.tabName
      // 全选当前供应商的所有省市
      // restrictComponentRef.value.selectAllData()
    }
  })

  // 删除运营商
  deleteSet.forEach((provider: string) => {
    const providerName = <supplierOperatorEnum>provider

    // 根据运营商名称找到该运营商的前端配置信息
    const operatorInfo = supplierOperatorMap.get(providerName)
    if (operatorInfo && restrictComponentRef.value) {
      // 清空当前运营商的所有省市
      taskRestrictData[<keyof typeof taskRestrictData>operatorInfo.province] = null
      taskRestrictData[<keyof typeof taskRestrictData>operatorInfo.city] = null
      // 移除标签卡
      restrictComponentRef.value.removeTab(<OperatorEnum>operatorInfo.tabName)
      // console.log('移除标签卡', operatorInfo.tabName)
    }
  })

  // 备份运营商
  providersOld = [...(form.serviceProviders ?? [])]
}

// ---------------------------------------- 外呼范围 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
/* 表单 */
:deep(.el-form) {
  max-width: 942px;
  margin: 0 auto;
}
/* 详情底部 内层容器 */
.detail-footer-inner {
  width: 942px;
  margin: 0 auto;
}
</style>
