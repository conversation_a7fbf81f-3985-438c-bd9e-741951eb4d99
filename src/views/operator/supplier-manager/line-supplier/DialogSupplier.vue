<template>
  <FormDialog
    :id="props.id"
    :visible="dialogVisible"
    :content="content"
    :contentDefault="contentDefault"
    :rules="rules"
    :submitCallback="submitCallback"
    :dialogStyle="dialogStyle"
    :dialogText="dialogText"
    class="supplier-dialog"
    @close="closeDialog"
    @finish="handleFinish"
  >
    <div class="form-section">
      <div class="form-section-header">
        基本信息
      </div>
      <el-form-item label="供应商名称：" prop="supplierName">
        <el-input v-model.trim="content.supplierName" placeholder="填写供应商名称" clearable maxlength="40" show-word-limit />
      </el-form-item>
      <el-form-item label="供应商简称：" prop="supplierProfile">
        <el-input v-model.trim="content.supplierProfile" placeholder="填写供应商简称" clearable maxlength="15" show-word-limit />
      </el-form-item>
      <el-form-item label="公司地址：" prop="supplierAddress">
        <el-input v-model.trim="content.supplierAddress" placeholder="填写公司详细地址，不超过250字" clearable maxlength="250" show-word-limit />
      </el-form-item>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        联系人信息
      </div>
      <el-form-item label="联系人：" prop="contactName">
        <el-input v-model.trim="content.contactName" placeholder="填写联系人名称" clearable />
      </el-form-item>
      <el-form-item label="联系电话：" prop="phoneNumber">
        <el-input v-model.trim="content.phoneNumber" placeholder="填写联系电话" clearable />
      </el-form-item>
      <el-form-item label="邮箱：" prop="email">
        <el-input v-model.trim="content.email" placeholder="填写邮箱" clearable />
      </el-form-item>
      <el-form-item label="职务：" prop="duty">
        <el-input v-model.trim="content.duty" placeholder="填写联系人公司职务" clearable maxlength="15" show-word-limit />
      </el-form-item>
      <el-form-item label="联系地址：" prop="contactAddress">
        <el-input
          v-model.trim="content.contactAddress"
          type="textarea"
          placeholder="填写联系人详细地址，不超过250字"
          clearable
          maxlength="250"
          show-word-limit
          autosize
          resize="none"
        />
      </el-form-item>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        合作状态
      </div>
      <el-form-item label="合作状态：" prop="cooperationStatus">
        <el-select v-model.trim="content.cooperationStatus" placeholder="选择合作状态">
          <el-option
            v-for="supplierStatusItem in supplierStatusList"
            :key="supplierStatusItem.name"
            :value="supplierStatusItem.val"
            :label="supplierStatusItem.text"
          />
        </el-select>
      </el-form-item>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        线路归属
      </div>
      <el-form-item label="线路归属：" prop="supplierBelong">
        <el-select v-model.trim="content.supplierBelong" placeholder="选择线路归属">
          <el-option
            v-for="item in enum2Options(SupplierBelongEnum)"
            :key="item.value"
            :value="item.value"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        备注
      </div>
      <el-form-item label="备注：">
        <el-input
          v-model.trim="content.notes"
          type="textarea"
          placeholder="填写备注，不超过250字（选填）"
          clearable
          maxlength="250"
          show-word-limit
          autosize
          resize="none"
        />
      </el-form-item>
    </div>
  </FormDialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, ref, watch } from 'vue'
import { SupplierInfo, SupplierBelongEnum } from '@/type/supplier'
import { supplierModel } from '@/api/supplier'
import { supplierStatusList } from '@/assets/js/map-supplier'
import { enum2Options, pickAttrFromObj } from '@/utils/utils'

// 动态引入组件
const FormDialog = defineAsyncComponent(() => import('@/components/FormDialog.vue'))

const props = defineProps<{
  visible: boolean,
  id: number,
  content: SupplierInfo
}>()
const emits = defineEmits([
  // 关闭弹窗
  'close',
  // 通知父组件更新弹窗状态并更新列表
  'update'
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)
// 弹窗样式
const dialogStyle = {
  // 弹窗宽度
  dialogWidth: '600px',
  // 表单项标签宽度
  labelWidth: '100px',
}

// 弹窗文本
const dialogText = {
  // 编辑实体时的弹窗标题
  editingTitle: '编辑供应商',
  // 新建实体时的弹窗标题
  creatingTitle: '创建供应商',
  // 取消按钮文本
  cancelButtonText: '取消',
  // 确定按钮文本
  confirmButtonText: '确定',
  // 编辑成功消息
  msgEditSuccessfully: '供应商编辑成功',
  // 创建成功消息
  msgCreateSuccessfully: '供应商创建成功',
  // 编辑失败消息
  msgEditFailed: '供应商编辑失败',
  // 创建失败消息
  msgCreateFailed: '供应商创建失败',
}

// 表单默认值
const contentDefault: SupplierInfo = {
  // 供应商ID
  id: -1,

  // 供应商名称
  supplierName: '',
  // 供应商简称
  supplierProfile: '',
  // 线路归属
  supplierBelong: SupplierBelongEnum['限时传送'],
  // 公司地址
  supplierAddress: '',

  // 联系人
  contactName: '',
  // 联系电话
  phoneNumber: '',
  // 邮箱
  email: '',
  // 职务
  duty: '',
  // 联系地址
  contactAddress: '',

  // 合作状态
  cooperationStatus: supplierStatusList.enabled.val,

  // 备注
  notes: '',
}
// 表单内容
const content = reactive<SupplierInfo>({ ...contentDefault })

// 表单校验规则
const rules = {
  supplierName: [{ required: true, trigger: 'blur', message: '供应商名称不能为空' }],
  supplierProfile: [{ required: true, trigger: 'blur', message: '供应商简称不能为空' }],
  supplierBelong: [{ required: true, trigger: 'change', message: '线路归属不能为空' }],
  // supplierAddress: [{ required: true, trigger: 'blur', message: '公司地址不能为空' }],
  // contactName: [{ required: true, trigger: 'blur', message: '联系人不能为空' }],
  phoneNumber: [{
    required: true, trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('联系电话不能为空'))
      } else {
        if (/^\d{5,20}$/.test(value)) {
          callback()
        } else {
          callback(new Error('联系电话格式不正确，请输入5-20位数字'))
        }
      }
    }
  }],
  email: [{
    trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback()
        // callback(new Error('邮箱不能为空'))
      } else {
        // a@b.c
        // 这5个基本元素都不能少
        // a b c 可以是大小英文字母、中文、数字、下划线、连字符
        // .c 可以重复匹配
        if (/^[A-Za-z0-9\u4e00-\u9fa5_-]+@[a-zA-Z0-9\u4e00-\u9fa5_-]+(\.[a-zA-Z0-9\u4e00-\u9fa5_-]+)+$/.test(value)) {
          callback()
        } else {
          callback(new Error('邮箱格式不正确，类似*******************的格式'))
        }
      }
    }
  }],
  // duty: [{ required: true, trigger: 'blur', message: '职务不能为空' }],
  // contactAddress: [{ required: true, trigger: 'blur', message: '联系地址不能为空' }],
  cooperationStatus: [{ required: true, trigger: 'blur', message: '合作状态不能为空' }],
}

// 表单提交到接口后的最终结果
const finalContent = ref<SupplierInfo>({
  id: -1,
  cooperationStatus: supplierStatusList.enabled.val,
})

/**
 * 表单弹窗组件的表单提交回调函数
 */
const submitCallback = async (result: SupplierInfo) => {
  // 将表单数据转为接口数据
  const params: SupplierInfo = pickAttrFromObj(result, [
    'id', 'supplierNumber', 'supplierName', 'supplierProfile', 'supplierBelong', 'supplierAddress',
    'contactName', 'phoneNumber', 'email', 'duty', 'contactAddress', 
    'cooperationStatus', 'notes', 'businessScope'
  ])
  params.businessScope = result.businessScope ?? ''
  if (params.id === -1) {
    // 新建
    params.id = undefined
  }

  // 请求接口
  let res: any
  if (params.id === undefined) {
    // 新增
    res = await supplierModel.addSupplier(params)
  } else {
    // 编辑
    res = await supplierModel.updateSupplier(params)
  }

  // 将提交保存后的实体信息更新到当前编辑实体
  finalContent.value = (res?.data ? res?.data : res) || {}
}

/**
 * 表单已提交
 */
const handleFinish = () => {
  // 通知父组件更新弹窗状态并更新列表
  emits('update', finalContent.value)
}

/**
 * 关闭弹窗
 */
const closeDialog = () => {
  // 关闭弹窗
  emits('close')
}

watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    // 每次显示弹窗时
    if (val) {
      // 有ID就使用传入的数据，没ID或者ID不正确就用表单默认值
      props.id > -1
        ? Object.assign(content, JSON.parse(JSON.stringify(props.content)))
        : Object.assign(content, JSON.parse(JSON.stringify(contentDefault)))
    }
  },
)
</script>

<style lang="postcss" scoped>
:deep(.el-form-item__content) {
  width: 450px;
}
</style>
