<template>
  <div v-loading="loadingBlackAllList" class="tab-container">
    <div class="tab-header">
      <div class="tw-flex tw-justify-end">
        <el-button type="primary" @click="onClickAddBlackList">
          新增黑名单
        </el-button>
      </div>
    </div>

    <!--表格-->
    <el-table
      stripe
      :data="blackCurrentList"
      :header-cell-style="tableHeaderStyle"
      class="tw-mt-[12px]"
    >
      <template #empty>
        暂无数据
      </template>

      <el-table-column align="left" prop="groupName" label="黑名单分组" min-width="100" show-overflow-tooltip />
      <el-table-column align="left" prop="supplyLines" label="生效范围" min-width="300" show-overflow-tooltip>
        <template v-slot="{row}">
          {{ formatBlackGroupScopeText(row) }}
        </template>
      </el-table-column>
      <el-table-column align="right" fixed="right" label="操作" width="100">
        <template v-slot="{row}">
          <el-button type="primary" link @click="onClickEditBlacklist(row)">
            编辑
          </el-button>
          <el-button type="danger" link @click="onClickDeleteBlacklist(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--分页条-->
    <PaginationBox
      v-if="blackAllList?.length"
      :pageSize="blackPageSize"
      :pageSizeList="blackPageSizeList"
      :currentPage="blackPageNum"
      :total="blackTableSize"
      @search="updateBlackCurrentList"
      @update="updateBlackCurrentList"
    />
  </div>

  <!--黑名单挂载弹窗-->
  <BlackGroupDialog
    :visible="blackGroupDialogVisible"
    :data="blackGroupDialogData"
    :list="blackAllList"
    @close="onDialogBlacklistClose"
    @update="onDialogBlacklistUpdate"
  />
</template>

<script setup lang="ts">
import { tableHeaderStyle } from '@/assets/js/constant'
import { SupplierBlackGroupInfo, SupplierBlackGroupParam, SupplierLineInfo } from '@/type/supplier'
import { computed, defineAsyncComponent, ref, watch } from 'vue'
import { Throttle, updateCurrentPageList } from '@/utils/utils'
import to from 'await-to-js'
import { supplierModel } from '@/api/supplier'
import { ElMessage } from 'element-plus'
import Confirm from '@/components/message-box'
import { useSupplierStore } from '@/store/supplier'

// 动态引入组件
const BlackGroupDialog = defineAsyncComponent(() => import('./BlackGroupDialog.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  needUpdate: boolean,
}>(), {
  needUpdate: false,
})
const emits = defineEmits(['update'])

const supplierStore = useSupplierStore()

watch(() => props.needUpdate, (val) => {
  if (val) {
    updateBlackAllList()
  }
})

// ---------------------------------------- 通用 开始 ----------------------------------------

// ---------------------------------------- 黑名单分组 开始 ----------------------------------------

// 黑名单分组列表，全部，接口数据
const blackAllList = ref<SupplierBlackGroupInfo[]>([])
// 黑名单分组列表，全部，正在加载
const loadingBlackAllList = ref<boolean>(false)
// 黑名单分组列表，全部，加载节流锁
const throttleBlackAllList = new Throttle(loadingBlackAllList)

// 黑名单分组列表，当前页，页面展示
const blackCurrentList = ref<SupplierBlackGroupInfo[]>([])

// 黑名单分组列表，当前页码，从1开始
const blackPageNum = ref<number>(1)
// 黑名单分组列表，每页大小
const blackPageSize = ref<number>(20)
// 黑名单分组列表，每页大小可选值
const blackPageSizeList = [10, 20, 50, 100]
// 黑名单分组列表，列表总长度
const blackTableSize = computed<number>(() => {
  return blackAllList.value?.length ?? 0
})

/**
 * 黑名单分组列表，更新全部
 */
const updateBlackAllList = async () => {
  // 节流锁上锁
  if (throttleBlackAllList.check()) {
    return
  }
  throttleBlackAllList.lock()

  // 处理参数
  const params: SupplierBlackGroupParam = {
    callLineSupplierId: supplierStore.currentSupplier.id ?? undefined
  }
  // 请求接口
  const [err, res] = <[any, SupplierBlackGroupInfo[]]>await to(supplierModel.getBlacklist(params))
  if (err) {
    // 节流锁解锁
    throttleBlackAllList.unlock()
    return
  }
  // 更新列表
  blackAllList.value = res?.length ? res : []
  // 更新当前页
  updateBlackCurrentList(blackPageNum.value, blackPageSize.value)

  emits('update')

  // 节流锁解锁
  throttleBlackAllList.unlock()
}
/**
 * 黑名单分组列表，更新当前页
 * @param {number} p 新页码
 * @param {number} s 新分页大小
 */
const updateBlackCurrentList = (p?: number, s?: number) => {
  if (p || s) {
    // 如果参数指定了页码或/和每页大小，则按参数更新列表
    p && (blackPageNum.value = p)
    s && (blackPageSize.value = s)
    blackCurrentList.value = updateCurrentPageList(blackAllList.value, blackPageNum.value, blackPageSize.value)
  } else {
    // 如果参数都没有指定，则页码和页面大小保持不变，请求接口更新
    updateBlackAllList()
  }
}
/**
 * 黑名单分组列表，点击新增按钮
 */
const onClickAddBlackList = () => {
  // 更新表单数据
  blackGroupDialogData.value = {
    isAllLinesActive: true,
    supplyLines: [],
  }
  // 显示弹窗
  blackGroupDialogVisible.value = true
}
/**
 * 黑名单分组列表，点击编辑按钮
 * @param {SupplierBlackGroupInfo} row 当前黑名单分组
 */
const onClickEditBlacklist = (row: SupplierBlackGroupInfo) => {
  // 更新表单数据
  blackGroupDialogData.value = row ?? {}
  // 显示弹窗
  blackGroupDialogVisible.value = true
}
/**
 * 黑名单分组列表，点击删除按钮
 * @param {SupplierBlackGroupInfo} row 当前黑名单分组
 */
const onClickDeleteBlacklist = (row: SupplierBlackGroupInfo) => {
  const name = row?.groupName ?? ''
  // 显示确认弹窗
  Confirm({
    text: `你确定要删除黑名单分组【${name}】吗？`,
    type: 'danger',
    title: `删除确认`,
    cancelText: '取消',
    confirmText: '确定',
  }).then(async () => {
    try {
      // 处理参数
      const params: SupplierBlackGroupParam = {
        callLineSupplierId: supplierStore.currentSupplier.id ?? undefined,
        blackListGroupId: row?.id ?? undefined,
      }
      // 请求接口
      await supplierModel.deleteBlacklist(params)
      ElMessage.success(`成功删除黑名单分组【${name}】`)
    } catch (e) {
      ElMessage.warning(`无法删除黑名单分组【${name}】`)
    }
  }).catch(() => {
  }).finally(() => {
    // 更新列表
    updateBlackAllList()
  })
}
/**
 * 黑名单分组，生效范围，格式化文本
 * @param {SupplierBlackGroupInfo} row 靓号限制信息
 */
const formatBlackGroupScopeText = (row: SupplierBlackGroupInfo) => {
  // 全部
  if (row?.isAllLinesActive) {
    return '全部'
  }

  // 部分
  const list = row?.supplyLines?.length ? row?.supplyLines : []
  let arr: string[] = []
  list.forEach((line: SupplierLineInfo) => {
    line?.lineName && arr.push(line?.lineName)
  })
  return arr.join('、') || '-'
}

// ---------------------------------------- 黑名单分组 结束 ----------------------------------------

// ---------------------------------------- 黑名单挂载弹窗 开始 ----------------------------------------

// 黑名单挂载弹窗 显示
const blackGroupDialogVisible = ref<boolean>(false)
// 黑名单挂载弹窗 数据
const blackGroupDialogData = ref<SupplierBlackGroupInfo>({})

/**
 * 黑名单挂载弹窗 关闭
 */
const onDialogBlacklistClose = () => {
  blackGroupDialogData.value = {}
  blackGroupDialogVisible.value = false
}
/**
 * 黑名单挂载弹窗 更新
 * @param {SupplierBlackGroupInfo} data 新数据
 */
const onDialogBlacklistUpdate = (data: SupplierBlackGroupInfo) => {
  blackGroupDialogData.value = data
  updateBlackAllList()
}

// ---------------------------------------- 黑名单挂载弹窗 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

updateBlackAllList()

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
/* 标签卡 */
.tab-container {
  padding: 0;
  text-align: left;
  /* 标签卡顶部 */
  .tab-header {
    padding: 12px 16px 0;
  }
}
</style>
