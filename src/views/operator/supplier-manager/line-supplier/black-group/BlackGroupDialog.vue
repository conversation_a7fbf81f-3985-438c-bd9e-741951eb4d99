<template>
  <el-dialog
    v-model="dialogVisible"
    width="70%"
    class="supplier-dialog"
    align-center
    :close-on-click-modal="false"
    @close="onClose"
  >
    <template #header>
      <div class="form-dialog-header">
        {{ props.data?.id === undefined || props.data?.id === null ? '新增' : '编辑' }}黑名单挂载
      </div>
    </template>

    <el-scrollbar class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          label-width="90px"
          label-position="right"
          :model="form"
          :rules="rules"
        >
          <el-form-item label="生效范围：" prop="supplyLineNumbers" required>
            <el-radio-group v-model="form.allLinesActive" @change="onChangeScopeIsAll">
              <el-radio :label="true">
                全部
              </el-radio>
              <el-radio :label="false">
                部分
              </el-radio>
              <SelectBox
                v-show="!form.allLinesActive"
                v-model:selectVal="form.supplyLineNumbers"
                :options="lineAllList||[]"
                name="lineName"
                val="lineNumber"
                placeholder="供应线路"
                style="width: 300px; max-width: 300px; margin: 0;"
                filterable
                class="tw-flex-grow"
                multiple
                canSelectAll
              />
            </el-radio-group>
          </el-form-item>
          <el-form-item label="挂载黑名单：" required>
            <el-select
              v-model="selectedId"
              :disabled="!!selectedList.length"
              placeholder="选择黑名单分组"
              style="width: 300px;"
              clearable
              filterable
              value-key="id"
              @visible-change="onChangeVisibleSelectedBlackList"
            >
              <el-option
                v-for="availableBlacklistItem in availableList"
                :key="availableBlacklistItem.id"
                :label="availableBlacklistItem.groupName"
                :value="availableBlacklistItem.id"
              />
            </el-select>
          </el-form-item>

          <el-table
            class="tw-w-full tw-mt-[12px]"
            :header-cell-style="tableHeaderStyle"
            stripe
            :data="selectedList"
          >
            <el-table-column align="left" fixed="left" prop="groupName" label="分组名称" min-width="150" show-overflow-tooltip />
            <el-table-column align="left" prop="targetType" label="人群类别" min-width="100" show-overflow-tooltip />
            <el-table-column align="left" prop="targetLevel" label="人群等级" min-width="100" show-overflow-tooltip />
            <el-table-column align="left" prop="limitDuration" label="限制时长" min-width="100" show-overflow-tooltip>
              <template #default="{row}:{row:SupplierBlackGroupInfo}">
                <template v-if="row?.limitDuration===-1">
                  永久生效
                </template>
                <span v-else>
                  {{ row?.limitDuration }} 天
                </span>
              </template>
            </el-table-column>
            <el-table-column align="left" prop="targetComment" label="分组规则" min-width="150" show-overflow-tooltip />
            <el-table-column align="left" prop="putThroughComment" label="影响接通" min-width="100" show-overflow-tooltip />
            <el-table-column align="left" prop="benefitComment" label="影响收益" min-width="100" show-overflow-tooltip />
            <el-table-column align="left" prop="costBenefitComment" label="影响本收" min-width="100" show-overflow-tooltip />
            <el-table-column align="left" prop="comment" label="备注" min-width="150" show-overflow-tooltip />
            <el-table-column align="center" prop="createTime" label="创建时间" min-width="170" show-overflow-tooltip :formatter="formatTableDatetime" />
            <el-table-column align="center" prop="updateTime" label="更新时间" min-width="170" show-overflow-tooltip :formatter="formatTableDatetime" />
            <el-table-column align="right" fixed="right" label="操作" width="80" show-overflow-tooltip>
              <template #default="scope">
                <el-button type="danger" link @click="onClickDeleteBlackList(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-form-item label="" prop="blackListGroupId">
          </el-form-item>
        </el-form>
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" :disabled="loadingConfirm" @click="handleCancel">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" :loading="loadingConfirm" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, watch } from 'vue'
import { formatDate, Throttle } from '@/utils/utils'
import { ElMessage, FormRules } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import { SearchLineParams, SupplierBlackGroupInfo, SupplierBlackGroupParam, SupplierLineInfo } from '@/type/supplier'
import { BlackListTypeEnum } from '@/type/dataFilter'
import { supplierModel } from '@/api/supplier'
import { useSupplierStore } from '@/store/supplier'
import { storeToRefs } from 'pinia'
import to from 'await-to-js'
import { blacklistModel } from '@/api/data-filter'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = defineProps<{
  visible: boolean,
  data: SupplierBlackGroupInfo,
  list: SupplierBlackGroupInfo[],
}>()
const emits = defineEmits([
  'close',
  'update'
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)

watch(
  () => props.visible,
  async (val) => {
    dialogVisible.value = val
    // 每次显示弹窗时
    if (val) {
      await nextTick()
      resetForm()
      // 更新表单数据
      // 生效范围
      if (typeof props.data.isAllLinesActive === 'boolean') {
        form.allLinesActive = props.data.isAllLinesActive
      } else {
        form.allLinesActive = false
      }
      // 黑名单分组
      if (typeof props.data.groupName === 'string') {
        // 存在选中的黑名单分组
        form.blackListGroupId = props.data.id
        if (typeof form.blackListGroupId !== 'number') {
          ElMessage({
            type: 'warning',
            message: '黑名单分组没有正确的ID，可能会出现数据错乱或无法保存'
          })
        }
        form.supplyLineNumbers = (props.data.supplyLines ?? []).map((line: SupplierLineInfo) => {
          return line?.lineNumber ?? ''
        })
        selectedList.value = [props.data]
      }
      // 更新其他内容
      await updateLineAllList()
      await updateBlackGroupAllList()
      updateAvailableList()
    }
  }
)

const supplierStore = useSupplierStore()
const { currentSupplier } = storeToRefs(supplierStore)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): SupplierBlackGroupParam => {
  return {
    callLineSupplierId: supplierStore.currentSupplier.id ?? -1,
    blackListGroupId: undefined,
    allLinesActive: true,
    supplyLineNumbers: [],
  }
}
// 表单数据
const form: SupplierBlackGroupParam = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  supplyLineNumbers: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      // 选择部分时，已选列表不能为空
      if (!form.allLinesActive && !value?.length) {
        callback(new Error('生效范围不能为空'))
      } else {
        callback()
      }
    }
  },
  blackListGroupId: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!selectedList.value?.length) {
        callback(new Error('挂载黑名单不能为空'))
      } else {
        callback()
      }
    }
  },
})
/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  // 表单DOM不存在
  if (!formRef.value) {
    ElMessage({
      type: 'warning',
      message: '表单不存在，无法保存'
    })
    return
  }
  // 表单DOM
  formRef.value.validate(async (valid: boolean) => {
    // 校验通过
    if (valid) {
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 提示用户检查表单
      // ElMessage({
      //   message: '请按提示正确填写信息',
      //   duration: 3000,
      //   type: 'warning',
      // })
    }
  })
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  if (typeof form.blackListGroupId !== 'number') {
    ElMessage({
      type: 'warning',
      message: '黑名单分组没有正确的ID，无法保存'
    })
    // 节流锁解锁
    throttleConfirm.unlock()
    return
  }

  // 处理参数
  const params: SupplierBlackGroupParam = {
    callLineSupplierId: form.callLineSupplierId ?? supplierStore.currentSupplier.id ?? undefined,
    blackListGroupId: form.blackListGroupId ?? props.data.id ?? undefined,
    allLinesActive: !!form.allLinesActive ?? false,
    supplyLineNumbers: form.supplyLineNumbers ?? [],
  }
  // 请求接口
  const [err,] = <[any, SupplierBlackGroupInfo]>await to(supplierModel.saveBlacklist(params))
  if (err) {
    ElMessage({
      type: 'error',
      message: '保存失败'
    })
    // 节流锁解锁
    throttleConfirm.unlock()
    return
  }
  ElMessage({
    message: '保存成功',
    type: 'success',
  })
  // 通知父组件更新
  emits('update', {})
  // 关闭弹窗
  closeDialog()

  // 节流锁解锁
  throttleConfirm.unlock()
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清空其它内容
  selectedId.value = null
  selectedList.value = []
  allList.value = []
  availableList.value = []
}
/**
 * 关闭弹窗，组件回调
 */
const onClose = () => {
  emits('close')
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 关闭弹窗，主动触发
 */
const closeDialog = () => {
  dialogVisible.value = false
}
/**
 * 点击确定按钮
 */
const handleConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const handleCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 生效范围 开始 ----------------------------------------

// 线路列表，正在加载
const loadingLineAllList = ref<boolean>(false)
// 线路列表，加载节流锁
const throttleLineAllList = new Throttle(loadingLineAllList)

// 线路列表，接口数据
const lineAllList = ref<SupplierLineInfo[]>([])

/**
 * 更新线路列表，全部，接口数据
 */
const updateLineAllList = async () => {
  // 节流锁上锁
  if (throttleLineAllList.check()) {
    return
  }
  throttleLineAllList.lock()

  try {
    // 处理参数
    const params: SearchLineParams = {
      callLineSupplierId: currentSupplier.value.id ?? -1
    }

    // 请求接口
    const res = <SupplierLineInfo[]>await supplierModel.getLineList(params)

    // 更新列表
    lineAllList.value = res?.length ? res : []

    // 生效范围为全部时，自动把所有供应线路都选中
    if (form.allLinesActive) {
      form.supplyLineNumbers = lineAllList.value.map((item: SupplierLineInfo) => {
        return item?.lineNumber ?? ''
      })
    }
  } catch (e) {
  } finally {
    // 节流锁解锁
    throttleLineAllList.unlock()
  }
}
/**
 * 更改生效范围
 * @param {boolean} val
 */
const onChangeScopeIsAll = (val: boolean) => {
  form.allLinesActive = val ?? false
  form.supplyLineNumbers = lineAllList.value.map((item: SupplierLineInfo) => {
    return item?.lineNumber ?? ''
  })
}

// ---------------------------------------- 生效范围 结束 ----------------------------------------

// ---------------------------------------- 黑名单分组 开始 ----------------------------------------

// 全部黑名单分组列表
const allList = ref<SupplierBlackGroupInfo[]>([])
// 全部黑名单分组列表，正在加载
const loadingAllList = ref(false)
// 全部黑名单分组列表，加载节流锁
const throttleAllList = new Throttle(loadingAllList)

// 可选黑名单分组列表
const availableList = ref<SupplierBlackGroupInfo[]>([])

// 选中的待添加的黑名单分组的ID
const selectedId = ref<number | null | undefined>(null)

// 已选黑名单分组列表
const selectedList = ref<SupplierBlackGroupInfo[]>([])

/**
 * 更新全部黑名单分组列表
 */
const updateBlackGroupAllList = async () => {
  // 节流锁上锁
  if (throttleAllList.check()) {
    return
  }
  throttleAllList.lock()

  // 处理参数
  const params = { groupType: BlackListTypeEnum['供应商'] }
  // 请求接口
  const [err, res] = <[any, SupplierBlackGroupInfo[]]>await to(blacklistModel.getGroupList(params))
  if (err) {
    ElMessage({
      type: 'error',
      message: '无法获取黑名单分组'
    })
    // 节流锁解锁
    throttleAllList.unlock()
    return
  }
  // 更新列表
  const list = res?.length ? res : []
  // 需要剔除当前供应商已配置的黑名单分组
  allList.value = list.filter((allItem: SupplierBlackGroupInfo) => {
    return !props.list.find((item: SupplierBlackGroupInfo) => {
      return item?.id === allItem?.id
    })
  })
  // 更新可选列表
  updateBlackGroupAvailableList()

  // 节流锁解锁
  throttleAllList.unlock()
}
/**
 * 更新可选黑名单分组列表
 */
const updateBlackGroupAvailableList = () => {
  availableList.value = JSON.parse(JSON.stringify(allList.value))
}
/**
 * 可添加黑名单分组列表 下拉选择框切换显示隐藏
 * @param {boolean} visible 显示隐藏状态
 */
const onChangeVisibleSelectedBlackList = async (visible: boolean) => {
  // 下拉框从隐藏到显示时
  if (visible) {
    return
  }

  // 下拉框从显示到隐藏时
  await nextTick()

  // 如果点击清空按钮，清完列表后，也会关闭选择器
  // 这种情况也不应该提交信息
  if (selectedId.value === undefined || selectedId.value === null) {
    return
  }

  // 找到索引位置
  const currentIndex = availableList.value.findIndex((item: SupplierBlackGroupInfo) => {
    return item.id === selectedId.value
  })
  // 判断是否重复添加
  if (form.blackListGroupId === selectedId.value) {
    // 提示用户重复添加
    ElMessage.warning('该黑名单分组已存在，请不要重复添加')
  } else {
    // 将这个黑名单分组添加进表单里，并将其从可选择的黑名单分组选项中去除
    form.blackListGroupId = selectedId.value
    selectedList.value.push(JSON.parse(JSON.stringify(availableList.value[currentIndex])))
    updateAvailableList()
  }

  // 清空选中的黑名单分组
  selectedId.value = null

  validForm()
}
/**
 * 已添加黑名单分组表格 点击删除按钮
 * @param {SupplierBlackGroupInfo} row 需要删除的黑名单分组信息
 */
const onClickDeleteBlackList = (row: SupplierBlackGroupInfo) => {
  // 已选列表移除
  form.blackListGroupId = undefined
  selectedList.value = []

  // 校验表单
  validForm()

  // 可选列表恢复
  updateAvailableList()
}
/**
 * 格式化表格时间
 * @param row 单元格所在行
 * @param column 单元格所在列
 * @param cellValue 单元格信息
 * @param {number} index 单元格行索引
 */
const formatTableDatetime = (row: any, column: any, cellValue: any, index: number) => {
  return formatDate(cellValue) ?? '-'
}
/**
 * 更新可选列表
 */
const updateAvailableList = () => {
  // 从全部列表里剔除已选，剩下的就是可选
  availableList.value = allList.value.filter((allItem: SupplierBlackGroupInfo) => {
    return !selectedList.value.find((item: SupplierBlackGroupInfo) => {
      return item?.id === allItem?.id
    })
  })
}

// ---------------------------------------- 黑名单分组 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style lang="postcss" scoped>
</style>
