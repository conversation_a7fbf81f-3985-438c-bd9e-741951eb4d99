<template>
  <!--弹窗宽度，弹窗本体最大宽度是外呼支持范围组件的910，再加上左右两侧内边距各24，总共910+48=958-->
  <el-dialog
    v-model="dialogVisible"
    width="958px"
    align-center
    class="supplier-dialog"
    :close-on-click-modal="false"
    @close="handleCancel"
  >
    <template #header>
      <div class="form-dialog-header">
        外呼支持范围
      </div>
    </template>

    <el-scrollbar class="form-dialog-main" view-class="form-dialog-main-inner">
      <CitySettingBox
        :taskRestrictData="taskRestrictData"
        :selectedOperatorList="selectedOperatorList"
        :all-operator-list="selectedOperatorList"
        :readonly="true"
        @update:data="handleCityUpdate"
      />
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="handleCancel">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, ref, watch } from 'vue'
import { OperatorEnum, RestrictModal } from '@/type/common'
import { CloseBold } from '@element-plus/icons-vue'
import { supplierOperatorEnum, supplierOperatorMap } from '@/assets/js/map-supplier'
import { MerchantLineConstituteParams } from '@/type/merchant'

// 动态引入组件
const CitySettingBox = defineAsyncComponent(() => import('@/components/CitySettingBox.vue'))

const props = defineProps<{
  visible: boolean,
  data: any[],
}>()
const emits = defineEmits([
  'update',
  'close',
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    // 每次显示弹窗时
    if (val) {
      Object.assign(form, JSON.parse(JSON.stringify(props.data)))
      convertDataApiToComponent()
    }
  },
  { deep: true, immediate: true }
)

// 表单数据 用于和父组件进行交互
const form = reactive(props.data)

/**
 * 关闭弹窗
 */
const closeDialog = () => {
  dialogVisible.value = false
  clearData()
  emits('close')
}

/**
 * 点击取消按钮
 */
const handleCancel = () => {
  closeDialog()
}

// 外呼范围组件缓存数据 按运营商和省市分类
class RestrictModalOrigin {
  allRestrictProvince = null
  allRestrictCity = null
  ydRestrictProvince = null
  ydRestrictCity = null
  ltRestrictProvince = null
  ltRestrictCity = null
  dxRestrictCity = null
  dxRestrictProvince = null
  virtualRestrictCity = null
  virtualRestrictProvince = null
  unknownRestrictCity = null
  unknownRestrictProvince = null
}

// 已选中范围
const taskRestrictData = reactive<RestrictModal>(new RestrictModalOrigin())

// 已选运营商列表
const selectedOperatorList = ref<('全部' | OperatorEnum)[]>([])

/**
 * 清空组件数据
 */
const clearData = () => {
  Object.assign(form, [])
  Object.assign(taskRestrictData, new RestrictModalOrigin())
  selectedOperatorList.value = ['全部']
}

/**
 * 地区组件数据更新
 * @param data 地区数据
 * @param operators 已选择的运营商列表
 */
const handleCityUpdate = (data: RestrictModal, operators: ('全部' | OperatorEnum)[]) => {
  Object.assign(taskRestrictData, data)
  selectedOperatorList.value = operators
}

/**
 * 更新外呼支持范围，接口数据转换成组件数据
 */
const convertDataApiToComponent = () => {
  // 清空组件数据
  clearData()

  selectedOperatorList.value = []

  // 遍历对象里包含的运营商，存放到组件对应数据里
  form.forEach((operator: MerchantLineConstituteParams) => {
    // 运营商 接口数值
    const operatorName = <supplierOperatorEnum>operator.serviceProvider

    // 获得当前运营商的一些配置信息，这个是前端自己维护的
    const operatorInfo = supplierOperatorMap.get(operatorName)
    if (operatorInfo) {
      // 将接口数据传给组件，左边是组件，右边是接口
      selectedOperatorList.value.push(<OperatorEnum>operatorInfo.tabName ?? '全部')
      // 省
      const provinceList = (operator.cityCodes ?? []).map((item: string) => {
        return item.slice(0, 2) + '0000'
      })
      const provinceCodes = new Set<string>(provinceList)
      taskRestrictData[<keyof typeof taskRestrictData>operatorInfo.province] = (Array.from(provinceCodes) ?? []).join(',')
      // 市
      taskRestrictData[<keyof typeof taskRestrictData>operatorInfo.city] = (operator.cityCodes ?? []).join(',')
    }
  })
}
</script>

<style scoped lang="postcss">
</style>
