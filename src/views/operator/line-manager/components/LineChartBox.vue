<template>
  <div class="line-chart-box tw-flex tw-flex-col tw-pt-[12px]">
    <!-- 针对今日、【昨日、前日】、【7日，15日】，接通率折线图、地区geo/柱状图统计传参逻辑均不同 -->
    <el-radio-group v-model="searchForm.date" class="tw-mb-[12px]" @change="handleDataRangeChange">
      <el-radio-button v-for="item in dateRadioList" :key="item.value" :label="item.value">{{ item.name }}</el-radio-button>
    </el-radio-group>
    <el-scrollbar height="126px" warp-class="tw-pb-1" view-class="tw-flex tw-items-center tw-gap-x-1">
      <div v-for="item in typeList" class="button-box" :class="{active: searchForm.type===item}" @click="handleActiveTab(item)">
        <div class="tw-flex tw-flex-col tw-justify-around">
          <span class="tw-text-[14px] tw-leading-[22px] tw-font-[600] tw-mb-[8px] tw-text-left tw-truncate" :class="searchForm.type===item ? 'tw-text-white' : 'tw-text-[var(--primary-black-color-600)]'">
            {{ findValueInEnum(item, MonitorTypeEnum) }}
          </span>
          <span class="tw-text-[26px] tw-leading-[28px] tw-font-[700] tw-text-left" :class="searchForm.type===item ? 'tw-text-white' : 'tw-text-[var(--el-color-primary)]'">
            {{ lineInfo ? formatNumber(lineInfo[monitorStatisticsTypeObj[item]?.num]?.split('/')[1] || 0, 2) : 0 }}
          </span>
          <span class="tw-text-[13px] tw-leading-[20px] tw-text-left tw-mt-[4px]" :class="searchForm.type===item ? 'tw-text-[var(--primary-black-color-200)]' : 'tw-text-[#898A8C]'">
            数量
          </span>
        </div>
        <el-progress
          v-if="!item?.endsWith('_DURATION')"
          type="circle"
          class="tw-shrink-0"
          :color="searchForm.type===item ? 'var(--primary-black-color-300)':'var(--el-color-primary)'"
          :percentage="lineInfo ? parseFloat(lineInfo[monitorStatisticsTypeObj[item]?.num]?.split('%')[0]) : 0"
          stroke-linecap="square"
          :width="86"
          :stroke-width="11"
        >
          <span
            class="tw-text-[16px] tw-font-[600]"
            :style="{color: searchForm.type===item ? '#fff' : 'var(--el-color-primary)'}">
            {{ (lineInfo && lineInfo?.todayConnected) ? 
            formatNumber(lineInfo ? lineInfo[monitorStatisticsTypeObj[item]?.num]?.split('%')[0] || 0 : 0, 2) + '%'
            : 0 }}
          </span>
        </el-progress>
      </div>
    </el-scrollbar>
    
    <div class="tw-flex tw-justify-end">
      <div class="tw-flex">
        <el-checkbox-group v-model="searchForm.operators" class="tw-ml-[30px]" @change="handleConditionChange()">
          <el-checkbox v-for="item in OperatorEnum" :key="item" :label="item" />
        </el-checkbox-group>
      </div>
    </div>
  </div>
  <div v-loading="!!loading1" class="tw-mt-[12px] tw-bg-white tw-w-full">
    <MixLineBarChart :data="lineChartYList || []" :legends="monitorStatisticsTypeObj[searchForm.type!]?.legend" :tooltip-sort="monitorStatisticsTypeObj[searchForm.type!]?.isConnect ? [0, 2, 1] : [2, 0, 1]" :title="lineChartTitle" :xName="lineChartXName">
      <div v-if="!isRecent" class="tw-absolute tw-right-1 tw-top-[8px] tw-z-10">
        <!-- <span>时间颗粒度：</span> -->
        <el-select v-model="searchForm.size" filterable style="width: 90px;height:24px" @change="handleIntervalChange">
          <el-option v-for="item in intervalList" :key="item.val" :label="item.name" :value="item.val" />
        </el-select>
      </div>
    </MixLineBarChart>
  </div>
  <div v-loading="!!loading2" class="tw-flex tw-mt-[12px] tw-bg-white tw-p-[16px] tw-flex-nowrap">
    <div class="tw-relative tw-flex-grow">
      <GeoChart
        :data="geoData || []"
        title="外呼情况展示"
        @province-change="handleProvinceChange"
        :legends="monitorStatisticsTypeObj[searchForm.type!]?.legend"
        :tooltip-sort="monitorStatisticsTypeObj[searchForm.type!]?.isConnect ? [0, 2, 1] : [2, 0, 1]"
      >
        <div class="tw-absolute tw-top-0 tw-left-0 tw-z-10 tw-px-[13px] tw-leading-[18px] tw-w-full">
          <div class="tw-pb-[4px] tw-flex tw-justify-end tw-h-[38px] tw-w-full tw-items-end">
            <el-button @click="goEditGeoTime" type="primary" link>时间范围</el-button>
          </div>
          <div v-if="!isRecent" class="tw-flex tw-w-full tw-gap-x-[4px] tw-justify-end tw-flex-wrap">
            <el-tag
              v-for="(item, index) in geoTodayData.startWorkTimeList"
              :key="item"
              type="info"
              effect="plain"
              class="tw-mr-[4px] tw-my-[2px] tw-w-[90px]"
            >{{ item + ' - ' +  geoTodayData.endWorkTimeList[index]}}</el-tag>
          </div>
          <div v-else class="tw-flex tw-w-full tw-gap-x-[4px] tw-justify-end tw-flex-wrap">
            <el-tag
              v-for="(item, index) in geoRecentData.startWorkTimeList"
              :key="item"
              type="info"
              effect="plain"
              class="tw-mr-[4px] tw-my-[2px] tw-w-[140px]"
            >{{ dayjs(item).format('MM月DD日') + ' 至 ' + dayjs(geoRecentData.endWorkTimeList[index]).format('MM月DD日')}}</el-tag>
          </div>
        </div>
      </GeoChart>
    </div>
    
    <div v-loading="!!loading3" class="tw-w-[500px] tw-shrink-0 tw-grow-0 tw-ml-[10px]">
      <div>
        <MixLineBarChart
          v-if="currentProvinceData.province"
          :scrollSpan="currentProvinceData.data.length > 5 ? 5 : undefined"
          :data="currentProvinceData.data"
          :title="currentProvinceData.province"
          :extraXLabel="'省份：' + currentProvinceData.province"
          xName="城市"
          :legends="monitorStatisticsTypeObj[searchForm.type!]?.legend"
          :tooltip-sort="monitorStatisticsTypeObj[searchForm.type!]?.isConnect ? [0, 2, 1] : [2, 0, 1]"
        />
      </div>
      <div>
        <MixLineBarChart
          v-if="preProvinceData.province"
          :scrollSpan="preProvinceData.data.length > 5 ? 5 : undefined"
          :data="preProvinceData.data"
          :title="preProvinceData.province"
          xName="城市"
          :extraXLabel="'省份：' + preProvinceData.province"
          :legends="monitorStatisticsTypeObj[searchForm.type!]?.legend"
          :tooltip-sort="monitorStatisticsTypeObj[searchForm.type!]?.isConnect ? [0, 2, 1] : [2, 0, 1]"
        />
      </div>
    </div>
  </div>
  
  <TimeRangePickerDialog
    v-model:visible="geoDialogVisible"
    :defaultStart="isRecent ? searchForm.date! : '08:00'"
    :defaultEnd="isRecent ? dayjs().format('YYYY-MM-DD') : '22:00'"
    :format="isRecent ? 'YYYY-MM-DD' : 'HH:mm'"
    :edit-data="isRecent ? geoRecentData : geoTodayData"
    @confirm="handleGeoTimeChange"
  />
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed, onUnmounted } from 'vue'
import MixLineBarChart from '@/components/charts/MixLineBarChart.vue'
import GeoChart from '@/components/charts/GeoChart.vue'
import { OperatorEnum } from '@/type/common'
import { MonitorInfoItem, MonitorChartSearchInfo, MonitorChartInfo, MonitorTypeEnum, } from '@/type/line'
import { dayjs, ElMessage } from 'element-plus'
import { time2Minute, pickAttrFromObj, findValueInEnum, formatNumber } from '@/utils/utils'
import TimeRangePickerDialog from '@/components/TimeRangePickerDialog.vue'
import to from 'await-to-js'
import { dateRadioList, intervalList, apiChartMap, apiMonitorMap, monitorStatisticsTypeObj } from './constant';
import { useUserStore } from '@/store/user'

const loading1 = ref(0) // 分时段统计
const loading2 = ref(0) // geo
const loading3 = ref(0) // 城市
const props = defineProps<{
  lineInfo: MonitorInfoItem | null,
  refresh: boolean,
  type: number, // 0：供应线路，1：商户线路/商户端统计（后端通过token去判断返回数据） 2： 供应线路下商户线路， 3：商户线路下供应线路; 2: 
}>()

const userInfo = useUserStore()

const lineInfo = ref<MonitorInfoItem | null>(props.lineInfo)
// 全部筛选条件管理
const searchForm = reactive<MonitorChartSearchInfo>({
  supplyLineNumber: props.lineInfo?.supplyLineNumber || undefined,
  tenantLineNumber: props.lineInfo?.tenantLineNumber || undefined,
  type: MonitorTypeEnum['外呼接通率'],
  operators: Object.values(OperatorEnum),
  // 今日，昨日，前日
  minutes: [],
  date: dateRadioList[0].value,
  // 历史
  dates: [],
  // 
  size: intervalList[0].val,
})
const isRecent = computed(() => {
  return dayjs(searchForm.date).isBefore(dayjs().add(-3, 'day'))
})

const emits = defineEmits([
  'update:refresh',
])
type MonitorChartItem = {
  name: string;
  xName?: string
  value1: number;
  value2: number;
  value3?: number;
}
// 头部时间范围、运营商、数据显示和选择

/**
 * handleLineInfoUpdate
 * @description 重新获取图表数据， 仅今日、昨日、前日，仅商户/供应线路
 */
const handleLineInfoUpdate = async () => {
  // 商户/供应线路下的供应商户线路，不更新
  if (isRecent.value || !searchForm.date) return (lineInfo.value = null)
  const apiFn = apiMonitorMap.get(searchForm.date!)
  if (!apiFn) return lineInfo.value = null
  const [err, res] = await to(apiFn[props.type]({
    recentMin: 0,
    tenantLineNumber: props.lineInfo?.tenantLineNumber,
    supplyLineNumber: props.lineInfo?.supplyLineNumber
  })) as [any, MonitorInfoItem[]]
  if (!res || res.length < 1) {
    lineInfo.value = null
  } else {
    // 针对顶部数据，在props.type不同情形下的处理
    // 后端返回有商户线路或供应线路编号的一个就再过滤下。前端临时方案
    lineInfo.value = res.find(item => {
      return (props.lineInfo?.tenantLineNumber && item.tenantLineNumber === props.lineInfo?.tenantLineNumber) 
      || (props.lineInfo?.supplyLineNumber && item.supplyLineNumber === props.lineInfo?.supplyLineNumber) 
    }) || null
  }
}
const handleDataRangeChange = () => {
  handleConditionChange()
  handleLineInfoUpdate()
}
/**
 * 选择条件变化（类型，运营商，日期）时，刷新图表
 */
const handleConditionChange = () => {
  if (isRecent.value) {
    // 更新呼通率标题和坐标名称
    lineChartTitle.value = '数据统计'
    lineChartXName.value = '日期'
    // 如切换成近7，15天，更新地图默认日期范围dates
    geoRecentData.endWorkTimeList = [dayjs().format('YYYY-MM-DD')]
    geoRecentData.startWorkTimeList = [searchForm.date!]
    handleGeoTimeChange(geoRecentData)
  } else {
    // 更新呼通率标题和坐标名称
    lineChartTitle.value = '分时段数据统计'
    lineChartXName.value = '时间'
  }
  if(!searchForm.operators || searchForm.operators.length === 0){
    return ElMessage({
      type: 'warning',
      message: '请至少选择一个运营商',
    })
  }
  (props.lineInfo?.supplyLineNumber || props.lineInfo?.tenantLineNumber) && search()
}
// 数据选择：CONNECT： 外呼接通率，2：无声占比，3：秒挂1s，4： 秒挂2s，5： 呼送失败
const handleActiveTab = (val: string) => {
  if (!val) return
  searchForm.type = val as MonitorTypeEnum
  handleConditionChange()
}

const typeList = computed(() => {
  const res = [
    'CONNECT', 'SILENCE', 'SILENCE_HANGUP', 'ASSISTANT',
    'ONE_SECOND', 'TWO_SECOND', 'FAIL', 'TRANS_CALL_SEAT',
    'CLASS_A', 'CLASS_B', 'CLASS_C', 'CLASS_D', 'PROMPT_SOUND'
  ]
  // 商户端线路报表不展示：送呼失败占比、运营商提示音占比、路由失败
  return userInfo.accountType !== 0 ?  res.filter(item => !['FAIL', 'PROMPT_SOUND'].includes(item)) : res
})

// 中部折线图
const lineChartYList = ref<MonitorChartItem[]>([])
const lineChartTitle = ref('分时段数据统计')
const lineChartXName = ref('时间')
// 获取折线图数据
const getLineData = async () => {
  loading1.value++
  const params: MonitorChartSearchInfo = pickAttrFromObj(searchForm, [
    'supplyLineNumber',
    'tenantLineNumber',
    'type',
    'operators',
  ])
  // 根据选择的时间，获取不同的参数，历史传dates，且不支持选择时间间隔，非历史传date，支持设置间隔
  if (!!isRecent.value) {
    const dates = []
    let m = searchForm.date!
    while (m <= dayjs().format('YYYY-MM-DD')) {
      dates.push(m)
      m = dayjs(m).add(1, 'day').format('YYYY-MM-DD')
    }
    params.size = 0
    params.dates = dates
  } else {
    params.size = searchForm.size
    params.date = searchForm.date
  }

  const [_, data] = await to(apiChartMap.get(searchForm.date!)![0](params)) as [any, MonitorChartInfo]

  if (data && Object.keys(data) && Object.keys(data).length > 0) {
    lineChartYList.value = []
    Object.keys(data).map(item => {
      lineChartYList.value.push({
        name: item.trim(),
        xName: !!isRecent.value ? item.trim() : item.split('-')[0],
        value1: ['CONNECT', 'FAIL'].includes(searchForm.type) ? (data[item].total || 0) : (data[item].num || 0),
        value2: +(data[item].num / data[item].total).toFixed(4),
        value3: ['CONNECT', 'FAIL'].includes(searchForm.type) ? (data[item].num || 0) : (data[item].total || 0),
      })
    })
    lineChartYList.value.sort((a, b) => {
      if(!!isRecent.value) {
        return dayjs(a.name).isAfter(dayjs(b.name)) ?  1 : -1
      } else {
        const bb = time2Minute(b.xName)
        const aa = time2Minute(a.xName)
        return aa - bb
      }
    })
  } else {
    lineChartYList.value = []
  }
  loading1.value--
}

const handleIntervalChange = () => {
  getLineData()
}


/** 底部geo图和柱状图 */ 
type ProvinceChart = {
  data: MonitorChartItem[]
  province?: string,
}
const geoData = ref<MonitorChartItem[]>([])
// 柱状图（当前）数据
const currentProvinceData = reactive<ProvinceChart>({ data: [] })
// 柱状图（上一个）数据
const preProvinceData = reactive<ProvinceChart>({ data: [] })

// 获取geo图和柱状图数据
const getGeoData = async () => {
  loading2.value++
  const params: MonitorChartSearchInfo = pickAttrFromObj(searchForm, [
    'supplyLineNumber',
    'tenantLineNumber',
    'type',
    'operators',
  ])
  if (!!isRecent.value) {
    params.dates = searchForm.dates
  } else {
    params.date = searchForm.date
    params.minutes = searchForm.minutes
  }
  const [err, data] = await to(apiChartMap.get(searchForm.date!)![1](params)) as [any, MonitorChartInfo] 
  const dataArr: {
    name: string;
    value1: number;
    value2: number;
    value3?: number;
  }[] = []
  data && Object.keys(data)?.map(item => {
    dataArr.push({
      value1: ['CONNECT', 'FAIL'].includes(searchForm.type) ? (data[item].total || 0) : (data[item].num || 0),
      value2: +(data[item].num / data[item].total).toFixed(4),
      value3: ['CONNECT', 'FAIL'].includes(searchForm.type) ? (data[item].num || 0) : (data[item].total || 0),
      name: item
    })
  })
  geoData.value = dataArr.sort((a,b) => b.value1 - a.value1)
  if (geoData.value?.length > 0) {
    await handleProvinceChange(geoData.value[1]?.name ? geoData.value[1].name : geoData.value[0].name)
    await handleProvinceChange(geoData.value[0].name)
  } else {
    preProvinceData.data = []
    preProvinceData.province = ''
    currentProvinceData.data = []
    currentProvinceData.province = ''
  }
  loading2.value--
}
// geo调整时间筛选处理
const geoTodayData = reactive<{
  startWorkTimeList: string[],
  endWorkTimeList: string[],
}>({
  startWorkTimeList: ['08:00'],
  endWorkTimeList: ['22:00'],
})
const geoRecentData = reactive<{
  startWorkTimeList: string[],
  endWorkTimeList: string[],
}>({
  startWorkTimeList: [dayjs().add(-6, 'day').format('YYYY-MM-DD')],
  endWorkTimeList: [dayjs().format('YYYY-MM-DD')],
})
const geoDialogVisible = ref(false)
const goEditGeoTime = () => {
  geoDialogVisible.value = true
}
const handleGeoTimeChange = (data: {
    startWorkTimeList: string[],
    endWorkTimeList: string[],
}) => {
  if (!isRecent.value) {
    Object.assign(geoTodayData, data)
    const minutes: number[] = []
    geoTodayData.startWorkTimeList.map((item, index) => {
      if (time2Minute(item) < time2Minute(geoTodayData.endWorkTimeList[index])) {
        let m = time2Minute(item)
        while (m < time2Minute(geoTodayData.endWorkTimeList[index])) {
          minutes.push(m)
          m = m + 30
        }
      }
    })
    searchForm.minutes = minutes
  } else {
    Object.assign(geoRecentData, data)
    const dates: string[] = []
    geoRecentData.startWorkTimeList.map((item, index) => {
      if (item <= geoRecentData.endWorkTimeList[index]) {
        let m = dayjs(item).format('YYYY-MM-DD')
        while (m <= geoRecentData.endWorkTimeList[index]) {
          dates.push(m)
          m = dayjs(m).add(1, 'day').format('YYYY-MM-DD')
        }
      }
    })
    searchForm.dates = dates
  }
  getGeoData()
}

// 点击geo省份处理函数，柱状图变化
const handleProvinceChange = async (prov: string) => {
  if (!prov || prov === currentProvinceData.province) return
  loading3.value++
  const params: MonitorChartSearchInfo = pickAttrFromObj(searchForm, [
    'supplyLineNumber',
    'tenantLineNumber',
    'type',
    'operators',
  ])
  params.province = prov
  if (!!isRecent.value) {
    params.dates = searchForm.dates
  } else {
    params.date = searchForm.date
    params.minutes = searchForm.minutes
  }
  const [err, provinceData] = await to(apiChartMap.get(searchForm.date!)![2](params)) as [any, MonitorChartInfo] 
  if (provinceData && Object.keys(provinceData)?.length > 0) {
    preProvinceData.data = [...currentProvinceData.data]
    preProvinceData.province = currentProvinceData.province
    if (provinceData) {
      currentProvinceData.data = []
      currentProvinceData.province = prov
      Object.keys(provinceData).map(item => {
        currentProvinceData.data.push({
          value1: ['CONNECT', 'FAIL'].includes(searchForm.type) ? (provinceData[item].total || 0) : (provinceData[item].num || 0),
          value2: provinceData[item].num * provinceData[item].total > 0 ? +(provinceData[item].num / provinceData[item].total).toFixed(4) : 0,
          value3: ['CONNECT', 'FAIL'].includes(searchForm.type) ? (provinceData[item].num || 0) : (provinceData[item].total || 0),
          name: item
        })
      })
      currentProvinceData.data.sort((a,b) => b.value1 - a.value1)
    }
  } else {
    ElMessage({
      type: 'warning',
      message: `${prov}数据获取失败`
    })
  }
  loading3.value--
}

// 初始化所有图数据
const search = () =>{
  handleLineInfoUpdate()
  getLineData()
  getGeoData()
  emits('update:refresh', false)
}

watch(() => props.lineInfo, () => {
  searchForm.tenantLineNumber = props.lineInfo?.tenantLineNumber || undefined
  searchForm.supplyLineNumber = props.lineInfo?.supplyLineNumber || undefined
  lineInfo.value = props.lineInfo
  search()
}, {deep: true})
watch(() => props.refresh, n => {
  n && search()
})
handleGeoTimeChange({
  startWorkTimeList: ['08:00'],
  endWorkTimeList: ['22:00'],
})
search()
onUnmounted(() => {
  // @ts-ignore
  lineChartYList.value = null
  // @ts-ignore
  geoData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.line-chart-box {
  :deep(.el-radio-button__inner) {
    border: 0;
  }
  .button-box {
    width: 260px;
    height: 110px;
    flex-shrink: 0;
    flex-grow: 0;
    padding: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 8px;
    padding: 8px 12px;
    position: relative;
    &.active {
      background: linear-gradient(180deg, #0167FF 0%, #019FFF 100%);;
      color: #fff;
      &::after {
        content: "";
        position: absolute;
        background: inherit;
        z-index: 0;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width:0;
        height: 0;
        border-top: 6px solid #019FFF;
		    border-left: 6px solid transparent;
		    border-right: 6px solid transparent;
      }
    }
  }
}
:deep(.el-checkbox__label) {
  font-size: 13px;
}
</style>
