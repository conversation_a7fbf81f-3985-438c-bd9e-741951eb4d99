<template>
  <div v-loading="loading1" class="tw-bg-white tw-py-[12px] tw-w-full">
    <div class="form-dialog-header tw-flex tw-px-[16px] tw-items-center">
      <div>通时分布</div>
      <el-tooltip content="刷新" placement="right" :show-after="500">
        <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="search()">
          <SvgIcon name="reset" color="inherit"/>
        </el-icon>
      </el-tooltip>
    </div>
    
    <LineChart
      :data="durationData || []"
      xName="通话时长"
      yName="数量"
      :legends="['数量', '占比']"
    ></LineChart>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed, } from 'vue'
import LineChart from '@/components/charts/LineChart2.vue'
import { apiDurationMap } from './constant'
import to from 'await-to-js'
import { onBeforeRouteLeave } from 'vue-router'

const loading1 = ref(false) // 分时段统计
const props = defineProps<{
  lineInfo: {
    tenantLineNumber?: string,
    supplyLineNumber?: string,
  } | null,
  type: number, // 0:供应，1商户
  refresh: boolean,
  recentMin?: number | null,
}>()
const emits = defineEmits([
  'update:refresh',
])

const durationData = ref<{
  name: string, value1: number, value2: any
}[] | null>([])
// 初始化所有图数据
const search = async () =>{
  loading1.value = true
  const [_, data] = await to(apiDurationMap[props.type]({
    recentMin: props.recentMin ?? 5,
    tenantLineNumber: props.lineInfo?.tenantLineNumber || '',
    supplyLineNumber: props.lineInfo?.supplyLineNumber || '',
  })) as [any, {
    proportion: string,
    second: number,
    callNum: number,
  }[]]
  durationData.value = (data || []).map(item => ({
    name: item.second > 70 ? '70+' : (item.second + ''),
    value1: item.callNum,
    value2: item.proportion,
  })).sort((a,b) => +a.name - +b.name)
  loading1.value = false
  emits('update:refresh', false)
}

watch(() => props.refresh, n => {
  n && !!props.lineInfo && search()
})

onBeforeRouteLeave(() => {
  durationData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.line-chart-box {
  :deep(.el-radio-button__inner) {
    border: 0;
  }
  .button-box {
    padding: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    border-radius: 8px;
    padding: 16px 12px;
    position: relative;
    &.active {
      background: linear-gradient(180deg, #0167FF 0%, #019FFF 100%);;
      color: #fff;
      &::after {
        content: "";
        position: absolute;
        background: inherit;
        z-index: 0;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width:0;
        height: 0;
        border-top: 6px solid #019FFF;
		    border-left: 6px solid transparent;
		    border-right: 6px solid transparent;
      }
    }
  }
}
</style>