<template>
  <div class="card-box">
    <div class="form-title">
      <span>短信通道</span>
      <el-icon size="12" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="init()"><SvgIcon name="reset" color="inherit"/></el-icon>
    </div>
    <div class="tw-w-full tw-flex tw-justify-end tw-mb-1">
      <el-button v-if="!smsChannelData" type="primary" @click="addSmsChannel">添加通道</el-button>
      <el-button v-else @click="loadSmsChannel">导入通道</el-button>
      <el-button v-if="smsChannelData?.smsAccountGroups" type="primary" @click="addAccountGroup()">新增运营商</el-button>
    </div>
    <!--线路组成表格-->
    <el-row class="tw-w-full first-line:tw-text-center tw-bg-[#f7f8fa] tw-h-[40px] tw-font-[600] tw-text-[var(--el-text-color-primary)] tw-leading-[40px] tw-border-b-[1px] tw-border-[#e5e7eb]">
      <el-col :span="2">运营商</el-col>
      <el-col :span="10">地区组</el-col>
      <el-col :span="10">账号组</el-col>
      <el-col :span="2" class="tw-text-right tw-pr-[16px]">操作</el-col>
    </el-row>
    <el-scrollbar class="tw-w-full" wrap-class="tw-w-full tw-grow">
      <el-row v-for="item in tableData" :key="item[0]" class="tw-border-b-[1px] tw-border-[#e5e7eb]">
        <el-col :span="2" class="tw-flex tw-items-center tw-justify-center">
          {{ item[0] ? findValueInEnum(item[0], SmsAccountServiceProviderEnum) : '-' }}
        </el-col>
        <el-col :span="22">
          <el-row v-for="row in item[1]" :key="row.id" class="tw-border-b-[1px] tw-border-[#e5e7eb]">
            <el-col :span="13">
              <div class="cell-content">
                <template v-if="row?.cityCodes?.length">
                  <el-tag v-for="(item,index) in translateScope(row.cityCodes)" :key="index" class="tw-m-[4px]">
                    {{ item }}
                  </el-tag>
                </template>
                <template v-else>
                  （空）
                </template>
              
                <div class="cell-button-box">
                  <div class="cell-button" @click="editCity(row, 0)">
                    查看
                  </div>
                  <div class="cell-button" @click="editCity(row, 1)">
                    编辑
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="10">
              <div class="cell-content">
                <template v-if="row.smsAccountGroupEntities && row.smsAccountGroupEntities?.length">
                  <el-tag v-for="(item,index) in translateAccount(row.smsAccountGroupEntities)" :key="index" class="tw-m-[4px]">
                    {{ item }}
                  </el-tag>
                </template>
                <template v-else>
                  （空）
                </template>
                <div class="cell-button-box">
                  <div class="cell-button" @click="editAccount(row, 0)">
                    查看
                  </div>
                  <div class="cell-button" @click="editAccount(row, 1)">
                    编辑
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="1" class="tw-flex tw-items-center tw-justify-end">
              <div class="cell-danger-button" type="danger" link @click="deleteChannel(row)">
                删除
              </div>
            </el-col>
          </el-row>
          <el-row class="tw-h-[40px] tw-pl-[16px] tw-flex tw-items-center">
            <el-button type="primary" plain size="small" @click="addAccountGroup(item[0])">
              +新增地区组
            </el-button>
          </el-row>
        </el-col>
      </el-row>
    </el-scrollbar>
  </div>
  <AccountEditDialog
    v-model:visible="visible"
    :data="currentChannel"
    :type="currentEditCategory"
    v-model:isCheck="isCheck"
    :serviceProviderList="smsServiceProviderList"
    :accountList="availableSmsAccounts||[]"
    :smsTemplateId="merchantStore.editingSmsTemplate?.id!"
    @confirm="updateChannelData()"
  />
  <LoadSmsChannelDialog
    v-model:visible="loadVisible"
    :data="smsChannelData"
    @confirm="updateChannelData()"
  />
</template>

<script setup lang="ts">
import { reactive, ref, watch, computed } from 'vue';
import { ElMessage, FormRules } from 'element-plus'
import { SmsAccountGroupItem } from '@/type/merchant'
import { useGlobalStore } from '@/store/globalInfo'
import { smsAccountModel } from '@/api/sms'
import { merchantSmsChannelModel } from '@/api/merchant'
import { useMerchantStore } from '@/store/merchant'
import { SmsChannelItem } from '@/type/merchant'
import { enum2Options, findValueInEnum } from '@/utils/utils'
import AccountEditDialog from './AccountEditDialog.vue'
import SvgIcon from '@/components/SvgIcon.vue';
import {
  SmsAccountItem,
  SmsAccountServiceProviderEnum,
} from '@/type/sms'
import Confirm from '@/components/message-box'
import { to } from 'await-to-js'
import LoadSmsChannelDialog from './LoadSmsChannelDialog.vue';

const merchantStore = useMerchantStore()
const globalStore = useGlobalStore()
const provinceMap = globalStore.getProvinceMap
const loading = ref(false)
const smsChannelData = ref<SmsChannelItem | null>(null)
// 将后端返回数据中同一个运营商的数据整合到一起
const tableData = computed(() => {
  const res: Map<SmsAccountServiceProviderEnum, SmsAccountGroupItem[]> = new Map()
  smsChannelData.value?.smsAccountGroups?.forEach(item => {
    if (!item.smsServiceProvider) return
    if (!res.has(item.smsServiceProvider)) {
      res.set(item.smsServiceProvider, [item])
    } else {
      res.set(item.smsServiceProvider, [...(res.get(item.smsServiceProvider) || []), item])
    }
  })
  return [...res.entries()] as [SmsAccountServiceProviderEnum, SmsAccountGroupItem[]][]
})

const updateChannelData = async () => {
  loading.value = true
  const [_ , res] = await to(merchantSmsChannelModel.getSmsChannelByTemplateId({
    templateId: merchantStore.editingSmsTemplate?.id!
  }))
  smsChannelData.value = res || null
  loading.value = false
}

const translateScope = (cityCodes: string[], ) => {
  const res = new Set<string>([])
  cityCodes?.map(city => {
    const code = city.slice(0, 2) + '0000'
    provinceMap.get(code) && res.add(provinceMap.get(code)!)
  })
  return res.size > 20 ? Array.from(res).slice(0, 20).concat('+' + (res.size - 20)) : res
}
const translateAccount = (arr?: {smsAccount?: SmsAccountItem}[]) => {
  return arr && arr?.length > 4 ? arr.slice(0, 4).map(item => item.smsAccount?.smsAccountName || '').concat('+' + (arr?.length - 4)) : arr?.map(item => item.smsAccount?.smsAccountName || '') || []
}

const getSmsAccount = async () => {
  loading.value = true
  const [err, data] = <[any, SmsAccountItem[]]>await to(smsAccountModel.findAllAccountList({
    secondIndustries: [merchantStore.editingSmsTemplate?.secondIndustry!],
    enableStatus: 'ENABLE',
  }))
  availableSmsAccounts.value =  data || []
  loading.value = false
}


/** 新增、编辑账号组弹窗 */
const availableSmsAccounts = ref<SmsAccountItem[] | null>([])
const currentChannel = ref<SmsAccountGroupItem | null>(null)
const isCheck = ref(true)
const visible = ref(false)
// 'new'；新增，展示全部 | 'cityCodes' | 'smsAccountNumbers'
const currentEditCategory = ref<'new' | 'cityCodes' | 'smsAccountNumbers'>('new')
const addSmsChannel = async () => {
  if (smsChannelData.value && smsChannelData.value.smsAccountGroups) return
  await merchantSmsChannelModel.addEmptySmsChannel({
    templateId: merchantStore.editingSmsTemplate?.id!,
  })
  ElMessage.success('添加成功')
  updateChannelData()
}

const editCity = (row: SmsAccountGroupItem, type: 0 | 1) => {
  currentChannel.value = row
  isCheck.value = type === 0
  currentEditCategory.value = 'cityCodes'
  visible.value = true
}

const editAccount = (row: SmsAccountGroupItem, type: 0 | 1) => {
  currentChannel.value = row
  isCheck.value = type === 0
  visible.value = true
  currentEditCategory.value = 'smsAccountNumbers'
}

// 从其他短信模板导入通道设置（直接覆盖）
const loadVisible = ref(false)
const loadSmsChannel = () => {
  loadVisible.value = true
}

// 新增账号组
const addAccountGroup = async (smsServiceProvider?: SmsAccountServiceProviderEnum | null) => {
  currentChannel.value = smsServiceProvider ? {
    smsServiceProvider: smsServiceProvider,
    cityCodes: [],
    id: undefined,
    smsAccountGroupEntities: [],
    smsAccountNumbers: [],
  } : null
  isCheck.value = false
  currentEditCategory.value = 'new'
  visible.value = true
}

const deleteChannel = async (row: SmsAccountGroupItem) => {
  if (!merchantStore.editingSmsTemplate?.id || !row?.id) return
  Confirm({
    text: `您确定要删除【${findValueInEnum(row.smsServiceProvider, SmsAccountServiceProviderEnum)}】的账号组吗?`,
    type: 'danger',
    title: '删除确认'
  }).then(async () => {
    const [err] = await to(merchantSmsChannelModel.deleteSmsChannelAccountGroup({
      templateId: merchantStore.editingSmsTemplate?.id!,
      smsAccountGroupId: row?.id!,
    }))
    !err && ElMessage.success('删除成功')
    updateChannelData()
  }).catch(() => {})
 
}

// 切换操作时展示的运营商列表
const smsServiceProviderList = enum2Options(SmsAccountServiceProviderEnum)
const smsServiceProviderCurrentList = computed(() => {
  let arr = smsChannelData.value?.smsAccountGroups?.map(item => item.smsServiceProvider) || []
  currentEditCategory.value !== 'new' && (arr = arr.filter(item => item != currentChannel.value?.smsServiceProvider)) 
  return smsServiceProviderList.filter(item => !arr || !arr.includes(item.value))
})

const init = () => {
  getSmsAccount()
  updateChannelData()
}

init()

</script>

<style scoped lang="postcss">
.card-box {
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  width: 100%;
  height: calc(100vh - 130px);
  padding-bottom: 20px;
}
.form-title {
  font-weight: 600;
  font-size: 16px;
  color: var(--primary-black-color-600);
  text-align: left;
}
.cell-content {
  position: relative;
  padding: 16px 75px 16px 16px;
  height: 100%;
  min-height: 80px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.cell-button-box {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  grid-template-columns: 1fr 1fr;
  height: 100%;
  width: 60px;
  padding: 6px 0;
  .cell-button {
    flex-grow: 1;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-color-primary);
    cursor: pointer;
    &:hover {
      background-color: rgba(0, 0, 0, 0.7);
      color: #fff;
    }
  }
}
.cell-danger-button {
  height: 100%;
  width: 100%;
  color: var(--el-color-danger);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
  }
}
</style>
