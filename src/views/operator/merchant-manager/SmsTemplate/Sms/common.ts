import { SystemVariableEnum } from '@/type/merchant'
import { VariableSmsPojoItem } from '@/type/sms'

/**
 * 格式化短信内容
 * @param sign 短信签名
 * @param content 短信内容（短信正文）
 * @param list 自定义变量列表
 * @return {string} 格式化后的短信全文
 */
export const formatSmsContent = (sign: string = '', content: string = '', list: VariableSmsPojoItem[] = []): string => {
  // 替换短信正文中的系统内置变量和自定义变量

  // 系统内置变量列表
  const systemList: VariableSmsPojoItem[] = [
    {
      variableName: SystemVariableEnum['手机尾号'],
      variableValue: '8888',
    },
    {
      variableName: SystemVariableEnum['归属城市'],
      variableValue: '苏州',
    },
    {
      variableName: SystemVariableEnum['下发日期'],
      variableValue: '今日',
    },
  ]
  // 全部变量列表
  const allList = systemList.concat(list)

  // 替换正文变量
  let str = content
  allList.forEach((item: VariableSmsPojoItem) => {
    // 全局查找替换
    str = str.replaceAll(`{{${item.variableName}}}`, item.variableValue)
  })

  // 拼接签名和正文
  // return sign ? `【${sign ?? ''}】${str}` : str
  return `【${sign ?? ''}】${str}`
}
