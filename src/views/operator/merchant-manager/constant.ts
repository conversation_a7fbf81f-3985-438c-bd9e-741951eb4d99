import { MerchantInfo, MerchantStatusEnum, MerchantCallbackTypeEnum, MerchantAccountInfo } from '@/type/merchant'

// 表单默认值类定义
export class MerchantOriginalData implements MerchantInfo {
  // 商户ID
  id: number = -1;

  // 商户名称
  tenantName: string = '';
  // 商户简称
  tenantShortName: string = '';

  // 联系人
  contactName: string = '';
  // 联系电话
  contactPhone: string = '';
  // 邮箱
  contactMail: string = '';
  // 联系地址
  contactAddress: string = '';

  // 合作状态
  status: MerchantStatusEnum = MerchantStatusEnum['启用']

  // 接口类型
  callbackType: MerchantCallbackTypeEnum = MerchantCallbackTypeEnum['三方通用接口']

  // 备注
  remark: string = '';
}

// 表单校验规则
export const merchantRules = {
  tenantName: [{ required: true, trigger: 'blur', message: '商户名称不能为空' }],
  tenantShortName: [{ required: true, trigger: 'blur', message: '商户简称不能为空' }],
  contactName: [{ required: true, trigger: 'blur', message: '联系人不能为空' }],
  contactPhone: [{
    trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback()
      } else {
        if (/^\d{5,20}$/.test(value)) {
          callback()
        } else {
          callback(new Error('联系电话格式不正确，请输入5-20位数字'))
        }
      }
    }
  }],
  contactMail: [{
    trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback()
      } else {
        // a@b.c
        // 这5个基本元素都不能少
        // a b c 可以是大小英文字母、中文、数字、下划线、连字符
        // .c 可以重复匹配
        if (/^[A-Za-z0-9\u4e00-\u9fa5_-]+@[a-zA-Z0-9\u4e00-\u9fa5_-]+(\.[a-zA-Z0-9\u4e00-\u9fa5_-]+)+$/.test(value)) {
          callback()
        } else {
          callback(new Error('邮箱格式不正确，类似*******************的格式'))
        }
      }
    }
  }],
  contactAddress: [{ required: false, trigger: 'blur', message: '联系地址不能为空' }],
  status: [{ required: true, trigger: 'blur', message: '合作状态不能为空' }],
}

export const merchantStatusList = ['启用', '禁用']

export const merchantAccountStatusList = [
  {
    name: '启用',
    value: true,
  },
  {
    name: '停用',
    value: false,
  },
]


export class AccountOriginalData implements MerchantAccountInfo {
  id = -1
  account = ''
  password = ''
  passwordConfirmed = ''
  name = ''
  phone = ''
  email = ''
  address = ''
  accountEnable = true
  isForEncryptionPhones = false
  isForEncryptionAgain = false
  note = ''
  roleId?: number
  groupId?: string
}
