<template>
  <FormDialog
    :id="props.id"
    :visible="dialogVisible"
    :content="content"
    :contentDefault="contentDefault"
    :rules="rules"
    :submitCallback="submitCallback"
    :dialogStyle="dialogStyle"
    :dialogText="dialogText"
    class="merchant-dialog"
    @close="closeDialog"
    @finish="handleFinish"
  >
    <div class="form-section">
      <div class="form-section-header">
        基本信息
      </div>
      <el-form-item label="商户名称：" prop="tenantName">
        <el-input v-model.trim="content.tenantName" placeholder="填写商户名称" clearable maxlength="40" show-word-limit />
      </el-form-item>
      <el-form-item label="商户简称：" prop="tenantShortName">
        <el-input v-model.trim="content.tenantShortName" placeholder="填写商户简称" clearable maxlength="15" show-word-limit />
      </el-form-item>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        联系人信息
      </div>
      <el-form-item label="联系人：" prop="contactName">
        <el-input v-model.trim="content.contactName" placeholder="填写商户联系人" clearable />
      </el-form-item>
      <el-form-item label="联系电话：" prop="contactPhone">
        <el-input v-model.trim="content.contactPhone" placeholder="填写联系电话" clearable />
      </el-form-item>
      <el-form-item label="邮箱：" prop="contactMail">
        <el-input v-model.trim="content.contactMail" placeholder="填写邮箱地址" clearable />
      </el-form-item>
      <el-form-item label="联系地址：" prop="contactAddress">
        <el-input
          v-model.trim="content.contactAddress"
          type="textarea"
          placeholder="填写联系地址，不超过250字"
          clearable
          maxlength="250"
          show-word-limit
          autosize
          resize="none"
        />
      </el-form-item>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        合作状态
      </div>
      <el-form-item label="合作状态：" prop="status">
        <el-select v-model.trim="content.status" placeholder="选择合作状态">
          <el-option
            v-for="merchantStatusItem in merchantStatusList"
            :key="merchantStatusItem.name"
            :value="merchantStatusItem.val"
            :label="merchantStatusItem.text"
          />
        </el-select>
      </el-form-item>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        运营备注
      </div>
      <el-form-item label="备注：">
        <el-input
          v-model.trim="content.remark"
          type="textarea"
          placeholder="填写备注，不超过250字（选填）"
          clearable
          maxlength="250"
          show-word-limit
          autosize
          resize="none"
        />
      </el-form-item>
    </div>
  </FormDialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, ref, watch } from 'vue'
import { MerchantInfo } from '@/type/merchant'
import { merchantStatusList } from '@/assets/js/map-merchant'
import { merchantModel } from '@/api/merchant'

// 动态引入组件
const FormDialog = defineAsyncComponent(() => import('@/components/FormDialog.vue'))

const props = defineProps<{
  visible: boolean,
  id: number,
  content: MerchantInfo
}>()
const emits = defineEmits([
  // 关闭弹窗
  'close',
  // 通知父组件更新弹窗状态并更新列表
  'update'
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)
// 弹窗样式
const dialogStyle = {
  // 弹窗宽度
  dialogWidth: '600px',
  // 表单项标签宽度
  labelWidth: '90px',
}
// 弹窗文本
const dialogText = {
  // 编辑实体时的弹窗标题
  editingTitle: '编辑商户',
  // 新建实体时的弹窗标题
  creatingTitle: '创建商户',
  // 取消按钮文本
  cancelButtonText: '取消',
  // 确定按钮文本
  confirmButtonText: '确定',
  // 编辑成功消息
  msgEditSuccessfully: '商户编辑成功',
  // 创建成功消息
  msgCreateSuccessfully: '商户创建成功',
  // 编辑失败消息
  msgEditFailed: '商户编辑失败',
  // 创建失败消息
  msgCreateFailed: '商户创建失败',
}

// 表单默认值
const contentDefault: MerchantInfo = {
  // 商户ID
  id: -1,

  // 商户名称
  tenantName: '',
  // 商户简称
  tenantShortName: '',

  // 联系人
  contactName: '',
  // 联系电话
  contactPhone: '',
  // 邮箱
  contactMail: '',
  // 联系地址
  contactAddress: '',

  // 合作状态
  status: merchantStatusList.enabled.val,

  // 备注
  remark: '',
}
// 表单内容
const content = reactive<MerchantInfo>({ ...contentDefault })

// 表单校验规则
const rules = {
  tenantName: [{ required: true, trigger: 'blur', message: '商户名称不能为空' }],
  tenantShortName: [{ required: true, trigger: 'blur', message: '商户简称不能为空' }],
  contactName: [{ required: true, trigger: 'blur', message: '联系人不能为空' }],
  contactPhone: [{
    trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback()
      } else {
        if (/^\d{5,20}$/.test(value)) {
          callback()
        } else {
          callback(new Error('联系电话格式不正确，请输入5-20位数字'))
        }
      }
    }
  }],
  contactMail: [{
    trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback()
      } else {
        // a@b.c
        // 这5个基本元素都不能少
        // a b c 可以是大小英文字母、中文、数字、下划线、连字符
        // .c 可以重复匹配
        if (/^[A-Za-z0-9\u4e00-\u9fa5_-]+@[a-zA-Z0-9\u4e00-\u9fa5_-]+(\.[a-zA-Z0-9\u4e00-\u9fa5_-]+)+$/.test(value)) {
          callback()
        } else {
          callback(new Error('邮箱格式不正确，类似*******************的格式'))
        }
      }
    }
  }],
  contactAddress: [{ required: false, trigger: 'blur', message: '联系地址不能为空' }],
  status: [{ required: true, trigger: 'blur', message: '合作状态不能为空' }],
}

// 表单提交到接口后的最终结果
const finalContent = ref<MerchantInfo>({
  id: -1,
  status: merchantStatusList.enabled.val,
})

/**
 * 表单弹窗组件的表单提交回调函数
 */
const submitCallback = async (result: MerchantInfo) => {
  // console.log('表单弹窗组件的表单提交回调函数 submitCallback', JSON.parse(JSON.stringify(result)))

  // 将表单数据转换成接口参数
  const {
    id,
    tenantNo,
    tenantName,
    tenantShortName,
    contactName,
    contactPhone,
    contactMail,
    contactAddress,
    remark,
    status,
  } = result

  // 处理参数
  const params = {
    id,
    tenantNo,
    tenantName,
    tenantShortName,
    contactName,
    contactPhone,
    contactMail,
    contactAddress,
    remark,
    status,
  }
  if (params.id === -1) {
    // 新建
    params.id = undefined
  }

  // 请求接口
  // 因为这个接口没有正确返回code，导致axios不能正确解析出data，所以需要手动解析
  let res: any = await merchantModel.saveMerchant(params)

  // 将提交保存后的实体信息更新到当前编辑实体
  finalContent.value = (res?.data ? res?.data : res) || {}
}

/**
 * 表单已提交
 */
const handleFinish = () => {
  // 通知父组件更新弹窗状态并更新列表
  emits('update', finalContent.value)
}

/**
 * 关闭弹窗
 */
const closeDialog = () => {
  // 关闭弹窗
  emits('close')
}

watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    // 每次显示弹窗时
    if (val) {
      // 有ID就使用传入的数据，没ID或者ID不正确就用表单默认值
      props.id > -1
        ? Object.assign(content, JSON.parse(JSON.stringify(props.content)))
        : Object.assign(content, JSON.parse(JSON.stringify(contentDefault)))
    }
  },
)
</script>

<style lang="postcss" scoped>
:deep(.el-form-item__content) {
  width: 460px;
}
</style>
