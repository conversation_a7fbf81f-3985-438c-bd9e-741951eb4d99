<template>
  <FormDialog
    :id="props.id"
    :visible="dialogVisible"
    :content="content"
    :contentDefault="contentDefault"
    :rules="rules"
    :submitCallback="submitCallback"
    :dialogStyle="dialogStyle"
    :dialogText="dialogText"
    class="merchant-dialog"
    @close="closeDialog"
    @finish="handleFinish"
  >
    <div class="form-section">
      <div class="form-section-header">
        账号信息
      </div>
      <!--编辑账号时，不允许修改账号名和密码-->
      <el-form-item label="登录账号：" prop="account">
        <el-input v-model.trim="content.account" placeholder="填写登录账号" clearable :disabled="props.id>-1" autocomplete="new-password" />
      </el-form-item>
      <el-form-item label="密码：" prop="password">
        <el-input v-model.trim="content.password" placeholder="填写密码" clearable show-password :disabled="props.id>-1" autocomplete="new-password" />
      </el-form-item>
      <el-form-item label="确认密码：" prop="passwordConfirmed">
        <el-input v-model.trim="content.passwordConfirmed" placeholder="确认密码" clearable show-password :disabled="props.id>-1" autocomplete="new-password" />
      </el-form-item>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        联系人信息
      </div>
      <el-form-item label="联系人：" prop="name">
        <el-input v-model.trim="content.name" placeholder="填写联系人" clearable />
      </el-form-item>
      <el-form-item label="联系电话：" prop="phone">
        <el-input v-model.trim="content.phone" placeholder="填写联系电话" clearable />
      </el-form-item>
      <el-form-item label="邮箱：" prop="email">
        <el-input v-model.trim="content.email" placeholder="填写邮箱地址" clearable />
      </el-form-item>
      <el-form-item label="联系地址：" prop="address">
        <el-input
          v-model.trim="content.address"
          type="textarea"
          placeholder="填写联系地址，不超过250字"
          clearable
          maxlength="250"
          show-word-limit
          autosize
          resize="none"
        />
      </el-form-item>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        启用状态
      </div>
      <el-form-item label="启用状态：" prop="enableStatus">
        <el-select v-model.trim="content.accountEnable" placeholder="选择启用状态">
          <el-option
            v-for="merchantAccountStatusItem in Object.values(merchantAccountStatusList)"
            :key="merchantAccountStatusItem.name"
            :value="merchantAccountStatusItem.val"
            :label="merchantAccountStatusItem.text"
          />
        </el-select>
      </el-form-item>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        数据传输
      </div>
      <el-form-item label="数据传输：" prop="isForEncryptionPhones">
        <el-radio-group v-model="content.isForEncryptionPhones" :disabled="props.id > -1">
          <el-radio :label="false">普通</el-radio>
          <el-radio :label="true">加密</el-radio>
        </el-radio-group>
      </el-form-item>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        运营备注
      </div>
      <el-form-item label="备注：">
        <el-input
          v-model.trim="content.note"
          type="textarea"
          placeholder="填写备注，不超过250字（选填）"
          clearable
          maxlength="250"
          show-word-limit
          autosize
          resize="none"
        />
      </el-form-item>
    </div>
  </FormDialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, ref, watch } from 'vue'
import { MerchantAccountInfo, } from '@/type/merchant'
import { merchantUserModel } from '@/api/user'
import { merchantAccountStatusList } from '@/assets/js/map-merchant'

// 动态引入组件
const FormDialog = defineAsyncComponent(() => import('@/components/FormDialog.vue'))

const props = defineProps<{
  // 弹窗显示隐藏
  visible: boolean,
  // 账号ID
  id: number,
  // 账号信息
  content: MerchantAccountInfo
  // 商户ID
  merchantId: number,
  // 当前商户的账号列表
  accountList: MerchantAccountInfo[]
}>()
const emits = defineEmits([
  // 关闭弹窗
  'close',
  // 通知父组件更新弹窗状态并更新列表
  'update'
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)
// 弹窗样式
const dialogStyle = {
  // 弹窗宽度
  dialogWidth: '600px',
  // 表单项标签宽度
  labelWidth: '90px',
}
// 弹窗文本
const dialogText = {
  // 编辑实体时的弹窗标题
  editingTitle: '编辑账号',
  // 新建实体时的弹窗标题
  creatingTitle: '创建账号',
  // 取消按钮文本
  cancelButtonText: '取消',
  // 确定按钮文本
  confirmButtonText: '确定',
  // 编辑成功消息
  msgEditSuccessfully: '账号编辑成功',
  // 创建成功消息
  msgCreateSuccessfully: '账号创建成功',
  // 编辑失败消息
  msgEditFailed: '账号编辑失败',
  // 创建失败消息
  msgCreateFailed: '账号创建失败',
}

// 表单默认值
const contentDefault: MerchantAccountInfo = {
  // 账号ID
  id: -1,

  // 登录账号
  account: '',
  // 密码
  password: '',
  // 确认密码
  passwordConfirmed: '',

  // 联系人
  name: '',
  // 联系电话
  phone: '',
  // 邮箱
  email: '',
  // 联系地址
  address: '',

  // 启用状态
  accountEnable: merchantAccountStatusList.enabled.val,

  // 数据传输方式
  isForEncryptionPhones: false,

  // 备注
  note: '',
}
// 表单内容
const content = reactive<MerchantAccountInfo>({ ...contentDefault })

// 表单校验规则
const rules = {
  account: [{
    required: true, trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      // 创建账号时进行校验，编辑账号时不校验，因为编辑账号时不允许修改账号名
      if (props.id === -1) {
        // 登录账号不能为空
        if (value === '') {
          callback(new Error('登录账号不能为空'))
          return
        }

        // 同一个商户里账号不能重名
        const hasSameAccount = props.accountList.some((item) => {
          return item?.account === value
        })
        if (hasSameAccount) {
          callback(new Error('已存在此账号，请勿重复添加'))
          return
        }

        // 账号名只能字母数字，限定长度
        if (!(/^[0-9A-Za-z]{1,30}$/).test(value)) {
          callback(new Error('请输入字母或数字，30字符以内'))
          return
        }
      }

      callback()
    },
  }],
  password: [{
    required: true, trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      if (props.id > -1) {
        // 编辑时，不允许编辑密码，不做校验
        callback()
      } else {
        // 创建时
        if (value === '') {
          callback(new Error('密码不能为空'))
        } else {
          callback()
        }
      }
    },
  }],
  passwordConfirmed: [{
    required: true, trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      if (props.id > -1) {
        // 编辑时，不允许编辑密码，不做校验
        callback()
      } else {
        // 创建时
        if (value.trim() === '') {
          callback(new Error('确认密码不能为空'))
        } else if (content.password !== '' && value !== content.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      }
    },
  }],
  isForEncryptionPhones: [{ required: true, trigger: 'change', message: '数据传输方式不能为空' }],
  name: [{ required: true, trigger: 'blur', message: '联系人不能为空' }],
  phone: [{
    trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback()
      } else {
        if (/^\d{5,20}$/.test(value)) {
          callback()
        } else {
          callback(new Error('联系电话格式不正确，请输入5-20位数字'))
        }
      }
    }
  }],
  email: [{
    trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback()
      } else {
        // a@b.c
        // 这5个基本元素都不能少
        // a b c 可以是大小英文字母、中文、数字、下划线、连字符
        // .c 可以重复匹配
        if (/^[A-Za-z0-9\u4e00-\u9fa5_-]+@[a-zA-Z0-9\u4e00-\u9fa5_-]+(\.[a-zA-Z0-9\u4e00-\u9fa5_-]+)+$/.test(value)) {
          callback()
        } else {
          callback(new Error('邮箱格式不正确，类似*******************的格式'))
        }
      }
    }
  }],
  address: [{ required: false, trigger: 'blur', message: '联系地址不能为空' }],
  accountEnable: [{ required: true, trigger: 'blur', message: '启用状态不能为空' }],
}

// 表单提交到接口后的最终结果
const finalContent = ref<MerchantAccountInfo>({
  id: -1,
})

/**
 * 表单弹窗组件的表单提交回调函数
 */
const submitCallback = async (result: MerchantAccountInfo) => {
  // console.log('表单弹窗组件的表单提交回调函数 submitCallback', JSON.parse(JSON.stringify(result)))

  // 将表单数据转换成接口参数
  const {
    id,
    account,
    password,
    name,
    phone,
    email,
    address,
    accountEnable,
    note,
    roleId,
    groupId,
    isForEncryptionPhones
  } = result

  // 处理参数
  const params = {
    id,
    account,
    password,
    name,
    phone,
    email,
    address,
    accountEnable: accountEnable ?? true,
    note,
    // 商户ID，这个账号隶属于哪个商户
    tenantId: props.merchantId ?? -1,
    // 账号类型 0 运营 1 商户
    accountType: 1,
    // 角色ID
    roleId,
    // 组ID
    groupId,

    // 这两个参数目前没用到，保持默认
    gender: 'MALE',
    department: '',

    isForEncryptionPhones,
  }
  if (params.id === -1) {
    // 新建
    // 没有ID、角色ID、组ID
    params.id = undefined
    params.roleId = undefined
    params.groupId = undefined
  } else {
    // 编辑
    // 不需要修改密码
    params.password = undefined
  }

  // 请求接口
  let res: any
  if (params.id === undefined) {
    // 新建
    res = await merchantUserModel.addAccount(params)
  } else {
    // 编辑
    res = await merchantUserModel.editAccount(params)
  }

  // 将提交保存后的实体信息更新到当前编辑实体
  finalContent.value = (res?.data ? res?.data : res) || {}
}

/**
 * 表单已提交
 */
const handleFinish = () => {
  // 通知父组件更新弹窗状态并更新列表
  emits('update', finalContent.value)
}

/**
 * 关闭弹窗
 */
const closeDialog = () => {
  // 关闭弹窗
  emits('close')
}

watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    // 每次显示弹窗时
    if (val) {
      // 有ID就使用传入的数据，没ID或者ID不正确就用表单默认值
      props.id > -1
        ? Object.assign(content, JSON.parse(JSON.stringify(props.content)))
        : Object.assign(content, JSON.parse(JSON.stringify(contentDefault)))

      // 编辑时，不允许编辑密码，接口参数不传密码，但是页面表单需要显示，可以填充假字符
      if (props.id > -1) {
        content.password = 'fake_password'
        content.passwordConfirmed = 'fake_password'
        contentDefault.password = 'fake_password'
        contentDefault.passwordConfirmed = 'fake_password'
      } else {
        content.password = ''
        content.passwordConfirmed = ''
        contentDefault.password = ''
        contentDefault.passwordConfirmed = ''
      }
    }
  },
)
</script>

<style lang="postcss" scoped>
:deep(.el-form-item__content) {
  width: 460px;
}
</style>
