import {
  MerchantLineConstituteParams,
  MerchantLineInfo,
  MerchantLineTypeEnum,
} from '@/type/merchant'
import { supplierOperatorList, } from '@/assets/js/map-supplier'
import { merchantLineStatusEnum, merchantLineStatusList } from '@/assets/js/map-merchant'

export class MerchantLineConstituteOrigin implements MerchantLineConstituteParams {
  cityCodes = []
  maxConcurrentLimit = 0
  serviceProvider =  supplierOperatorList.all.val
  supplyLineNumbers = []
  tenantLineNumber = undefined
}

export const merchantLineInfoDefault: MerchantLineInfo = {
  // 线路ID
  id: -1,

  // 线路编号
  lineNumber: '',
  // 线路类型
  lineType: MerchantLineTypeEnum['AI外呼'],
  // 线路名称
  lineName: '',
  // 线路状态
  enableStatus: merchantLineStatusList.enabled.val,
  // 并发上限
  concurrentLimit: '',
  // 适用行业 二级行业 单选 组件数据
  secondIndustry: '',
  // 适用行业 二级行业 单选 接口数据
  secondIndustries: [],

  // 线路组成
  supplyLineGroups: [
    // 默认运营商是“全部”，地区组和线路组为空
    {
      // 接口数据
      cityCodes: [],
      maxConcurrentLimit: 0,
      serviceProvider: supplierOperatorList.all.val,
      supplyLineNumbers: [],
      tenantLineNumber: '',
      // 页面数据
      supplyLineList: [],
      supplyLineNameList: [],
    },
  ],

  // 备注
  notes: '',
}

// 线路组模板
export class lineConstituteItemDefault implements MerchantLineConstituteParams {
  id = undefined
  serviceProvider = supplierOperatorList.all.val
  cityCodes = []
  maxConcurrentLimit = undefined
  supplyLineNumbers = []
  tenantLineNumber
  supplyLineList = []
  supplyLineNameList = []
  constructor(tenantLineNumber: string | undefined, serviceProvider: string) {
    this.tenantLineNumber = tenantLineNumber || undefined
    this.serviceProvider = serviceProvider
  }
}

//
export const  dialogText = {
  // 编辑实体时的弹窗标题
  editingTitle: '查看供应线路',
  // 新建实体时的弹窗标题
  creatingTitle: '创建供应线路',
  // 取消按钮文本
  cancelButtonText: '取消',
  // 确定按钮文本
  confirmButtonText: '确定',
  // 编辑成功消息
  msgEditSuccessfully: '商户线路编辑成功',
  // 创建成功消息
  msgCreateSuccessfully: '商户线路创建成功',
  // 编辑失败消息
  msgEditFailed: '商户线路编辑失败',
  // 创建失败消息
  msgCreateFailed: '商户线路创建失败',
}