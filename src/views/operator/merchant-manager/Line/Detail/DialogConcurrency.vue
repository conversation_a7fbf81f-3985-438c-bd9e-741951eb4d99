<template>
  <el-dialog
    v-model="props.visible"
    width="600px"
    class="merchant-dialog"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        并发支配限制
      </div>
    </template>

    <div class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="120px"
        >
          <el-form-item label="最大可支配并发：" prop="supplyLimit">
            <div class="tw-flex tw-items-center">
              <el-input
                v-model.number="form.supplyLimit"
                type="number"
                placeholder="请输入并发数"
                :min="MIN_CONCURRENCY"
                :max="MAX_CONCURRENCY"
                style="width: 150px;"
                @blur="onInputBlur"
              />
              <el-button
                type="primary"
                link
                class="tw-ml-[8px]"
                @click="onClickReset"
              >
                清空
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :disabled="loadingConfirm" :icon="CloseBold" @click="onClickCancel">
          取消
        </el-button>
        <el-button :loading="loadingConfirm" type="primary" :icon="Select" @click="onClickConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, watch } from 'vue'
import { Throttle } from '@/utils/utils'
import { ElMessage, FormRules } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import to from 'await-to-js'
import { SupplierLineInfo } from '@/type/supplier'
import { MerchantSupplyLineParams } from '@/type/merchant'
import { merchantSupplyLineModel } from '@/api/merchant'
import { useMerchantStore } from '@/store/merchant'
import { trace } from '@/utils/trace'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = defineProps<{
  visible: boolean,
  data: SupplierLineInfo,
}>()
const emits = defineEmits([
  'update:visible',
  'confirm',
])

const merchantStore = useMerchantStore()

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 并发最小值
const MIN_CONCURRENCY = 1
// 并发最大值
const MAX_CONCURRENCY = 100000

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): MerchantSupplyLineParams => {
  return {
    supplyLimit: undefined,
    supplyLineNumber: '',
    tenantLineNumber: '',
  }
}
// 表单数据
const form: MerchantSupplyLineParams = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  supplyLimit: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!form.supplyLimit && !!props.data?.priorityStatus) return callback(new Error(`优先线路的并发不能为空`))
      if (!form.supplyLimit) return callback()
      if (typeof form.supplyLimit === 'number') {
        // 更正数值范围
        if (form.supplyLimit < MIN_CONCURRENCY) {
          form.supplyLimit = MIN_CONCURRENCY
          callback(new Error(`并发数范围为${MIN_CONCURRENCY}~${MAX_CONCURRENCY}`))
        } else if (form.supplyLimit > MAX_CONCURRENCY) {
          form.supplyLimit = MAX_CONCURRENCY
          callback(new Error(`并发数范围为${MIN_CONCURRENCY}~${MAX_CONCURRENCY}`))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
      })
    }
  })
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 处理参数
  const params: MerchantSupplyLineParams = {
    supplyLimit: form.supplyLimit ?? undefined,
    supplyLineNumber: form.supplyLineNumber ?? undefined,
    tenantLineNumber: form.tenantLineNumber ?? undefined,
  }
  // 请求接口
  let err
  trace({
    page: '编辑商户线路-线路配置-修改最大可支配并发',
    params,
  })
  const [e, _] = await to(merchantSupplyLineModel.changeMaxConcurrency(params))
  if (err) {
    ElMessage({
      type: 'error',
      message: '保存失败'
    })
    // 节流锁解锁
    throttleConfirm.unlock()
    return
  }
  ElMessage({
    message: '保存成功',
    type: 'success',
  })
  emits('confirm')
  // 关闭弹窗
  closeDialog()

  // 节流锁解锁
  throttleConfirm.unlock()
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
}
/**
 * 更新表单
 * 从props里读取
 */
const updateForm = () => {
  form.supplyLimit = props.data.supplyLimit
  form.tenantLineNumber = merchantStore.editingMerchantLine.lineNumber
  form.supplyLineNumber = props.data.lineNumber
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击确定按钮
 */
const onClickConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}
/**
 * 点击清空按钮
 */
const onClickReset = () => {
  form.supplyLimit = undefined
}
/**
 * 表单输入框失去焦点
 */
const onInputBlur = () => {
  validForm()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  if (val) {
    // 显示弹窗时
    // 更新表单
    await nextTick()
    resetForm()
    updateForm()
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style lang="postcss" scoped>
</style>
