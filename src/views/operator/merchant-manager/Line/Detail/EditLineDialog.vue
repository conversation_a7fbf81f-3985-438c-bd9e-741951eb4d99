<template>
  <!-- 线路复制弹窗，选择运营商 -->
  <el-dialog
    v-model="dialogVisible"
    align-center
    width="75vw"
    :close-on-click-modal="false"
    class="dialog-form"
    @close="cancel"
  >
    <template #header>
      <div class="form-dialog-header">
        线路组
      </div>
    </template>

    <div v-loading="loading" class="tw-flex tw-flex-col">
      <!-- 筛选部分 -->
      <el-form-item v-show="!props.readonly" label="线路名称：">
        <el-select
          v-model="selectedLineNumber"
          placeholder="请选择需要添加的供应线路"
          style="width: 240px;"
          clearable
          filterable
          :loading="loading"
          :disabled="props.readonly"
          @visible-change="handleLineSelected"
        >
          <el-option
            v-for="lineItem in availableLineList"
            :key="lineItem.lineNumber"
            :value="lineItem.lineNumber"
            :label="lineItem.lineName ?? ''"
          />
        </el-select>
      </el-form-item>

      <!-- 展示方式选择：表格 | 卡片 -->
      <el-form-item label="展示方式：" for="listType">
        <el-radio-group v-model="listType" @change="onChangeListType" class="tw-ml-[6px]">
          <el-radio label="list">表格展示</el-radio>
          <el-radio label="card">卡片展示</el-radio>
        </el-radio-group>
      </el-form-item>

      <!--表格形式展示-->
      <el-table
        v-show="listType === 'list'"
        id="supplier-line-table"
        :data="selectedLineList"
        row-key="lineNumber"
        :header-cell-style="tableHeaderStyle"
        empty-text="暂无数据"
        size="small"
        stripe
      >
        <el-table-column v-if="!props.readonly" align="center" width="60">
          <div v-show="!props.readonly" class="sortable-handler tw-cursor-pointer">
            <el-icon>
              <Switch />
            </el-icon>
          </div>
        </el-table-column>
        <el-table-column align="center" prop="priority" label="优先级" type="index" width="60" />
        <el-table-column align="left" prop="lineName" label="线路名称" min-width="240" show-overflow-tooltip />
        <el-table-column align="center" prop="lineNumber" label="线路编号" min-width="160" show-overflow-tooltip />
        <el-table-column align="left" prop="concurrentLimit" label="并发上限" width="100" show-overflow-tooltip />
        <el-table-column v-if="!props.readonly" align="right" label="操作" fixed="right" width="180">
          <template v-slot="scope">
            <div class="tw-flex tw-justify-end tw-gap-x-[8px]">
              <!--挂起按钮-->
              <el-switch
                v-model="scope.row.pending"
                inline-prompt
                active-text="已挂起"
                inactive-text="未挂起"
                :disabled="props.readonly"
                @click="changePending(scope.row)"
              />
              <!--排序按钮-->
              <el-button
                class="tw-ml-[6px]"
                type="primary"
                link
                :disabled="props.readonly"
                @click="handleChangeSort(scope.row)"
              >
                排序
              </el-button>
              <!--删除按钮-->
              <el-button
                type="danger"
                style="margin-left: 6px;"
                link
                :disabled="props.readonly"
                @click="handleConfirmDelete(scope.row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!--卡片形式展示-->
      <el-scrollbar v-show="listType === 'card'" id="supplier-line-card-list">
        <div id="weight-draggable-boxes" class="line-card-list">
          <div v-for="(lineItem, index) in selectedLineList" :key="lineItem.lineNumber!" class="line-card-item">
            <div class="tw-flex-none tw-mr-1">{{ index + 1 }}</div>
            <div class="tw-flex-auto tw-line-clamp-2">
              {{ lineItem.lineName }}
            </div>
            <div v-show="!props.readonly" class="tw-flex-none tw-w-[16px]">
              <el-button type="info" link :icon="CloseBold" @click="handleConfirmDelete(lineItem)"></el-button>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="cancel">
          {{ !props.readonly ? "取消" : "关闭" }}
        </el-button>
        <el-button v-if="!props.readonly" type="primary" :icon="Select" @click="confirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 编辑线路组弹窗下 - 修改线路优先级弹窗 -->
  <el-dialog
    v-model="dialogSortVisible"
    align-center
    width="500px"
    :close-on-click-modal="false"
    class="merchant-dialog"
  >
    <template #header>
      <div class="form-dialog-header">
        调整优先级顺序
      </div>
    </template>
    <div class="tw-p-[16px]">
      <span>当前优先级顺序为：{{ lineOldSort }}，调整到第</span>
      <el-input-number v-model="lineNewSort" class="tw-mx-[4px]" style="width: 120px" :precision="0" :min="0" :max="selectedLineList.length || 0" />
      <span>级后</span>
    </div>
    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="dialogSortVisible=false">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="handleChangeSortConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { nextTick, reactive, onMounted, ref, toRaw, watch } from 'vue'
import { CloseBold, Select, Switch } from '@element-plus/icons-vue'
import { ElMessage, } from 'element-plus'
import { MerchantLineConstituteParams, MerchantLineInfo, } from '@/type/merchant'
import { SupplierLineInfo, } from '@/type/supplier'
import { formatterEmptyData } from '@/utils/utils'
import { useMerchantStore } from '@/store/merchant'
import Sortable from 'sortablejs'
import { supplierModel } from '@/api/supplier'
import Confirm from '@/components/message-box'
import { tableHeaderStyle } from '@/assets/js/constant'
import { getSupplierLineGatewayText, getSupplyLineSecondIndustriesText } from '@/utils/line'
import { MerchantLineConstituteOrigin } from '../constant'
import type { FormInstance, } from 'element-plus'
import to from 'await-to-js'
import SelectPageBox from '@/components/SelectPageBox.vue'

type LineInfoItem = Pick<SupplierLineInfo, 'lineName' | 'lineNumber' | 'concurrentLimit' | 'pending'>
const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  data: MerchantLineConstituteParams | null,
  merchantInfo: MerchantLineInfo,
  readonly: boolean,
  allLineList: LineInfoItem[], // 全部可选的线路（线路类型、行业等）
}>();

const loading = ref(false)

const dialogVisible = ref(false)
const listType = ref<'card' | 'list'>('list')
const addData = reactive<MerchantLineConstituteParams>(new MerchantLineConstituteOrigin())

const merchantStore = useMerchantStore()
const availableLineList = ref<LineInfoItem[]>([]) // 可添加的供应线路列表
const selectedLineList = ref<LineInfoItem[]>([]) // 已添加的供应线路列表
const selectedLineNumber = ref<string | null>(null)

const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

const confirm = () => {  
  emits('confirm', selectedLineList.value)
  cancel()
}


/**
 *  更新全部供应线路列表，
 */
const updateAllLineList = async () => {
  loading.value = true
  selectedLineList.value = addData.supplyLineList ? JSON.parse(JSON.stringify(addData.supplyLineList)) : []
  availableLineList.value = []
  const list = addData.supplyLineNumbers || []
  props.allLineList?.forEach(item => {
    if (item.lineNumber && !list.includes(item.lineNumber)) {
      availableLineList.value.push(item)
    }
  })
  loading.value = false
}

const initTableSortable = async () => {
  // 第一次nextTick，防止DOM没准备好
  await nextTick()

  const dom: HTMLElement | null = document.querySelector(listType.value === 'list' ? '#supplier-line-table tbody' : '#weight-draggable-boxes')
  // const scrollDom: HTMLElement | null = document.querySelector('#supplier-line-table .el-scrollbar__wrap')
  new Sortable(<HTMLElement>dom, {
    // 动画时长
    animation: 300,
    // 表格展示：拖动手柄，只有拖动该列才能生效，拖动表格其他列不生效
    // 卡片展示：整个卡片都能拖动
    handle: listType.value === 'list' ? '.sortable-handler' : undefined,
    // 拖拽结束后
    onEnd: async (e) => {
      // 1. 拖拽项目后，更新表格各行数据的位置
      // 2. 通过Vue的响应式重新渲染Element表格

      // 拖拽的项目
      const current = selectedLineList.value[<number>e.oldIndex]
      // 从旧位置提出来
      selectedLineList.value.splice(<number>e.oldIndex, 1)
      // 插入到新位置
      selectedLineList.value.splice(<number>e.newIndex, 0, current)

      // 备份表格数据
      const list = toRaw(selectedLineList.value)
      // 备份表格当前滚动位置
      // const top = scrollDom?.scrollTop
      // 清空表格
      selectedLineList.value = []

      // 第二次nextTick，是等表格清空完，准备重新渲染表格
      await nextTick()

      // 恢复表格数据的备份
      selectedLineList.value.push(...list)

      // 第三次nextTick，是等表格重新渲染后，恢复滚动位置
      await nextTick()

      // 恢复滚动位置的备份
      // lineTableRef.value.setScrollTop(top)

      // 这里不能立马更新表单数据，要等点击确定后才能更新，防止用户点击取消
      // 更新表单数据
      // form.supplyLineGroups![rowIndex.value].supplyLineNumbers = []
      // selectedLineList.value.forEach((item) => {
      //   if (item.lineNumber) {
      //     form.supplyLineGroups![rowIndex.value].supplyLineNumbers!.push(item.lineNumber)
      //   }
      // })
    }
  })
}

const init = async () => {
  if (!props.merchantInfo?.lineType || !props.merchantInfo?.secondIndustries?.length) {
    ElMessage.warning('请先设置线路类型和适用行业')
    return
  }
  listType.value = 'list'
  Object.assign(addData, props.data || new MerchantLineConstituteOrigin())
  updateAllLineList()
  onChangeListType()
}

/**
 * 线路组弹窗 点击单选 切换查看方式
 */
const onChangeListType = () => {
  // 修改模式允许拖动；查看模式不能拖动
  if (!props.readonly) {
    initTableSortable()
  }
}

/**------------------------------------------操作线路： 开始------------------------------------------------- */
// 新增线路
const handleLineSelected = async () => {
  loading.value = true
  if (!selectedLineList.value?.length) {
    selectedLineList.value = []
  }
  const lineNumbersInSelectedLineList = selectedLineList.value?.map(item => item?.lineNumber)
  const availableLineListNew: LineInfoItem[] = [] // 新的可选线路列表

  // 根据选中的供应线路编号
  // 加入已选择线路列表(校验是否已经存在，防止重复添加)
  // 并从可选列表中删除
  availableLineList.value.forEach(item => {
    if (item.lineNumber && selectedLineNumber.value === item?.lineNumber) {
      if (!lineNumbersInSelectedLineList.includes(item.lineNumber)) {
        selectedLineList.value.unshift(item)
      } else {
        ElMessage.warning(`【${item.lineName}】已存在，请不要重复添加供应线路`)
      }
    } else {
      availableLineListNew.push(item)
    }
  })
  // 更新可选列表
  availableLineList.value = availableLineListNew
  // 清空选中的供应线路编号
  selectedLineNumber.value = null

  loading.value = false
}

// 删除线路
const handleConfirmDelete = (item: LineInfoItem) => {
  loading.value = true
  // 根据当前供应线路ID，找到列表中对应某项，移除它，并更新页面视图
  const currentLineNumber = item.lineNumber
  const listPosition = selectedLineList.value.findIndex(item => item.lineNumber === currentLineNumber)
  if (listPosition > -1) {
    const len = selectedLineList.value?.length ?? 0
    if (len && listPosition < len) {
      availableLineList.value.push(selectedLineList.value.splice(listPosition, 1)[0])
    }
  }
  loading.value = false
}

// 线路组弹窗 点击线路挂起开关
const changePending = (row: LineInfoItem) => {
  if (props.readonly) return
  Confirm({
    text: `<p>您确定要${row.pending ? '挂起' : '取消挂起'}【${row?.lineName || ''}】?</p>`,
    type: 'warning',
    title: `${row.pending ? '挂起' : '取消挂起'}确认`,
    confirmText: `${row.pending ? '挂起' : '确认'}`
  }).then(async () => {
    loading.value = true
    const [err] = await to(supplierModel.switchLine({
      supplyLineNumber: row.lineNumber!,
      pendingStatus: row.pending!,
    }))
    !err && ElMessage.success('操作成功')
    loading.value = false
  }).catch(() => {
    row.pending = !row.pending
  }).finally(() => {
  })
}

/**------------------------------------------操作线路：结束------------------------------------------------- */

/** ----------------------------------------------修改线路优先级------------------------------------------------------*/
// 修改线路优先级
const lineNewSort = ref<number | null>(0) // 新的优先级
const lineOldSort = ref(0) // 旧的优先级
const dialogSortVisible = ref(false) // 修改线路优先级弹窗
const handleChangeSort = async (val: SupplierLineInfo) => {
  if (props.readonly) return
  lineOldSort.value = selectedLineList.value.findIndex((item) => {
    return item.lineNumber === val.lineNumber
  }) + 1
  lineNewSort.value = null
  dialogSortVisible.value = true
}

const handleChangeSortConfirm = () => {
  if (lineNewSort.value == null || lineOldSort.value === lineNewSort.value) {
    dialogSortVisible.value = false
    return
  }
  const item = selectedLineList.value.splice(lineOldSort.value - 1, 1)
  if (lineOldSort.value > lineNewSort.value) {
    item[0] && selectedLineList.value.splice(lineNewSort.value, 0, item[0])
  } else {
    item[0] && selectedLineList.value.splice(lineNewSort.value - 1, 0, item[0])
  }
  dialogSortVisible.value = false
}

/** ----------------------------------------------修改线路优先级： 结束------------------------------------------------------*/


watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n) {
    init()
  }
})
</script>

<style lang="postcss" scoped>
/* 线路卡片列表 */
.line-card-list {
  display: grid;
  grid-template-columns: repeat(5, minmax(0, 1fr));
  gap: 10px;
  margin: 12px 0;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
}
/* 线路卡片单项 */
.line-card-item {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  padding: 0 8px;
  height: 32px;
  border: solid 1px #E1E3E6;
  border-radius: 4px;
  font-size: 12px;
  line-height: 14px;
  user-select: none;
  cursor: pointer;
}
:deep(.el-checkbox .el-checkbox__label) {
  --el-checkbox-font-size: 13px !important;
}
</style>