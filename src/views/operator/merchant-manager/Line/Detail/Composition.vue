<template>
  <!--线路组成-->
  <div class="tw-bg-white">
    <!--线路组成表格-->
    <el-table
      v-loading="loading"
      :data="supplyLineGroups"
      :header-cell-style="tableHeaderStyle"
      :span-method="mergeCell"
    >
      <el-table-column label="运营商" width="120" align="center" prop="serviceProvider">
        <template #default="{row, $index}">
          <el-select
            :model-value="row.serviceProvider"
            style="width: 90px;"
            size="small"
            @change="(val: string) => onChangeOperator(row, val)"
          >
            <el-option
              v-for="operatorItem in supplierOperatorList"
              :key="operatorItem.val"
              :label="operatorItem.text"
              :value="operatorItem.val"
              :disabled="selectedOperatorList?.includes(operatorItem.val)"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="地区组" min-width="300" align="center" prop="cityCodes">
        <template #default="scope">
          <div class="cell-content">
            <template v-if="scope?.row?.cityCountStrList?.length">
              <el-tag v-for="(item,index) in scope?.row?.cityCountStrList??[]" :key="index" class="tw-m-[4px]">
                {{ item }}
              </el-tag>
            </template>
            <template v-else>
              （空）
            </template>
          </div>

          <div v-if="!props.readonly" class="edit-button" @click="onClickEditCity(scope)">
            编辑
          </div>
          <div v-else class="edit-button" @click="onClickEditCity(scope)">
            查看
          </div>

          <template v-if="computeAddCityButtonVisible(scope) && !props.readonly">
            <div class="add-city-button">
              <el-button size="small" type="primary" plain @click="onClickAddCity(scope)">
                添加地区组
              </el-button>
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="最大可用并发" width="110" align="center" prop="concurrentLimit">
        <template #default="{row}">
          {{ computeConcurrentLimit(row) }}
        </template>
      </el-table-column>
      <el-table-column label="线路组" min-width="200" align="center" prop="supplyLineNumbers">
        <template #default="{row, $index}">
          <div class="cell-content">
            <template v-if="!!row?.supplyLineNameList?.length">
              <el-tag class="tw-m-[4px]">
                {{ row?.supplyLineNameList?.at(0) }}
              </el-tag>
              <el-tag v-if="row?.supplyLineNameList?.length > 1" class="tw-m-[4px]">
                {{ '+' + (row?.supplyLineNameList?.length - 1) }}
              </el-tag>
            </template>
            <template v-else>
              （空）
            </template>
          </div>

          <div class="cell-button-box">
            <div class="cell-button" @click="onClickViewSupplyLine(row, $index)">
              查看
            </div>
            <div class="cell-button" @click="onClickCopyLine(row)">
              复制
            </div>
            <div v-if="!props.readonly" class="cell-button" @click="onClickModifyLine(row, $index)">
              编辑
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="!props.readonly" label="操作" width="60" align="center" fixed="right">
        <template #default="scope">
          <div class="edit-button delete-button" @click="onClickDeleteLine(scope)">
            删除
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!--新增运营商按钮-->
    <el-dropdown placement="bottom" trigger="click" class="tw-m-[10px]" @command="onClickAddOperator">
      <!--按钮本体-->
      <el-button type="primary" size="small">
        <el-icon :size="16">
          <SvgIcon name="add1" />
        </el-icon>
        <span>新增运营商</span>
      </el-button>
      <!--运营商下拉菜单-->
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="operatorItem in supplierOperatorList"
            :key="operatorItem.val"
            :disabled="selectedOperatorList?.includes(operatorItem.val)"
            :command="operatorItem"
          >
            {{ operatorItem.text }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
  <!--编辑线路组弹窗-->
  <EditLineDialog
    v-model:visible="dialogSupplierLineVisible"
    :data="currentDialogSupplyLine"
    :merchantInfo="props.merchantInfo"
    :readonly="props.readonly || dialogSupplyLineReadonly"
    :allLineList="allLineList || []"
    @confirm="updateLine"
  />

  <!-- 线路复制弹窗，选择运营商 -->
  <el-dialog
    v-model="dialogCopyVisible"
    align-center
    width="400px"
    :close-on-click-modal="false"
    class="dialog-form"
  >
    <template #header>
      <div class="form-dialog-header">
        复制线路组
      </div>
    </template>
    <el-form
      :rule="copyRules"
      label-width="100px"
       :model="copyRowInfo"
      class="tw-w-full"
    >
      <el-form-item label="选择运营商：" prop="serviceProvider">
        <el-select
          v-model="copyRowInfo.serviceProvider"
          placeholder="选择运营商"
          style="width: 100%;"
        >
          <el-option
            v-for="operatorItem in supplierOperatorList"
            :key="operatorItem.val"
            :label="operatorItem.text"
            :value="operatorItem.val"
          />
        </el-select>
      </el-form-item>
    </el-form>
     
    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="dialogCopyVisible=false">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="handleCopyLineConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!--外呼支持范围弹窗-->
  <DialogScope
    :visible="dialogScopeVisible"
    :data="dialogScopeData"
    :readonly="props.readonly"
    @update="updateScope"
    @close="onCloseDialogScope"
  />
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, nextTick, onMounted, reactive, ref, toRaw, watch } from 'vue'
import { CloseBold, Select, Switch } from '@element-plus/icons-vue'
import { useGlobalStore } from '@/store/globalInfo'
import { ElMessage, TableColumnCtx } from 'element-plus'
import {
  MerchantLineConstituteParams,
  MerchantLineInfo,
} from '@/type/merchant'
import { SupplierLineInfo, SupplierMapItemType } from '@/type/supplier'
import { useMerchantStore } from '@/store/merchant'
import {
  supplierLineStatusEnum,
  supplierOperatorEnum,
  supplierOperatorList,
  supplierOperatorMap
} from '@/assets/js/map-supplier'
import { supplierModel } from '@/api/supplier'
import Confirm from '@/components/message-box'
import { tableHeaderStyle } from '@/assets/js/constant'
import EditLineDialog from './EditLineDialog.vue'
import { lineConstituteItemDefault } from '../constant'

const DialogScope = defineAsyncComponent(() => import('./DialogScope.vue'))


// props & emits
const emits = defineEmits(['update:visible', 'update:supplyLineGroups'])
const props = defineProps<{
  merchantInfo: MerchantLineInfo,
  readonly: boolean,
  supplyLineGroups: MerchantLineConstituteParams[] | undefined | null
}>();


// ---------------------------------------- 通用 开始 ----------------------------------------

// 全局
const globalStore = useGlobalStore()
const merchantStore = useMerchantStore()
// 当前编辑的线路的商户信息
const account = merchantStore.currentAccount

const loading = ref(false)

const supplyLineGroups = ref(props.supplyLineGroups || null) // 线路组成
const cityCode2NameMap = ref<Record<string, string>>({}) // 
const allLineList = ref<{
  lineName?: string,
  lineNumber?: string,
  concurrentLimit?: number | string
  pending?: boolean
}[] | null>([]) // 全部可选的供应线路

const selectedOperatorList = computed(() => {
  const res = new Set<string>([])
  supplyLineGroups.value?.forEach(item => {
    item.serviceProvider && res.add(item.serviceProvider)
  })
  return [...res]
})

const init = async () => {
  // 更新省市行政代码与名称映射表
  cityCode2NameMap.value = await globalStore.updateCityCode2NameMap()

  // 将线路组成按运营商排序并分类汇总，防止表格不连续
  const groupList: Record<string, MerchantLineConstituteParams[]> = {};
  (props.supplyLineGroups || [])?.forEach((item: MerchantLineConstituteParams) => {
    const operator: string = item?.serviceProvider ?? ''
    if (operator) {
      if (groupList[operator]?.length) {
        groupList[operator].push(item)
      } else {
        groupList[operator] = [item]
      }
    }
  })
  supplyLineGroups.value = Object.values(groupList).flat()

  // 更新全部可选线路
  await updateAllLineList()

  // 线路组展示文本
  updateSupplyLineName()
  // 地区组展示文本
  updateCityStr()
}

onMounted(() =>{
  init()
})

// 当前编辑的表格行索引，从0开始，非法值或默认值为-1
const rowIndex = ref(-1)
/**
 * 合并单元格
 * @param data 单元格信息
 * @return {[number, number]} 返回的对象中，第一个数rowspan表示合并的行数，第二个数colspan表示合并的列数
 * 比如：[1, 1]，保持不动，[2, 1] 上下两行合并。特别地，[0, 0]，删除当前单元格
 */
const mergeCell = (data: {
  // 当前行
  row: MerchantLineConstituteParams,
  // 当前列
  column: TableColumnCtx<MerchantLineConstituteParams>,
  // 当前行号
  rowIndex: number,
  // 当前列号
  columnIndex: number,
}): [number, number] => {
  // 回调函数参数提取出来作为内部变量
  const { row, rowIndex, columnIndex } = data

  const list = supplyLineGroups.value || []

  // 只看第一列运营商列
  if (columnIndex === 0) {
    if (rowIndex === 0 || rowIndex > 0 && row?.serviceProvider !== list[rowIndex - 1]?.serviceProvider) {
      // 如果当前单元格和上面单元格不一样，或者是第一列，那就以此为开始，向下计算有多少相同的单元格，算出需要合并的行数
      let rowspan = 0
      for (let i = rowIndex; i < list.length; i++) {
        if (list[i]?.serviceProvider === row?.serviceProvider) {
          rowspan++
        } else {
          break
        }
      }
      // 上下单元格合并（合并rowspan行，保持当前列不动）
      return [rowspan, 1]
    }
    // 如果当前单元格和上面单元格相同，那就删除当前单元格，给上面的单元格腾出位置
    // 删除当前单元格（合并0行，合并0列）
    return [0, 0]
  }
  // 其余列保持不动（合并1行，合并1列）
  return [1, 1]
}


/**
 * 点击线路组成的删除按钮
 * @param scope
 */
const onClickDeleteLine = (scope: any) => {
  const row: MerchantLineConstituteParams = scope?.row

  let cityStr
  if (row?.cityCodes?.length) {
    cityStr = row?.cityCountStrList
  } else {
    cityStr = '（空）'
  }

  let supplyLineNameStr
  if (row?.supplyLineNameList?.length) {
    supplyLineNameStr = row?.supplyLineNameList[0] + ` 等${row?.supplyLineNameList?.length}个线路`
  } else {
    supplyLineNameStr = '（空）'
  }

  const text = `<span style="font-weight: bold;">运营商：</span>
  ${(supplierOperatorMap.get(<supplierOperatorEnum>row?.serviceProvider)?.text ?? '')}
  <br><br><span style="font-weight: bold;">地区组：</span>
  ${cityStr}
  <br><br><span style="font-weight: bold;">最大可用并发：</span>
  ${row?.maxConcurrentLimit ?? '（空）'}
  <br><br><span style="font-weight: bold;">线路组：</span>
  ${supplyLineNameStr}`

  Confirm({
    text: text,
    type: 'danger',
    title: '确定要删除此条线路组成吗？'
  }).then(() => {
    rowIndex.value = scope?.$index ?? -1
    if (supplyLineGroups.value?.length) {
      if (supplyLineGroups.value?.length >= rowIndex.value) {
        supplyLineGroups.value.splice(rowIndex.value, 1)
      }
    }
    emits('update:supplyLineGroups', supplyLineGroups.value)
  }).catch(() => {
  })
}

// -------------------- 运营商 开始 --------------------

// 运营商，数据类型，自定义字段
interface OperatorItem extends SupplierMapItemType {
  disabled?: boolean
}





/**
 * 点击按钮 添加运营商
 */
const onClickAddOperator = (operator: OperatorItem) => {
  if (!supplyLineGroups.value?.length) {
    supplyLineGroups.value = []
  } else if (operator.val === supplierOperatorEnum.ALL) {
    return ElMessage.warning('存在多个运营商，无法添加全部')
  } else if (selectedOperatorList.value?.includes(supplierOperatorEnum.ALL)) {
    return ElMessage.warning('已选择全部，无法添加其他运营商')
  }
  supplyLineGroups.value.push(new lineConstituteItemDefault(props.merchantInfo.lineNumber ,operator.val))
  emits('update:supplyLineGroups', supplyLineGroups.value)
}

/**
 * 切换运营商
 * @param scope 当前行数据
 */
const onChangeOperator = (row: MerchantLineConstituteParams, newVal: string) => {
  if (selectedOperatorList.value?.length > 1 && newVal === supplierOperatorEnum.ALL) {
    return ElMessage.warning('存在多个运营商，无法切换至全部')
  }
  const oldVal = row.serviceProvider
  supplyLineGroups.value && supplyLineGroups.value.forEach((item, index) => {
    if (item.serviceProvider === oldVal && supplyLineGroups.value) {
      supplyLineGroups.value[index].serviceProvider = newVal
    }
  })
  emits('update:supplyLineGroups', supplyLineGroups.value)
}

// -------------------- 运营商 结束 --------------------

// -------------------- 地区组 开始 --------------------

// ---------- 地区组表格列 开始 ----------

//
/**
 * 是否展示添加地区组按钮
 * @param {any} scope 当前行数据
 * @return {boolean} 是否显示按钮
 */
const computeAddCityButtonVisible = (scope: any): boolean => {
  // console.log(scope.$index)
  // 只有一条地区组（同时也侧面说明只有一个运营商），那必须显示添加地区组按钮
  if (supplyLineGroups.value?.length === 1) {
    return true
  }

  // 如果当前单元格和下面单元格的运营商相同，那就不显示按钮，否则显示（寻找临界的那一行）
  if (supplyLineGroups.value && supplyLineGroups.value.length > 1) {
    // 数组别越界
    if (scope.$index < supplyLineGroups.value.length - 1) {
      return scope.row?.serviceProvider !== supplyLineGroups.value[scope.$index + 1]?.serviceProvider
    } else {
      return true
    }
  }

  return false
}

/**
 * 地区组表格列 更新表格上展示的地区组文本
 */
const updateCityStr = () => {
  supplyLineGroups.value?.forEach((item: MerchantLineConstituteParams) => {
    // 当前地区组
    const list = item?.cityCodes ?? []

    // 城市字符串数组
    let res: string[] = []

    // 按省份区分的城市数量
    let countList: {
      [provinceName: string]: number
    } = {}

    list?.forEach((city: string) => {
      // console.log(city)
      const code = city.slice(0, 2) + '0000'
      !countList[code] ? countList[code] = 1 : countList[code]++
    })

    Object.keys(countList).forEach((key) => {
      const province = globalStore.getProvinceList?.find((item) => {
        return item.includes(key)
      }) ?? null
      if (province) {
        const provinceName = province.split(',').at(0) ?? ''
        const cityCount = countList[key] ?? 0
        if (!!provinceName) {
          res.push(`${provinceName}(${cityCount})`)
        }
      }
    })

    item.cityCountStrList = res

    // console.log('item.cityCountStrList', item.cityCountStrList)
  })
}

// ---------- 地区组表格列 结束 ----------

// ---------- 地区组弹窗 开始 ----------

// 是否显示地区组弹窗
const dialogScopeVisible = ref(false)
// 地区组弹窗组件数据
const dialogScopeData = ref<MerchantLineConstituteParams[]>([])

/**
 * 地区组弹窗 点击添加地区组按钮
 * @param scope 当前行数据
 */
const onClickAddCity = (scope: any) => {
  if (Array.isArray(supplyLineGroups.value)) {
    const operator = supplierOperatorMap.get(scope.row?.serviceProvider)
    if (!!operator) {
      supplyLineGroups.value.splice(scope.$index + 1, 0,
        new lineConstituteItemDefault(props.merchantInfo.lineNumber, scope.row?.serviceProvider)
      )
    }
  } else {
    supplyLineGroups.value = []
  }
}
/**
 * 地区组弹窗 点击编辑地区组按钮
 * @param scope 当前行数据
 */
const onClickEditCity = (scope: any) => {
  rowIndex.value = scope.$index ?? -1
  dialogScopeData.value = [scope.row]
  dialogScopeVisible.value = true
}
/**
 * 地区组弹窗 更新选中地区数据
 * @param data 弹窗组件传来的选中地区的数据
 */
const updateScope = (data: MerchantLineConstituteParams[]) => {
  // console.log('/////////////// form.supplyLineGroups', JSON.parse(JSON.stringify(form.supplyLineGroups?.at(2)?.cityCodes)))
  if (!!supplyLineGroups.value?.at(rowIndex.value)) {
    supplyLineGroups.value.at(rowIndex.value)!.cityCodes = data[0].cityCodes ?? []
  }
  updateCityStr()
  emits('update:supplyLineGroups', supplyLineGroups.value)
}
/**
 * 地区组弹窗 关闭弹窗
 */
const onCloseDialogScope = () => {
  dialogScopeVisible.value = false
  dialogScopeData.value = []
}

// ---------- 地区组弹窗 结束 ----------
// -------------------- 地区组 结束 --------------------


// -------------------- 最大支持并发 开始 --------------------

/**
 * 计算每条线路组的最大并发
 * @param row 当前表格行
 */
const computeConcurrentLimit = (row: MerchantLineConstituteParams) => {
  // console.log('computeConcurrentLimit', row.supplyLineNumbers, row.maxConcurrentLimit)
  if (!row?.supplyLineList || !row?.supplyLineList?.length) {
    return '（空）'
  }
  // @ts-ignore
  return row?.supplyLineList?.reduce((previousValue: number, currentItem: SupplierLineInfo) => {
    return previousValue + (currentItem?.concurrentLimit ? parseInt(currentItem.concurrentLimit+'') : 0)
  }, 0)
}

// -------------------- 最大支持并发 结束 --------------------

// -------------------- 线路组弹窗-编辑、查看某个线路租 开始 --------------------

// 显示线路组弹窗
const dialogSupplierLineVisible = ref(false)
// 线路组弹窗只读
const dialogSupplyLineReadonly = ref(false)
// 当前编辑的线路组
const currentDialogSupplyLine = ref<MerchantLineConstituteParams | null>(null)

/**
 * 线路组成 更新展示的线路组（供应线路）名称
 */
const updateSupplyLineName = () => {
  supplyLineGroups.value?.forEach((group: MerchantLineConstituteParams) => {
    // 根据线路编号，找到对应线路信息
    const supplyLineList: SupplierLineInfo[] = []
    const supplyLineNameList: (string | null)[] = []
    group?.supplyLineNumbers?.forEach((num) => {
      const item = allLineList.value?.find((item) => {
        return item?.lineNumber === num
      })
      if (!!item) {
        supplyLineNameList.push(item?.lineName ?? null)
        supplyLineList.push(toRaw(item))
      }
    })
    // 得到的线路信息列表放到当前组成中（表格单行）
    group.supplyLineList = supplyLineList
    // console.log('group.supplyLineList', group.supplyLineList)
    // 线路名称汇总并去重
    const lineNameSet = new Set(supplyLineNameList)
    lineNameSet.delete(null)
    group.supplyLineNameList = <string[]>Array.from(lineNameSet)
    // console.log('group.supplyLineNameList', group.supplyLineNameList)
  })
}
/**
 * 线路组成 点击线路组的查看按钮
 * @param scope 当前行数据
 */
const onClickViewSupplyLine = (row: MerchantLineConstituteParams, index: number) => {
  if (!row) return ElMessage.warning('获取当前线路组失败')
  // 更新当前编辑的线路组
  currentDialogSupplyLine.value = row
  rowIndex.value = index
  // 禁止修改
  dialogSupplyLineReadonly.value = true
  // 显示弹窗
  dialogSupplierLineVisible.value = true
}
/**
 * 线路组成 点击线路组的修改按钮
 * @param scope 当前行数据
 */
const onClickModifyLine = async (row: MerchantLineConstituteParams, index: number) => {
  if (!props.merchantInfo.secondIndustries?.length || !props.merchantInfo.lineType) {
    return ElMessage.warning('请先设置线路类型和适用行业')
  }
  if (!row) {
    return ElMessage.warning('获取当前线路组失败')
  }
  // 更新当前编辑的线路组
  currentDialogSupplyLine.value = row
  rowIndex.value = index
  // 支持修改
  dialogSupplyLineReadonly.value = false
  // 显示弹窗
  dialogSupplierLineVisible.value = true
}

const updateLine = (data: SupplierLineInfo[]) => {
  if (!!supplyLineGroups.value?.at(rowIndex.value)) {
    supplyLineGroups.value[rowIndex.value].supplyLineNumbers = data?.map(item => item.lineNumber!) || []
    supplyLineGroups.value[rowIndex.value].supplyLineList = data || []
  }
  emits('update:supplyLineGroups', supplyLineGroups.value)
}

// -------------------- 线路组弹窗-编辑、查看某个线路租 结束 --------------------


// -------------------- 线路组弹窗-复制某个线路租 开始 --------------------
const dialogCopyVisible = ref(false)
const copyRowInfo = ref<MerchantLineConstituteParams>({
  supplyLineNumbers: [],
  serviceProvider: undefined,
  cityCodes: []
})
const copyRules = {
  serviceProvider:  { required: true, message: '请选择运营商', trigger: 'change' },
}

/**
 * 线路组成 点击线路组的复制按钮
 * @param row 当前行数据
 */
const onClickCopyLine = (row: MerchantLineConstituteParams) => {
  dialogCopyVisible.value = true
  copyRowInfo.value = {
    supplyLineNumbers: row.supplyLineNumbers,
    serviceProvider: undefined,
    cityCodes: []
  }
}
const handleCopyLineConfirm = () => {
  if (!copyRowInfo.value.serviceProvider) {
    return ElMessage.warning('请选择运营商')
  }
  // if--已存在的运营商， 需要插入
  // else if-- 不存在该运营商，且保证都不是全部，则新增；
  // else--- 提示：无法同时保留全部和其他运营商
  const index = supplyLineGroups.value?.findIndex(item => item.serviceProvider === copyRowInfo.value.serviceProvider) ?? -1
  if (index > -1) {
    supplyLineGroups.value?.splice(index, 0, copyRowInfo.value)
  } else if (copyRowInfo.value.serviceProvider !== supplierOperatorEnum.ALL && !selectedOperatorList.value?.includes(supplierOperatorEnum.ALL)) {
    supplyLineGroups.value?.push(copyRowInfo.value)
  } else {
    return ElMessage.warning('无法同时保留全部和其他运营商')
  }
  dialogCopyVisible.value = false
  emits('update:supplyLineGroups', supplyLineGroups.value)
}

// -------------------- 线路组弹窗-复制某个线路租 结束 --------------------


/** 更新全部供应线路列表 */
const updateAllLineList = async () => {
  // 如未选择线路的无行业和线路类型
  if (!props.merchantInfo.secondIndustries?.length || !props.merchantInfo.lineType) {
    allLineList.value = []
    return
  }
  loading.value = true
  // 请求接口，获取全部线路(【启用、行业、数据加密方式、线路类型】匹配)
  const res = <SupplierLineInfo[]>await supplierModel.getLineList({
    enableStatus: supplierLineStatusEnum.ENABLED,
    secondIndustries: props.merchantInfo.secondIndustries,
    supplyLineType: props.merchantInfo.lineType,
    isForEncryptionPhones: account.isForEncryptionPhones
  })
  allLineList.value = Array.isArray(res) ? res.map(item => ({
    lineNumber: item.lineNumber,
    lineName: item.lineName,
    concurrentLimit: item.concurrentLimit,
    pending: item.pending,
  })) : []
  updateSupplyLineName()
  loading.value = false
}
/**
 * 如果线路类型或者适用行业发生变化，更新全部供应线路列表
 */
watch([() => props.merchantInfo.lineType, () => props.merchantInfo.secondIndustries], () => {
  updateAllLineList()
})
watch(() => props.supplyLineGroups, () => {
  supplyLineGroups.value = JSON.parse(JSON.stringify(props.supplyLineGroups || []))
  updateSupplyLineName()
  // 地区组展示文本
  updateCityStr()
}, {deep: true})

</script>

<style scoped lang="postcss">
/* 表格 单元格 外层容器 */
:deep(.el-table .el-table__cell) {
  position: relative;
  width: 100%;
  height: 100%;
  /* padding: 16px 8px; */
}
/* 表格 单元格 内层容器 */
:deep(.el-table .cell) {
  min-width: 60px;
  padding: 0;
}
/* 表格 单元格 */
.cell-content {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 70px;
  padding-right: 60px;
  text-align: left;
}
/* 表格 单元格 独立按钮 */
.edit-button {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 100%;
  min-height: 1px;
  font-size: 14px;
  color: var(--el-color-primary);
  text-align: center;
  cursor: pointer;
  &.delete-button {
    color: var(--el-color-danger);
  }
}
/* 表格 单元格 悬浮按钮 鼠标悬浮时 */
:deep(.el-table__cell:hover .edit-button) {
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  &.delete-button {
    background-color: var(--el-color-danger);
    color: #fff;
  }
}
/* 添加地区组按钮 */
.add-city-button {
  padding: 20px 4px 4px;
  text-align: left;
}
/* 表格 单元格 多按钮容器 */
.cell-button-box {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
  flex-wrap: nowrap;
  height: 100%;
}
/* 表格 单元格 多按钮 */
.cell-button {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 100%;
  font-size: 14px;
  color: var(--el-color-primary);
  text-align: center;
  cursor: pointer;
  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
  }
}

</style>
