<template>
  <el-dialog
    v-model="dialogVisible"
    align-center
    width="1000px"
    :close-on-click-modal="false"
    class="merchant-dialog"
    @close="handleCancel"
  >
    <template #header>
      <div class="form-dialog-header">
        地区组
      </div>
    </template>

    <el-scrollbar v-loading="loading" class="form-dialog-main" view-class="form-dialog-main-inner">
      <el-form :disabled="props.readonly">
        <CitySettingBox
          :taskRestrictData="taskRestrictData"
          :selectedOperatorList="selectedOperatorList"
          :all-operator-list="selectedOperatorList"
          :readonly="props.readonly"
          needUnknown
          @update:data="handleCityUpdate"
        />
      </el-form>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <template v-if="props.readonly">
          <el-button :icon="CloseBold" @click="handleCancel">
            关闭
          </el-button>
        </template>

        <template v-else>
          <el-button :icon="CloseBold" @click="handleCancel">
            取消
          </el-button>
          <el-button type="primary" :icon="Select" @click="handleConfirm">
            确定
          </el-button>
        </template>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, ref, watch } from 'vue'
import { supplierOperatorEnum, supplierOperatorList, supplierOperatorMap } from '@/assets/js/map-supplier'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { SupplierMapItemType } from '@/type/supplier'
import { OperatorEnum, RestrictModal } from '@/type/common'
import { MerchantLineConstituteParams } from '@/type/merchant'
import { useGlobalStore } from '@/store/globalInfo'
import { storeToRefs } from 'pinia'

// 动态引入组件
const CitySettingBox = defineAsyncComponent(() => import('@/components/CitySettingBox.vue'))

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)

const props = withDefaults(defineProps<{
  visible: boolean,
  data: MerchantLineConstituteParams[],
  readonly?: boolean
}>(), {
  visible: false,
  readonly: false
})

const emits = defineEmits([
  'update',
  'close',
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    // 每次显示弹窗时
    if (val) {
      Object.assign(form, JSON.parse(JSON.stringify(props.data)))
      convertDataApiToComponent()
    }
  },
  { deep: true, immediate: true }
)

// 表单数据 用于和父组件进行交互
const form = reactive<MerchantLineConstituteParams[]>(props.data)

/**
 * 关闭弹窗
 */
const closeDialog = () => {
  dialogVisible.value = false
  clearData()
  emits('close')
}

/**
 * 点击确定按钮
 */
const handleConfirm = () => {
  updateTaskRestrictData()
  emits('update', form)
  closeDialog()
  emits('close')
}

/**
 * 点击取消按钮
 */
const handleCancel = () => {
  closeDialog()
}

/**
 * 清空组件数据
 */
const clearData = () => {
  Object.assign(form, [])
  Object.assign(taskRestrictData, new RestrictModalOrigin())
  selectedOperatorList.value = ['全部']
}

// 外呼范围组件缓存数据 按运营商和省市分类
class RestrictModalOrigin {
  allRestrictProvince = null
  allRestrictCity = null
  ydRestrictProvince = null
  ydRestrictCity = null
  ltRestrictProvince = null
  ltRestrictCity = null
  dxRestrictCity = null
  dxRestrictProvince = null
  virtualRestrictCity = null
  virtualRestrictProvince = null
  unknownRestrictCity = null
  unknownRestrictProvince = null
}

// 已选中范围
const taskRestrictData = reactive<RestrictModal>(new RestrictModalOrigin())

/**
 * 更新外呼支持范围，组件数据转换成接口数据
 */
const updateTaskRestrictData = () => {
  // console.log('taskRestrictData', taskRestrictData)

  // 外呼范围表单数据整理，按运营商分类
  Object.values(supplierOperatorList).forEach((item: SupplierMapItemType) => {
    // taskRestrictData是按运营商分类的，所以这里遍历的是选中的运营商

    // 临时缓存，按运营商分类
    // const group = {}

    // 运营商名称
    const serviceProvider = supplierOperatorList[item.name].val

    // 组件数据，省市参数名
    const province = <keyof RestrictModal>item.province
    const city = <keyof RestrictModal>item.city

    // 排除null和空字符串''
    if (taskRestrictData[city]) {
      // 如果有城市数据，一定有省份数据，所以此处使用城市判断是否有数据

      // 组件数据，当前运营商省份合集，字符串拼接形式
      // const provinceStr = taskRestrictData[province]
      // 组件数据，当前运营商城市合集，字符串拼接形式
      const cityStr = taskRestrictData[city]
      // 将数据转换格式，放进当前组件的表单对象里
      const index = form.findIndex((item) => {
        return item?.serviceProvider === serviceProvider
      })
      if (index > -1) {
        form[index].cityCodes = cityStr?.split(',') ?? []
      } else {
        form!.push({
          cityCodes: cityStr?.split(',') ?? [],
          serviceProvider,
        })
      }
    }
  })
}

// 已选运营商列表 默认全部
const selectedOperatorList = ref<('全部' | OperatorEnum)[]>([])

/**
 * 地区组件数据更新
 * @param data 地区数据
 * @param operators 已选择的运营商列表
 */
const handleCityUpdate = (data: RestrictModal, operators: ('全部' | OperatorEnum)[]) => {
  Object.assign(taskRestrictData, data)
  selectedOperatorList.value = operators
}

/**
 * 更新外呼支持范围，接口数据转换成组件数据
 */
const convertDataApiToComponent = () => {
  loading.value = true

  // 清空组件数据
  clearData()

  selectedOperatorList.value = []

  // 遍历对象里包含的运营商，存放到组件对应数据里
  form.forEach((operator: MerchantLineConstituteParams) => {
    // 运营商 接口数值
    const operatorName = <supplierOperatorEnum>operator.serviceProvider

    // 获得当前运营商的一些配置信息，这个是前端自己维护的
    const operatorInfo = supplierOperatorMap.get(operatorName)
    if (operatorInfo) {
      // 将接口数据传给组件，左边是组件，右边是接口
      selectedOperatorList.value.push(<OperatorEnum>operatorInfo.tabName ?? '全部')
      // 省
      const provinceList = (operator.cityCodes ?? []).map((item: string) => {
        return item.slice(0, 2) + '0000'
      })
      const provinceCodes = new Set<string>(provinceList)
      taskRestrictData[<keyof typeof taskRestrictData>operatorInfo.province] = (Array.from(provinceCodes) ?? []).join(',')
      // 市
      taskRestrictData[<keyof typeof taskRestrictData>operatorInfo.city] = (operator.cityCodes ?? []).join(',')
    }
  })

  setTimeout(() => {
    loading.value = false
  }, 200)
}
</script>

<style scoped lang="postcss">
.form-dialog-content {
  padding: 0;
}
</style>
