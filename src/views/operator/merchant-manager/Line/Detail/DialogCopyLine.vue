<template>
  <FormDialog
    :id="-1"
    :visible="dialogVisible"
    :content="content"
    :contentDefault="contentDefault"
    :rules="rules"
    :submitCallback="submitCallback"
    :dialogStyle="dialogStyle"
    :dialogText="dialogText"
    class="merchant-dialog"
    @close="closeDialog"
    @finish="handleFinish"
  >
    <!--使用v-if用来避免：keepalive缓存导致选中的线路没有正确清空-->
    <template v-if="dialogVisible">
      <div class="form-section">
        <el-form-item label="选择线路：" required prop="lineNumber">
          <el-select v-model.trim="content.lineNumber" placeholder="选择需要复制的线路" filterable clearable>
            <el-option
              v-for="lineItem in availableLineList"
              :key="lineItem.lineNumber"
              :value="lineItem.lineNumber"
              :label="lineItem.lineName+''"
            />
          </el-select>
          <div style="font-size: 13px;">
            支持模糊搜索
          </div>
        </el-form-item>
      </div>
    </template>
  </FormDialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, ref, watch } from 'vue'
import { MerchantLineInfo } from '@/type/merchant'
import { merchantModel } from '@/api/merchant'
import { Throttle } from '@/utils/utils'
import { merchantLineStatusEnum } from '@/assets/js/map-merchant'
import { useMerchantStore } from '@/store/merchant'

// 动态引入组件
const FormDialog = defineAsyncComponent(() => import('@/components/FormDialog.vue'))

const merchantStore = useMerchantStore()

const props = defineProps<{
  // 弹窗显示隐藏
  visible: boolean,
}>()
const emits = defineEmits([
  // 关闭弹窗
  'close',
  // 确定
  'confirm'
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)

watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    // 每次显示弹窗时
    if (val) {
      // 清空可选线路列表
      availableLineList.value = []
      // 获取可选线路列表
      updateAvailableLineList()
    }
  },
)

// 弹窗样式
const dialogStyle = {
  // 弹窗宽度
  dialogWidth: '600px',
  // 表单项标签宽度
  labelWidth: '80px',
}
// 弹窗文本
const dialogText = {
  // 编辑实体时的弹窗标题
  editingTitle: '选择线路',
  // 新建实体时的弹窗标题
  creatingTitle: '选择线路',
  // 取消按钮文本
  cancelButtonText: '取消',
  // 确定按钮文本
  confirmButtonText: '确定',
  // 编辑成功消息
  msgEditSuccessfully: '线路复制成功',
  // 创建成功消息
  msgCreateSuccessfully: '线路复制成功',
  // 编辑失败消息
  msgEditFailed: '线路复制失败',
  // 创建失败消息
  msgCreateFailed: '线路复制失败',
}

// 表单默认值
const contentDefault: MerchantLineInfo = {
  // 线路编号
  lineNumber: '',
}
// 表单内容
const content = reactive<MerchantLineInfo>({ ...contentDefault })

// 表单校验规则
const rules = {
  lineNumber: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('线路不能为空'))
      } else {
        callback()
      }
    },
  }],
}

// 可选线路列表
const availableLineList = ref<MerchantLineInfo[]>([])
// 正在加载可选线路列表
const loadingAvailableLineList = ref<boolean>(false)
// 可选线路列表节流锁
const throttleAvailableLineList = new Throttle(loadingAvailableLineList)

/**
 * 获取可选线路列表
 */
const updateAvailableLineList = async () => {
  // 节流锁上锁
  if (throttleAvailableLineList.check()) {
    return
  }
  throttleAvailableLineList.lock()

  try {
    // 获取全部商户线路中启用状态的线路
    const res = <MerchantLineInfo[]>await merchantModel.getAllMerchantLines()
    const arr = Array.isArray(res) ? res : []
    availableLineList.value = arr.filter((item: MerchantLineInfo) => {
      return item?.enableStatus === merchantLineStatusEnum.ENABLED
    })
  } catch (e) {
  } finally {
    // 节流锁解锁
    throttleAvailableLineList.unlock()
  }
}

// 复制的新的线路信息
const newLineInfo = reactive<MerchantLineInfo>({})

/**
 * 表单弹窗组件的表单提交回调函数
 */
const submitCallback = (result: MerchantLineInfo) => {
  // 原线路信息复制到新线路
  const item = availableLineList.value.find((item) => {
    return item?.lineNumber === result.lineNumber
  }) ?? {}

  // 商户和账号改成当前的商户和当前账号
  item.tenantId = merchantStore.currentMerchant.id!
  item.adminId = merchantStore.currentAccount.id!

  // 原线路的一些唯一参数需要删除或重置
  item.groupId = undefined
  item.id = -1
  item.lineNumber = ''

  Object.assign(newLineInfo, JSON.parse(JSON.stringify(item)))
}

/**
 * 表单已提交
 */
const handleFinish = () => {
  emits('confirm', newLineInfo)
}

/**
 * 关闭弹窗
 */
const closeDialog = () => {
  content.lineNumber = ''
  emits('close')
}
</script>

<style lang="postcss" scoped>
:deep(.el-form-item__content) {
  width: 470px;
}
.el-form .el-form-item:first-child {
  margin-top: 0;
}
</style>
