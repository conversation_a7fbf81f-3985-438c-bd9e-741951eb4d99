<template>
  <!--地区分布弹窗-->
  <el-dialog
    v-model="dialogVisible"
    align-center
    width="960px"
    :close-on-click-modal="false"
    class="merchant-dialog"
    @close="handleCancel"
  >
    <template #header>
      <div class="form-dialog-header">
        支持地区
      </div>
    </template>

    <el-scrollbar v-loading="loadingRegion" class="form-dialog-main" view-class="form-dialog-main-inner">
      <!--地图组件-->
      <GeoChart
        title=""
        :data="regionChartData"
        :legends="chartLegendList"
        :tooltipSort="[0, 1]"
        :visualMapEnabled="false"
        :visualMapSingleColor="true"
      >
        <!--线路支持度图例-->
        <div class="legend">
          <div class="legend-title">线路支持度：</div>
          <div class="legend-list">
            <div class="legend-item legend-item-1">100%</div>
            <div class="legend-item legend-item-2">&ge;50%</div>
            <div class="legend-item legend-item-3">&lt;50%</div>
            <div class="legend-item legend-item-4">0%</div>
          </div>
        </div>
        <!--运营商切换按钮-->
        <div class="operator">
          <el-select v-model="operator" size="small" style="width: 120px;" @change="onChangeOperator">
            <el-option
              v-for="item in Object.values(operatorList)"
              :key="item.name"
              :label="getOperatorStr(item)"
              :value="item.val"
            />
          </el-select>
        </div>
      </GeoChart>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="handleCancel">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, nextTick, ref, watch } from 'vue'
import { formatNumberPercent, Throttle } from '@/utils/utils'
import { MerchantLineConstituteParams } from '@/type/merchant'
import { CloseBold } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { supplierOperatorEnum, supplierOperatorList, supplierOperatorMap } from '@/assets/js/map-supplier'
import { useGlobalStore } from '@/store/globalInfo'
import { RegionChartItem } from '@/type/task'
import { SupplierLineInfo, SupplierMapItemType } from '@/type/supplier'
import { merchantModel } from '@/api/merchant'

// 动态引入组件
const GeoChart = defineAsyncComponent(() => import('@/components/charts/GeoChart.vue'))

const globalStore = useGlobalStore()

const props = defineProps<{
  // 弹窗显示隐藏
  visible: boolean,
  // 外呼支持数据
  data: MerchantLineConstituteParams[],
}>()
const emits = defineEmits([
  // 关闭弹窗
  'close',
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)

watch(
  () => props.visible,
  async (val) => {
    dialogVisible.value = val
    // 每次显示弹窗时
    if (val) {
      // 节流锁上锁
      throttleRegion.lock()

      // 运营商选项恢复默认
      operator.value = supplierOperatorList['all'].val

      regionList.value = props.data

      await updateProvinceCityInfo()
      await updateAllLineList()
      await nextTick()
      updateRegion()

      // 节流锁解锁
      throttleRegion.unlock()

      updateOperatorCountrySupportRate()
    }
  },
)

/**
 * 关闭弹窗
 */
const closeDialog = () => {
  dialogVisible.value = false
  emits('close')
  setTimeout(() => {
    clearData()
  }, 200)
}

/**
 * 点击取消按钮
 */
const handleCancel = () => {
  closeDialog()
}

/**
 * 清空组件数据
 */
const clearData = () => {
  regionChartData.value = []
}

// 正在加载地区分布
const loadingRegion = ref(false)
// 地区分布节流锁
const throttleRegion = new Throttle(loadingRegion)
// 当前运营商
const operator = ref<supplierOperatorEnum>(supplierOperatorList['all'].val)
// 运营商信息列表
const operatorList = ref<{
  [operatorName: string]: SupplierItem
}>(supplierOperatorList)
// 接口数据
const regionList = ref<MerchantLineConstituteParams[]>([])
// 图表数据
const regionChartData = ref<RegionChartItem[]>([])
// 全部省市信息
const provinceAllMap = ref(globalStore.provinceAllMap)
// 全部省份信息
const provinceList = ref(globalStore.getProvinceList)
// 全部省份集合
let provinceAllSet = new Set<string>([])
// 图表图例名称
const chartLegendList = ['最大支持并发', '线路支持度']

interface SupplierItem extends SupplierMapItemType {
  rate?: string | null
}

/**
 * 获取运营商显示的文本
 * 运营商名称和该运营商的全国支持度
 * @param operator
 */
const getOperatorStr = (operator: SupplierItem) => {
  return `${operator?.tabName ?? ''}  ${operator?.rate ?? '暂无数据'}`
}

/**
 * 更新运营商的全国支持度
 */
const updateOperatorCountrySupportRate = () => {
  // 全部城市数量
  let countryTotalCityCount = 0
  Object.keys(provinceAllMap.value).forEach((provinceName: string) => {
    countryTotalCityCount += provinceAllMap.value[provinceName]?.length ?? 0
  })

  // 遍历当前商户线路的所有线路组，统计每条线路组的城市代码去重数量，按运营商分类

  const cityCodeSet: {
    [operatorVal: string]: Set<string>
  } = {}

  regionList.value.forEach((item: MerchantLineConstituteParams) => {
    // 运营商正确
    if (!!Object.values(supplierOperatorList).find((operator) => {
      return operator.val === item?.serviceProvider
    })) {
      // 将当前线路组的所有城市代码放进集合
      item?.cityCodes?.forEach((code: string) => {
        if (!cityCodeSet[item.serviceProvider!]) {
          cityCodeSet[item.serviceProvider!] = new Set([])
        }
        cityCodeSet[item.serviceProvider!].add(code)
      })
    }
  })

  // 恢复默认值
  Object.values(operatorList.value).forEach((item) => {
    item.rate = null
  })
  // 计算全国支持度
  Object.keys(cityCodeSet).forEach((operatorVal: string) => {
    const operatorName: string = supplierOperatorMap.get(<supplierOperatorEnum>operatorVal)?.name ?? ''
    const rate = cityCodeSet[operatorVal].size / countryTotalCityCount * 100
    operatorList.value[operatorName].rate = formatNumberPercent(rate, 2)
  })
}

const updateProvinceCityInfo = async () => {
  if (!Object.keys(provinceAllMap.value)?.length) {
    await globalStore.getProvinceInfo()
  }

  const list = provinceList.value.map((item: string) => {
    return (item.split(','))[1]
  })
  provinceAllSet = new Set(list)
}

const areaColorList: string[] = [
  '#000D4D',
  '#072CA6',
  '#165DFF',
  '#6AA1FF',
]

const allSupplierLineList = ref<SupplierLineInfo[]>([])
/**
 * 获取全部供应线路列表
 */
const updateAllLineList = async () => {
  if (allSupplierLineList.value?.length) {
    return
  }

  try {
    // 请求接口
    const res = <SupplierLineInfo[]>await merchantModel.getSupplierAvailableLineList()
    allSupplierLineList.value = Array.isArray(res) ? res : []
  } catch (e) {
  }
}

/**
 * 根据供应线路编号找到对应的供应线路信息
 */
const findSupplierLineInfoByNumber = (row: MerchantLineConstituteParams) => {
  // 有供应线路列表
  // 遍历线路组，根据编号找到线路信息
  let result: SupplierLineInfo[] = []
  row?.supplyLineNumbers?.forEach((lineNumber: string) => {
    const item = allSupplierLineList.value.find((item: SupplierLineInfo) => {
      return item?.lineNumber === lineNumber
    })
    if (item) {
      result.push(item)
    }
  })
  return JSON.parse(JSON.stringify(result))
}

/**
 * 更新地区分布数据
 */
const updateRegion = () => {
  // 节流锁上锁
  // if (throttleRegion.check()) {
  //   return
  // }
  // throttleRegion.lock()

  try {
    // ---------- 准备常量数据 开始 ----------

    // 清空组件数据
    regionChartData.value = []

    // 统计所有省份的城市数量
    const cityTotalCount: { [provinceName: string]: number } = {}
    Object.keys(provinceAllMap.value).forEach((provinceName: string) => {
      cityTotalCount[provinceName] = provinceAllMap.value[provinceName]?.length ?? 0
    })

    // 筛选出当前运营商的数据
    // 运营商因为地区组不同，可能有多条数据，所以这里是数组
    const list: MerchantLineConstituteParams[] = regionList.value.filter((item) => {
      return item?.serviceProvider === operator.value
    })

    // ---------- 准备常量数据 结束 ----------

    // ---------- 遍历处理数据 开始 ----------

    // 配置的省份的支持城市的代码集合
    const cityCodeSet: {
      [provinceName: string]: Set<string>
    } = {}

    // 每个省份的每条不同线路的并发数量
    const provinceLimit: {
      [provinceName: string]: {
        [lineNumber: string]: number
      }
    } = {}

    // 遍历同一个运营商的每个地区组
    list.forEach((currentOperatorData: MerchantLineConstituteParams) => {
      // 获取当前线路组的所有线路信息
      currentOperatorData.supplyLineList = findSupplierLineInfoByNumber(currentOperatorData)

      // console.log('findSupplierLineInfoByNumber(currentOperatorData)', JSON.parse(JSON.stringify(currentOperatorData)))

      // 记录当前线路组是否已经计算过并发量
      const hasUpdatedLimit: {
        [provinceName: string]: boolean
      } = {}

      // 遍历每个城市
      currentOperatorData?.cityCodes?.forEach((cityCode: string) => {
        // 城市代码转换成省份代码
        const prefix = cityCode.slice(0, 2) ?? '00'
        const provinceName: string = provinceList.value.find((name: string) => {
          return name.includes(prefix + '0000')
        }) ?? '00'

        // 更新对应省份的城市代码集合
        if (!cityCodeSet[provinceName]) {
          cityCodeSet[provinceName] = new Set([cityCode])
        } else {
          cityCodeSet[provinceName].add(cityCode)
        }

        // 更新对应省份的并发量
        // 当前线路组的当前省份是否已经计算过并发量，没有就计算，有就跳过
        if (!hasUpdatedLimit[provinceName]) {
          hasUpdatedLimit[provinceName] = true

          // 将当前省份的所有线路并发都计算一下，如果计算过，就不重复计算了
          // console.log('currentOperatorData.supplyLineList', JSON.parse(JSON.stringify(currentOperatorData.supplyLineList)))
          currentOperatorData.supplyLineList?.forEach((line: SupplierLineInfo) => {
            const lineNumber: string = line?.lineNumber ?? ''
            if (lineNumber) {
              if (!provinceLimit[provinceName]) {
                provinceLimit[provinceName] = {}
              }
              provinceLimit[provinceName][lineNumber] = <number>line?.concurrentLimit
            }
          })
        }
      })
    })

    // ---------- 遍历处理数据 结束 ----------

    // ---------- 更新图表数据 开始 ----------

    // 更新图表数据
    Object.keys(cityCodeSet).forEach((provinceName: string) => {
      // 当前省份支持度=支持的城市数/总共的城市数
      let rate = (cityCodeSet[provinceName].size ?? 0) / cityTotalCount[provinceName]
      if (isNaN(rate) || rate < 0) {
        rate = 0
      } else if (rate > 1) {
        rate = 1
      }

      // 每个省份按照支持度设置不同的颜色
      let areaColor = areaColorList[0] ?? ''
      if (1 <= rate) {
        areaColor = areaColorList[0]
      } else if (0.5 <= rate && rate < 1) {
        areaColor = areaColorList[1]
      } else if (0 < rate && rate < 0.5) {
        areaColor = areaColorList[2]
      } else if (rate <= 0) {
        areaColor = areaColorList[3]
      }

      // console.log('cityCodeSet', provinceName, cityCodeSet, provinceLimit)

      // 省份最大并发（城市合计并去重）
      const provinceTotalLimit: number = Object.values(provinceLimit[provinceName])?.reduce((prev, curr) => {
        return prev + curr
      }, 0) ?? 0

      regionChartData.value.push({
        // 省份名称
        name: (provinceName.split(','))[0] ?? '',
        // 最大支持并发，圆圈大小
        value1: provinceTotalLimit,
        // 省份支持度，省份颜色
        value2: rate,
        // 省份地图样式
        itemStyle: {
          // 地图背景色
          areaColor: areaColor,
        },
      })
    })

    // ---------- 更新图表数据 结束 ----------

  } catch (e) {
    ElMessage({
      message: '支持地区数据获取失败',
      type: 'error',
    })
  }
  // } finally {
  //   // 节流锁解锁
  //   throttleRegion.unlock()
  // }
}

/**
 * 切换运营商
 */
const onChangeOperator = () => {
  if (operator.value) {
    updateRegion()
  }
}
</script>

<style lang="postcss" scoped>
.legend {
  position: absolute;
  top: 12px;
  left: 16px;
  z-index: 10;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.legend-title {
  color: #313233;
  font-weight: bold;
}
.legend-list {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-radius: 4px;
}
.legend-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 52px;
  height: 24px;
  font-size: 12px;
  color: #fff;
}
.legend-item-1 {
  background-color: #000D4D;
}
.legend-item-2 {
  background-color: #072CA6;
}
.legend-item-3 {
  background-color: #165DFF;
}
.legend-item-4 {
  background-color: #6AA1FF;
}
.operator {
  position: absolute;
  top: 12px;
  right: 16px;
  z-index: 10;
}
</style>
