<template>
  <el-dialog
    :model-value="props.visible"
    width="600px"
    class="merchant-dialog"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        {{ props.isEdit ? '编辑' : '添加' }}短信变量
      </div>
    </template>

    <div class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="90px"
        >
          <el-form-item label="变量类型：" prop="variableType">
            <el-select
              v-model="form.variableType"
              placeholder="请选择变量类型"
              disabled
              class="tw-w-full"
            >
              <el-option label="自定义" value="CUS" />
            </el-select>
          </el-form-item>
          <el-form-item label="变量名称：" prop="variableName">
            <el-input
              v-model="form.variableName"
              type="text"
              placeholder="请输入变量名称"
              clearable
              show-word-limit
              maxlength="20"
              class="tw-w-full"
              :disabled="props.isEdit"
              @input="onInputVariableName"
            />
          </el-form-item>
          <el-form-item label="变量说明：" prop="variableComment">
            <el-input
              v-model="form.variableComment"
              type="text"
              placeholder="请输入变量说明"
              clearable
              show-word-limit
              maxlength="20"
              class="tw-w-full"
            />
          </el-form-item>
          <el-form-item label="字段类型：" prop="columnType">
            <el-select
              v-model="form.columnType"
              placeholder="请选择字段类型"
              class="tw-w-full"
            >
              <el-option
                v-for="enumEntity in Object.entries(SmsVariableColumnTypeEnum)"
                :index="enumEntity[0]"
                :label="enumEntity[0]"
                :value="enumEntity[1]"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" :disable="loadingConfirm" @click="onClickCancel">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" :loading="loadingConfirm" @click="onClickConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref, watch } from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { Throttle } from '@/utils/utils'
import { ElMessage, FormRules } from 'element-plus'
import to from 'await-to-js'
import { SmsVariableColumnTypeEnum, SmsVariableItem, SmsVariableParams, SystemVariableEnum } from '@/type/merchant'
import { merchantSmsVariableModel } from '@/api/merchant'
import { useMerchantStore } from '@/store/merchant'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
  data: SmsVariableItem,
  isEdit: boolean,
}>(), {
  visible: false,
  data: () => ({}),
  isEdit: false,
})
const emits = defineEmits([
  'update:visible',
  'confirm'
])

const merchantStore = useMerchantStore()

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = () => ({
  id: undefined,
  variableType: 'CUS',
  variableName: '',
  variableComment: '',
  columnType: SmsVariableColumnTypeEnum['数字'],
})
// 表单数据
const form: SmsVariableItem = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  variableName: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('变量名称不能为空'))
      } else if (value?.match(/[^a-zA-Z_]/g)) {
        callback(new Error('变量名称只能包含英文大小写和下划线'))
      } else if (Object.values(SystemVariableEnum).includes(value)) {
        callback(new Error('变量名称不能是系统内置变量'))
      } else {
        // 判断是否重复添加
        const isSameName = variableList.value?.some((item: SmsVariableItem) => {
          // 编辑时允许和原来的自己重名
          return item.variableName !== props.data?.variableName && item.variableName === value
        })
        if (isSameName) {
          // 校验不通过
          callback(new Error('变量名称已存在'))
        } else {
          // 校验通过
          callback()
        }
      }
    }
  },
  columnType: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('字段类型不能为空'))
      } else {
        callback()
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
      })
    }
  })
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 处理参数
  const params: SmsVariableParams = {
    id: form.id ?? undefined,
    tenantId: merchantStore.currentMerchant.id ?? undefined,
    groupId: merchantStore.currentAccount.groupId ?? undefined,
    variableType: form.variableType,
    variableName: form.variableName,
    variableComment: form.variableComment,
    columnType: form.columnType,
  }

  // 请求接口
  let error
  if (props.isEdit) {
    // 编辑
    const [err, _] = <[any, SmsVariableItem]>await to(merchantSmsVariableModel.editSmsVariable(params))
    error = err
  } else {
    // 新建
    const [err, _] = <[any, SmsVariableItem]>await to(merchantSmsVariableModel.addSmsVariable(params))
    error = err
  }

  // 返回失败结果
  if (error) {
    ElMessage({
      type: 'error',
      message: '保存失败'
    })
    // 节流锁解锁
    throttleConfirm.unlock()
    return
  }

  // 返回成功结果
  ElMessage({
    message: '保存成功',
    type: 'success',
  })
  emits('confirm')
  // 关闭弹窗
  closeDialog()

  // 节流锁解锁
  throttleConfirm.unlock()
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击确定按钮
 */
const onClickConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 变量名称 开始 ----------------------------------------

/**
 * 变量名称，发生变化
 * @param val 文本框内容
 */
const onInputVariableName = (val: string) => {
  // 字符串内容只能英文大小写和下划线_，其他全部移除
  form.variableName = val.replace(/[^a-zA-Z_]/g, '')
}

// ---------------------------------------- 变量名称 结束 ----------------------------------------

// ---------------------------------------- 自定义变量 开始 ----------------------------------------

// 列表，全部，接口数据
const variableList = ref<SmsVariableItem[]>([])

/**
 * 更新全部列表
 */
const updateVariableList = async () => {
  // 处理参数
  const params: SmsVariableParams = {
    groupId: merchantStore.currentAccount.groupId ?? '',
  }

  // 请求接口
  const [err, res] = <[any, SmsVariableItem[]]>await to(merchantSmsVariableModel.getSmsVariable(params))

  // 返回失败结果
  if (err) {
    ElMessage.error('无法获取短信变量列表')
    variableList.value = []
    return
  }

  // 返回成功结果
  // 更新全部列表
  variableList.value = res?.length ? res : []
}

// ---------------------------------------- 自定义变量 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  // 每次显示弹窗时
  if (val) {
    // 更新表单数据
    await nextTick()
    resetForm()
    Object.assign(form, props.data)

    // 更新其他数据
    await updateVariableList()
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
