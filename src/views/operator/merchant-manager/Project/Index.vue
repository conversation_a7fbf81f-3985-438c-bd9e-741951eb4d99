<template>
  <!--顶部-->
  <div class="tw-p-[16px] tw-bg-white">
    <!--功能按钮组-->
    <div v-if="!!merchantStore.currentAccount?.accountEnable" class="tw-flex tw-justify-end">
      <el-button v-if="!currentList?.length" type="primary" :icon="Plus" @click="onClickAdd">
        新增项目
      </el-button>
    </div>
  </div>

  <!--表格-->
  <el-table
    v-loading="loadingList"
    :data="currentList"
    :header-cell-style="tableHeaderStyle"
  >
    <el-table-column align="center" prop="id" label="项目编号" width="80" show-overflow-tooltip />
    <el-table-column align="left" prop="programName" label="项目名称" min-width="200" show-overflow-tooltip />
    <el-table-column align="left" prop="productName" label="产品" min-width="200" show-overflow-tooltip />
    <el-table-column align="left" prop="secondIndustryName" label="行业" min-width="200" show-overflow-tooltip />
    <el-table-column v-if="!!merchantStore.currentAccount?.accountEnable" align="right" label="操作" width="60">
      <template v-slot="{row}">
        <el-button type="primary" link @click="onClickEdit(row)">
          编辑
        </el-button>
      </template>
    </el-table-column>
  </el-table>

  <!--分页条-->
  <PaginationBox
    :currentPage="pageNum"
    :pageSize="pageSize"
    :pageSizeList="pageSizeList"
    :total="total"
    @search="updateAllList"
    @update="updateCurrentList"
  />

  <!--编辑弹窗-->
  <EditDialog
    v-model:visible="editDialogVisible"
    :data="editDialogData"
    @confirm="onEditDialogConfirm"
  />
</template>

<script setup lang="ts">
import { tableHeaderStyle } from '@/assets/js/constant'
import { Throttle, updateCurrentPageList } from '@/utils/utils'
import PaginationBox from '@/components/PaginationBox.vue'
import { computed, nextTick, ref, watch } from 'vue'
import to from 'await-to-js'
import { ElMessage } from 'element-plus'
import { MerchantAccountInfo, MerchantProjectItem, } from '@/type/merchant'
import { Plus } from '@element-plus/icons-vue'
import { useMerchantStore } from '@/store/merchant'
import { merchantProjectModel } from '@/api/merchant'
import EditDialog from './EditDialog.vue'

// ---------------------------------------- 通用 开始 ----------------------------------------

const merchantStore = useMerchantStore()

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表格 开始 ----------------------------------------

// 列表，正在加载
const loadingList = ref<boolean>(false)
// 列表，加载节流锁
const throttleList = new Throttle(loadingList)

// 列表，全部，接口数据
const allList = ref<MerchantProjectItem[]>([])

// 列表，筛选，全部的子集
const filterList = ref<any[]>([])

// 列表，当前页，筛选的子集
const currentList = ref<MerchantProjectItem[]>([])
// 列表，当前页，页码
const pageNum = ref(1)
// 列表，当前页，每页大小
const pageSize = ref(20)
// 列表，当前页，每页大小可选数值
const pageSizeList: number[] = [10, 20, 50, 100]

// 列表，表格展示总数
const total = computed(() => {
  return filterList.value.length ?? 0
})

/**
 * 更新全部列表
 */
const updateAllList = async () => {
  // 节流锁上锁
  if (throttleList.check()) {
    return
  }
  throttleList.lock()

  // 处理参数
  const params: MerchantProjectItem = {
    groupId: merchantStore.currentAccount.groupId ?? undefined,
  }

  // 请求接口
  const [err, res] = <[any, MerchantProjectItem[]]>await to(merchantProjectModel.getProjectList(params))

  // 返回失败结果
  if (err) {
    ElMessage({
      message: '无法获取项目列表',
      type: 'error',
    })
    allList.value = []
    // 节流锁解锁
    throttleList.unlock()
    return
  }

  // 返回成功结果
  // 更新全部列表
  allList.value = res?.length ? res : []
  // 更新筛选列表
  updateFilterList()
  // 更新当前页列表
  updateCurrentList(pageNum.value, pageSize.value)
  // 节流锁解锁
  throttleList.unlock()
}
/**
 * 更新筛选列表
 */
const updateFilterList = () => {
  filterList.value = allList.value
}
/**
 * 更新当前页列表
 * @param {number} p 新页码
 * @param {number} s 新分页大小
 */
const updateCurrentList = (p?: number, s?: number) => {
  // 如果参数指定了页码或/和每页大小，则按参数更新列表；
  // 否则保持当前页码和大小不变直接更新。
  // 这里的判断条件相当于排除了undefined, null, 0等逻辑假值，
  // 另外，页码为0或者分页大小为0，本身就是逻辑错误的，不应该更新分页。
  if (p || s) {
    pageNum.value = p || 1
    pageSize.value = s || 20
  }
  // 更新当前页列表
  currentList.value = updateCurrentPageList(filterList.value, pageNum.value, pageSize.value)
}

// ---------------------------------------- 列表 结束 ----------------------------------------

// ---------------------------------------- 编辑弹窗 开始 ----------------------------------------

// 编辑弹窗，是否显示
const editDialogVisible = ref<boolean>(false)
// 编辑弹窗，表单数据
const editDialogData = ref<MerchantProjectItem>({})
// 编辑弹窗，是否是编辑模式，是——编辑，否——新建
const editDialogIsEdit = ref<boolean>(false)

/**
 * 点击新增项目按钮
 */
const onClickAdd = () => {
  editDialogData.value = {}
  editDialogIsEdit.value = false
  editDialogVisible.value = true
}
/**
 * 点击编辑项目按钮
 * @param {MerchantProjectItem} current 点击的项目信息
 */
const onClickEdit = (current: MerchantProjectItem) => {
  editDialogData.value = JSON.parse(JSON.stringify(current))
  editDialogIsEdit.value = true
  editDialogVisible.value = true
}

/**
 * 编辑弹窗，提交表单
 */
const onEditDialogConfirm = () => {
  updateAllList()
}

// ---------------------------------------- 编辑弹窗 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => merchantStore.currentAccount, async (val: MerchantAccountInfo | null | undefined) => {
  if (typeof val?.id === 'number') {
    await nextTick()
    await updateAllList()
  }
}, { immediate: true, deep: true })

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
