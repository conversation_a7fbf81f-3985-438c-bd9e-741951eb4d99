<template>
  <el-drawer
    v-model="visible"
    :before-close="closeDetails"
    :size="drawerWidth"
  >
    <template #header>
      <div class="tw-w-full tw-flex tw-items-center tw-justify-between tw-py-[12px]">
        <div class="tw-text-[16px] tw-font-[600] tw-text-left tw-text-[var(--primary-black-color-600)]">{{ title }}</div>
      </div>
    </template>
    <div class="module-container">
      <div class="search-box">
        <div class="tw-grid tw-grid-cols-4 tw-gap-[8px]">
          <el-select v-model="searchForm.domain" class="tw-w-full" placeholder="请选择域名" clearable>
            <el-option v-for="item in domainAvailableList" :key="item" :label="item" :value="item" />
          </el-select>
          <el-input
            v-model.trim="searchForm.originalUrl"
            placeholder="请输入原始短链"
            @keyup.enter="search()"
            clearable
          >
          </el-input>
          <el-date-picker
            v-model="searchForm.date"
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placeholder="请选择日期"
            :clearable="false"
            :disabled-date="disabledForRecent30Days"
          />
          <div class="tw-justify-start tw-flex tw-items-center tw-ml-1">
            <el-button type="primary" @click="search()" link>
              <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
              <span>查询</span>
            </el-button>
          </div>
        </div>
      </div>
      <div class="tw-flex tw-w-full tw-my-[8px]">
        <RadioButtonBox v-model:active="activeTab" :list="tabList" @update:active="search()"/>
      </div>
      <!-- 数据统计  -->
      <el-table
        v-if="activeTab==='数据统计'"
        :data="smsStatisticList"
        v-loading="loading"
        class="tw-grow"
        row-key="id"
        :header-cell-style="tableHeaderStyle"
        @sort-change="handleSortChange"
        stripe
      >
        <el-table-column property="domain" label="域名" align="left" min-width="120"></el-table-column>
        <el-table-column property="originalUrl" label="原始链接" show-overflow-tooltip align="left" min-width="200"></el-table-column>
        <el-table-column property="sendCount" label="发送数量" sortable align="left" width="120">
          <template #header="{ column }">
            <div class="tw-flex tw-items-center tw-justify-start">
              <span>{{column.label}}</span>
              <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
                <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
                <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
              </div>
            </div>
          </template>
          <template #default="{ row }">
            {{ formatNumber(row.sendCount) }}
          </template>
        </el-table-column>
        <el-table-column property="clickCount" label="点击数量" sortable align="left" width="120">
          <template #header="{ column }">
            <div class="tw-flex tw-items-center tw-justify-start">
              <span>{{column.label}}</span>
              <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
                <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
                <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
              </div>
            </div>
          </template>
          <template #default="{ row }">
            {{ formatNumber(row.clickCount) }}
          </template>
        </el-table-column>
        <el-table-column property="clickPercent" label="点击率" sortable align="left" width="120">
          <template #header="{ column }">
            <div class="tw-flex tw-items-center tw-justify-start">
              <span>{{column.label}}</span>
              <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
                <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
                <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
              </div>
            </div>
          </template>
          <template #default="{ row }">
            {{ formatNumberPercent(row.clickPercent, 2) }}
          </template>
        </el-table-column>
        <template #empty>
          <el-empty v-if="!smsStatisticList || smsStatisticList.length < 1" description="暂无数据" />
        </template>
      </el-table>
      <!-- 时间-时间分布  -->
      <div v-else-if="activeTab===tabList[1]" v-loading="!!loading" class="tw-mt-[12px] tw-grow tw-bg-white">
        <MixLineBarChart
          :data="smsStatisticByDate || []"
          :legends="legends"
          :tooltip-sort="legendsSort"
          title="时间分布"
          xName="时间"
        />
      </div>
      <!-- 地区-点击占比统计  -->
      <div v-else-if="activeTab===tabList[2]" v-loading="!!loading" class="tw-mt-[12px] tw-grow tw-bg-white">
        <GeoChart
          :data="smsStatisticByArea || []"
          title="地区分布"
          :legends="legendsByArea"
          :tooltip-sort="legendsSortByArea"
          canSwitch2Table
        />
      </div>
      <!-- 设备-点击统计 + 浏览器-点击统计  -->
      <div v-else-if="activeTab===tabList[3]" v-loading="!!loading" class="tw-mt-[12px] tw-grow tw-w-full tw-h-[400px] tw-grid tw-grid-cols-2 tw-gap-x-1">
        <div class="card-box">
          <PieChart :data="smsStatisticByDevice || []" title="设备分布" yName="点击占比" :richWidth="[58, 50, 36]"/>
        </div>
        <div class="card-box">
          <PieChart :data="smsStatisticByBrowser || []" title="浏览器分布" yName="点击占比" :richWidth="[58, 50, 36]"/>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, watch, } from 'vue'
import to from 'await-to-js';
import { handleTableSort, obj2List, formatNumber, formatNumberPercent } from '@/utils/utils'
import { tableHeaderStyle } from '@/assets/js/constant'
import { StatisticSearchOrigin as SearchOrigin, ChartItem } from './constant'
import { disabledForRecent30Days } from '@/utils/constant'
import { smsUrlModel } from '@/api/sms'

import { useSmsStore } from '@/store/sms'
import { SmsStatisticSearch, SmsStatisticItem } from '@/type/sms'
import { CategoryChartItem, } from '@/type/task'

import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import RadioButtonBox from '@/components/RadioButtonBox.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import GeoChart from '@/components/charts/GeoChart.vue'
import PieChart from '@/components/charts/PieChart.vue'
import MixLineBarChart from '@/components/charts/MixLineBarChart.vue'
import dayjs from 'dayjs';

const props = withDefaults(defineProps<{
  visible: boolean,
  linkName?: string,
  linkNumber: string,
  isVolcano?: boolean,
  type: number, // 0: 普通短链，1：千人千链
}>(),{
  
})
const emits = defineEmits(['update:visible',])

const smsInfo = useSmsStore()
const domainAvailableList = computed(() => !props.isVolcano ? smsInfo.domainAllList : smsInfo.volcanoDomainList)

// 抽屉宽度
const drawerWidth = computed(() => (window.innerWidth > 1400 ? '75%' : '950px'))
const title = computed(() => (`${props.type === 0 ? '普通短链' : '千人千链'}【${props.linkName}】`))

// tab 切换
const tabList = ['数据统计', '时间分布', '地区分布', '设备分布']
const activeTab = ref(tabList[0])

const loading = ref(false)
const searchForm = reactive<SmsStatisticSearch>(new SearchOrigin(props.linkNumber))

const smsStatisticList = ref<SmsStatisticItem[] | null>(null) // 数据统计
const smsStatisticByDate = ref<ChartItem[] | null>(null) // 时间-点击统计
const smsStatisticByArea = ref<ChartItem[] | null>(null) // 地区-点击统计
const smsStatisticByDevice = ref<CategoryChartItem[] | null>(null) // 设备-点击统计
const smsStatisticByBrowser = ref<CategoryChartItem[] | null>(null) // 浏览器-点击统计

/** 获取不同域名的统计数据列表 */
const getStatisticList = async () => {
  loading.value = true
  const params: SmsStatisticSearch = {
    ...searchForm,
    date: searchForm.date ? dayjs(searchForm.date).format('YYYY-MM-DD') : undefined
  }
  const [err, res] = await to(
    props.type === 1
    ? smsUrlModel.findThousandStatisticList(params)
    : smsUrlModel.findNoramlStatisticList(params)
  )
  smsStatisticList.value = res || []
  loading.value = false
}
const handleSortChange = (params: { prop: string, order: string }) => {
  smsStatisticList.value = handleTableSort(smsStatisticList.value || [], params.prop, params.order)
}

// 获取点击-时间分布
const legends = ['点击数量', '点击率', '发送数量']
const legendsSort = [0, 2, 1]
const obj2ListParams = { num: 'clickCount', rate: 'rate', total: 'sendCount' }
const getDataByDate = async () => {
  loading.value = true
  const [_, data] = await to(props.type === 1
    ? smsUrlModel.findThousandStatisticByDate(searchForm)
    : smsUrlModel.findNormalStatisticByDate(searchForm)
  )
  smsStatisticByDate.value = obj2List(data || null, obj2ListParams)
  loading.value = false
}


const legendsByArea = ['点击数量', '占比']
const legendsSortByArea = [0, 1]
// 获取地区分布
const getDataByArea = async () => {
  loading.value = true
  const [err1, data] = await to(props.type === 1
    ? smsUrlModel.findThousandStatisticByArea(searchForm)
    : smsUrlModel.findNormalStatisticByArea(searchForm)
  )
  smsStatisticByArea.value = obj2List(data || null, { num: 'clickCount', rate: 'rate'})
  loading.value = false
}

// 获取设备、浏览器分布
const getDataByDevice = async () => {
  loading.value = true
  const [err1, data1] = await to(props.type === 1
    ? smsUrlModel.findThousandStatisticByDevice(searchForm)
    : smsUrlModel.findNormalStatisticByDevice(searchForm)
  )
  smsStatisticByDevice.value = []
  data1 && Object.keys(data1)?.forEach(item => {
    let str = (data1[item].rate || 0) as string | number
    if (typeof str === 'string' && str?.includes('%')) {
      str = str.replace(/%/g, ''); // 替换%
      str = str.replace(/\s+/g, ''); // 替换空字符
      str = +str
    } else {
      str = +str || 0
    }
    smsStatisticByDevice.value?.push({
      name: item.trim(),
      num: data1[item].clickCount || 0,
      value: str || 0,
    })
  })
  const [err2, data2] = await to(props.type === 1
    ? smsUrlModel.findThousandStatisticByBrowser(searchForm)
    : smsUrlModel.findNormalStatisticByBrowser(searchForm)
  )
  smsStatisticByBrowser.value = []
  data2 && Object.keys(data2)?.forEach(item => {
    let str = (data2[item].rate || 0) as string | number
    if (typeof str === 'string' && str?.includes('%')) {
      str = str.replace(/%/g, ''); // 替换%
      str = str.replace(/\s+/g, ''); // 替换空字符
      str = +str
    } else {
      str = +str || 0
    }
    smsStatisticByBrowser.value?.push({
      name: item.trim(),
      num: data2[item].clickCount || 0,
      value: str || 0,
    })
  })
  loading.value = false
}

const actionMap = new Map<string, Function>([
  ['数据统计', getStatisticList],
  ['时间分布', getDataByDate],
  ['地区分布', getDataByArea],
  ['设备分布', getDataByDevice],
])
// 根据所在tab获取统计数据接口
const search = async () => {
  if (actionMap.get(activeTab.value)) {
    await actionMap.get(activeTab.value)!()
  }
}

const clearAll = () => {
  smsStatisticList.value = null
  smsStatisticByDate.value = null
  smsStatisticByArea.value = null
  smsStatisticByDevice.value = null
  smsStatisticByBrowser.value = null
}
const closeDetails = () => {
  visible.value = false
  emits('update:visible', false)
}
const visible = ref(false)
watch([() => props.visible, () => props.linkNumber ], () => {
  visible.value = props.visible
  if (props.visible) {
    activeTab.value = tabList[0]
    Object.assign(searchForm, new SearchOrigin(props.linkNumber))
    search()
  } else {
    clearAll()
  }
})

</script>

<style scoped lang="postcss" type="text/postcss">
.card-box {
  padding: 0 8px;
}
.module-container {
  height: 100%;
  padding: 12px;
}
</style>
