<template>
  <HeaderBox title="火山短链管理" class="tw-grow-0 tw-shrink-0"/>
  <div class="module-container">
    <TabsBox v-model:active="activeTab" :tabList="tabList"></TabsBox>
    <ShortUrl :type="activeTab == tabList[0] ? 1 : 0" isVolcano/>
  </div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, ref, } from 'vue'
import HeaderBox from '@/components/HeaderBox.vue'
import TabsBox from '@/components/TabsBox.vue'
const ShortUrl = defineAsyncComponent({loader: () => import('./ShortUrl.vue')})

const tabList: string[] = ['千人千链', '普通短链',]

const activeTab = ref(tabList[0] || '')
</script>

<style scoped lang="postcss" type="text/postcss">
.module-container {
  width: 100%;
  min-width: 1080px;
}
</style>
