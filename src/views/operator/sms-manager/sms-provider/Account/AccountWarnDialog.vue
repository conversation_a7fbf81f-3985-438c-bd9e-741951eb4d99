<template>
  <el-dialog
    v-model="visible"
    width="640px"
    align-center
    class="dialog-form"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">短信账号预警</div>
    </template>
    <el-form
      v-loading="loading"
      ref="formRef"
      label-width="90px"
      :model="form"
      :rules="rules"
    >
      <el-form-item label="账号预警：" prop="enableStatus">
        <el-switch
          v-model="form.enableStatus"
          class="tw-mx-[8px]"
          inline-prompt
          active-text="开启"
          inactive-text="关闭"
          :active-value="'ENABLE'"
          :inactive-value="'DISABLE'"
        />
      </el-form-item>
      <div>
        <el-form-item label="提交预警：" prop="submitCount">
          连续发送
          <InputNumberBox v-model:value="form.submitCount" :disabled="form.enableStatus!='ENABLE'" :precision="0" class="tw-mx-[8px]" style="width: 100px;" :min="1" append="次"/>
          ，提交失败率高于
          <InputNumberBox v-model:value="submitFailRate" :disabled="form.enableStatus!='ENABLE'" class="tw-mx-[8px]" :precision="2" style="width: 100px;" :min="1" :max="100" append="%"/>
          时，自动挂起账号
        </el-form-item>
        <el-form-item label="发送预警：" prop="sendCount">
          连续发送
          <InputNumberBox v-model:value="form.sendCount" :disabled="form.enableStatus!='ENABLE'" :precision="0" class="tw-mx-[8px]" style="width: 100px;" :min="1" append="次"/>
          ，发送失败率高于
          <InputNumberBox v-model:value="sendFailRate" :disabled="form.enableStatus!='ENABLE'" class="tw-mx-[8px]" :precision="2" style="width: 100px;" :min="1" :max="100" append="%"/>
          时，自动挂起账号
        </el-form-item>
        <el-form-item label="回执预警：" prop="receiptCount">
          连续发送
          <InputNumberBox v-model:value="form.receiptCount" :disabled="form.enableStatus!='ENABLE'" class="tw-mx-[8px]" :precision="0" style="width: 100px;" :min="1" append="次"/>
          ，回执超时率高于
          <InputNumberBox v-model:value="receiptFailRate" :disabled="form.enableStatus!='ENABLE'" class="tw-mx-[8px]" :precision="2" style="width: 100px;" :min="1" :max="100" append="%"/>
          时，自动挂起账号
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="cancel" :icon="CloseBold">取消</el-button>
      <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, watch, } from 'vue'
import { ElMessage, FormRules } from 'element-plus'
import { SmsAccountWarningConfig } from '@/type/sms'
import { smsAccountModel } from '@/api/sms'
import to from 'await-to-js'
import InputNumberBox from '@/components/InputNumberBox.vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { pickAttrFromObj } from '@/utils/utils'

const props = defineProps<{
  accountNumber: string | null,
  visible: boolean
}>();
const emits = defineEmits(['update:visible'])

const loading = ref(false)
const visible = ref(false)
const formRef = ref() // 表单DOM

// 表单默认数据，用函数返回值达到深拷贝效果
class formDefault implements SmsAccountWarningConfig {
  enableStatus = 'ENABLE'
  id = undefined
  receiptCount = null
  receiptFailCount = null
  sendFailCount = null
  sendCount = null
  smsAccountNumber = props.accountNumber || ''
  submitCount = null
  submitFailCount = null
}

// 表单数据
const form: SmsAccountWarningConfig = reactive(new formDefault())
const submitFailRate = ref<null | number>(null)
const sendFailRate = ref<null | number>(null)
const receiptFailRate = ref<null | number>(null)
// 表单校验规则
const rules: FormRules = reactive({
  enableStatus: [
    { required: true, message: '请选择启用状态', trigger: 'change' },
    { validator: (rule: any, value: any, callback: any) => {
      if (form.enableStatus === 'ENABLE' && !form.submitFailCount && !form.sendFailCount && !form.receiptFailCount) {
        return callback(new Error('请至少设置一项预警'))
      }
      return callback()
    } , trigger: ['change' ]}
  ],
  submitCount: [
    { validator: (rule: any, value: any, callback: any) => {
      if (!form.submitCount && !submitFailRate.value) {
        return callback()
      }
      if (!submitFailRate.value || !form.submitCount) {
        return callback(new Error('连续发送或提交失败率不能为空'))
      }
      const pre =  submitFailRate.value
      form.submitFailCount = Math.round(form.submitCount * pre / 100) || 1
      submitFailRate.value = form.submitFailCount / form.submitCount * 100
      if (pre !== submitFailRate.value) {
        ElMessage.warning('以为您自动调整提交失败率，以保证失败次数为整数')
      }
      callback()
    } , trigger: ['change', 'blur' ]}
  ],
  sendCount: [
    { validator: (rule: any, value: any, callback: any) => {
      if (!form.sendCount && !sendFailRate.value) {
        return callback()
      }
      if (!form.sendCount || !sendFailRate.value) {
        return callback(new Error('连续发送或发送失败率不能为空'))
      }
      const pre =  sendFailRate.value
      form.sendFailCount = Math.round(form.sendCount * pre / 100) || 1
      sendFailRate.value = form.sendFailCount / form.sendCount * 100
      if (pre !== sendFailRate.value) {
        ElMessage.warning('以为您自动调整发送失败率，以保证失败次数为整数')
      }
      callback()
    } , trigger: ['change', 'blur' ]}
  ],
  receiptCount: [
    { validator: (rule: any, value: any, callback: any) => {
      if (!form.receiptCount && !receiptFailRate.value) {
        return callback()
      }
      if (!form.receiptCount || !receiptFailRate.value) {
        return callback(new Error('连续发送或回执失败率不能为空'))
      }
      const pre =  receiptFailRate.value
      form.receiptFailCount = Math.round(form.receiptCount * pre/ 100) || 1
      receiptFailRate.value = form.receiptFailCount / form.receiptCount * 100
      if (pre !== receiptFailRate.value) {
        ElMessage.warning('以为您自动调整回执失败率，以保证失败次数为整数')
      }
      callback()
    } , trigger: ['change', 'blur' ]}
  ],
})

const cancel = () => {
  visible.value = false
  emits('update:visible', false)
}

const confirm = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true
      const [err] = await to(smsAccountModel.saveSmsAccountWarning(pickAttrFromObj(form, 
        ['enableStatus', 'submitFailCount', 'sendFailCount', 'receiptFailCount', 'submitCount', 'sendCount', 'receiptCount', 'id', 'smsAccountNumber']
      )))
      if (!err) {
        ElMessage.success('操作成功')
        cancel()
      }
      loading.value = false
    }
  })
}

const updateWarnData = async () => {
  if (!props.accountNumber) return
  const [_ , res] = await to(smsAccountModel.findSmsAccountWarning({
    accountNumber: props.accountNumber
  }))
  Object.assign(form, res || new formDefault())
  form.smsAccountNumber = form.smsAccountNumber || props.accountNumber
  submitFailRate.value = (form.submitFailCount && form.submitCount) ? form.submitFailCount / form.submitCount * 100 : null
  sendFailRate.value = (form.sendFailCount && form.sendCount) ? form.sendFailCount / form.sendCount * 100 : null
  receiptFailRate.value = (form.receiptFailCount && form.receiptCount) ? form.receiptFailCount / form.receiptCount * 100 : null
}



/** watch */ 
// 监听visible
watch(() => props.visible, n => {
  visible.value = n
  if(n) {
    updateWarnData()
  }
})
</script>

<style scoped lang="postcss">
.card-box {
  flex-direction: column;
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
    .el-input__wrapper {
      width: 100%
    }
  }
  .el-form-item__label {
    padding-right: 0;
    font-size: 13px;
  }
  :deep(.el-form-item__content) {
    font-size: 13px;
    color: var(--primary-black-color-400);
  }
}
</style>
