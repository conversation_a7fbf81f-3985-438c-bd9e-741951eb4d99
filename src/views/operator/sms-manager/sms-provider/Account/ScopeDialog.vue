<template>
  <el-dialog
    :model-value="props.visible"
    width="960px"
    class="sms-dialog"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        支持范围
      </div>
    </template>

    <el-scrollbar class="form-dialog-main" max-height="80vh">
      <div class="form-dialog-main-inner">
        <CitySettingBox
          ref="restrictComponentRef"
          :taskRestrictData="taskRestrictData"
          :selectedOperatorList="selectedOperatorList"
          :allOperatorList="selectedOperatorList"
          :readonly="true"
        />
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="onClickCancel">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref, watch } from 'vue'
import { OperatorEnum, RestrictModal } from '@/type/common'
import { CloseBold } from '@element-plus/icons-vue'
import CitySettingBox from '@/components/CitySettingBox.vue'
import { deduplicateBaseArray } from '@/utils/utils'
import { SmsAccountServiceProviderEnum } from '@/type/sms'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
  list: string[],
  operator: SmsAccountServiceProviderEnum,
}>(), {
  visible: false,
  list: () => ([]),
  operator: SmsAccountServiceProviderEnum['中国移动'],
})
const emits = defineEmits([
  'update:visible',
])

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

/**
 * 重置表单
 */
const resetForm = () => {
  // 清除其他内容
  Object.assign(taskRestrictData, new RestrictModalOrigin())
  selectedOperatorList.value = [OperatorEnum['移动']]
}
/**
 * 更新表单
 */
const updateForm = () => {
  cityCodesApiToComponent()
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 支持范围 开始 ----------------------------------------

// 支持范围 组件数据 类
class RestrictModalOrigin {
  allRestrictProvince = null
  allRestrictCity = null
  ydRestrictProvince = null
  ydRestrictCity = null
  ltRestrictProvince = null
  ltRestrictCity = null
  dxRestrictProvince = null
  dxRestrictCity = null
  unknownRestrictProvince = null
  unknownRestrictCity = null
  virtualRestrictProvince = null
  virtualRestrictCity = null
}

// 支持范围 组件引用
const restrictComponentRef = ref()
// 支持范围 组件数据 已选运营商列表
const selectedOperatorList = ref<OperatorEnum[]>([OperatorEnum['移动']])
// 支持范围 组件数据 已选中省市
const taskRestrictData = reactive<RestrictModal>(new RestrictModalOrigin())

/**
 * 支持范围 接口数据转换成组件数据
 */
const cityCodesApiToComponent = () => {
  // 市行政代码 列表
  const cityList = props.list?.length ? props.list : []
  // 市行政代码 字符串拼接
  const cityStr = cityList.join(',')

  // 省行政代码 列表
  // 从市行政代码里提取前2位，后面4位补0
  // 因为同省的市可能有多个，所以需要去重
  const provinceList = deduplicateBaseArray(cityList.map((cityCode: string) => {
    return cityCode.slice(0, 2) + '0000'
  }))
  // 省行政代码 字符串拼接
  const provinceStr = provinceList.join(',')

  switch (props.operator) {
    case SmsAccountServiceProviderEnum['中国移动']:
      taskRestrictData.ydRestrictCity = cityStr
      taskRestrictData.ydRestrictProvince = provinceStr
      break;
    case SmsAccountServiceProviderEnum['中国联通']:
      taskRestrictData.ltRestrictCity = cityStr
      taskRestrictData.ltRestrictProvince = provinceStr
      break;
    case SmsAccountServiceProviderEnum['中国电信']:
      taskRestrictData.dxRestrictCity = cityStr
      taskRestrictData.dxRestrictProvince = provinceStr
      break;
    case SmsAccountServiceProviderEnum['未知']:
      taskRestrictData.unknownRestrictCity = cityStr
      taskRestrictData.unknownRestrictProvince = provinceStr
      break;
    default:
      taskRestrictData.ydRestrictCity = cityStr
      taskRestrictData.ydRestrictProvince = provinceStr
      break;
  }
}

// ---------------------------------------- 支持范围 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  // 每次显示弹窗时
  if (val) {
    // 更新表单数据
    await nextTick()
    resetForm()
    updateForm()
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
