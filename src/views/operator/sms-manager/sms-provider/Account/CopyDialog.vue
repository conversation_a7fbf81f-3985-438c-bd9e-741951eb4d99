<template>
  <el-dialog
    :model-value="props.visible"
    width="600px"
    class="sms-dialog"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        复制短信对接账号
      </div>
    </template>

    <div class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="130px"
        >
          <el-form-item label="选择短信对接账号：" required prop="id">
            <el-select
              v-model.number="form.id"
              filterable
              clearable
              placeholder="选择需要复制的短信对接账号"
            >
              <el-option
                v-for="item in allList"
                :key="item.id"
                :value="item.id"
                :label="item.smsAccountName"
              />
            </el-select>
            <div style="font-size: 13px;">
              支持模糊搜索
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="onClickCancel">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="onClickConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref, watch } from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { Throttle } from '@/utils/utils'
import { ElMessage, FormRules } from 'element-plus'
import to from 'await-to-js'
import { smsAccountModel } from '@/api/sms'
import { SmsAccountItem, SmsAccountStatusEnum, SmsProviderAccountParams } from '@/type/sms'
import router from '@/router'
import { useSmsProviderStore } from '@/store/sms-provider'
import { storeToRefs } from 'pinia'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
}>(), {
  visible: false,
})
const emits = defineEmits([
  'update:visible',
  'confirm'
])

const smsProviderStore = useSmsProviderStore()
const { currentProvider } = storeToRefs(smsProviderStore)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): SmsProviderAccountParams => {
  return {
    id: undefined,
  }
}
// 表单数据
const form: SmsProviderAccountParams = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  id: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (typeof value !== 'number') {
        callback(new Error('短信对接账号' + '不能为空'))
      } else {
        // 校验通过
        callback()
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
      })
    }
  })
}
/**
 * 提交表单
 */
const submit = () => {
  // 根据ID找到信息，移除ID
  const row = JSON.parse(JSON.stringify(allList.value.find(item => item.id === form.id) ?? {}))
  row.id = undefined
  // 缓存数据
  smsProviderStore.editingSmsAccount = JSON.parse(JSON.stringify(row))
  ElMessage({
    message: '复制成功',
    duration: 3000,
    type: 'success',
  })
  // 关闭弹窗
  closeDialog()
  // 切换路由
  router.push({ name: 'SmsAccountEditDetail' })
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}
/**
 * 点击确定按钮
 */
const onClickConfirm = () => {
  validForm(submit)
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 列表 开始 ----------------------------------------

// 列表，正在加载
const loadingList = ref<boolean>(false)
// 列表，加载节流锁
const throttleList = new Throttle(loadingList)

// 列表，全部，接口数据
const allList = ref<SmsAccountItem[]>([])

/**
 * 更新全部列表
 */
const updateAllList = async () => {
  // 节流锁上锁
  if (throttleList.check()) {
    return
  }
  throttleList.lock()

  // 处理参数
  const params: SmsProviderAccountParams = {
    supplierId: currentProvider.value.id ?? undefined,
  }

  // 请求接口
  const [err, res] = <[any, SmsAccountItem[]]>await to(smsAccountModel.findAllAccountList(params))

  // 返回失败结果
  if (err) {
    ElMessage.error('无法获取短信对接账号列表')
    allList.value = []
    // 节流锁解锁
    throttleList.unlock()
    return
  }

  // 返回成功结果
  // 更新全部列表
  // 过滤出启用状态的数据
  allList.value = <SmsAccountItem[]>(res?.length ? res : []).filter((item: SmsAccountItem) => {
    return item?.enableStatus === SmsAccountStatusEnum['启用']
  })
  // 节流锁解锁
  throttleList.unlock()
}

// ---------------------------------------- 列表 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  // 每次显示弹窗时
  if (val) {
    // 更新表单数据
    await nextTick()
    resetForm()
    await updateAllList()
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
