<template>
  <el-drawer
    :model-value="props.visible"
    title="对接账号详情"
    class="submodule-detail"
    size="98%"
    align-center
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    :show-close="false"
    :destroy-on-close="true"
    @close="closeDialog"
  >
    <!--抽屉顶部-->
    <template #header>
      <div class="drawer-header tw-max-w-[1000px]">
        线路详情
      </div>
    </template>

    <!--抽屉主体-->
    <el-scrollbar class="drawer-main">
      <div class="drawer-main-inner tw-max-w-[1000px]">
        <!--表单组件-->
        <Form ref="formWrapRef" :readonly="true" />
      </div>
    </el-scrollbar>

    <!--抽屉底部-->
    <template #footer>
      <div class="drawer-footer tw-max-w-[1000px]">
        <el-button :icon="CloseBold" @click="onClickClose">
          关闭
        </el-button>
        <el-button type="primary" :icon="Select" @click="onClickEdit">
          修改
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import router from '@/router'
import { CloseBold, Select } from '@element-plus/icons-vue'
import Form from './components/Form.vue'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
}>(), {
  visible: false,
})
const emits = defineEmits([
  'update:visible',
])

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
}
/**
 * 点击关闭按钮
 */
const onClickClose = () => {
  closeDialog()
}
/**
 * 点击修改按钮
 */
const onClickEdit = () => {
  closeDialog()
  router.push({ name: 'SmsAccountEditDetail' })
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
