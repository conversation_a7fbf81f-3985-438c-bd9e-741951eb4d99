<template>
  <!--表单-->
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-position="right"
    label-width="110px"
    inline
    class="tw-max-w-[1000px] tw-mx-auto tw-p-[12px] tw-bg-white"
  >
    <div class="form-section">
      <div class="form-section-header">
        账号归属
      </div>
      <div class="tw-grid tw-grid-cols-3 tw-gap-0">
        <el-form-item label="供应商ID：">
          <span>
            {{ form.supplierId }}
          </span>
        </el-form-item>
        <el-form-item label="供应商名称：">
          <span>
            {{ form.supplierName }}
          </span>
        </el-form-item>
        <el-form-item label="供应商简称：">
          <span>
            {{ form.supplierProfile }}
          </span>
        </el-form-item>
      </div>
      <div class="tw-grid tw-grid-cols-2 tw-gap-0">
        <el-form-item label="所属商户：" prop="account">
          <div class="tw-flex tw-items-center tw-w-full">
            <el-select
              v-model="form.account"
              placeholder="请选择所属商户主账号"
              filterable
              class="tw-grow tw-mr-[8px]"
              :disabled="props.readonly"
              @visible-change="onMasterAccountVisibleChange"
            >
              <el-option
                v-for="masterAccountItem in masterAccountList"
                :key="masterAccountItem.groupId"
                :label="masterAccountItem.account"
                :value="masterAccountItem.account"
              />
            </el-select>
            <el-tooltip class="tw-shrink-0" content="所属商户：已不再通过该属性限制短信账号的选择，随便填写即可" placement="bottom" effect="dark">
              <el-icon size="16">
                <SvgIcon name="warning" />
              </el-icon>
            </el-tooltip>
          </div>
          
        </el-form-item>
      </div>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        基本信息
      </div>
      <div class="tw-grid tw-grid-cols-2 tw-gap-0">
        <el-form-item label="账号编号：">
          <el-input
            v-model="form.smsAccountNumber"
            placeholder="创建成功后自动生成"
            disabled
            class="tw-w-full"
          />
        </el-form-item>
        <el-form-item label="账号名称：" prop="smsAccountName">
          <el-input
            v-model.trim="form.smsAccountName"
            placeholder="请输入账号名称"
            clearable
            maxlength="20"
            show-word-limit
            class="tw-w-full"
            :disabled="props.readonly"
          />
        </el-form-item>
        <el-form-item label="启用状态：" prop="enableStatus">
          <el-select
            v-model="form.enableStatus"
            placeholder="请选择启用状态"
            class="tw-w-full"
            :disabled="props.readonly"
          >
            <el-option
              v-for="SmsAccountStatusItem in Object.entries(SmsAccountStatusEnum)"
              :key="SmsAccountStatusItem[1]"
              :value="SmsAccountStatusItem[1]"
              :label="SmsAccountStatusItem[0]"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="挂起状态：" prop="pending">
          <el-select
            v-model="form.pending"
            placeholder="请选择挂起状态"
            class="tw-w-full"
            :disabled="props.readonly"
          >
            <el-option :value="false" label="未挂起" />
            <el-option :value="true" label="已挂起" />
          </el-select>
        </el-form-item>
        <el-form-item label="运营备注：" prop="notes" class="tw-col-span-2">
          <el-input
            v-model.trim="form.notes"
            type="textarea"
            placeholder="请输入运营备注"
            maxlength="250"
            show-word-limit
            autosize
            resize="none"
            class="tw-w-full"
            :disabled="props.readonly"
          />
        </el-form-item>
      </div>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        对接信息
      </div>
      <div class="tw-grid tw-grid-cols-2 tw-gap-0">
        <el-form-item label="对接协议：" prop="smsProtocol">
          <el-select
            v-model="form.smsProtocol"
            placeholder="请选择对接协议"
            class="tw-w-full"
            :disabled="props.readonly"
            @change="onSmsProtocolChange"
          >
            <el-option
              v-for="SmsProtocolItem in Object.entries(SmsProtocolEnum)"
              :key="SmsProtocolItem[1]"
              :value="SmsProtocolItem[1]"
              :label="SmsProtocolItem[0]"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="protocolVersionList.length" label="协议版本：" prop="version">
          <el-select
            v-model="form.version"
            placeholder="请选择协议版本"
            class="tw-w-full"
            :disabled="props.readonly"
          >
            <el-option
              v-for="protocolVersionItem in protocolVersionList"
              :key="protocolVersionItem[1]"
              :value="protocolVersionItem[1]"
              :label="protocolVersionItem[0]"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else label="协议版本：">
          <el-select
            v-model="form.version"
            placeholder="无需选择协议版本"
            class="tw-w-full"
            disabled
          />
        </el-form-item>
        <el-form-item label="对接地址：" prop="connectAddress">
          <el-input
            v-model.trim="form.connectAddress"
            placeholder="请输入对接地址"
            class="tw-w-full"
            :disabled="props.readonly"
          />
        </el-form-item>
        <el-form-item label="对接端口：" prop="connectPort">
          <el-input-number
            v-model.number="form.connectPort"
            placeholder="请输入对接端口"
            :precision="0"
            :min="0"
            :max="65535"
            :step="1"
            style="width: 100%;"
            :disabled="props.readonly"
          />
        </el-form-item>
        <el-form-item label="扩展码：">
          <el-input
            v-model.trim="form.extensionCode"
            maxlength="20"
            show-word-limit
            placeholder="请输入扩展码"
            style="width: 100%;"
            clearable
            :disabled="props.readonly"
          />
        </el-form-item>
        <el-form-item label="账号：" prop="connectAccount">
          <el-input
            v-model.trim="form.connectAccount"
            placeholder="请输入账号"
            clearable
            maxlength="200"
            show-word-limit
            class="tw-w-full"
            :disabled="props.readonly"
          />
        </el-form-item>
        <el-form-item label="密码：" prop="password">
          <el-input
            v-model.trim="form.password"
            type="password"
            placeholder="请输入密码"
            clearable
            maxlength="200"
            show-password
            autocomplete="new-password"
            class="tw-w-full"
            :disabled="props.readonly"
          />
        </el-form-item>
        <el-form-item label="接入码：" prop="srcId">
          <el-input
            v-model.trim="form.srcId"
            placeholder="请输入接入码"
            clearable
            maxlength="20"
            show-word-limit
            class="tw-w-full"
            :disabled="props.readonly"
            @input="onScrIdInput"
          />
        </el-form-item>
        <el-form-item label="服务ID：" prop="serviceId">
          <el-input
            v-model.trim="form.serviceId"
            placeholder="请输入服务ID"
            clearable
            maxlength="20"
            show-word-limit
            class="tw-w-full"
            :disabled="props.readonly"
            @input="onServiceIdInput"
          />
        </el-form-item>
        <el-form-item label="最大连接数：" prop="maxChannels">
          <el-input-number
            v-model.number="form.maxChannels"
            placeholder="请输入最大连接数"
            :precision="0"
            :min="1"
            :max="10_0000"
            :step="1"
            style="width: 100%;"
            :disabled="props.readonly"
          />
        </el-form-item>
        <el-form-item label="是否回执：" prop="isReturn">
          <el-select
            v-model="form.isReturn"
            placeholder="请选择是否回执"
            class="tw-w-full"
            :disabled="props.readonly"
          >
            <el-option :value="true" label="是" />
            <el-option :value="false" label="否" />
          </el-select>
        </el-form-item>
        <el-form-item label="回执超时时间：" prop="returnTimeout">
          <div class="tw-flex tw-flex-row tw-justify-start tw-items-center tw-w-full">
            <el-input-number
              v-model.number="form.returnTimeout"
              placeholder="请输入回执超时时间"
              :precision="0"
              :min="1"
              :max="600"
              :step="1"
              style="width: 100%;"
              :disabled="props.readonly"
            />
            <span class="tw-flex-none tw-ml-[8px]">s</span>
            <!--信息提示图标-->
            <div class="tw-flex-none tw-flex tw-justify-center tw-items-center tw-ml-[8px]">
              <el-tooltip content="短信提交成功后，等待回执时间的最大时长，超时未返回，则记为回执超时" placement="bottom" effect="dark">
                <el-icon size="20">
                  <SvgIcon name="warning" />
                </el-icon>
              </el-tooltip>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="发送延迟：" prop="sendDelay">
          <div class="tw-flex tw-flex-row tw-justify-start tw-items-center tw-w-full">
            <el-input-number
              v-model.number="form.sendDelay"
              placeholder="请输入发送延迟"
              :precision="0"
              :min="0"
              :max="600"
              :step="1"
              style="width: 100%;"
              :disabled="props.readonly"
            />
            <span class="tw-flex-none tw-ml-[8px]">s</span>
          </div>
        </el-form-item>
        <br>
        <el-form-item label="配置信息：" class="tw-col-span-2">
          <el-input
            v-model.trim="form.configInfo"
            type="textarea"
            placeholder="请输入配置信息"
            maxlength="2000"
            show-word-limit
            autosize
            resize="none"
            class="tw-w-full"
            :disabled="props.readonly"
          />
        </el-form-item>
      </div>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        限制信息
      </div>
      <div class="tw-grid tw-grid-cols-2 tw-gap-0">
        <el-form-item label="单次提交上限：">
          <div class="tw-flex tw-flex-row tw-justify-start tw-items-center tw-w-full">
            <el-input-number
              v-model.number="form.singleSubmitLimit"
              placeholder="请输入单次提交上限"
              :precision="0"
              :min="1"
              :step="1"
              style="width: 100%;"
              :disabled="props.readonly"
            />
            <span class="tw-flex-none tw-ml-[8px]">个</span>
          </div>
        </el-form-item>
        <el-form-item label="单日提交上限：">
          <div class="tw-flex tw-flex-row tw-justify-start tw-items-center tw-w-full">
            <el-input-number
              v-model.number="form.singleDaySubmitLimit"
              placeholder="请输入单日提交上限"
              :precision="0"
              :min="1"
              :step="1"
              style="width: 100%;"
              :disabled="props.readonly"
            />
            <span class="tw-flex-none tw-ml-[8px]">个</span>
          </div>
        </el-form-item>
        <el-form-item label="适用行业：" prop="secondIndustries">
          <el-cascader
            v-model="form.secondIndustries"
            placeholder="请选择适用行业"
            clearable
            :options="industryOptions"
            :props="industryProps"
            :show-all-levels="false"
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="4"
            class="tw-w-full"
            :disabled="props.readonly"
          />
        </el-form-item>
        <el-form-item label="适用业务：" prop="smsAccountBusinessType">
          <el-select
            v-model="form.smsAccountBusinessType"
            placeholder="请选择适用业务"
            class="tw-w-full"
            :disabled="props.readonly"
          >
            <el-option
              v-for="SmsAccountBusinessTypeItem in Object.entries(SmsAccountBusinessTypeEnum)"
              :key="SmsAccountBusinessTypeItem[1]"
              :value="SmsAccountBusinessTypeItem[1]"
              :label="SmsAccountBusinessTypeItem[0]"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提交速率限制：">
          <div class="tw-flex tw-flex-row tw-justify-start tw-items-center tw-w-full">
            <el-input-number
              v-model.number="form.submitSpeedLimit"
              placeholder="请输入提交速率限制"
              :precision="0"
              :min="1"
              :step="1"
              style="width: 100%;"
              :disabled="props.readonly"
            />
            <span class="tw-flex-none tw-ml-[8px]">次/秒</span>
          </div>
        </el-form-item>
      </div>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        频率限制
      </div>
      <div class="tw-grid tw-grid-cols-2 tw-gap-0">
        <el-form-item ref="submitRestrictionsRef" label="提交限制：">
          <div class="tw-flex tw-flex-col tw-justify-start tw-items-left tw-w-full tw-h-full">
            <div v-show="!props.readonly">
              <el-button
                type="primary"
                link
                :icon="Plus"
                @click="addRestriction(1)"
              >
                添加提交限制
              </el-button>
            </div>
            <div
              v-for="(submitRestriction, index) in form.submitRestrictions"
              v-if="form.submitRestrictions?.length"
              :key="submitRestriction + index"
              class="tw-flex tw-justify-between tw-mt-[8px]"
            >
              <el-input-number
                :model-value="parseInt(submitRestriction.split('-')[0]) || undefined"
                placeholder="请输入"
                :controls="false"
                :precision="0"
                :min="1"
                :max="10"
                :step="1"
                clearable
                style="width: 70px;"
                :disabled="props.readonly"
                @change="(v: string) => updateCallingRestriction(v, submitRestriction.split('-')[1], index)"
              />
              <span>次</span>
              <el-input-number
                :model-value="parseInt(submitRestriction.split('-')[1]) || undefined"
                placeholder="请输入"
                :controls="false"
                :precision="0"
                :min="1"
                :max="128"
                :step="1"
                clearable
                style="width: 70px;"
                :disabled="props.readonly"
                @change="(v: string) => updateCallingRestriction(submitRestriction.split('-')[0], v, index)"
              />
              <span>小时</span>
              <el-button
                v-show="!props.readonly"
                type="primary"
                link
                :icon="CloseBold"
                @click="delRestriction(1, submitRestriction)"
              />
            </div>
          </div>
        </el-form-item>
        <el-form-item ref="sendRestrictionsRef" label="发送限制：">
          <div class="tw-flex tw-flex-col tw-justify-start tw-items-left tw-w-full tw-h-full">
            <div v-show="!props.readonly">
              <el-button
                type="primary"
                link
                :icon="Plus"
                @click="addRestriction(2)"
              >
                添加发送限制
              </el-button>
            </div>
            <div
              v-for="(sendRestriction, index) in form.sendRestrictions"
              v-if="form.sendRestrictions?.length"
              :key="sendRestriction + index"
              class="tw-flex tw-justify-between tw-mt-[8px]"
            >
              <el-input-number
                :model-value="parseInt(sendRestriction.split('-')[0]) || undefined"
                placeholder="请输入"
                :controls="false"
                :precision="0"
                :min="1"
                :max="10"
                :step="1"
                clearable
                style="width: 70px;"
                :disabled="props.readonly"
                @change="(v: string) => updateDialingRestriction(v, sendRestriction.split('-')[1], index)"
              />
              <span>次</span>
              <el-input-number
                :model-value="parseInt(sendRestriction.split('-')[1]) || undefined"
                placeholder="请输入"
                :controls="false"
                :precision="0"
                :min="1"
                :max="128"
                :step="1"
                clearable
                style="width: 70px;"
                :disabled="props.readonly"
                @change="(v: string) => updateDialingRestriction(sendRestriction.split('-')[0], v, index)"
              />
              <span>小时</span>
              <el-button
                v-show="!props.readonly"
                type="primary"
                link
                :icon="CloseBold"
                @click="delRestriction(2, sendRestriction)"
              />
            </div>
          </div>
        </el-form-item>
      </div>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        时间限制
      </div>
      <el-form-item label="时间限制：" prop="disableTimeSlots">
        <div v-show="!props.readonly" class="tw-min-w-[100px]">
          <el-button size="small" @click="onClickTimeSlot">
            选择时间段
          </el-button>
        </div>
      </el-form-item>
      <br>
      <el-form-item label="选中时间段：">
        <TagsBox :tagsArr="timeSlotStrList" tagsName="选中时间段" />
      </el-form-item>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        账号计费
      </div>
      <div class="tw-grid tw-grid-cols-2 tw-gap-0">
        <el-form-item label="计费方式：" prop="billingCycle">
          <div class="tw-flex tw-flex-row tw-justify-start tw-items-center tw-w-full">
            <el-input-number
              v-model.number="form.billingCycle"
              placeholder="请输入计费方式"
              :precision="0"
              :min="1"
              :step="1"
              disabled
              style="width: 100%;"
            />
            <span class="tw-flex-none tw-ml-[8px]">字/条</span>
          </div>
        </el-form-item>
        <el-form-item label="短信单价：" prop="unitPrice">
          <div class="tw-flex tw-flex-row tw-justify-start tw-items-center tw-w-full">
            <el-input-number
              v-model.number="form.unitPrice"
              placeholder="请输入短信单价"
              :precision="3"
              :min="0"
              :max="2.000"
              :step="0.001"
              style="width: 100%;"
              :disabled="props.readonly"
            />
            <span class="tw-flex-none tw-ml-[8px]">元</span>
          </div>
        </el-form-item>
      </div>
    </div>

    <div class="form-section">
      <div class="form-section-header">
        支持发送范围
      </div>
      <div class="tw-grid tw-grid-cols-2 tw-gap-0">
        <el-form-item label="支持运营商：">
          <el-select
            v-model="form.serviceProvider"
            placeholder="请选择支持运营商"
            class="tw-w-full"
            :disabled="props.readonly"
            @change="onServiceProviderChange"
          >
            <el-option
              v-for="SmsAccountServiceProviderItem in Object.entries(SmsAccountServiceProviderEnum)"
              :key="SmsAccountServiceProviderItem[1]"
              :value="SmsAccountServiceProviderItem[1]"
              :label="SmsAccountServiceProviderItem[0]"
            />
          </el-select>
        </el-form-item>
        <br>
        <el-form-item label="支持省市：" prop="cityCodes">
        </el-form-item>
        <br>
        <div class="tw-col-span-2">
          <CitySettingBox
            ref="restrictComponentRef"
            :taskRestrictData="taskRestrictData"
            :selectedOperatorList="selectedOperatorList"
            :allOperatorList="selectedOperatorList"
            :readonly="props.readonly"
            loadByCity
            @update:data="onCityCodesUpdate"
          />
        </div>
      </div>
    </div>
  </el-form>

  <!--时间限制弹窗-->
  <TimeRangePickerDialog
    v-model:visible="timeRangePickerDialogVisible"
    format="HH:mm"
    defaultStart="00:00"
    defaultEnd="24:00"
    :edit-data="{startWorkTimeList, endWorkTimeList}"
    @confirm="onTimeRangerPickerConfirm"
  />
</template>

<script setup lang="ts">
import {
  computed,
  defineAsyncComponent,
  nextTick,
  onActivated,
  onDeactivated,
  onMounted,
  onUnmounted,
  reactive,
  ref
} from 'vue'
import { CloseBold, Plus } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { ElMessage, FormRules } from 'element-plus'
import { accountDetailFormRules } from './FormRules'
import {
  SmsAccountBusinessTypeEnum,
  SmsAccountItem,
  SmsAccountServiceProviderEnum,
  SmsAccountStatusEnum,
  SmsCmppProtocolVersionEnum,
  SmsCmppProtocolVersionList,
  SmsProtocolEnum
} from '@/type/sms'
import { useSmsProviderStore } from '@/store/sms-provider'
import { useUserStore } from '@/store/user'
import SvgIcon from '@/components/SvgIcon.vue'
import { MerchantAccountInfo } from '@/type/merchant'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import {
  convertTimeSlotApiToComponent,
  convertTimeSlotComponentToApi,
  convertTimeSlotComponentToDisplay,
  deduplicateBaseArray
} from '@/utils/utils'
import { OperatorEnum, RestrictModal } from '@/type/common'

// 动态引入组件
const TimeRangePickerDialog = defineAsyncComponent(() => import('@/components/TimeRangePickerDialog.vue'))
const TagsBox = defineAsyncComponent(() => import('@/components/TagsBox.vue'))
const CitySettingBox = defineAsyncComponent(() => import('@/components/CitySettingBox.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  readonly: boolean,
}>(), {
  readonly: false,
})

const globalStore = useGlobalStore()
const { allIndustryList } = storeToRefs(globalStore)
const userStore = useUserStore()
const smsProviderStore = useSmsProviderStore()
const { editingSmsAccount } = storeToRefs(smsProviderStore)

// 编辑模式 true 编辑 false 新增
const isEdit = computed(() => {
  return typeof form.id === 'number'
})

// 默认开始时间
const defaultStartTime = '00:00'
// 默认结束时间
const defaultEndTime = '24:00'

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): SmsAccountItem => ({
  supplierId: smsProviderStore.currentProvider.id,
  supplierNumber: smsProviderStore.currentProvider.supplierNumber,
  supplierName: smsProviderStore.currentProvider.supplierName,
  supplierProfile: smsProviderStore.currentProvider.supplierProfile,

  id: undefined,
  groupId: userStore.groupId,
  account: undefined, // 所属商户（主账号）

  smsAccountNumber: '', // 账号编号
  smsAccountName: '', // 账号名称
  enableStatus: SmsAccountStatusEnum['启用'], // 启用状态
  pending: false, // 挂起状态
  notes: '', // 运营备注

  smsProtocol: SmsProtocolEnum.CMPP, // 对接协议
  version: undefined, // 协议版本
  connectAddress: '', // 对接地址
  connectPort: undefined, // 对接端口
  extensionCode: undefined, // 扩展码
  connectAccount: '', // 账号
  password: '', // 密码
  srcId: '', // 请输入接入码
  serviceId: '', // 服务ID
  maxChannels: 1, // 最大连接数
  isReturn: true, // 是否回执
  returnTimeout: undefined, // 回执超时时间
  sendDelay: 0, // 发送延迟
  configInfo: '', // 配置信息

  singleSubmitLimit: undefined, // 单次提交上限
  singleDaySubmitLimit: undefined, // 单日提交上限
  secondIndustries: undefined, // 适用行业
  smsAccountBusinessType: SmsAccountBusinessTypeEnum['群发'], // 适用业务
  submitSpeedLimit: undefined, // 提交速率限制

  submitRestrictions: [], // 提交限制
  sendRestrictions: [], // 发送限制

  disableTimeSlots: convertTimeSlotComponentToApi([defaultStartTime], [defaultEndTime]), // 时间限制

  billingCycle: 70, // 计费周期
  unitPrice: undefined, // 短信单价

  serviceProvider: SmsAccountServiceProviderEnum['中国移动'], // 支持运营商
  cityCodes: [], // 支持省市
})
// 表单数据
const form: SmsAccountItem = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive(accountDetailFormRules)

/**
 * 重置表单
 */
const resetForm = () => {
  // console.log('resetForm')
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  editingSmsAccount.value = {}
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
  Object.assign(taskRestrictData, new RestrictModalOrigin())
}
/**
 * 更新表单
 */
const updateForm = () => {
  // console.log('updateForm')
  // 读取缓存
  Object.assign(form, JSON.parse(JSON.stringify(editingSmsAccount.value)))
  // 如果是只读模式
  if (props.readonly) {
    return
  }
  // 如果是编辑模式
  // 预先设置好短信供应商的相关信息
  form.supplierId = smsProviderStore.currentProvider.id
  form.supplierNumber = smsProviderStore.currentProvider.supplierNumber
  form.supplierName = smsProviderStore.currentProvider.supplierName
  form.supplierProfile = smsProviderStore.currentProvider.supplierProfile
  // 固定的表单值，不允许修改
  form.billingCycle = 70
  // 新建时，继承短信供应商的信息
  if (!isEdit.value) {
    form.smsProtocol = smsProviderStore.currentProvider.smsProtocol
    form.version = smsProviderStore.currentProvider.smsProtocol === SmsProtocolEnum.CMPP
      ? SmsCmppProtocolVersionEnum['2.0']
      : null
    form.configInfo = smsProviderStore.currentProvider.configInfo
  }
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 账号归属 开始 ----------------------------------------

// 商户主账号列表
const masterAccountList = ref<MerchantAccountInfo[]>([])

/**
 * 获取商户主账号列表
 */
const getMasterAccountList = async () => {
  // console.log('获取商户主账号列表')
  try {
    const res = await monitorStatisticModel.getAllMainAccount()
    masterAccountList.value = res?.length ? res : []
  } catch (e) {
    ElMessage.error('无法获取商户主账号列表')
  } finally {
  }
}
/**
 * 所属商户主账号 选择框显显示隐藏变化
 * @param visible 是否显示
 */
const onMasterAccountVisibleChange = (visible: boolean) => {
  if (!visible) {
    // 选择框隐藏后
    // 更新表单
    const masterAccount: MerchantAccountInfo | undefined = masterAccountList.value.find((item: MerchantAccountInfo) => item.account === form.account)
    if (masterAccount) {
      form.groupId = masterAccount.groupId
      form.account = masterAccount.account
    }
  }
}

// ---------------------------------------- 账号归属 结束 ----------------------------------------

// ---------------------------------------- 对接信息 开始 ----------------------------------------

// 对接协议版本列表
const protocolVersionList = ref<(string | SmsCmppProtocolVersionEnum)[][]>([])

/**
 * 对接协议 选择框变化
 */
const onSmsProtocolChange = () => {
  form.version = null
  updateProtocolVersionList()
}
/**
 * 更新对接协议版本列表
 */
const updateProtocolVersionList = () => {
  // console.log('更新对接协议版本列表')
  if (form.smsProtocol === SmsProtocolEnum.CMPP) {
    protocolVersionList.value = SmsCmppProtocolVersionList
    form.version = SmsCmppProtocolVersionEnum['2.0']
  } else {
    protocolVersionList.value = []
    form.version = null
  }
}
/**
 * 接入码 输入框变化
 */
const onScrIdInput = () => {
  // 移除非数字字符
  if (typeof form.srcId === 'string') {
    form.srcId = form.srcId.replace(/\D/g, '')
  }
}
/**
 * 服务ID 输入框变化
 */
const onServiceIdInput = () => {
  // 移除非数字字符
  if (typeof form.serviceId === 'string') {
    form.serviceId = form.serviceId.replace(/\D/g, '')
  }
}

// ---------------------------------------- 对接信息 结束 ----------------------------------------

// ---------------------------------------- 限制信息 开始 ----------------------------------------

// 适用行业，级联选择器，数据内容
const industryOptions = ref<{
  label: string,
  value: string,
  children: {
    label: string,
    value: string
  }[]
}[]>([])
// 适用行业，级联选择器，配置信息
const industryProps = { multiple: true, emitPath: false }

/**
 * 更新全部二级行业列表
 */
const updateAllIndustryList = async () => {
  await globalStore.getAllIndustryList()
  industryOptions.value = allIndustryList.value.map((primaryItem) => {
    return {
      label: primaryItem?.primaryIndustry ?? '',
      value: primaryItem?.primaryIndustry ?? '',
      children: (primaryItem?.secondaryIndustries ?? []).map((secondItem) => {
        return {
          label: secondItem?.name ?? '',
          value: secondItem?.name ?? '',
        }
      })
    }
  })
}

// ---------------------------------------- 限制信息 结束 ----------------------------------------

// ---------------------------------------- 频率限制 开始 ----------------------------------------

// 提交限制DOM
const submitRestrictionsRef = ref()
// 发送限制DOM
const sendRestrictionsRef = ref()

/**
 * 频率限制新增规则
 * type: 1: 提交限制; 2: 发送限制
 */
const addRestriction = (type: 1 | 2) => {
  let data: string[] | undefined = []
  if (type === 1) {
    data = form.submitRestrictions
  } else {
    data = form.sendRestrictions
  }
  if (!data) {
    data = []
  } else {
    let msg = ''
    data.map(item => {
      const arr = item.split('-')
      if (!arr[0] || !arr[1]) {
        msg = '请确保上一条频率限制规则填写完整'
      }
    })
    if (msg) {
      return ElMessage({
        type: 'warning',
        message: msg
      })
    }
    data.push('-')
  }
}
/**
 * 频率限制删除规则
 * @param {number} type 1: 提交限制; 2: 发送限制
 * @param {string} row 要删除的项
 */
const delRestriction = async (type: 1 | 2, row: string) => {
  if (type === 1) {
    form.submitRestrictions = form.submitRestrictions?.filter(item => item !== row) || []
    // 更新表单校验结果
    await nextTick()
    submitRestrictionsRef.value?.validate()
  }
  if (type === 2) {
    form.sendRestrictions = form.sendRestrictions?.filter(item => item !== row) || []
    // 更新表单校验结果
    await nextTick()
    sendRestrictionsRef.value?.validate()
  }
}
/**
 * 更新提交限制
 * @param val1 次数
 * @param val2 小时
 * @param index 更新对应的提交限制，转换为val1-val2
 */
const updateCallingRestriction = async (val1: string, val2: string, index: number) => {
  if (form.submitRestrictions) {
    form.submitRestrictions[index] = `${val1 || ''}-${val2 || ''}`
  }
  // 更新表单校验结果
  await nextTick()
  submitRestrictionsRef.value?.validate()
}
/**
 * 更新发送限制
 * @param val1 次数
 * @param val2 小时
 * @param index 更新对应的发送限制，转换为val1-val2
 */
const updateDialingRestriction = async (val1: string, val2: string, index: number) => {
  if (form.sendRestrictions) {
    form.sendRestrictions[index] = `${val1 || ''}-${val2 || ''}`
  }
  // 更新表单校验结果
  await nextTick()
  sendRestrictionsRef.value?.validate()
}

// ---------------------------------------- 频率限制 结束 ----------------------------------------

// ---------------------------------------- 时间限制 开始 ----------------------------------------

// 显示时间限制弹窗
const timeRangePickerDialogVisible = ref(false)
// 允许时间段开始位置 组件数据
const startWorkTimeList = ref<string[]>([defaultStartTime])
// 禁止时间段开始位置 组件数据
const endWorkTimeList = ref<string[]>([defaultEndTime])
// 允许时间段的文本 页面展示
const timeSlotStrList = ref<string[]>([])

/**
 * 点击选择时间段按钮
 */
const onClickTimeSlot = () => {
  timeRangePickerDialogVisible.value = true
}
/**
 * 选中时间段 接口数据转换成组件数据
 */
const timeListApiToComponent = () => {
  const result = convertTimeSlotApiToComponent(form.disableTimeSlots ?? [])
  startWorkTimeList.value = result.startWorkTimeList
  endWorkTimeList.value = result.endWorkTimeList
}
/**
 * 选中时间段 组件数据转换成接口数据
 */
const timeListComponentToApi = () => {
  form.disableTimeSlots = convertTimeSlotComponentToApi(startWorkTimeList.value, endWorkTimeList.value)
}
/**
 * 选中时间段 组件数据转换成展示文本
 */
const timeListComponentToDisplay = () => {
  timeSlotStrList.value = convertTimeSlotComponentToDisplay(startWorkTimeList.value, endWorkTimeList.value)
}
/**
 * 时间限制弹窗更新
 * @param data 禁止时间段 组件数据
 */
const onTimeRangerPickerConfirm = (data: {
  startWorkTimeList: string[],
  endWorkTimeList: string[],
}) => {
  startWorkTimeList.value = data.startWorkTimeList
  endWorkTimeList.value = data.endWorkTimeList
  timeListComponentToApi()
  timeListComponentToDisplay()
}

// ---------------------------------------- 时间限制 结束 ----------------------------------------

// ---------------------------------------- 支持发送范围 开始 ----------------------------------------

const serviceProviderToOperatorMap = new Map([
  [SmsAccountServiceProviderEnum['中国移动'], OperatorEnum['移动']],
  [SmsAccountServiceProviderEnum['中国联通'], OperatorEnum['联通']],
  [SmsAccountServiceProviderEnum['中国电信'], OperatorEnum['电信']],
  [SmsAccountServiceProviderEnum['未知'], OperatorEnum['未知']],
])
const OperatorToServiceProviderMap = new Map([
  [OperatorEnum['移动'], SmsAccountServiceProviderEnum['中国移动']],
  [OperatorEnum['联通'], SmsAccountServiceProviderEnum['中国联通']],
  [OperatorEnum['电信'], SmsAccountServiceProviderEnum['中国电信']],
  [OperatorEnum['未知'], SmsAccountServiceProviderEnum['未知']],
])

// 支持省市 组件数据 类
class RestrictModalOrigin {
  allRestrictProvince = null
  allRestrictCity = null
  ydRestrictProvince = null
  ydRestrictCity = null
  ltRestrictProvince = null
  ltRestrictCity = null
  dxRestrictProvince = null
  dxRestrictCity = null
  unknownRestrictProvince = null
  unknownRestrictCity = null
  virtualRestrictProvince = null
  virtualRestrictCity = null
}

// 支持省市 组件引用
const restrictComponentRef = ref()
// 支持省市 组件数据 已选运营商列表
const selectedOperatorList = ref<OperatorEnum[]>([OperatorEnum['移动']])
// 支持省市 组件数据 已选中省市
const taskRestrictData = reactive<RestrictModal>(new RestrictModalOrigin())

/**
 * 支持运营商 接口数据转换成组件数据
 */
const serviceProviderApiToComponent = () => {
  selectedOperatorList.value = [
    serviceProviderToOperatorMap.get(<SmsAccountServiceProviderEnum>form.serviceProvider)
    ?? OperatorEnum['移动']
  ]
}
/**
 * 支持运营商 组件数据转换成接口数据
 * @deprecated
 */
const serviceProviderComponentToApi = () => {
  form.serviceProvider = OperatorToServiceProviderMap.get(<OperatorEnum>selectedOperatorList.value.at(0))
    ?? SmsAccountServiceProviderEnum['中国移动']
}
/**
 * 支持运营商 选择框变化
 */
const onServiceProviderChange = () => {
  // 将新的运营商传给组件
  serviceProviderApiToComponent()
  // 切换组件当前激活的运营商标签卡
  if (restrictComponentRef.value?.activeOperator) {
    restrictComponentRef.value.activeOperator = selectedOperatorList.value.at(0) ?? OperatorEnum['移动']
  }

  // 如果运营商不是未知：组件的省市数据先清空，再重新赋值新的供应商的；接口的省市数据保持不动
  // 如果运营商是未知：组件的省市数据先清空，再重新赋值新的供应商的；接口和组件的省市数据全选
  Object.assign(taskRestrictData, new RestrictModalOrigin())
  if (form.serviceProvider !== SmsAccountServiceProviderEnum['未知']) {
    // 不是未知运营商
    cityCodesApiToComponent()
  } else {
    // 是未知运营商
    restrictComponentRef.value?.selectAllData()
  }
}
/**
 * 支持省市 接口数据转换成组件数据
 */
const cityCodesApiToComponent = () => {
  // 市行政代码 列表
  const cityList = form.cityCodes?.length ? form.cityCodes : []
  // 市行政代码 字符串拼接
  const cityStr = cityList.join(',')

  // 省行政代码 列表
  // 从市行政代码里提取前2位，后面4位补0
  // 因为同省的市可能有多个，所以需要去重
  const provinceList = deduplicateBaseArray(cityList.map((cityCode: string) => {
    return cityCode.slice(0, 2) + '0000'
  }))
  // 省行政代码 字符串拼接
  const provinceStr = provinceList.join(',')

  switch (form.serviceProvider) {
    case SmsAccountServiceProviderEnum['中国移动']:
      taskRestrictData.ydRestrictCity = cityStr
      taskRestrictData.ydRestrictProvince = provinceStr
      break;
    case SmsAccountServiceProviderEnum['中国联通']:
      taskRestrictData.ltRestrictCity = cityStr
      taskRestrictData.ltRestrictProvince = provinceStr
      break;
    case SmsAccountServiceProviderEnum['中国电信']:
      taskRestrictData.dxRestrictCity = cityStr
      taskRestrictData.dxRestrictProvince = provinceStr
      break;
    case SmsAccountServiceProviderEnum['未知']:
      taskRestrictData.unknownRestrictCity = cityStr
      taskRestrictData.unknownRestrictProvince = provinceStr
      break;
    default:
      taskRestrictData.ydRestrictCity = cityStr
      taskRestrictData.ydRestrictProvince = provinceStr
      break;
  }
}
/**
 * 支持省市 组件数据转换成接口数据
 */
const cityCodesComponentToApi = async (str: string | null | undefined) => {
  form.cityCodes = !str?.length ? [] : str?.split(',')
}
/**
 * 支持省市 组件更新
 */
const onCityCodesUpdate = () => {
  let str: string | null | undefined
  switch (form.serviceProvider) {
    case SmsAccountServiceProviderEnum['中国移动']:
      str = taskRestrictData.ydRestrictCity
      break;
    case SmsAccountServiceProviderEnum['中国联通']:
      str = taskRestrictData.ltRestrictCity
      break;
    case SmsAccountServiceProviderEnum['中国电信']:
      str = taskRestrictData.dxRestrictCity
      break;
    case SmsAccountServiceProviderEnum['未知']:
      str = taskRestrictData.unknownRestrictCity
      break;
    default:
      str = taskRestrictData.ydRestrictCity
      break;
  }
  cityCodesComponentToApi(str)
}

// ---------------------------------------- 外呼范围 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

const update = () => {
  // console.log('update')
  // console.log('editingSmsAccount.value?.id', editingSmsAccount.value?.id, typeof editingSmsAccount.value?.id)
  // console.log('props.readonly', props.readonly)
  // 更新表单
  updateForm()

  // 更新其他
  // 更新行业列表
  updateAllIndustryList()
  // 呼叫时间段限制
  timeListApiToComponent()
  timeListComponentToDisplay()
  // 支持运营商
  serviceProviderApiToComponent()
  // 支持省市
  cityCodesApiToComponent()

  // 允许编辑
  if (!props.readonly) {
    // 获取商户主账号列表
    getMasterAccountList()
    // 更新对接协议版本列表
    updateProtocolVersionList()
  }
}
const reset = () => {
  // 重置表单
  resetForm()
}
onMounted(() => {
  // console.log('onMounted', 'Form')
  update()
})
onActivated(() => {
  // console.log('onActivated', 'Form')
})
onDeactivated(() => {
  // console.log('onDeactivated', 'Form')
})
onUnmounted(() => {
  // console.log('onUnmounted', 'Form')
  reset()
})
defineExpose({
  formRef,
  form,
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.el-form :deep(.el-form-item__content) {
  font-size: 13px;
}
</style>
