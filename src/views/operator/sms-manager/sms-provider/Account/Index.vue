<template>
  <!--顶部-->
  <div class="tw-p-[16px] tw-bg-white">
    <!--搜索表单-->
    <el-form class="search-form tw-grid tw-grid-cols-5 tw-gap-[8px] " label-position="top">
      <el-form-item label="账号名称">
        <el-input
          v-model.trim="searchForm.smsAccountName"
          placeholder="请输入账号名称"
          clearable
          class="tw-w-full"
        />
      </el-form-item>
      <el-form-item label="账号编号">
        <el-input
          v-model.trim="searchForm.smsAccountNumber"
          placeholder="请输入账号编号"
          clearable
          class="tw-w-full"
        />
      </el-form-item>
      <el-form-item label="启用状态">
        <el-select
          v-model="searchForm.enableStatus"
          placeholder="全部"
          clearable
          class="tw-w-full"
        >
          <el-option
            v-for="SmsAccountStatusItem in Object.entries(SmsAccountStatusEnum)"
            :key="SmsAccountStatusItem[1]"
            :label="SmsAccountStatusItem[0]"
            :value="SmsAccountStatusItem[1]"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="适用行业">
        <el-cascader
          v-model="searchForm.secondIndustries![0]"
          placeholder="请选择适用行业"
          clearable
          :options="industryOptions"
          :props="industryProps"
          :show-all-levels="false"
          collapse-tags
          collapse-tags-tooltip
          :max-collapse-tags="2"
          class="tw-w-full"
        />
      </el-form-item>
      <!-- <el-form-item label="所属商户">
        <el-select
          v-model="searchForm.account"
          placeholder="请选择所属商户主账号"
          clearable
          filterable
          class="tw-w-full"
        >
          <el-option
            v-for="masterAccountItem in masterAccountList"
            :key="masterAccountItem.groupId"
            :label="masterAccountItem.account"
            :value="masterAccountItem.account"
          />
        </el-select>
      </el-form-item> -->
    </el-form>

    <!--分割线-->
    <hr class="tw-my-[12px]">

    <!--搜索按钮组-->
    <div class="tw-flex tw-justify-end">
      <el-button type="primary" link @click="onClickReset">
        <el-icon size="--el-font-size-base">
          <SvgIcon name="reset" color="var(--el-color-primary)" />
        </el-icon>
        <span>重置</span>
      </el-button>
      <el-button type="primary" link @click="onClickSearch">
        <el-icon size="--el-font-size-base">
          <SvgIcon name="search" color="var(--el-color-primary)" />
        </el-icon>
        <span>查询</span>
      </el-button>
    </div>

    <!--新增按钮-->
    <div class="tw-mt-[6px] tw-ml-auto tw-text-right">
      <el-dropdown placement="top" trigger="click">
        <el-button type="primary" :icon="Plus">
          新增短信对接账号
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="onClickAdd">新建账号</el-dropdown-item>
            <el-dropdown-item @click="onClickCopy">复制账号</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>

  <!--表格-->
  <el-table
    v-loading="loadingList"
    :data="currentList"
    :header-cell-style="tableHeaderStyle"
    border
  >
    <el-table-column align="left" fixed="left" prop="smsAccountName" label="账号名称" min-width="150" show-overflow-tooltip />
    <el-table-column align="left" fixed="left" prop="smsAccountNumber" label="账号编号" min-width="160" show-overflow-tooltip />
    <!-- <el-table-column align="left" prop="account" label="所属商户" min-width="120" show-overflow-tooltip /> -->
    <el-table-column align="left" prop="tenantSmsTemplates" label="所属短信模板" min-width="110" show-overflow-tooltip>
      <template #default="{row}:{row:SmsAccountItem}">
        <el-button link type="primary" @click="onClickRelateSmsTemplate(row)">
          {{ formatSmsTemplate(row) }}
        </el-button>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="enableStatus" label="启用状态" min-width="80" show-overflow-tooltip>
      <template #default="{row}:{row:SmsAccountItem}">
        <span v-if="row.enableStatus===SmsAccountStatusEnum['启用']" class="status-box green-status tw-text-center">启用</span>
        <span v-else-if="row.enableStatus===SmsAccountStatusEnum['停用']" class="status-box orange-status tw-text-center">停用</span>
        <span v-else class="status-box gray-status tw-text-center">{{ row.enableStatus }}</span>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="secondIndustries" label="适用行业" min-width="100" show-overflow-tooltip />
    <el-table-column align="left" prop="smsAccountBusinessType" label="适用业务" min-width="100" show-overflow-tooltip>
      <template #default="{row}:{row:SmsAccountItem}">
        <span v-if="row.smsAccountBusinessType===SmsAccountBusinessTypeEnum['群发']">群发</span>
        <span v-else-if="row.smsAccountBusinessType===SmsAccountBusinessTypeEnum['挂短']">挂短</span>
        <span v-else>{{ row.smsAccountBusinessType }}</span>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="disableTimeSlots" label="时间限制" min-width="130" show-overflow-tooltip :formatter="formatTimeSlots" />
    <el-table-column align="left" prop="submitRestrictions" label="提交限制" min-width="130" show-overflow-tooltip :formatter="formatRestrictions" />
    <el-table-column align="left" prop="sendRestrictions" label="发送限制" min-width="130" show-overflow-tooltip :formatter="formatRestrictions" />
    <el-table-column align="left" prop="returnTimeout" label="回执超时时间" min-width="110" show-overflow-tooltip />
    <el-table-column align="left" prop="sendDelay" label="发送延迟" min-width="100" show-overflow-tooltip />
    <el-table-column align="left" prop="singleSubmitLimit" label="单次提交上限" min-width="110" show-overflow-tooltip />
    <el-table-column align="left" prop="singleDaySubmitLimit" label="单日提交上限" min-width="110" show-overflow-tooltip />
    <el-table-column align="left" prop="scope" label="支持范围" min-width="160" show-overflow-tooltip>
      <template #default="{row}:{row:SmsAccountItem}">
        <div class="tw-cursor-pointer tw-truncate tw-select-none tw-text-[--el-color-primary]" @click="onClickScope(row)">
          {{ formatScope(row) }}
        </div>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="billingCycle" label="计费周期" min-width="100" show-overflow-tooltip>
      <template #default="{row}:{row:SmsAccountItem}">
        {{ row.billingCycle ? row.billingCycle + '字' : '-' }}
      </template>
    </el-table-column>
    <el-table-column align="left" prop="unitPrice" label="单价（元）" min-width="100" show-overflow-tooltip>
      <template #default="{row}:{row:SmsAccountItem}">
        {{ typeof row.unitPrice === 'number' ? row.unitPrice + '元/条' : '-' }}
      </template>
    </el-table-column>
    <el-table-column align="center" prop="pending" label="挂起状态" fixed="right" min-width="80" show-overflow-tooltip>
      <template #default="{row}:{row:SmsAccountItem}">
        <template v-if="typeof row.pending === 'boolean'">
          <el-switch
            v-model="row.pending"
            inline-prompt
            active-text="已挂起"
            inactive-text="未挂起"
            :active-value="true"
            :inactive-value="false"
            @click="onSwitchAccountStatus(row)"
          />
        </template>
        <template v-else>
          -
        </template>
      </template>
    </el-table-column>
    <el-table-column align="right" fixed="right" label="操作" width="130">
      <template #default="{row}:{row:SmsAccountItem}">
        <el-button type="primary" link @click="onClickEditDetail(row)">
          编辑
        </el-button>
        <el-button type="primary" link @click="onClickViewDetail(row)">
          详情
        </el-button>
        <el-button type="primary" link @click="onClickEarlyWarning(row)">
          预警
        </el-button>
      </template>
    </el-table-column>
  </el-table>

  <!--分页条-->
  <PaginationBox
    :currentPage="pageNum"
    :pageSize="pageSize"
    :pageSizeList="pageSizeList"
    :total="total"
    @search="updateAllList"
    @update="updateCurrentList"
  />

  <!--查看详情-->
  <ViewDetail v-model:visible="smsAccountViewDetailVisible" />

  <!--短信对接账号复制弹窗-->
  <CopyDialog v-model:visible="copyDialogVisible" />

  <!--关联短信模板弹窗-->
  <RelateSmsTemplateDialog
    v-model:visible="relateSmsTemplateDialogVisible"
    :data="relateSmsTemplateDialogData"
  />

  <!--支持范围弹窗-->
  <ScopeDialog
    v-model:visible="scopeDialogVisible"
    :list="scopeDialogList"
    :operator="scopeDialogOperator"
  />

  <!-- 预警弹窗 -->
  <AccountWarnDialog
    v-model:visible="warnDialogVisible"
    :accountNumber="warnDialogAccount"
  />
</template>

<script setup lang="ts">
import { tableHeaderStyle } from '@/assets/js/constant'
import {
  convertTimeSlotApiToComponent,
  convertTimeSlotComponentToDisplay,
  Throttle,
  updateCurrentPageList
} from '@/utils/utils'
import PaginationBox from '@/components/PaginationBox.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import { computed, nextTick, reactive, ref, watch } from 'vue'
import to from 'await-to-js'
import { CascaderProps, ElMessage } from 'element-plus'
import { useSmsProviderStore } from '@/store/sms-provider'
import {
  SmsAccountBusinessTypeEnum,
  SmsAccountItem,
  SmsAccountPendingParams,
  SmsAccountServiceProviderEnum,
  SmsAccountStatusEnum,
  SmsProviderAccountParams,
  SmsProviderAccountSearch,
  SmsProviderItem
} from '@/type/sms'
import { smsAccountModel } from '@/api/sms'
import { storeToRefs } from 'pinia'
import { Plus } from '@element-plus/icons-vue'
import CopyDialog from './CopyDialog.vue'
import RelateSmsTemplateDialog from './RelateSmsTemplateDialog.vue'
import { useGlobalStore } from '@/store/globalInfo'
import { MerchantAccountInfo, SmsTemplateItem } from '@/type/merchant'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import router from '@/router'
import ScopeDialog from './ScopeDialog.vue'
import AccountWarnDialog from './AccountWarnDialog.vue'
import ViewDetail from './Detail/ViewDetail.vue'
import Confirm from '@/components/message-box'

// ---------------------------------------- 通用 开始 ----------------------------------------

const globalStore = useGlobalStore()
const smsProviderStore = useSmsProviderStore()
const { currentProvider, accountList } = storeToRefs(smsProviderStore)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 搜索 开始 ----------------------------------------

// 搜索条件 默认值
const searchFormDefault = () => ({
  smsAccountName: '',
  smsAccountNumber: '',
  enableStatus: undefined,
  secondIndustries: [],
  account: undefined,
})
// 搜索条件 表单值
const searchForm: SmsProviderAccountSearch = reactive(searchFormDefault())

// 搜索条件 行业 级联选择器 数据内容
const industryOptions = ref<any[]>([])
// 搜索条件 行业 级联选择器 配置信息
const industryProps: CascaderProps = {
  multiple: false,
  emitPath: false,
  value: 'name',
  label: 'name',
  children: 'secondaryIndustries',
}

// 商户主账号列表
const masterAccountList = ref<MerchantAccountInfo[]>([])

/**
 * 获取行业列表
 */
const getIndustryList = async () => {
  // console.log('获取行业列表')
  try {
    await globalStore.getAllIndustryList()
    industryOptions.value = globalStore.getIndustryOption
  } catch (e) {
    ElMessage.error('无法获取行业列表')
  } finally {
  }
}
/**
 * 获取商户主账号列表
 */
const getMasterAccountList = async () => {
  // console.log('获取商户主账号列表')
  try {
    const res = await monitorStatisticModel.getAllMainAccount()
    masterAccountList.value = res?.length ? res : []
  } catch (e) {
    ElMessage.error('无法获取商户主账号列表')
  } finally {
  }
}
/**
 * 搜索条，重置表单
 */
const resetSearchForm = () => {
  // 表单数据恢复默认值
  Object.assign(searchForm, searchFormDefault())
}
/**
 * 搜索条，点击重置按钮
 */
const onClickReset = () => {
  resetSearchForm()
}
/**
 * 搜索条，点击查询按钮
 */
const onClickSearch = () => {
  updateAllList()
}

// ---------------------------------------- 搜索 结束 ----------------------------------------

// ---------------------------------------- 按钮 开始 ----------------------------------------

/**
 * 点击新建按钮
 */
const onClickAdd = () => {
  // 缓存数据
  smsProviderStore.editingSmsAccount = {}
  // 切换路由
  router.push({ name: 'SmsAccountEditDetail' })
}
/**
 * 点击复制按钮
 */
const onClickCopy = () => {
  // 显示短信对接账号复制弹窗
  copyDialogVisible.value = true
}

// ---------------------------------------- 按钮 结束 ----------------------------------------

// ---------------------------------------- 表格 开始 ----------------------------------------

// 列表，正在加载
const loadingList = ref<boolean>(false)
// 列表，加载节流锁
const throttleList = new Throttle(loadingList)

// 列表，全部，接口数据
// 将列表放到sessionStorage中，方便父组件的短信供应商使用
// const allList = ref<SmsAccountItem[]>([])

// 列表，筛选，全部的子集
const filterList = ref<any[]>([])

// 列表，当前页，筛选的子集
const currentList = ref<SmsAccountItem[]>([])
// 列表，当前页，页码
const pageNum = ref(1)
// 列表，当前页，每页大小
const pageSize = ref(20)
// 列表，当前页，每页大小可选数值
const pageSizeList: number[] = [10, 20, 50, 100]

// 列表，表格展示总数
const total = computed(() => {
  return filterList.value.length ?? 0
})

/**
 * 更新全部列表
 */
const updateAllList = async () => {
  // 节流锁上锁
  if (throttleList.check()) {
    return
  }
  throttleList.lock()

  // 处理参数
  const params: SmsProviderAccountParams = {
    smsAccountName: searchForm.smsAccountName || undefined,
    smsAccountNumber: searchForm.smsAccountNumber || undefined,
    enableStatus: searchForm.enableStatus || undefined,
    secondIndustries: searchForm.secondIndustries?.at(0) ? searchForm.secondIndustries : undefined,
    account: searchForm.account || undefined,
    supplierId: smsProviderStore.currentProvider?.id ?? undefined,
  }

  // 请求接口
  const [err, res] = <[any, SmsAccountItem[]]>await to(smsAccountModel.findAccountList(params))

  // 返回失败结果
  if (err) {
    ElMessage({
      message: '无法获取账号列表',
      type: 'error',
    })
    accountList.value = []
    // 节流锁解锁
    throttleList.unlock()
    return
  }

  // 返回成功结果
  // 更新全部列表
  accountList.value = res?.length ? res : []
  // 更新筛选列表
  updateFilterList()
  // 更新当前页列表
  updateCurrentList(pageNum.value, pageSize.value)
  // 节流锁解锁
  throttleList.unlock()
}
/**
 * 更新筛选列表
 */
const updateFilterList = () => {
  filterList.value = accountList.value
}
/**
 * 更新当前页列表
 * @param {number} p 新页码
 * @param {number} s 新分页大小
 */
const updateCurrentList = (p?: number, s?: number) => {
  // 如果参数指定了页码或/和每页大小，则按参数更新列表；
  // 否则保持当前页码和大小不变直接更新。
  // 这里的判断条件相当于排除了undefined, null, 0等逻辑假值，
  // 另外，页码为0或者分页大小为0，本身就是逻辑错误的，不应该更新分页。
  if (p || s) {
    pageNum.value = p || 1
    pageSize.value = s || 20
  }
  // 更新当前页列表
  currentList.value = updateCurrentPageList(filterList.value, pageNum.value, pageSize.value)
}
/**
 * 点击关联短信模板
 * @param row 短信对接账号信息
 */
const onClickRelateSmsTemplate = (row: SmsAccountItem) => {
  if (!row?.tenantSmsTemplates?.length) {
    return ElMessage.warning('暂无关联短信模板')
  }
  // 显示关联短信模板弹窗
  relateSmsTemplateDialogData.value = row.tenantSmsTemplates
  relateSmsTemplateDialogVisible.value = true
}
/**
 * 点击表格行的编辑详情按钮
 * @param row 当前行数据，短信对接账号信息
 */
const onClickEditDetail = (row: SmsAccountItem) => {
  // 缓存数据
  smsProviderStore.editingSmsAccount = JSON.parse(JSON.stringify(row))
  // 切换路由
  router.push({ name: 'SmsAccountEditDetail' })
}
/**
 * 点击表格行的查看详情按钮
 * @param row 当前行数据，短信对接账号信息
 */
const onClickViewDetail = (row: SmsAccountItem) => {
  // 缓存数据
  smsProviderStore.editingSmsAccount = JSON.parse(JSON.stringify(row))
  // 显示查看详情抽屉
  smsAccountViewDetailVisible.value = true
}

/** 短信账户预警 开始 */
const warnDialogVisible = ref(false) // 是否显示预警弹窗
const warnDialogAccount = ref<string | null>(null) // 预警的账号
/**
 * 点击表格行的预警按钮
 * @param row 短信对接账号信息
 */
const onClickEarlyWarning = (row: SmsAccountItem) => {
  warnDialogAccount.value = row.smsAccountNumber || ''
  warnDialogVisible.value = true
}
/** 短信账户预警 结束 */

/**
 * 格式化时间限制
 * @param row 行数据
 * @param column 列数据
 * @param cellValue 单元格
 * @param index 行索引
 */
const formatTimeSlots = (row: SmsAccountItem, column: any, cellValue: number[], index: number) => {
  const data: {
    startWorkTimeList: string[],
    endWorkTimeList: string[]
  } = convertTimeSlotApiToComponent(cellValue)
  const list = convertTimeSlotComponentToDisplay(data.startWorkTimeList, data.endWorkTimeList)
  return list?.length ? list.join('、') : '-'
}
/**
 * 格式化频率限制
 * @param row 行数据
 * @param column 列数据
 * @param cellValue 单元格
 * @param index 行索引
 */
const formatRestrictions = (row: SmsAccountItem, column: any, cellValue: string[], index: number) => {
  if (!cellValue?.length) {
    return '-'
  }
  const list = cellValue.map((item: string) => {
    const arr = item.split('-')
    return arr.at(0) + '次/' + arr.at(1) + '时'
  })
  return list.join('、')
}
/**
 * 格式化支持范围
 * @param row 行数据
 */
const formatScope = (row: SmsAccountItem) => {
  // 去重记录选中的省份
  const provinceCodes = new Set<string>([])
  // 遍历城市列表
  const list = row.cityCodes?.length ? row.cityCodes : []
  list.forEach((item: string) => {
    // 城市前两位是省份
    provinceCodes.add(item.slice(0, 2) + '0000')
  })

  // 确定运营商
  const entity: [string, SmsAccountServiceProviderEnum] | undefined = Object.entries(SmsAccountServiceProviderEnum).find(([key, value]) => {
    return value === row.serviceProvider
  })
  const serviceProvider = entity?.at(0) ?? ''

  // 拼接整理结果并返回展示
  return (serviceProvider ? serviceProvider + ':' : '')
    + `${provinceCodes.size || 0}省${row.cityCodes?.length || 0}市`
}
/**
 * 点击支持范围
 * @param row 行数据
 */
const onClickScope = (row: SmsAccountItem) => {
  // 显示支持范围弹窗
  scopeDialogList.value = row.cityCodes?.length ? row.cityCodes : []
  scopeDialogOperator.value = row.serviceProvider ?? SmsAccountServiceProviderEnum['中国移动']
  scopeDialogVisible.value = true
}
/**
 * 格式化关联短信模板
 * @param row 行数据
 */
const formatSmsTemplate = (row: SmsAccountItem) => {
  if (row?.tenantSmsTemplates?.length) {
    const nameList = row.tenantSmsTemplates.map((item: SmsTemplateItem) => {
      return item?.templateName ?? ''
    })
    const nameSet = new Set(nameList)
    nameSet.delete('')
    return Array.from(nameSet).join('、') ?? '-'
  }
  return '-'
}
/**
 * 切换账号挂起状态
 * @param row 选中的列表行
 */
const onSwitchAccountStatus = (row: SmsAccountItem) => {
  if (typeof row.id !== 'number') {
    ElMessage({
      message: 'ID不正确',
      type: 'warning',
    })
    return
  }

  // 期望的挂起状态
  // 此处点击事件回调里，挂起状态已经变成了期望的挂起状态，所以直接使用
  const expectPending = row.pending ?? false
  const expectPendingStr = `${expectPending ? '' : '取消'}挂起`
  Confirm({
    text: `您确定要${expectPendingStr}【${row.smsAccountName ?? ''}】吗？`,
    type: 'warning',
    title: `${expectPendingStr}确认`,
    confirmText: `${expectPendingStr}`,
  }).then(async () => {
    try {
      // 处理参数
      const params: SmsAccountPendingParams = {
        smsAccountNumber: row.smsAccountNumber ?? undefined,
        status: expectPending,
      }
      // 请求接口
      await smsAccountModel.switchSmsAccountStatus(params)
      // 返回成功结果
      ElMessage({
        message: `【${row.smsAccountName ?? ''}】${expectPendingStr}成功`,
        type: 'success',
      })
    } catch (err) {
      ElMessage({
        message: `【${row.smsAccountName ?? ''}】通道${expectPendingStr}失败`,
        type: 'error',
      })
    }
  }).catch(() => {
    // 取消
  }).finally(() => {
    // 更新全部列表
    updateAllList()
  })
}

// ---------------------------------------- 表格 结束 ----------------------------------------

// ---------------------------------------- 短信对接账号复制弹窗 开始 ----------------------------------------

// 短信对接账号复制弹窗，是否显示
const copyDialogVisible = ref<boolean>(false)

// ---------------------------------------- 短信对接账号复制弹窗 结束 ----------------------------------------

// ---------------------------------------- 支持范围弹窗 开始 ----------------------------------------

// 支持范围弹窗 是否显示
const scopeDialogVisible = ref<boolean>(false)
// 支持范围弹窗 城市代码列表
const scopeDialogList = ref<string[]>([])
// 支持范围弹窗 当前运营商
const scopeDialogOperator = ref<SmsAccountServiceProviderEnum>(SmsAccountServiceProviderEnum['中国移动'])

// ---------------------------------------- 支持范围弹窗 结束 ----------------------------------------

// ---------------------------------------- 关联短信模板弹窗 开始 ----------------------------------------

// 关联短信模板弹窗，是否显示
const relateSmsTemplateDialogVisible = ref<boolean>(false)
// 关联短信模板弹窗，数据
const relateSmsTemplateDialogData = ref<SmsAccountItem[]>([])

// ---------------------------------------- 关联短信模板弹窗 结束 ----------------------------------------

// ---------------------------------------- 查看详情 开始 ----------------------------------------

// 查看详情，是否显示
const smsAccountViewDetailVisible = ref<boolean>(false)

// ---------------------------------------- 查看详情 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

getIndustryList()
getMasterAccountList()

watch(currentProvider, async (val: SmsProviderItem | null | undefined) => {
  // 选中供应商或供应商变化时，更新对应供应商的账号列表
  if (typeof val?.id === 'number') {
    await nextTick()
    resetSearchForm()
    await updateAllList()
  }
}, { deep: true })

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.search-form {
  .el-form-item {
    margin-bottom: 0;
  }
}
.el-table {
  font-size: 13px;
  :deep(.cell) {
    padding: 0 8px;
  }
}
</style>
