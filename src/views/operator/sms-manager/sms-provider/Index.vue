<template>
  <!--模块标题-->
  <HeaderBox title="短信供应商" />

  <!--模块主体-->
  <div v-loading="loadingProviderAllList" class="module-main module-main-scroll tw-min-w-[1080px]">

    <!--左半部分-->
    <div class="aside-list-box">
      <!--创建按钮-->
      <el-button
        class="tw-w-[226px] tw-mx-[16px] tw-box-border"
        size="default"
        type="primary"
        @click="onClickCreateProvider"
      >
        创建供应商
      </el-button>

      <!--搜索框-->
      <el-input
        v-model.trim="providerSearchVal"
        class="tw-mt-[16px] tw-mx-[16px] tw-mb-[12px]"
        style="width:226px"
        placeholder="搜索供应商"
        clearable
        @input="onProviderSearchInput"
        @clear="onProviderSearchClear"
      >
        <template #suffix>
          <el-icon :size="16">
            <SvgIcon name="search" />
          </el-icon>
        </template>
      </el-input>

      <!--状态标签卡-->
      <TabsBox
        class="provider-tab"
        :active="providerStatusTab"
        :tabList="providerStatusTabList"
        @update:active="onChangeTab"
      />

      <!--列表-->
      <el-scrollbar ref="providerListRef" class="tw-px-[16px]">
        <!--列表里的单项-->
        <div
          v-for="providerItem in providerCurrentList"
          :key="providerItem.id??Math.random()"
          ref="providerItemRef"
          class="aside-normal-item"
          :class="[{'aside-active-item':providerItem.id===currentProvider.id}]"
        >
          <!--内层容器-->
          <div class="aside-normal-item-inner" @click="onClickProviderItem(providerItem)">
            <!--行容器-->
            <div class="tw-flex tw-items-center">
              <!--编号-->
              <div class="tw-flex-1 tw-text-left tw-break-words tw-truncate tw-text-[var(--primary-black-color-400)]">
                {{ providerItem.supplierNumber }}
              </div>
              <!--联系人姓名-->
              <div class="tw-grow-0 tw-shrink-0 tw-w-[60px] tw-text-right tw-break-words tw-truncate tw-text-[var(--primary-black-color-500)]">
                {{ providerItem.contactName }}
              </div>
            </div>

            <!--供应商名称-->
            <div class="tw-text-left tw-text-[14px] tw-font-bold tw-break-words">
              {{ providerItem.supplierName }}
            </div>

            <!--日期时间-->
            <div class="tw-text-[13px] tw-break-words tw-text-[var(--primary-black-color-400)]">
              创建时间：{{ dayjs(providerItem.createTime).format('YYYY-MM-DD HH:mm:ss') }}
            </div>
          </div>

          <!--按钮-->
          <div class="aside-item-btn-box">
            <el-tooltip content="编辑供应商" placement="right" :show-after="500">
              <span class="tw-cursor-pointer" @click="onClickEditProvider(providerItem)">
                <el-icon :size="16" color="#fff">
                  <SvgIcon name="edit" color="#fff" />
                </el-icon>
              </span>
            </el-tooltip>
          </div>
        </div>
        <!--空数据提示-->
        <div v-show="!providerFilterList.length" class="tw-bg-white">
          <el-empty />
        </div>
      </el-scrollbar>

      <!--分页条-->
      <PaginationBox
        class="tw-border-t-[1px] tw-border-t-[#ebeef5]"
        :pageSize="providerPageSize"
        :currentPage="providerPageNum"
        :total="providerPageTotal"
        :mini="true"
        @search="updateProviderAllList(currentProvider)"
        @update="updateProviderCurrentList"
      />
    </div>

    <!--右半部分-->
    <el-scrollbar class="tw-grow" wrap-class="tw-p-[16px]">

      <!--无数据-->
      <div v-show="!providerFilterList.length" class="tw-bg-white tw-w-full">
        <el-empty />
      </div>

      <!--有数据-->
      <div v-show="providerFilterList.length" class="tw-flex tw-flex-col tw-w-full tw-h-full">

        <!--供应商信息模块-->
        <div v-loading="loadingProviderAllList">
          <!--标题和内容-->
          <div class="card-box tw-flex-col">
            <!--标题-->
            <div class="tw-flex tw-flex-row tw-items-center tw-w-full">
              <p class="tw-text-[14px] tw-font-bold tw-text-[#313233]">
                {{ currentProvider.supplierName }}
              </p>
              <p class="tw-ml-[8px] tw-text-[13px] tw-font-normal tw-text-[#626366]">
                简称：{{ currentProvider.supplierProfile }}
              </p>
              <p class="tw-ml-auto tw-text-[13px] tw-font-normal tw-text-[#626366]">
                <span class="info-title">编号：</span>
                <span class="info-content-dark">{{ currentProvider.supplierNumber }}</span>
              </p>
            </div>

            <!--基础信息-->
            <div class="tw-flex tw-flex-row tw-justify-start tw-flex-wrap tw-w-full tw-mt-[8px]">
              <p class="info-item">
                <span class="info-title">业务范围：</span>
                <span class="info-content-dark">
                  {{ businessScopeText }}
                </span>
              </p>
            </div>
          </div>
        </div>

        <!--标签卡-->
        <TabsBox v-model:active="moduleTab" :tabList="Object.values(moduleTabList)" class="module-tab tw-mt-[16px]" />

        <!--账号列表-->
        <Account v-if="moduleTab===ModuleTabEnum.ACCOUNT" />

      </div>

    </el-scrollbar>

  </div>

  <!--供应商弹窗-->
  <ProviderDialog
    v-model:visible="providerDialogVisible"
    :data="providerDialogData"
    :isEdit="providerDialogIsEdit"
    :allList="providerAllList"
    @confirm="onConfirmProviderDialog"
  />
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { computed, nextTick, onActivated, ref } from 'vue'
import {
  findValueInEnum,
  getListFirstItem,
  searchInList,
  searchModeEnum,
  Throttle,
  updateCurrentPageList
} from '@/utils/utils'
import { SmsAccountItem, SmsProviderItem, SmsProviderParams, SmsProviderStatusEnum } from '@/type/sms'
import HeaderBox from '@/components/HeaderBox.vue'
import TabsBox from '@/components/TabsBox.vue'
import PaginationBox from '@/components/PaginationBox.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import Account from './Account/Index.vue'
import ProviderDialog from './ProviderDialog.vue'
import { smsProviderModel } from '@/api/sms'
import { SupplierBlackGroupInfo } from '@/type/supplier'
import to from 'await-to-js'
import { ElMessage } from 'element-plus'
import { useSmsProviderStore } from '@/store/sms-provider'
import { storeToRefs } from 'pinia'

// ---------------------------------------- 通用 开始 ----------------------------------------

const smsProviderStore = useSmsProviderStore()
const { currentProvider, accountList } = storeToRefs(smsProviderStore)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 供应商列表 开始 ----------------------------------------

// 标签卡列表
const providerStatusTabList = ['启用', '停用']
// 当前选中标签卡
const providerStatusTab = ref(providerStatusTabList[0])

// 供应商列表DOM
const providerListRef = ref()
// 供应商单项DOM
const providerItemRef = ref()

// 供应商列表，全部
const providerAllList = ref<SmsProviderItem[]>([])
// 供应商列表，全部，正在加载
const loadingProviderAllList = ref<boolean>(false)
// 供应商列表，全部，加载节流锁
const throttleProviderAllList = new Throttle(loadingProviderAllList)

// 供应商列表，筛选
const providerFilterList = ref<SmsProviderItem[]>([])
// 供应商列表，总数
const providerPageTotal = computed(() => {
  return providerFilterList.value.length ?? 0
})
// 供应商搜索框文本
const providerSearchVal = ref<string>('')

// 供应商列表，当前页
const providerCurrentList = ref<SmsProviderItem[]>([])
// 供应商列表，当前页，页码
const providerPageNum = ref(1)
// 供应商列表，当前页，每页大小
const providerPageSize = ref(200)

/**
 * 更新全部供应商列表
 * @param {SmsProviderItem} current 当前选中信息
 */
const updateProviderAllList = async (current?: SmsProviderItem) => {
  // 节流锁上锁
  if (throttleProviderAllList.check()) {
    return
  }
  throttleProviderAllList.lock()

  // 处理参数
  const statusItem = Object.entries(SmsProviderStatusEnum).find(([key,]) => {
    return providerStatusTab.value === key
  })
  const params: SmsProviderParams = {
    status: <SmsProviderStatusEnum>(statusItem?.at(1) || SmsProviderStatusEnum['启用']),
  }
  // 如果有供应商信息，则切换到对应标签卡
  if (current && typeof current.id === 'number' && current.cooperationStatus) {
    params.status = current.cooperationStatus
    providerStatusTab.value = findValueInEnum(current.cooperationStatus, SmsProviderStatusEnum) ?? providerStatusTabList[0]
  }

  // 请求接口
  const [err, res] = await to(smsProviderModel.findProviderList(params))

  // 返回失败结果
  if (err) {
    ElMessage({
      message: '获取供应商列表失败',
      type: 'error',
    })
    // 节流锁解锁
    throttleProviderAllList.unlock()
    return
  }

  // 返回成功结果
  // 按创建时间倒序排序
  providerAllList.value = (res?.length ? res : []).sort((a: SmsProviderItem, b: SmsProviderItem) => {
    return dayjs(a.createTime).isAfter(dayjs(b.createTime)) ? -1 : 1
  })
  // 搜索筛选
  updateFilterList()
  // 更新当前页码
  updateProviderCurrentList(providerPageNum.value, providerPageSize.value)
  // 找到当前供应商在列表的位置
  await scrollProviderList(current)
  // 节流锁解锁
  throttleProviderAllList.unlock()
}
/**
 * 搜索供应商
 */
const updateFilterList = () => {
  providerFilterList.value = searchInList(providerAllList.value, 'supplierName', providerSearchVal.value, searchModeEnum.FUZZY)
}
/**
 * 供应商列表搜索框文本变化时
 */
const onProviderSearchInput = () => {
  // 搜索供应商
  updateFilterList()
  // 页码从1开始
  providerPageNum.value = 1
  // 更新当前页码列表
  updateProviderCurrentList()
}
/**
 * 供应商列表搜索框文本清空时
 */
const onProviderSearchClear = () => {
  onProviderSearchInput()
}
/**
 * 切换标签卡
 * @param index {string} 选中菜单项的索引名
 */
const onChangeTab = (index: SmsProviderStatusEnum) => {
  // 如果标签卡索引相同则不执行后续动作
  if (index === providerStatusTab.value) {
    return
  }
  // 如果节流锁生效，不响应
  if (throttleProviderAllList.check()) {
    return
  }
  // 切换标签卡
  providerStatusTab.value = index
  // 更新供应商全部列表
  updateProviderAllList()
}
/**
 * 供应商列表滚动到当前供应商的位置
 * @param {SmsProviderItem} current 当前选中供应商信息
 */
const scrollProviderList = async (current?: SmsProviderItem) => {
  // 尝试在当前列表找到选中的
  let itemIndex = -1
  if (typeof current?.id === 'number') {
    itemIndex = providerFilterList.value.findIndex((item: SmsProviderItem) => {
      return item.id === current?.id
    })
  }

  if (itemIndex === -1) {
    // 列表里找不到当前索引
    // 如果列表有数据，将列表第一个项目信息展示出来
    const resultItem = <SmsProviderItem>getListFirstItem(providerFilterList.value, {})
    currentProvider.value = JSON.parse(JSON.stringify(resultItem))
  } else {
    // 更新当前选中供应商信息
    currentProvider.value = providerFilterList.value[itemIndex]
  }

  // 等DOM更新后再继续操作，防止滚动列表的高度不正确
  await nextTick()
  // 滚动到当前选中的位置
  if (providerListRef.value) {
    const currentProviderDom = providerItemRef.value?.find((item: HTMLElement) => item.classList.contains('aside-active-item'))
    // 减去的数值是边距
    const currentProviderOffsetTop = Math.max((currentProviderDom?.offsetTop ?? 0) - 8, 0)
    providerListRef.value.setScrollTop(currentProviderOffsetTop)
  }
}
/**
 * 更新供应商列表，当前页
 * @param {number} p 新页码
 * @param {number} s 新分页大小
 */
const updateProviderCurrentList = (p?: number, s?: number) => {
  // 如果参数指定了页码或/和每页大小，则按参数更新列表；
  // 否则保持当前页码和大小不变直接更新。
  // 这里的判断条件相当于排除了undefined null 0等逻辑非值，
  // 另外，页码为0或者分页大小为0，本身就是逻辑错误的，不应该更新分页。
  if (p || s) {
    providerPageNum.value = p || 1
    providerPageSize.value = s || 20
  }
  // 更新当前页码
  providerCurrentList.value = updateCurrentPageList(providerFilterList.value, providerPageNum.value, providerPageSize.value)
}
/**
 * 点击供应商单项
 * @param {SmsProviderItem} current
 */
const onClickProviderItem = (current: SmsProviderItem) => {
  // 更新当前供应商信息
  currentProvider.value = JSON.parse(JSON.stringify(current))
}
/**
 * 点击创建供应商按钮
 */
const onClickCreateProvider = () => {
  // 清空数据
  providerDialogData.value = {}
  // 新建模式
  providerDialogIsEdit.value = false
  // 显示弹窗
  providerDialogVisible.value = true
}
/**
 * 点击编辑供应商按钮
 */
const onClickEditProvider = (current: SmsProviderItem) => {
  // 更新数据
  providerDialogData.value = JSON.parse(JSON.stringify(current))
  // 编辑模式
  providerDialogIsEdit.value = true
  // 显示弹窗
  providerDialogVisible.value = true
}

// ---------------------------------------- 供应商列表 结束 ----------------------------------------

// ---------------------------------------- 供应商信息 开始 ----------------------------------------

// 业务范围展示文本
const businessScopeText = computed(() => {
  // 列表不存在或为空
  if (!accountList.value?.length) {
    return '-'
  }

  // 遍历列表，将行业提取出来组成数组并用集合去重
  let list: string[] = []
  accountList.value?.forEach((item: SmsAccountItem) => {
    // 如果有行业信息，就追加到数组里
    if (item.secondIndustries?.length) {
      list.push(...item.secondIndustries)
    }
  })
  // 数组去重
  const set = new Set(list)

  // 将集合迭代成列表并拼接成字符串，用于页面展示
  return Array.from(set).join(',')
})

// ---------------------------------------- 供应商信息 结束 ----------------------------------------

// ---------------------------------------- 模块标签卡 开始 ----------------------------------------

// 模块标签卡枚举
enum ModuleTabEnum {
  ACCOUNT = '账号列表',
}

// 全部模块标签卡
const moduleTabList: ModuleTabEnum[] = Object.values(ModuleTabEnum)
// 当前模块标签卡
const moduleTab = ref<ModuleTabEnum>(ModuleTabEnum.ACCOUNT)

// ---------------------------------------- 模块标签卡 结束 ----------------------------------------

// ---------------------------------------- 供应商弹窗 开始 ----------------------------------------

// 供应商弹窗，显示
const providerDialogVisible = ref<boolean>(false)
// 供应商弹窗，是否编辑（新建，编辑）
const providerDialogIsEdit = ref<boolean>(false)
// 供应商弹窗，数据
const providerDialogData = ref<SupplierBlackGroupInfo>({})

/**
 * 黑名单挂载弹窗，表单提交
 */
const onConfirmProviderDialog = () => {
  updateProviderAllList()
}

// ---------------------------------------- 供应商弹窗 结束 ----------------------------------------

// ---------------------------------------- 立即执行 结束 ----------------------------------------

onActivated(() => {
  const current = JSON.parse(JSON.stringify(smsProviderStore.currentProvider))
  smsProviderStore.clear()
  updateProviderAllList(current)
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
:deep(.provider-tab) {
  padding: 8px 12px 0;
  .normal-tab {
    width: 100%;
    height: 100%;
    padding: 4px 12px;
    border-bottom: none;
    font-size: 14px;
    line-height: 22px;
    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
