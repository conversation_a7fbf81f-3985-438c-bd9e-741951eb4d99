<template>
  <HeaderBox title="账号管理" />
  <div class="account-container">
    <div class="tw-flex tw-justify-end tw-mb-[12px]"><el-button type="primary" class="tw-w-[68px] tw-h-[32px]"  @click="edit()">新建账号</el-button></div>
    <div class="tw-w-full tw-p-[16px] tw-bg-white tw-mb-[16px]">
      <div class="tw-grid tw-grid-cols-5 2xl:tw-grid-cols-7 tw-gap-4 tw-border-b-[1px] tw-pb-[12px]">
        <div class="item">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入用户姓名"
            clearable
            @keyup.enter="search"
          >
          </el-input>
        </div>
        <div class="item">
          <el-select v-model="searchForm.roleId" placeholder="选择角色" clearable>
            <el-option v-for="item in roleList" :key="item.id" :label="item.roleName" :value="item.id" />
          </el-select>
        </div>
        <div class="item">
          <el-input
            v-model="searchForm.account"
            clearable
            placeholder="请输入用户账号"
            @keyup.enter="search"
          >
          </el-input>
        </div>
        <div class="item tw-col-span-2">
          <TimePickerBox
            v-model:start="searchForm.startTime"
            v-model:end="searchForm.endTime"
            placeholder="最后登录时间"
            separator="-"
          />
        </div>
      </div>
      <div class="tw-flex tw-justify-end tw-mt-[12px]">
        <el-button type="primary" @click="clearSearchForm" link>
          <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
          <span>重置</span>
        </el-button>
        <el-button type="primary" @click="search" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>
   
    <el-table
      :data="tableTempData"
      v-loading="loading"
      :max-height="maxTableHeight"
      ref="tableRef"
      row-key="id"
      :header-cell-style="tableHeaderStyle"
      stripe
    >
      <el-table-column property="account" label="账号" align="left" min-width="160" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column property="name" label="姓名" align="left" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column property="gender" label="性别" align="left" min-width="80">
        <template #default="{ row }">
          {{ row.gender === 'FEMALE' ? '女' : '男'}}
        </template>
      </el-table-column>
      <el-table-column property="phone" label="联系方式" align="left" min-width="120">
        <template #default="{ row }">
          {{ filterPhone(row.phone, 3, 4) || '-'}}
        </template>
      </el-table-column>
      <el-table-column property="roleId" label="角色" align="left" :formatter="formatterEmptyData" min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
          {{ roleList.find(item => item.id == row.roleId)?.roleName || '' }}
        </template>
      </el-table-column>
      <el-table-column property="department" label="部门" align="left" min-width="160" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column property="latestLoginTime" label="最后登录时间" sortable align="center" :formatter="formatterEmptyData" min-width="160">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="createTime" label="创建时间" sortable align="center" :formatter="formatterEmptyData" min-width="160">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="240" align="right" fixed="right">
        <template #default="{ row }">
          <div class="table-btn-box">
            <el-button type="primary" link @click="editPassWord(row)">修改密码</el-button>
            <el-button type="primary" link @click="edit(row)">编辑信息</el-button>
            <el-button type="primary" link @click="editFreezeStatus(row)">{{row.accountEnable ? '冻结' : '解冻'}}</el-button>
            <el-button type="primary" link @click="del(row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="tableData.length || 0"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
    <AccountEditDialog
      v-model:visible="editVisible"
      :rowData="editData"
      :roleList="roleList"
      @confirm="search"
    >
    </AccountEditDialog>
    <PasswordEditDialog
      v-model:editPassWordVisible="editPassWordVisible"
      :passwordData="passwordData"
      :isSelf="false"
      @confirm="confirmPassword"
    >
    </PasswordEditDialog>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, } from 'vue'
import AccountEditDialog from './AccountEditDialog.vue'
import PasswordEditDialog from '@/components/PasswordEditDialog.vue'
import { ElMessage, } from 'element-plus'
import { AccountItem, RoleResponse } from '@/type/user'
import { filterPhone, formatterEmptyData } from '@/utils/utils'
import PaginationBox from '@/components/PaginationBox.vue'
import { aiTeamModel,  } from '@/api/user'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { useUserStore } from '@/store/user'
import Confirm from '@/components/message-box'
import HeaderBox from '@/components/HeaderBox.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { tableHeaderStyle } from '@/assets/js/constant'

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const userStore = useUserStore()
const editVisible = ref(false)
const editPassWordVisible = ref(false)
class AccountOrigin {
  id = undefined
  account = ''
  name = ''
  gender = 'MALE'
  phone = ''
  department = undefined
  roleId = undefined
  password = undefined
  email = undefined
  address = undefined
  createTime = undefined
  latestLoginTime = undefined
  accountType = 0
  tenantId = userStore.tenantId
  accountEnable = true
}
const editData = reactive<AccountItem>(new AccountOrigin())
const passwordData = reactive<{
    id: number,
    account: string,
    newPassword: string
}>({
  id: -1,
  account: '',
  newPassword: ''
})
const currentPage = ref(1)
const pageSizeList = [20, 50, 100]
const pageSize = ref(pageSizeList[0])
const tableData = ref<AccountItem[]>([])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}
const tableTempData = computed(() => {
  return tableData.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})
class searchOrigin {
  name = ''
  account = ''
  roleId = null
  startTime = undefined
  endTime = undefined
}
const tableRef = ref(null)
const maxTableHeight = ref<number>(document.body.clientHeight - 300)
window.onresize = () => {
  return (() => {
    maxTableHeight.value = document.body.clientHeight -300
  })()
}
const searchForm = reactive(new searchOrigin())
const edit = (row?: AccountItem) => {
  editVisible.value = true
  Object.assign(editData, row || new AccountOrigin())
}
const editPassWord = (row: AccountItem) => {
  editPassWordVisible.value = true
  passwordData.id = row.id as number
  passwordData.account = row.account
  passwordData.newPassword = ''
}
const editFreezeStatus =  (row: AccountItem) => {
  Confirm({ 
    text: `您确定要【${row.accountEnable ? '冻结' : '解冻'}】账号【${row.account}】吗?`,
    type: 'warning',
    title: `${row.accountEnable ? '冻结' : '解冻'}账号确认`
  }).then(async () => {
    const params = {
      account: row.account,
      accountEnable: !row.accountEnable,
      accountType: 0
    }
    await aiTeamModel.editUserFreezeStatus(params)
    ElMessage({
      type: 'success',
      message: '操作成功'
    })
    search()
  }).catch(() => {})
}
const clearSearchForm = () => {
  Object.assign(searchForm, new searchOrigin())
  ElMessage({
    type: 'success',
    message: '重置成功'
  })
}
const confirmPassword = async (params: {
  id: number,
  newPassword: string
}) => {
  await aiTeamModel.changeAccountPassword(params)
  ElMessage({
    type: 'success',
    message: '操作成功'
  })
  editPassWordVisible.value = false
  search()
}
const search = async () => {
  loading.value = true
  tableData.value = await aiTeamModel.getMerchantAccountList(searchForm) as AccountItem[] || []
  loading.value = false
}
const roleList = ref<RoleResponse[]>([])
const init = async () => {
  roleList.value = await aiTeamModel.searchRoleList() as RoleResponse[]
}
init()
search()
// 操作区
const del = async (row: AccountItem) => {
  Confirm({ 
    text: `您确定要【删除】账号【${row.account}】吗?`,
    type: 'danger',
    title: `删除确认`
  }).then(async () => {
    const params = {
      id: row.id as number
    }
    await aiTeamModel.deleteAccount(params)
    ElMessage({
      type: 'success',
      message: '账号删除成功'
    })
    search()
  }).catch(() => {})
}

</script>

<style scoped lang="postcss" type="text/postcss">
.account-container {
  padding: 16px;
  width: 100%;
  box-sizing: border-box;
  min-width: 1080px;
  .item {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .label {
      width: 120px
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-table {
    font-size: 13px;
    :deep(.caret-wrapper) {
      display: none;
    }
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
}
</style>