<template>
  <HeaderBox title="角色管理" />
  <div class="role-container">
    <div class="tw-flex tw-justify-end tw-mb-[12px]"><el-button type="primary" class="tw-w-[68px] tw-h-[32px]"  @click="edit()">新建角色</el-button></div>
    <div class="tw-w-full tw-p-[16px] tw-grid tw-grid-cols-5 2xl:tw-grid-cols-7 tw-bg-white tw-mb-[16px]">
      
      <div class="item">
        <el-input
          v-model="name"
          placeholder="请输入用户角色名称"
          @keyup.enter="search()"
          clearable
        >
        </el-input>
      </div>
      <div class="tw-pl-2 tw-flex">
        <el-button type="primary" @click="search" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>
    <el-table
      :data="tableTempData"
      v-loading="loading"
      :header-cell-style="tableHeaderStyle"
      stripe
      :max-height="maxTableHeight"
      row-key="id"
    >
      <el-table-column property="roleName" label="角色名称" align="left" width="160" show-overflow-tooltip></el-table-column>
      <el-table-column property="authorityMap" label="操作权限" align="left" min-width="300">
        <template #default="{ row }">
          <TagsBox :tagsArr="filterAuthorityCodes(row.authorityMap ? Object.keys(row.authorityMap) : [])" tagsName="操作权限" :tagsNum="6" :type="''" effect="light" :showTotal="true"></TagsBox>
        </template>
      </el-table-column>
      <el-table-column property="updateTime" label="最近修改时间" align="center" width="160" sortable>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="right" width="120">
        <template #default="{ row }">
          <el-button type="primary" link @click="edit(row)">编辑</el-button>
          <el-button type="primary" link @click="handleDel(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="tableData.length || 0"
      @search="search(true)"
      @update="updatePage"
    >
    </PaginationBox>
    <RoleEdit
      v-model:visible="addVisible"
      :editData="addData"
      @confirm="confirmAdd"
    ></RoleEdit>
  </div>
</template>

<script lang="ts" setup>
import { RoleResponse } from '@/type/user'
import { reactive, computed, ref, } from 'vue'
import { ElMessage, } from 'element-plus'
import RoleEdit from './RoleEdit.vue'
import {asyncRouter} from '@/router/asyncRoute/index'
import { aiTeamModel } from '@/api/user'
import PaginationBox from '@/components/PaginationBox.vue'
import { useGlobalStore } from '@/store/globalInfo'
import { storeToRefs } from 'pinia'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'
import TagsBox from '@/components/TagsBox.vue'
import Confirm from '@/components/message-box'
import HeaderBox from '@/components/HeaderBox.vue'
import { tableHeaderStyle } from '@/assets/js/constant'

// 搜索区
const name = ref('')
const globalStore = useGlobalStore()
const userStore = useUserStore()
const allAsyncRouter = asyncRouter()
const { loading } = storeToRefs(globalStore)
const currentPage = ref(1)
const pageSizeList = [20, 50, 100]
const pageSize = ref(pageSizeList[0])
const tableData = ref<RoleResponse[]>([])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}
const tableTempData = computed(() => {
  return tableData.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})
const menuAllList = allAsyncRouter ? allAsyncRouter[0].children!.filter(item => (item.meta.type??1)>0 && (item.meta.type??1)<3) : []

const search = async (refresh?: boolean) => {
  loading.value = true
  if (!tableData.value || tableData.value.length == 0 || refresh) {
    tableData.value = await aiTeamModel.searchRoleList() as RoleResponse[] || []
  }
  if (name.value.trim()!= '') {
    tableData.value = tableData.value.filter(item => item.roleName.includes(name.value.trim()))
  }
  loading.value = false
}
// 表格区
const maxTableHeight = ref<number>(document.body.clientHeight - 260)
window.onresize = () => {
  return (() => {
    maxTableHeight.value = document.body.clientHeight -260
  })()
}
const filterAuthorityCodes = (codes?: string[]) => {
  if (!codes || codes.length < 1) return []
  let res: string[] = []
  menuAllList.map(item => { 
    if (item.children && item.children.length > 0) {
      item.children.map(subItem => {
        if (codes.includes(subItem.meta.id) && subItem.meta.type !== 3) {
          res.push(subItem.meta.title)
        }
      })
    } else {
      if (codes.includes(item.meta.id)) {
        res.push(item.meta.title)
      }
    }
  })
  return res
}
// 操作区
const addVisible = ref(false)
class RoleResponseOrigin {
  id = undefined
  roleName = ''
  note = ''
  accountType = 0
  tenantId = userStore.tenantId
  authorityMap = {}
  createTime = undefined
  updateTime = undefined
}
const addData = reactive<RoleResponse>(new RoleResponseOrigin())
const edit = (row?: RoleResponse) => {
  addVisible.value = true
  Object.assign(addData, row || new RoleResponseOrigin())
}
const confirmAdd = () => {
  search(true)
}
const handleDel = async (row: RoleResponse) => {
  Confirm({ 
    text: `您确定要【删除】账号【${row.roleName}】吗?`,
    type: 'danger',
    title: '删除账号'
  }).then(async () => {
    const params = {
      id: row.id as number
    }
    await aiTeamModel.delRole(params)
    ElMessage({
      type: 'success',
      message: '账号删除成功'
    })
    search(true)
  }).catch(() => {})
}
search(true)
</script>

<style scoped lang="postcss" type="text/postcss">
.role-container {
  padding: 16px;
  width: 100%;
  box-sizing: border-box;
  min-width: 1080px;
  .item {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .label {
      width: 120px
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-table {
    font-size: 13px;
    :deep(.caret-wrapper) {
      display: none;
    }
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
}
</style>