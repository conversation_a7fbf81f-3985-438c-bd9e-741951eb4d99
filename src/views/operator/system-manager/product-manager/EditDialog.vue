<template>
  <el-dialog
    :model-value="visible"
    width="600px"
    :close-on-click-modal="false"
    align-center
    class="dialog-form"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">{{ title }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="80px"
        ref="editRef"
      >
        <el-form-item label="产品名称：" prop="productName">
          <el-input v-model="editData.productName" clearable placeholder="请填写产品名称，20字符以内"></el-input>
        </el-form-item>
        <el-form-item label="所属行业：" prop="industrySecondFieldId">
          <el-cascader
            v-model="editData.industrySecondFieldId"
            class="tw-w-full"
            placeholder="请选择行业"
            :options="allIndustryList"
            :props="cascaderProps"
            clearable
          />
        </el-form-item>

      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, reactive, watch} from 'vue'
import { ElMessage, CascaderProps } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { useGlobalStore } from '@/store/globalInfo'
import type { FormInstance, } from 'element-plus'
import to from 'await-to-js';
import { ProductItemOrigin, ProductItem } from '@/type/industry'
import { productModel } from '@/api/industry'
import { Throttle, pickAttrFromObj } from '@/utils/utils'

const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean,
  rowData: ProductItem | undefined,
}>();
const loading = ref(false)
const throttleLoading = new Throttle(loading)
const globalStore = useGlobalStore()
const cascaderProps: CascaderProps = {
  value: 'id',
  label: 'name',
  children: 'secondaryIndustries',
  emitPath: false,
}
const allIndustryList = globalStore.getIndustryOption
const editData = reactive<ProductItem>(new ProductItemOrigin())
const visible = ref(false)
const editRef = ref<FormInstance  | null>(null)
const title = computed(() => { 	return editData.id ? '编辑产品' : '添加产品'})
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  emits('update:visible', false)
}
const validateName = async (rule: any, value: any, callback: any) => {
 if (value.length > 20 || value.length < 2) {
    return callback(new Error('产品名称必须为2~20位'))
  }
  return callback()
}

const rules = {
  productName: [
    { required: true, message: '请填写产品名称', trigger: 'blur' },
    { validator: validateName, trigger: 'blur' },
  ],
  industrySecondFieldId:[
    { required: true, message: '请选择所属行业', trigger: 'change' },
  ],
}

const confirm = () => {
  if (throttleLoading.check()) {
    return
  }
  editData.industrySecondFieldName = globalStore.getSecondIndustryList.find(item => item.id === editData.industrySecondFieldId)?.name || ''
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      throttleLoading.lock()
      const [err, _] = await to(editData.id ? productModel.edit(editData) : productModel.add(editData))
      if (!err) {
        ElMessage({
          type: 'success',
          message: '操作成功'
        })
        emits('confirm')
      }
      cancel()
      throttleLoading.unlock()
    }
  })
}

watch(() => props.visible, n => {
  visible.value = n
  if (n && props.rowData) {
    loading.value = false
    editRef.value?.clearValidate()
    Object.assign(editData, pickAttrFromObj(props.rowData, [
      'id', 'industrySecondFieldId', 'industrySecondFieldName', 'productName'
    ]))
  } else {
    Object.assign(editData, new ProductItemOrigin())
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
</style>
