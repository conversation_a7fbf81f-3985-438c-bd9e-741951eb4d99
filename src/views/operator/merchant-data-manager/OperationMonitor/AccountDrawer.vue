<template>
  <!--表单抽屉组件-->
  <el-drawer
    v-model="visible"
    :before-close="handleClose"
    :size="drawerWidth()"
    :with-header="false"
  >
    <div class="tw-px-[16px] tw-bg-white tw-h-[50px] tw-text-[16px] tw-font-semibold tw-text-left tw-text-[var(--primary-black-color-600)] tw-flex tw-items-center tw-justify-between">
      <span>{{ props.account || '账号详情' }}</span>
      <el-button link @click="handleClose">
        <el-icon :size="20" color="var(--primary-black-color-400)"><CloseBold /></el-icon>
      </el-button>
    </div>
    <div class="tw-w-full tw-p-[16px] sm:tw-p-[6px] tw-h-[calc(100%-50px)] tw-bg-[#f2f3f5]">
      <div class="tw-flex tw-justify-end tw-w-full tw-mb-[8px]">
        <el-button @click="exportXlsx" :size="isMobile ? 'small' : 'default'"><el-icon :size="16">
          <SvgIcon name="download3" color="none"></SvgIcon></el-icon>
          <span>导出数据</span>
        </el-button>
      </div>
      <div class="card-box tw-flex-col tw-mb-[12px] sm:tw-p-[6px]">
        <div class="tw-grid tw-grid-cols-4 sm:tw-grid-cols-2 tw-gap-[8px] tw-border-b-[1px] tw-pb-[12px] tw-w-full">
          <div class="item">
            <el-input
              v-model="searchForm.taskName"
              placeholder="任务名称"
              clearable
              @keyup.enter="search()"
            >
            </el-input>
          </div>
          <div class="item">
            <el-select v-model="searchForm.taskType" placeholder="任务类型" class="tw-w-full" @change="search()" clearable>
              <el-option v-for="item in taskTypeList" :key="item.value" :label="item.name" :value="item.value"/>
            </el-select>
          </div>
          <div class="item">
            <el-select v-model="speechCraftName" placeholder="话术名称" class="tw-w-full" clearable @change="search()">
              <el-option v-for="item in speechCraftNameList" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div class="item">
            <el-select v-model="searchForm.callStatus" placeholder="任务状态" class="tw-w-full" @change="search()" clearable>
              <el-option v-for="item in taskStatusList" :key="item" :label="item" :value="item"/>
            </el-select>
          </div>
        </div>
        <div class="tw-mt-[12px] tw-self-end">
          <el-button type="primary" @click="clearSearchForm" link>
            <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
            <span>重置</span>
          </el-button>
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
      <div class="tw-flex tw-flex-col tw-h-[calc(100%-180px)]">
        <TaskTable
          class="task-table"
          v-loading="loading"
          :currentId="currentTask.id||''"
          :tableData="tableData||[]"
          :groupId="props.groupId"
          :taskType="searchForm.taskType || TaskTypeEnum['AI外呼']"
          showPagination
          showTotal
          @update:table="search()"
          @go-detail="goDetail"
        >
          <template v-slot:operate="{ row, index }">
            <el-button link type="primary" @click="goDetail(row, index)">详情</el-button>
          </template>
        </TaskTable>
      </div>
    </div>
    <el-drawer
      v-model="taskDetailVisible"
      :before-close="closeDetails"
      :size="isMobile ? '100%': '900px'"
      :with-header="false"
    >
      <el-scrollbar ref="taskDetailScrollbar" class="tw-w-full tw-h-full tw-bg-[#f2f3f5]">
        <div class="tw-bg-white tw-h-[50px] tw-px-[16px] tw-text-[16px] tw-font-semibold tw-text-left tw-text-[var(--primary-black-color-600)] tw-flex tw-items-center tw-justify-between">
          <span>{{ currentTask.taskName || '' }}</span>
          <el-button link @click="closeDetails">
            <el-icon :size="20" color="var(--primary-black-color-400)"><CloseBold /></el-icon>
          </el-button>
        </div>
        <TaskDetails
          :currentTask="currentTask"
          v-model:needRefresh="needRefresh"
          :readonly="true"
          isFromList
          @update-task="search"
          @update-change="handleNextTask"
        >
        </TaskDetails>
      </el-scrollbar>
    </el-drawer>
  </el-drawer>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, defineAsyncComponent, onUnmounted } from 'vue'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { enum2Options, } from '@/utils/utils'
import { exportTable2Excel } from '@/utils/export'
import { MonitorSearchInfo, MonitorInfoItem, } from '@/type/monitor-statistic'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import { TaskManageItem, TaskStatusEnum, TaskTypeEnum, TaskManageOrigin } from '@/type/task'
import to from 'await-to-js';
import { useGlobalStore } from '@/store/globalInfo'
import { onBeforeRouteLeave } from 'vue-router'

const TaskDetails = defineAsyncComponent({loader: () => {return import('@/components/task/TaskDetails.vue')}})
const TaskTable = defineAsyncComponent({loader: () => {return import('@/components/task/TaskTable.vue')}})


const props = defineProps<{
  account: string,
  groupId: string,
  visible: boolean,
}>();
const emits = defineEmits(['update:visible',])

const globalInfo = useGlobalStore()
const isMobile = globalInfo.isMobile

// class
class SearchFormOrigin {
  taskName = undefined
  callStatus = undefined
  taskType = undefined
  tenantName = undefined // 商户名称
  account = props.account // 商户账号
  ifFindAll = '1' // 查询所有
  startTime = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
  endTime = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
}
const speechCraftName = ref('')

// 任务状态搜索项
const taskStatusList = Object.values(TaskStatusEnum).filter(item => item !== '全部')
const taskTypeList = enum2Options(TaskTypeEnum)
const currentId = ref<number | string>('')
// 抽屉宽度
const drawerWidth = () => {
  return window.innerWidth > 1400 ? '70%' : (window.innerWidth > 600 ? '1000px': '100%')
}


/** 任务列表 开始 */
const searchForm = reactive<MonitorSearchInfo>(new SearchFormOrigin())
const clearSearchForm = () => {
  Object.assign(searchForm, new SearchFormOrigin())
}
const loading = ref(false)
const tableData = ref<MonitorInfoItem[] | null>([])
const speechCraftNameList = ref<Set<string> | null>(new Set([]))
// 搜索
const search = async (needUpdateScript:boolean = false) => {
  loading.value = true
  const [_, res] = await to(monitorStatisticModel.findMonitorListByCondition(searchForm)) as [any, MonitorInfoItem[]]
  const res1 = res?.filter(item => !speechCraftName.value || item.speechCraftName === speechCraftName.value)

  if (needUpdateScript) {
    // 由于sort中的a不会包含第一个res的数据，需要把他补上
    speechCraftNameList.value = new Set(res1[0]?.speechCraftName ? [res1[0]?.speechCraftName] : [])
  }
  // 排序并更新可筛选的话术列表
  tableData.value = res1?.sort((a, b) => {
    needUpdateScript && a.speechCraftName && speechCraftNameList.value?.add(a.speechCraftName)
    return dayjs(a.createTime).isAfter(b.createTime) ? -1 : 1
  }) || []

  loading.value = false
}

/** 导出 */
const exportXlsx = () => {
  const dom = document.querySelector('.task-table')
  dom && exportTable2Excel(dom, `【${props.account}】任务统计_${dayjs().format('YYYY-MM-DD')}.xlsx`)
}
/** 任务列表 结束 */


/** 任务详情抽屉(二级抽屉) 开始*/
const currentTask = reactive<TaskManageItem>(new TaskManageOrigin(TaskTypeEnum['AI外呼']))
const taskDetailVisible = ref(false)
const needRefresh = ref(false)
// 打开任务详情，二级抽屉关闭
const goDetail = async (item: MonitorInfoItem, index: number) => {
  if (item.id && item.id > 0) {
    taskDetailVisible.value = true
    needRefresh.value = true
    Object.assign(currentTask, item)
  } else {
    ElMessage({
      type: 'warning',
      message: '任务出错'
    })
  }
}
// 二级抽屉内部切换任务
const handleNextTask = (flag: number) => {
  if (!tableData.value) return
  const index = tableData.value.findIndex(v => v.id === currentTask.id)
  if (tableData.value[index + flag] && tableData.value[index + flag].taskName!=='合计') {
    Object.assign(currentTask, tableData.value[index + flag])
    needRefresh.value =true
  } else {
    ElMessage.warning('已至任务列表最' + (flag > 0 ? '底部' : '顶部'))
  }
}

// 二级抽屉关闭
const closeDetails = () => {
  Object.assign(currentTask, new TaskManageOrigin(TaskTypeEnum['AI外呼']))
  taskDetailVisible.value = false
}
/** 任务详情抽屉 开始*/



// 一级弹窗显示隐藏
const visible = ref<boolean>(props.visible)
// 弹窗关闭，通知父组件同步状态
const handleClose = () => {
  visible.value = false
  emits('update:visible', false)
}
watch(() => props.visible, n => {
  visible.value = n
  if (n) {
    speechCraftName.value = ''
    taskDetailVisible.value = false
    speechCraftNameList.value = new Set([])
    Object.assign(currentTask, new TaskManageOrigin(TaskTypeEnum['AI外呼']))
    clearSearchForm()
    search(true)
  } else {
    tableData.value = null
  }
})

onUnmounted(() => {
  tableData.value = null
  speechCraftNameList.value = null
})
onBeforeRouteLeave(() => {
  tableData.value = null
  speechCraftNameList.value = null
})

</script>

<style lang="postcss" scoped>
.el-table {
  font-size: 13px;
}
</style>
