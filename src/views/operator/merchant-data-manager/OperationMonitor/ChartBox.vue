<template>
  <div class="tw-mb-[8px] tw-flex">
    <SelectBox
      v-model:selectVal="globalStore.masterAccountGroups"
      :options="masterAccountList||[]"
      class="tw-w-[300px]"
      name="account"
      val="account"
      placeholder="选择主账号"
      filterable
      clearable
      canSelectAll
      multiple
    >
    </SelectBox>
    <el-button link type="primary" @click="updateAccountData" class="tw-ml-[12px]">
      <el-icon :size="16" class="tw-mr-0.5">
        <SvgIcon name="filter" color="none"></SvgIcon>
      </el-icon>
      <span>筛选</span>
    </el-button>
  </div>
  <div class="tw-flex-col tw-overflow-y-hidden">
    <div v-if="!!currentExpandChart" class="tw-bg-white tw-relative tw-rounded-[4px] tw-col-span-3">
      <div class="tw-absolute tw-right-[16px] tw-top-[16px] tw-cursor-pointer tw-z-[99] tw-text-[#999]" @click="changeExpandChart(0)">
        <el-icon :size="20"><Close /></el-icon>
      </div>
      <div class="tw-absolute tw-right-[16px] tw-top-[40px] tw-z-[9]">
        <el-radio-group v-if="currentExpandChart?.title === '待呼名单分布'" v-model="taskRange" size="small" @change="handleTaskRangeChange">
          <el-radio-button v-for="item in taskRangeList" :key="item.value" :label="item.value">{{ item.name }}</el-radio-button>
        </el-radio-group>
      </div>
      <BarChart
        :data="currentExpandChart?.data || []"
        :title="currentExpandChart?.title || ''"
        :xName="currentExpandChart?.xName || ''"
        :legends="currentExpandChart?.legends || []"
        :scrollSpan="10"
        :height="isMobile ? '90vw': '27vw'"
      >
      </BarChart>
    </div>
    <div v-else class="tw-w-full tw-grid tw-grid-cols-3 sm:tw-grid-cols-1 tw-gap-[16px] sm:tw-px-[6px]">
      <div class="tw-bg-white tw-relative tw-rounded-[4px] tw-flex" v-loading="loading1">
        <div class="tw-absolute tw-right-[16px] tw-top-[16px] tw-cursor-pointer tw-z-[9] tw-text-[#999]" @click="changeExpandChart(1)">
          <el-icon :size="20"><FullScreen /></el-icon>
        </div>
        <BarChart
          :data="concurrencyList||[]"
          title="并发数量分布"
          xName="账号"
          :legends="['锁定并发', '实际并发']"
          :scrollSpan="3"
          :height="isMobile ? '90vw': '27vw'"
        >
        </BarChart>
      </div>
      <div class="tw-bg-white tw-relative tw-rounded-[4px]" v-loading="loading1">
        <div class="tw-absolute tw-right-[16px] tw-top-[16px] tw-cursor-pointer tw-z-[9] tw-text-[#999]" @click="changeExpandChart(2)">
          <el-icon :size="20"><FullScreen /></el-icon>
        </div>
        <BarChart
          :data="phoneNumberList||[]"
          title="导入名单分布"
          xName="账号"
          :legends="['导入名单']"
          :scrollSpan="3"
          :height="isMobile ? '90vw': '27vw'"
        >
        </BarChart>
      </div>
      <div class="tw-bg-white tw-relative tw-rounded-[4px]" v-loading="loading1">
        <div class="tw-absolute tw-right-[16px] tw-top-[16px] tw-cursor-pointer tw-z-[9] tw-text-[#999]" @click="changeExpandChart(3)">
          <el-icon :size="20"><FullScreen /></el-icon>
        </div>
        <div class="tw-absolute tw-right-[16px] tw-top-[40px] tw-z-[9]">
          <el-radio-group v-model="taskRange" size="small">
            <el-radio-button v-for="item in taskRangeList" :key="item.value" :label="item.value">{{ item.name }}</el-radio-button>
          </el-radio-group>
        </div>
        <BarChart
          :data="taskRange !== 1 ? totalCallingNumberList||[] : callingNumberList||[]"
          :scrollSpan="3"
          xName="账号"
          :legends="['首呼', '补呼']"
          title="待呼名单分布"
          :height="isMobile ? '90vw': '27vw'"
        >
        </BarChart>
      </div>
    </div>

    <div class="tw-bg-white tw-relative tw-w-full tw-mt-[16px] tw-rounded-[4px]" v-loading="loading1">
      <MixLineBarChart :data="putThroughTempList||[]" :showLabel="true" :showScroll="range==='全部'" xName="账号" title="账号接通分布" :legends="putThroughLegends" :scrollSpan="isMobile ? 4 : 10">
        <div class="tw-absolute tw-right-1 tw-top-1 tw-z-10">
          <el-radio-group v-model="range">
            <el-radio-button v-for="item in rangeList" :label="item" :key="item"/>
          </el-radio-group>
        </div>
      </MixLineBarChart>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { watch, computed, ref, reactive, onUnmounted, } from 'vue'
import BarChart from '@/components/charts/BarChart.vue'
import MixLineBarChart from '@/components/charts/MixLineBarChart.vue'
import to from 'await-to-js'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import { MonitorStatisticItem, } from '@/type/monitor-statistic'
import { useGlobalStore } from '@/store/globalInfo'
import { onBeforeRouteLeave } from 'vue-router'
import { FullScreen, Close } from '@element-plus/icons-vue'
import SelectBox from '@/components/SelectBox.vue'

const props = defineProps<{
  needUpdate: boolean
}>();
const emits = defineEmits(['update:needUpdate',])

const globalInfo = useGlobalStore()
const isMobile = globalInfo.isMobile

const currentExpandChart = ref<null | {
  title: string,
  xName: string,
  legends: string[],
  data: {name: string,value: number[]}[]
}>(null)

/** 中部echart图 */
const loading1 = ref(false)
// const monitorAccountList = ref<MonitorStatisticItem[] | null>([]) // 全部账号维度数据，通过一个接口获取
/** 获取图表数据（三个柱状图） */ 
const concurrencyList = ref<{name: string,value: number[]}[] | null>([]) // 并发
const phoneNumberList = ref<{ value: number[], name: string}[] | null>([]) // 任务数 => 导入名单数
const callingNumberList = ref<{name: string,value: number[]}[] | null>([]) // 待呼，进行中的任务
const totalCallingNumberList = ref<{name: string,value: number[]}[] | null>([]) // 待呼，全部的任务
/** 账号接通分布折线图 */
const putThroughList = ref<{ value1: number, value2: number, name: string }[] | null>([]) // 名单接通数，接通率
const rangeList = ref(['全部', '前20', '后20'])
const range = ref('全部')
const putThroughLegends = ['名单接通数', '名单接通率', '']

// 待呼名单，任务范围选择器
const taskRangeList = [
  { name: '所有任务', value: 0 },
  { name: '进行中任务', value: 1 },
]
const taskRange = ref(taskRangeList[0].value)
const handleTaskRangeChange = () => {
  if (!currentExpandChart.value) return
  if (currentExpandChart.value) {
    currentExpandChart.value.data = taskRange.value  === taskRangeList[0].value ? totalCallingNumberList.value || [] : callingNumberList.value || []
  }
}

// 接通分布实际显示内容
const putThroughTempList = computed(() => {
  if (!putThroughList.value) return []
  switch(range.value) {
    case '全部': return putThroughList.value;
    case '前20': return putThroughList.value.slice(0, 20);
    case '后20': return putThroughList.value.slice(-20);
    default: return putThroughList.value;
  }
})
// 获取锁定并发图数据\账号任务数量分布数据\账号维度剩余待呼名单分布数据\账号维度接通率展示数据
const updateAccountData = async () => {
  loading1.value = true
  const [_, data1] = await to(monitorStatisticModel.findAccountData()) as [any, MonitorStatisticItem[]]
  // 并发数量
  const monitorAccountListTemp = data1 || []
  concurrencyList.value = monitorAccountListTemp.sort((a,b) => b.concurrentUsedNum - a.concurrentUsedNum).flatMap(item => {
    return !globalInfo.masterAccountGroups?.length || globalInfo.masterAccountGroups?.includes(item.account) ? [{
      name: item.account,
      value: [item.concurrentUsedNum || 0, item.concurrentRealNum || 0],
    }] : []
  })
  // 导入名单分布
  phoneNumberList.value =  monitorAccountListTemp.sort((a,b) => b.phoneNum - a.phoneNum).flatMap(item => {
    return !globalInfo.masterAccountGroups?.length || globalInfo.masterAccountGroups?.includes(item.account) ? [{
      name: item.account,
      value: [item.phoneNum || 0],
    }] : []
  })
  // 待呼名单（全部任务）
  totalCallingNumberList.value = monitorAccountListTemp.sort((a,b) => b.remainPhoneNum - a.remainPhoneNum).flatMap(item => {
    return !globalInfo.masterAccountGroups?.length || globalInfo.masterAccountGroups?.includes(item.account) ? [{
      name: item.account,
      value: [item.remainPhoneNum || 0, item.remainPhoneNumRecall || 0],
    }] : []
  })
  // 待呼名单（进行中任务）
  callingNumberList.value = monitorAccountListTemp.sort((a,b) => b.remainPhoneNum - a.remainPhoneNum).flatMap(item => {
    return !globalInfo.masterAccountGroups?.length || globalInfo.masterAccountGroups?.includes(item.account) ? [{
      name: item.account,
      value: [item.remainPhoneNumProcess || 0, item.remainPhoneNumProcessRecall || 0],
    }] : []
  })
  putThroughList.value = monitorAccountListTemp.sort((a,b) => b.putThroughNum - a.putThroughNum).flatMap(item => {
    return !globalInfo.masterAccountGroups?.length || globalInfo.masterAccountGroups?.includes(item.account) ? [{
      name: item.account,
      value1: item.putThroughNum || 0,
      value2: item.putThroughRate || 0,
    }] : []
  })
  // 如果当前存在展开，更新扩展；
  if (currentExpandChart.value?.title) {
    const index = ['并发数量分布', '导入名单分布', '待呼名单分布'].findIndex(item => item === currentExpandChart.value?.title)
    index > -1 && changeExpandChart(index + 1)
  }
  loading1.value = false
}


/**
 * 根据flag展开/关闭中部echart图
 * @param {number} flag 1:并发数量分布, 2:导入名单分布, 3:待呼名单分布
 */
const changeExpandChart = (flag: number) => {
  switch(flag) {
    case 1 : {
      currentExpandChart.value = {
        title: '并发数量分布',
        xName: '账号',
        legends: ['锁定并发', '实际并发'],
        data: concurrencyList.value || []
      }
      break
    }
    case 2 : {
      currentExpandChart.value = {
        title: '导入名单分布',
        xName: '账号',
        legends: ['导入名单'],
        data: phoneNumberList.value || []
      }
      break
    }
    case 3 : {
      currentExpandChart.value = {
        title: '待呼名单分布',
        xName: '账号',
        legends: ['首呼', '补呼'],
        data: taskRange.value !== 1 ? totalCallingNumberList.value || [] : callingNumberList.value || []
      }
      break
    }
    default: currentExpandChart.value = null
  }
  // 如当前扩展的为空，则返回缩小模式
  if (!currentExpandChart.value?.data?.length) {
    currentExpandChart.value = null
  }
}

// 进入该页面，读取缓存，并选择第一个选项进行搜索
const globalStore = useGlobalStore()
const masterAccountList = ref<{
  account: string,
  groupId: string,
}[]>([]) // 主账号列表，全量
const getMasterAccountList = async () => {
  const [err, res] = await to(monitorStatisticModel.getAllMainAccount())
  masterAccountList.value = (res || []).filter(item => !!item.account && !!item.groupId)
}

const init = async () => {
  await updateAccountData()
  await getMasterAccountList()
  // updateAccountPhoneNumData()
  emits('update:needUpdate', false)
}
watch(() => props.needUpdate, n => {
  n && init()
})
init()

const clearAllData = () => {
  callingNumberList.value = null
  putThroughList.value = null
  concurrencyList.value = null
  phoneNumberList.value = null
}
onUnmounted(() => {
  clearAllData()
})
onBeforeRouteLeave(() => {
  clearAllData()
})
</script>

<style scoped lang="postcss" type="text/postcss">

</style>