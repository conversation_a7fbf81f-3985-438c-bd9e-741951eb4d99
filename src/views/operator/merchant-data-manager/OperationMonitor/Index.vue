<template>
  <HeaderBox title="运行监控" :can-refresh="true" @refresh="init"/>
  <el-scrollbar class="tw-min-w-[1080px] sm:tw-min-w-full" view-class="tw-p-[16px] sm:tw-p-0 sm:tw-overflow-x-hidden">
    <Statistic v-model:needUpdate="needUpdate[0]"/>
    <!-- tab：账号分布 | 账号统计 -->
    <div class="tw-flex tw-w-full tw-mb-[8px] tw-mt-[16px] sm:tw-mb-[4px] sm:tw-mt-[8px] sm:tw-px-[6px]">
      <RadioButtonBox v-model:active="viewType" :list="viewTypeList"/>
    </div>

    <!-- 账号分布 -->
    <ChartBox v-if="viewType===viewTypeList[0]" v-model:needUpdate="needUpdate[1]"/>

    <AccountTable v-if="viewType===viewTypeList[1]" v-model:needUpdate="needUpdate[1]" />

    <ConcurrentTable v-if="viewType===viewTypeList[2]" v-model:needUpdate="needUpdate[1]" />

  </el-scrollbar>
</template>

<script lang="ts" setup>
import { reactive, ref, defineAsyncComponent, } from 'vue'
import HeaderBox from '@/components/HeaderBox.vue'
import RadioButtonBox from '@/components/RadioButtonBox.vue'
import Statistic from './Statistic.vue'

const ChartBox = defineAsyncComponent({loader: () => {return import('./ChartBox.vue')}})
const AccountTable = defineAsyncComponent({loader: () => {return import('./AccountTable.vue')}})
const ConcurrentTable = defineAsyncComponent({loader: () => {return import('./ConcurrentTable.vue')}})

const viewTypeList = ref(['账号分布', '账号统计', '并发统计'])
const viewType = ref(viewTypeList.value[0])

const needUpdate = ref([false, false])

// 初始化数据
const init = async () => {
  if (!needUpdate.value[0]) needUpdate.value[0] = true
  if (!needUpdate.value[1]) needUpdate.value[1] = true
}
init()
</script>

<style scoped lang="postcss" type="text/postcss">
</style>
