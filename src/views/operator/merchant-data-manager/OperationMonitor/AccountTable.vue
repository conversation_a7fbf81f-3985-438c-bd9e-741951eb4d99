<template>
  <div class="card-box tw-flex-col tw-grow">
    <div class="title-normal tw-self-start tw-mb-[16px] sm:tw-px-[6px]">
      <span>账号数据统计</span>
      <el-tooltip content="刷新账号数据统计" placement="right" :show-after="500">
        <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="search"><SvgIcon name="reset" color="inherit"/></el-icon>
      </el-tooltip>
    </div>
    <div class="tw-flex tw-items-center tw-w-full tw-mb-[6px]">
      <!-- <div class="tw-grid tw-grid-cols-2 tw-gap-[8px] tw-w-2/3 sm:tw-grow">
        <div class="item">
          <el-input
            v-model="searchForm.account"
            placeholder="账号"
            @keyup.enter="search"
            clearable
          >
          </el-input>
        </div>
        <div class="item">
          <el-input
            v-model="searchForm.tenantName"
            placeholder="商户名称"
            @keyup.enter="search"
            clearable
          >
          </el-input>
        </div>
      </div> -->
      <SelectBox
        v-model:selectVal="globalInfo.masterAccountGroups"
        :options="masterAccountList||[]"
        class="tw-w-[300px]"
        name="account"
        val="account"
        placeholder="选择主账号"
        filterable
        clearable
        canSelectAll
        multiple
      >
      </SelectBox>
      
      <div class="tw-grow-0 tw-shrink-0 tw-px-[4px]">
        <el-button type="primary" @click="search" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
          <span>筛选</span>
        </el-button>
      </div>
    </div>
    <div class="tw-flex tw-justify-end tw-w-full tw-mb-[8px]">
      <el-button :size="isMobile ? 'small' : 'default'" @click="exportXlsx"><el-icon :size="16">
        <SvgIcon name="download3" color="none"></SvgIcon></el-icon>
        <span>导出数据</span>
      </el-button>
    </div>
    <el-table
      :data="tableTempData"
      v-loading="loading3"
      :header-cell-style="tableHeaderStyle"
      :row-style="getRowStyle"
      @sort-change="handleSortChange"
      stripe
      class="tw-grow account-table"
      row-key="id"
    >
      <el-table-column property="account" label="账号" align="left" fixed="left" :min-width="isMobile ? 110 : 160" :formatter="formatterEmptyData">
        <template #default="{ row }">
          <div class="tw-truncate sm:tw-font-[600]" @click="goDetail(row)">
            {{ row.account }}
          </div>
        </template>
      </el-table-column>
      <el-table-column property="name" label="账号名称" align="left" :min-width="isMobile ? 120 : 160" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="tenantName" label="商户名称" align="left" :min-width="isMobile ? 120 : 160" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column property="tenantNo" label="商户编号" align="left" :min-width="isMobile ? 130 : 160" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column property="planedPhoneNum" label="导入名单" sortable="custom" align="left" :min-width="isMobile ? 100 : 120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber1(row.planedPhoneNum) }}
        </template>
      </el-table-column>
      <el-table-column property="calledPhoneNum" label="已呼叫名单" sortable="custom" align="left" :min-width="isMobile ? 100 : 120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber1(row.calledPhoneNum) }}
        </template>
      </el-table-column>
      <el-table-column property="calledPhoneRate" label="名单执行度" sortable="custom" align="left" :min-width="isMobile ? 100 : 120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.calledPhoneRate, 2) + '%'}}
        </template>
      </el-table-column>
      <el-table-column property="outboundCalledRate" label="外呼执行度" sortable="custom" align="left" :min-width="isMobile ? 160 : 200" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.outboundCalledRate ?  `${formatNumber(row.outboundCalledRate, 2)}%（${formatNumber1(row.outboundCalledNum)||'-'}/${formatNumber1(row.outboundTotalNum)||'-'}）` : 0 }}
        </template>
      </el-table-column>
      <el-table-column property="firstCallRemainNum" label="首呼剩余" sortable="custom" align="left" :min-width="isMobile ? 100 : 120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber1(row.firstCallRemainNum) }}
        </template>
      </el-table-column>
      <el-table-column property="recalledRemainNum" label="补呼剩余" sortable="custom" align="left" :min-width="isMobile ? 100 : 120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber1(row.recalledRemainNum) }}
        </template>
      </el-table-column>
      <el-table-column property="averageCallDurations" label="平均通时" sortable="custom" align="left" :min-width="isMobile ? 100 : 120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatDuration(row.averageCallDurations ? row.averageCallDurations/1000 : row.averageCallDurations)}}
        </template>
      </el-table-column>
      <el-table-column property="classANum" label="意向A" sortable="custom" align="left" :min-width="isMobile ? 100 : 120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ `${formatNumber(row.putThroughNum ? (row.classANum || 0)*100/row.putThroughNum : 0, 1)}% / ${formatNumber1(row.classANum)}` }}
        </template>
      </el-table-column>
      <el-table-column property="classBNum" label="意向B" sortable="custom" align="left" :min-width="isMobile ? 100 : 120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ `${formatNumber(row.putThroughNum ? (row.classBNum || 0)*100/row.putThroughNum : 0, 1)}% / ${formatNumber1(row.classBNum)}` }}
        </template>
      </el-table-column>
      <el-table-column property="classCNum" label="意向C" sortable="custom" align="left" :min-width="isMobile ? 100 : 120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ `${formatNumber(row.putThroughNum ? (row.classCNum || 0)*100/row.putThroughNum : 0, 1)}% / ${formatNumber1(row.classCNum)}` }}
        </template>
      </el-table-column>
      <el-table-column property="classDNum" label="意向D" sortable="custom" align="left" :min-width="isMobile ? 100 : 120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ `${formatNumber(row.putThroughNum ? (row.classDNum || 0)*100/row.putThroughNum : 0, 1)}% / ${formatNumber1(row.classDNum)}` }}
        </template>
      </el-table-column>
      <el-table-column property="calledNum" label="呼叫数" align="left" sortable="custom" :min-width="isMobile ? 100 : 120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber1(row.calledNum) }}
        </template>
      </el-table-column>
      <el-table-column property="putThroughNum" label="接通数" sortable="custom" align="left" :min-width="isMobile ? 100 : 120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber1(row.putThroughNum) }}
        </template>
      </el-table-column>
      <el-table-column property="putThroughRates" label="接通率" sortable="custom" align="left" :min-width="isMobile ? 100 : 120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.putThroughRates, 2) + '%'}}
        </template>
      </el-table-column>
      <el-table-column property="totalTasksNum" label="创建任务" align="left" sortable="custom" :min-width="isMobile ? 100 : 120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber1(row.totalTasksNum) }}
        </template>
      </el-table-column>
      <el-table-column property="executingTasksNum" label="进行中任务" align="left" sortable="custom" :min-width="isMobile ? 100 : 120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="!isMobile" label="操作" align="right" fixed="right" min-width="80">
        <template #default="{ row }">
          <el-button v-if="row.account !== '合计'" link @click="goDetail(row)" type="primary">详情</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      class="tw-flex-grow-0"
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
  
  <AccountDrawer
    :account="currentAccount?.account || ''"
    :groupId="currentAccount?.groupId || ''"
    v-model:visible="accountDrawerVisible"
  />
</template>

<script lang="ts" setup>
import { watch, computed, ref, reactive, defineAsyncComponent, onUnmounted } from 'vue'
import { CloseBold, CaretTop, CaretBottom, } from '@element-plus/icons-vue'
import { formatNumber, formatNumber1, formatDuration, formatterEmptyData, handleTableSort } from '@/utils/utils'
import PaginationBox from '@/components/PaginationBox.vue'
import to from 'await-to-js'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import { MonitorAccountItem } from '@/type/monitor-statistic'
import { tableHeaderStyle } from '@/assets/js/constant'
import { exportTable2Excel } from '@/utils/export'
import dayjs from 'dayjs'
import { useGlobalStore } from '@/store/globalInfo'
import { onBeforeRouteLeave } from 'vue-router'
import SelectBox from '@/components/SelectBox.vue'

const AccountDrawer = defineAsyncComponent({loader: () => {return import('./AccountDrawer.vue')}})

const props = defineProps<{
  needUpdate: boolean
}>();
const emits = defineEmits(['update:needUpdate',])

const globalInfo = useGlobalStore()
const isMobile = globalInfo.isMobile

/** 底部账号维度列表 开始 */
const loading3 = ref(false)
const tableData = ref<MonitorAccountItem[] | null>([]) // 全部数据
// 实际显示数据
const tableTempData = computed(() => {
  const data = handleTableSort(tableData.value || [], prop.value, order.value)
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})
const getRowStyle = ({row}: {row: MonitorAccountItem}) => {
  if (row.account === '合计') {
    return {
      fontWeight: 700,
    }
  } else {
    return null
  }
}

/** 搜索和接口数据获取 */
const searchForm = reactive<{
  tenantName?: string
  account?: string
}>({
  tenantName: undefined, // 商户名称
  account: undefined, // 商户账号
})
const search = async () => {
  loading3.value = true
  try {
    const [_, data] = await to(monitorStatisticModel.findMonitorAccountList(searchForm)) as [any, MonitorAccountItem[]]
    const totalInfo:MonitorAccountItem = {
      account: '合计',
      groupId: '',
      planedPhoneNum: 0,
      calledPhoneNum: 0,
      firstCallRemainNum: 0,
      recalledRemainNum: 0,
      intentionNums: 0,
      classANum: 0,
      classBNum: 0,
      classCNum: 0,
      classDNum: 0,
      calledNum: 0,
      outboundTotalNum: 0,
      outboundCalledNum: 0,
      putThroughNum: 0,
      totalTasksNum: 0,
      executingTasksNum: 0,
    };
    const data1 = (data || []).flatMap(item => {
      if (globalInfo.masterAccountGroups?.length && !globalInfo.masterAccountGroups?.includes(item.account)) {
        return []
      }
      totalInfo.planedPhoneNum = (totalInfo.planedPhoneNum||0) + (item.planedPhoneNum||0)
      totalInfo.calledPhoneNum = (totalInfo.calledPhoneNum||0) + (item.calledPhoneNum||0)
      totalInfo.firstCallRemainNum = (totalInfo.firstCallRemainNum||0) + (item.firstCallRemainNum||0)
      totalInfo.recalledRemainNum = (totalInfo.recalledRemainNum||0) + (item.recalledRemainNum||0)
      totalInfo.intentionNums = (totalInfo.intentionNums||0) + (item.intentionNums||0)
      totalInfo.classANum = (totalInfo.classANum||0) + (item.classANum||0)
      totalInfo.classBNum = (totalInfo.classBNum||0) + (item.classBNum||0)
      totalInfo.classCNum = (totalInfo.classCNum||0) + (item.classCNum||0)
      totalInfo.classDNum = (totalInfo.classDNum||0) + (item.classDNum||0)
      totalInfo.calledNum = (totalInfo.calledNum||0) + (item.calledNum||0)
      totalInfo.outboundTotalNum = (totalInfo.outboundTotalNum||0) + (item.outboundTotalNum||0)
      totalInfo.outboundCalledNum = (totalInfo.outboundCalledNum||0) + (item.outboundCalledNum||0)
      totalInfo.putThroughNum = (totalInfo.putThroughNum||0) + (item.putThroughNum||0)
      totalInfo.totalTasksNum = (totalInfo.totalTasksNum||0) + (item.totalTasksNum||0)
      totalInfo.executingTasksNum = (totalInfo.executingTasksNum||0) + (item.executingTasksNum||0)
      return [{
        ...item,
        outboundCalledRate: (item.outboundCalledNum && item.outboundTotalNum) ? item.outboundCalledNum*100/item.outboundTotalNum : 0,
        putThroughRates: (item.planedPhoneNum && item.calledNum) ? (item.putThroughNum||0)*100/item.calledNum : 0,
        calledPhoneRate: item.planedPhoneNum ? (item.calledPhoneNum||0)*100/item.planedPhoneNum : 0,
      }]
    })
    totalInfo.putThroughRates = (totalInfo.planedPhoneNum && totalInfo.calledNum) ? (totalInfo.putThroughNum||0)*100/totalInfo.calledNum : 0
    totalInfo.calledPhoneRate = (totalInfo.planedPhoneNum) ? (totalInfo.calledPhoneNum||0)*100/totalInfo.planedPhoneNum : 0
    totalInfo.outboundCalledRate = (totalInfo.outboundCalledNum && totalInfo.outboundTotalNum) ? totalInfo.outboundCalledNum*100/totalInfo.outboundTotalNum : 0,
    tableData.value = data1 && data1?.length > 0 ? [totalInfo, ...(data1 || [])] : data1
    total.value = tableData.value?.length || 0
  } catch(err){} finally {
    loading3.value = false
    emits('update:needUpdate', false)
  }
}

/** 分页 */ 
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(20)
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  search()
}

/** 排序 */
const prop = ref('')
const order = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  prop.value = params.prop
  order.value = params.order
}

/** 导出 */
const exportXlsx = () => {
  const dom = document.querySelector('.account-table')
  dom && exportTable2Excel(dom, `账号数据统计_${dayjs().format('YYYY-MM-DD')}.xlsx`)
}

/** 进入账号详情 */
const accountDrawerVisible = ref(false)
const currentAccount = ref<MonitorAccountItem | null>(null)
const goDetail = async (item: MonitorAccountItem) => {
  if (item.account && item.account !== '合计') {
    accountDrawerVisible.value =true
    currentAccount.value = item
  }
}

/** 底部账号维度列表 结束 */


// 进入该页面，读取缓存，并选择第一个选项进行搜索
const masterAccountList = ref<{
  account: string,
  groupId: string,
}[]>([]) // 主账号列表，全量
const getMasterAccountList = async () => {
  const [err, res] = await to(monitorStatisticModel.getAllMainAccount())
  masterAccountList.value = (res || []).filter(item => !!item.account && !!item.groupId)
}
const init = async () => {
  await getMasterAccountList()
  await search()
}
watch(() => props.needUpdate, n => {
  n && !accountDrawerVisible.value && init()
})
init()

onUnmounted(() => {
  tableData.value = null
})
onBeforeRouteLeave(() => {
  tableData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.el-table {
  font-size: var(--el-font-size-base);
  @media screen and (max-width: 600px) {
    font-size: 10px;
    .el-button {
      font-size: 10px
    }
  }

  :deep(.cell) {
    padding: 0 8px;
    @media screen and (max-width: 600px) {
      padding: 0 2px;
    }
  }
  :deep(.caret-wrapper) {
    display: none;
  }
}
.card-box {
  height: calc(100vh - 275px);
  @media screen and (max-width: 600px) {
    padding-left: 4px;
    padding-right: 4px;
  }
}
.title-normal {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-black-color-600);
  line-height: 20px;
  display: flex;
  align-items: center;
}
</style>