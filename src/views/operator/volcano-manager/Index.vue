<template>
  <HeaderBox title="火山运营" class="tw-grow-0 tw-shrink-0"/>
  <div class="module-container">
    <TabsBox v-model:active="activeTab" :tabList="tabList"></TabsBox>
    <Plan v-if="activeTab == tabList[0]" />
    <SmsTemplate v-else-if="activeTab == tabList[1]" />
    <TemplateBox v-else-if="activeTab == tabList[2]" />
    <Package v-else-if="activeTab == tabList[3]" />
  </div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, computed, ref, watch, } from 'vue'
import HeaderBox from '@/components/HeaderBox.vue'
import TabsBox from '@/components/TabsBox.vue'

const Plan = defineAsyncComponent({loader: () => import('./Plan.vue')})
const TemplateBox = defineAsyncComponent({loader: () => import('./Template.vue')})
const SmsTemplate = defineAsyncComponent({loader: () => import('./SmsTemplate.vue')})
const Package = defineAsyncComponent({loader: () => import('./Package.vue')})

// 路由
const tabList: string[] = ['营销计划列表', '火山短信模板列表', '白泽模板创建列表', '人群包列表',]

const activeTab = ref(tabList[0] || '')
</script>

<style scoped lang="postcss" type="text/postcss">
.module-container {
  width: 100%;
  min-width: 1080px;
}
.tab-box :deep(.normal-tab) {
  width: 140px;
}
</style>
