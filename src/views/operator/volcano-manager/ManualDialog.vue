<template>
  <el-dialog
    :model-value.sync="visible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">手动匹配</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="80px"
        ref="editRef"
      >
        <div class="info-title tw-text-left tw-pl-[12px] tw-text-[13px] tw-my-[12px]">
          <div>已选择
            <span class="tw-text-[var(--el-color-primary)]">【{{props.selectIds?.length||0}}条】</span>
            数据。
          </div>
          <div class="tw-text-[#E54B17]">1.该操作会根据所选模版重新创建任务，请确保模版准确无误</div>
          <div class="tw-text-[#E54B17] ">2.已创建的历史任务仍然存在，如需删除请在商户端操作</div>
        </div>
        
        <el-form-item label="任务模板：" prop="modelName">
          <el-select
            class="tw-w-full"
            filterable
            v-model="editData.modelName"
            placeholder="请选择任务模板"
          >
            <el-option v-for="item in templateOption" :key="item.id" :label="item.templateName" :value="item.templateName">
              <div class="tw-flex tw-justify-between">
                <span class="tw-text-[#313233]">{{ item.templateName }}</span>
                <span class="tw-text-[#969799]">{{ findValueInEnum(item.taskType, TaskTypeEnum) }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, watch, reactive,} from 'vue'
import type { FormInstance, } from 'element-plus'
import { ElMessage } from 'element-plus'
import { volcanoModel } from '@/api/volcano'
import { Select, Close, CloseBold } from '@element-plus/icons-vue'
import to from 'await-to-js'
import { traceApi } from '@/utils/trace';
import { TemplateBaseItem, TaskTypeEnum } from '@/type/task'
import { findValueInEnum } from '@/utils/utils';


const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean,
  selectIds?: number[],
}>();
const visible = ref(false)
const loading = ref(false)

class EditDataOrigin {
  modelName = undefined
  ids = []
}
const editData = reactive<{
  modelName?: string,
  ids?: number[],
}>(new EditDataOrigin())

const editRef = ref<FormInstance | null>(null)

const rules = {
  modelName: [
    { required: true, message: '请选择任务模板', trigger: 'change' },
  ],
}
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  emits('update:visible', false)
}


const confirm = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const err = await traceApi(
        `火山运营-重新执行-手动匹配`,
        editData,
        volcanoModel.manualBatch
      )
      loading.value = false
      if (!err) {
        ElMessage.success('操作成功')
        !err && emits('confirm')
        cancel()
      }
    }
  })
}

const templateOption = ref<TemplateBaseItem[] | null>([])
/** watch */ 
// 监听visible
watch(() => props.visible, async n => {
  visible.value = n
  if (n) {
    loading.value = true
    editData.ids = props.selectIds
    // 获取火山模板列表
    const [_, res] = await to(volcanoModel.findVolcanoTemplateList())
    templateOption.value = (res || []).map(item => ({templateName: item.templateName, id: item.id, taskType: item.taskType}))
    loading.value = false
  } else {
    Object.assign(editData, new EditDataOrigin())
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {

  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    .el-form-item__content {
      align-items: flex-start;
      justify-content: flex-start;
    }
    &:first-child {
      margin-top: 14px;
    }
    &.muti-items {
      margin-top: 0;
      width: 320px;
      &:last-child {
        margin-left: 20px;
      }
    }
  }
  .el-table {
    font-size: var(--el-font-size-base);
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>
