<template>
  <div class="sms-container">
    <div class="search-box">
      <div class="tw-grid tw-grid-cols-6 tw-gap-[8px]">
        <div class="item-col">
          <span class="label">执行状态：</span>
          <el-select
            v-model="searchForm.status"
            clearable
            placeholder="请选择执行状态"
            style="width: 100%;"
            @change="search()"
          >
            <el-option
              v-for="item in enum2Options(RecordStatusEnum)"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="item-col tw-col-span-2">
          <span class="label">执行时间：</span>
          <TimePickerBox
            v-model:start="searchForm.startTime"
            v-model:end="searchForm.endTime"
            placeholder="执行时间"
            separator="-"
            :maxRange="60*60*1000*24*3"
            :clearable="false"
            format="YYYY-MM-DD HH:mm:ss"
            @change="search()"
          />
        </div>
        <div class="item-btn">
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
      <div class="tw-flex tw-items-center tw-justify-end">
        <el-button @click="downloadTemplate">
          <el-icon :size="16" class="tw-mr-0.5">
            <SvgIcon name="download3" color="none"></SvgIcon>
          </el-icon>
         
          <span>下载导入模板</span>
        </el-button>
        <input ref="fileRef" type="file" @change="handleFileChange" accept=".xls,.xlsx" class="batch-upload tw-hidden"/>
        <el-button :loading="loading" @click="handleUpload" type="primary" class="tw-ml-1">
          <el-icon :size="16" class="tw-mr-0.5">
            <SvgIcon name="upload"></SvgIcon>
          </el-icon>
          <span>导入模板</span>
        </el-button>
      </div>
    </div>

    <el-table
      :data="tableTempData"
      v-loading="loading"
      class="tw-grow"
      row-key="id"
      :header-cell-style="tableHeaderStyle"
      @sort-change="handleSortChange"
      stripe
      border
    >
      <el-table-column property="account" label="所属账号" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="channelNumber" label="渠道号" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="messageSign" label="短信签名" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="volcanoSmsTemplate" label="火山短信模板" show-overflow-tooltip  align="left" min-width="240" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="updateTime" label="执行时间" align="center" width="160" sortable>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.updateTime ? dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="recordStatus" label="执行状态" align="center" width="120">
        <template #default="{ row }">
          <span
            v-if="row?.recordStatus"
            class="status-box-mini tw-mx-auto"
            :class="recordStatusClass[row.recordStatus as RecordStatusEnum] || 'blue-status'"
          >
            {{ findValueInEnum(row.recordStatus, RecordStatusEnum) || row.recordStatus }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
  <UploadDialog
    v-model:visible="uploadVisible"
    :uploadData="uploadData"
    @confirm="search"
  >
  </UploadDialog>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onUnmounted, onMounted, } from 'vue'
import { ElMessage, } from 'element-plus'
import dayjs from 'dayjs'
import PaginationBox from '@/components/PaginationBox.vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { enum2Options, findValueInEnum, formatterEmptyData, handleTableSort } from '@/utils/utils'
import { generateExcelByAoa, readXlsx } from '@/utils/export'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from 'await-to-js'
import { volcanoModel } from '@/api/volcano'
import { VolcanoTemplateLogItem, RecordStatusEnum } from '@/type/volcano'
import SvgIcon from '@/components/SvgIcon.vue'
import UploadDialog from './UploadDialog.vue'
import { recordStatusClass } from './constant'

const loading = ref(false)
const currentPage = ref(1)
const total = ref(0)
const pageSizeList = [20, 50, 100]
const pageSize = ref(pageSizeList[0])
const tableData = ref<VolcanoTemplateLogItem[] | null>([])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}

const orderCol = ref('')
const orderType = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  orderCol.value = params.prop
  orderType.value = params.order
}
const tableTempData = computed(() => {
  const data = handleTableSort(tableData.value || [], orderCol.value, orderType.value)
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

const searchForm = reactive<{
  status?: RecordStatusEnum,
  startTime?: string,
  endTime?: string
}>({
  status: undefined,
  startTime: dayjs().subtract(1, 'd').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
  endTime:  dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
})
const search = async () => {
  if (loading.value) return ElMessage.warning('请勿频繁操作')
  loading.value = true
  const res = await to(volcanoModel.findTemplateLogList(searchForm))
  orderCol.value = 'updateTime'
  orderType.value = 'descending'
  tableData.value = res[1] || []
  total.value = tableData.value?.length || 0
  loading.value = false
}

const fileRef = ref() // 上传文件
// 模板字段
const uploadTitleObject: Record<string, keyof VolcanoTemplateLogItem> = {
  '所属账号': 'account',
  '渠道号': 'channelNumber',
  '短信签名': 'messageSign',
  '火山短信模板': 'volcanoSmsTemplate',
}
const uploadData = ref<VolcanoTemplateLogItem[] | null>([])
const uploadVisible = ref(false)
const handleUpload = () => {
  // @ts-ignore
  document.querySelector('.batch-upload')!.click()
}
// 上传文件转化为表格数据
const handleFileChange = async (e: Event) => {
  loading.value = true
  // 读取xls
  const { data } = await readXlsx(e) as { data: Record<string, any>[] }
  if (!data?.length)  {
    loading.value = false
    return ElMessage.error('上传文件为空')
  }

  // xls数据转换为需要的数据
  const err: string[] = []
  const channelNumberSet = new Set()
  uploadData.value = data.map((item, index) => {
    const res: Partial<VolcanoTemplateLogItem> = {}
    // 读取当前行全部属性
    Object.keys(uploadTitleObject).forEach(title => {
      if (!item[title]) err.push(`第${index + 1}行：${title}不能为空`)
      res[uploadTitleObject[title]] = item[title] || undefined
    })
    // 校验渠道号重复
    if (item['渠道号'] && channelNumberSet.has(item['渠道号'])) {
      err.push(`第${index + 1}行：渠道号重复`)
    } else {
      item['渠道号'] && channelNumberSet.add(item['渠道号'])
    }
    return res as VolcanoTemplateLogItem
  }) || []
  // 清空set
  channelNumberSet.clear()
  // 清空已上传文件
  fileRef.value.value = null
  // 清除loading
  loading.value = false
  // 校验异常则不进入打开执行弹窗
  if (err.length) return ElMessage.warning(err.join('\n'))
  // 校验通过，打开执行弹窗
  uploadVisible.value = true
}

const downloadTemplate = async () => {
  generateExcelByAoa([
    Object.keys(uploadTitleObject)
  ], `火山上传模板.xlsx`, )
}

onMounted(() => {
  search()
})
onUnmounted(() => {
  tableData.value = null
})

</script>

<style scoped lang="postcss" type="text/postcss">
.sms-container {
  width: 100%;
  box-sizing: border-box;
  height: calc(100% - 48px);
  background-color: #fff;
  display: flex;
  flex-direction: column;
}
:deep(.el-table .caret-wrapper) {
  display: none;
}
.el-table {
  font-size: 13px;
  :deep(.cell) {
    padding: 0 8px;
  }
}
</style>
