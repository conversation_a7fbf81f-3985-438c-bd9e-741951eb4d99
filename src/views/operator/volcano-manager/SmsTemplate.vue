<template>
  <div class="sms-container">
    <div class="search-box">
      <div class="tw-grid tw-grid-cols-6 tw-gap-[8px]">
        <div class="item-col">
          <span class="label">执行状态：</span>
          <el-select
            v-model="searchForm.status"
            clearable
            placeholder="请选择执行状态"
            style="width: 100%;"
            @change="search()"
          >
            <el-option
              v-for="item in enum2Options(RecordStatusEnum)"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="item-col tw-col-span-2">
          <span class="label">执行时间：</span>
          <TimePickerBox
            v-model:start="searchForm.startTime"
            v-model:end="searchForm.endTime"
            placeholder="执行时间"
            separator="-"
            :maxRange="60*60*1000*24*7"
            :clearable="false"
            format="YYYY-MM-DD HH:mm:ss"
            @change="search()"
          />
        </div>
        <div class="item-btn">
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
    </div>
    <div class="tw-flex tw-items-center tw-justify-end tw-mb-[6px] tw-px-[12px]">
      <el-button @click="downloadTable">
        <el-icon :size="16" class="tw-mr-0.5">
          <SvgIcon name="download3" color="none"></SvgIcon>
        </el-icon>
        <span>导出表格</span>
      </el-button>
    </div>
    <el-table
      :data="tableTempData"
      v-loading="loading"
      class="tw-grow"
      row-key="id"
      :header-cell-style="tableHeaderStyle"
      @sort-change="handleSortChange"
      stripe
      border
    >
      <el-table-column property="channelNumber" label="渠道号" show-overflow-tooltip fixed="left" align="left" width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="marketingType" label="营销类型" align="center" width="80">
        <template #default="{ row }">
          {{ row.marketingType || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="shortLink" label="网申链接" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="smsSign" label="短信签名" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="smsContent" label="短信文案" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="volcanoAccount" label="火山账号" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="account" label="白泽账号" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="updateTime" label="执行时间" align="center" width="160" sortable>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.updateTime ? dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="updateBy" label="操作账号" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="recordStatus" label="火山模板执行状态" align="center" width="160">
        <template #default="{ row }">
          <span
            v-if="row?.recordStatus"
            class="status-box-mini tw-mx-auto"
            :class="recordStatusClass[row.recordStatus as RecordStatusEnum] || 'blue-status'"
          >
            {{ findValueInEnum(row.recordStatus, RecordStatusEnum) || row.recordStatus }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column property="volcanoTemplateId" label="火山回执" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>

      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onUnmounted, onMounted, } from 'vue'
import { ElMessage, } from 'element-plus'
import dayjs from 'dayjs'
import PaginationBox from '@/components/PaginationBox.vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { enum2Options, findValueInEnum, formatterEmptyData, handleTableSort } from '@/utils/utils'
import { exportExcel } from '@/utils/export'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from 'await-to-js'
import { volcanoModel } from '@/api/volcano'
import { ApplySmsTemplateItem, RecordStatusEnum } from '@/type/volcano'
import SvgIcon from '@/components/SvgIcon.vue'
import { recordStatusClass } from './constant'

const loading = ref(false)
const currentPage = ref(1)
const total = ref(0)
const pageSizeList = [20, 50, 100]
const pageSize = ref(pageSizeList[0])
const tableData = ref<ApplySmsTemplateItem[] | null>([])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}

const orderCol = ref('')
const orderType = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  orderCol.value = params.prop
  orderType.value = params.order
}
const tableTempData = computed(() => {
  const data = handleTableSort(tableData.value || [], orderCol.value, orderType.value)
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

const searchForm = reactive<{
  status?: RecordStatusEnum,
  startTime?: string,
  endTime?: string
}>({
  status: undefined,
  startTime: dayjs().subtract(1, 'd').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
  endTime:  dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
})
const search = async () => {
  if (loading.value) return ElMessage.warning('请勿频繁操作')
  loading.value = true
  const res = await to(volcanoModel.findApplySmsTemplateList(searchForm))
  orderCol.value = 'updateTime'
  orderType.value = 'descending'
  tableData.value = res[1] || []
  total.value = tableData.value?.length || 0
  loading.value = false
}

// 导出表格
const downloadTable = async () => {
  const data: any[] = tableData.value?.flatMap((item, index) => {
    return [{
      '渠道号': item.channelNumber || '',
      '营销类型': item.marketingType || '',
      '网申链接': item.shortLink || '',
      '短信签名': item.smsSign || '',
      '短信文案': item.smsContent || '',
      '火山账号': item.volcanoAccount || '',
      '白泽账号': item.account || '',
      '执行时间': item.updateTime ? dayjs(item.updateTime).format('YYYY-MM-DD HH:mm:ss') : '',
      '操作账号': item.updateBy || '',
      '火山模板执行状态': findValueInEnum(item.recordStatus, RecordStatusEnum) || item.recordStatus,
      '火山回执': item.volcanoTemplateId || '',
    }]
  }) || []
  if (!data || data?.length < 1) {
    loading.value = false
    return ElMessage.warning('无导入失败数据')
  }
  exportExcel(data,  `火山短信模板列表${searchForm.startTime}-${searchForm.endTime}.xlsx`)
}

onMounted(() => {
  search()
})
onUnmounted(() => {
  tableData.value = null
})

</script>

<style scoped lang="postcss" type="text/postcss">
.sms-container {
  width: 100%;
  box-sizing: border-box;
  height: calc(100% - 48px);
  background-color: #fff;
  display: flex;
  flex-direction: column;
}
:deep(.el-table .caret-wrapper) {
  display: none;
}
.el-table {
  font-size: 13px;
  :deep(.cell) {
    padding: 0 8px;
  }
}
</style>
