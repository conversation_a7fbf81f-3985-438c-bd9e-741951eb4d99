<template>
  <div class="search-box">
    <div class="tw-grid tw-grid-cols-6 sm:tw-grid-cols-2 tw-gap-[8px] tw-w-full">
      <div class="item">
        <el-input
          v-model="searchForm.identifier"
          placeholder="接口地址"
          clearable
          @keyup.enter="search()"
        >
        </el-input>
      </div>
      <div class="item">
        <el-select v-model="searchForm.type" placeholder="接口类型" class="tw-w-full" @change="search()" clearable>
          <el-option v-for="item in enum2Options(InterfaceTypeEnum)" :key="item.value" :label="item.name" :value="item.value"/>
        </el-select>
      </div>
      <div class="item">
        <el-select v-model="searchForm.unNormal" placeholder="是否只看异常" class="tw-w-full" @change="search()" clearable>
          <el-option label="只看异常" :value="true"/>
        </el-select>
      </div>
      <div class="item tw-col-span-2">
        <TimePickerBox
          v-model:start="searchForm.timeStart"
          v-model:end="searchForm.timeEnd"
          :disabledDate="disabledDate"
          :maxRange="60*60*24*1000*7"
          :clearable="false"
          @change="search"
        />
      </div>
      <div class="item-btn">
        <el-button type="primary" @click="search()" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>
  </div>

  <el-table
    :data="tableData"
    v-loading="loading"
    class="tw-grow"
    row-key="id"
    :header-cell-style="tableHeaderStyle"
    @sort-change="handleSortChange"
    stripe
  >
    <el-table-column property="realUrl" label="接口原始地址" align="left" min-width="360" :formatter="formatterEmptyData"></el-table-column>
    <el-table-column property="identifier" label="接口配置地址" align="left" min-width="360" :formatter="formatterEmptyData"></el-table-column>
    <el-table-column property="name" label="接口名称" align="left" width="200" :formatter="formatterEmptyData"></el-table-column>
    <el-table-column property="type" label="接口类型" align="center" width="120">
      <template #default="{row} : { row: InterfaceLogItem }">
        <span
          v-if="row?.interfacetype"
          class="status-box-mini tw-mx-auto"
          :class="typeClassObj[row.interfacetype] || 'blue-status'"
        >
        {{ findValueInEnum(row.interfacetype, InterfaceTypeEnum) || row.interfacetype }}
        </span>
        <span v-else>-</span>
      </template>
    </el-table-column>
    <el-table-column property="serviceId" label="服务名" align="left" width="120"></el-table-column>
    <el-table-column property="remark" label="备注" align="left" min-width="120" :formatter="formatterEmptyData"></el-table-column>
    <el-table-column property="createTime" label="创建时间" fixed="right" align="center" width="160" sortable>
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-center">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
      <template #default="{ row }">
        {{ row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
      </template>
    </el-table-column>
    <el-table-column property="ifPass" label="是否通过" fixed="right" align="center" width="120">
      <template #default="{row} : { row: InterfaceLogItem }">
        <span
          v-if="findValueInStatus(row.ifPass)"
          class="status-box-mini tw-mx-auto"
          :class="row.ifPass ? 'green-status' : 'red-status'"
        >
        {{ findValueInStatus(row.ifPass) }}
        </span>
        <span v-else>-</span>
      </template>
    </el-table-column>
    <template #empty>
      <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
    </template>
  </el-table>
  <PaginationBox
    :pageSize="searchForm.limit"
    :currentPage="searchForm.page"
    :pageSizeList="[20, 50, 100, 200]"
    :total="total"
    @search="search"
    @update="updateList"
  >
  </PaginationBox>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onUnmounted, onMounted, watch, } from 'vue'
import dayjs from 'dayjs'
import PaginationBox from '@/components/PaginationBox.vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { findValueInEnum, formatterEmptyData, enum2Options, findValueInStatus } from '@/utils/utils'
import { InterfaceLogItem, InterfaceTypeEnum, InterfaceLogSearchParams } from '@/type/authorization'
import { authorizationModel } from '@/api/authorization'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from 'await-to-js'
import { typeClassObj } from '../interface/constant'

const loading = ref(false)

const total = ref(0)
const tableData = ref<InterfaceLogItem[] | null>([])
const updateList = (p: number, s: number) => {
  searchForm.page = p
  searchForm.limit = s
  search()
}

const orderCol = ref('createTime')
const orderType = ref('desc')
const handleSortChange = (params: { prop: string, order: string }) => {
  orderCol.value = params.prop
  orderType.value = params.order
}

const searchForm = reactive<InterfaceLogSearchParams>({
  identifier: '',
  unNormal: undefined,
  type: undefined,
  page: 1,
  limit: 20,
  timeStart: dayjs().startOf('day').format('YYYY-MM-DDTHH:mm:ss[Z]'),
  timeEnd: dayjs().endOf('day').format('YYYY-MM-DDTHH:mm:ss[Z]'),
})

const search = async () => {
  loading.value = true
  const res = await to(authorizationModel.findInterfacePermissionLog({
    ...searchForm,
    timeStart: dayjs(searchForm.timeStart).format('YYYY-MM-DDTHH:mm:ss[Z]'),
    timeEnd: dayjs(searchForm.timeEnd).format('YYYY-MM-DDTHH:mm:ss[Z]'),
  }))
  tableData.value = (res[1]?.data || []) as InterfaceLogItem[]
  total.value = res[1]?.total || 0
  loading.value = false
}

const disabledDate = (time: Date) => {
  const _minTime = dayjs().add(-1, 'month').startOf('day').valueOf()
  const _maxTime = dayjs().endOf('day').valueOf()
  return time.getTime() > _maxTime || time.getTime() < _minTime
}

onMounted(() => {
  search()
})
onUnmounted(() => {
  tableData.value = null
})

</script>

<style scoped lang="postcss" type="text/postcss">
:deep(.el-table .caret-wrapper) {
  display: none;
}
.el-table {
  font-size: 13px;
  :deep(.cell) {
    padding: 0 8px;
  }
}
.status-box-mini {
  width: 80px;
}
</style>
