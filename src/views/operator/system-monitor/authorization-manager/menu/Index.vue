<template>
  <div class="search-box">
    <div class="tw-grid tw-grid-cols-4 sm:tw-grid-cols-2 tw-gap-[8px] tw-w-full">
      <div class="item">
        <el-input
          v-model.trim="searchForm.menuName"
          placeholder="菜单名称"
          clearable
          @keyup.enter="search()"
        >
        </el-input>
      </div>
      <div class="item">
        <el-input
          v-model.trim="searchForm.menuCode"
          placeholder="菜单编号"
          clearable
          @keyup.enter="search()"
        >
        </el-input>
      </div>
      <div class="item">
        <el-select v-model="searchForm.menuType" placeholder="菜单类型" class="tw-w-full" @change="search()" clearable>
          <el-option v-for="item in enum2Options(MenuTypeEnum)" :key="item.value" :label="item.name" :value="item.value"/>
        </el-select>
      </div>
      <div class="item-btn">
        <el-button type="primary" @click="search()" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>
  </div>
  <div class="tw-bg-white tw-px-[12px] tw-pb-[12px] tw-flex tw-justify-end">
    <el-button @click="editMenu()" type="primary">
      新增菜单
    </el-button>
    <el-button @click="initMenu()" type="primary">
      同步菜单
    </el-button>
    <el-button @click="deleteAll()" type="danger">
      清空菜单
    </el-button>
  </div>

  <el-table
    :data="tableTempData"
    v-loading="loading"
    class="tw-grow"
    row-key="id"
    :header-cell-style="tableHeaderStyle"
    @sort-change="handleSortChange"
    stripe
  >
    <el-table-column property="menuName" fixed="left" label="菜单名称" align="left" width="160" :formatter="formatterEmptyData"></el-table-column>
    <el-table-column property="menuCode" sortable="custom" label="菜单编号" align="left" width="120" :formatter="formatterEmptyData">
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-start">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column property="menuType" label="菜单类型" align="center" width="160">
      <template #default="{ row }">
        <span
          v-if="row?.menuType"
          class="status-box-mini tw-mx-auto"
          :class="row.menuType === MenuTypeEnum['商户端'] ? 'blue-status' : 'orange-status'"
        >
          {{ findValueInEnum(row.menuType, MenuTypeEnum) || row.menuType }}
        </span>
        <span v-else>-</span>
      </template>
    </el-table-column>
    <el-table-column property="description" label="菜单描述" align="left" min-width="240" :formatter="formatterEmptyData"></el-table-column>

    <el-table-column property="createTime" label="创建时间" align="center" width="160" sortable>
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-center">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
      <template #default="{ row }">
        {{ row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
      </template>
    </el-table-column>
    <el-table-column property="updateTime" label="更新时间" align="center" width="160" sortable :formatter="formatterEmptyData">
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-center">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
      <template #default="{ row }">
        {{ row.updateTime ? dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="180" align="right" fixed="right">
      <template #default="{ row }">
        <el-button type="primary" link @click="editMenu(row)">编辑菜单</el-button>
        <el-button type="primary" link @click="editInterface(row)">接口详情</el-button>
        <el-button type="danger" link @click="deleteMenu(row)">删除</el-button>
      </template>
    </el-table-column>
    <template #empty>
      <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
    </template>
  </el-table>
  <PaginationBox
    :pageSize="pageSize"
    :currentPage="currentPage"
    :total="total"
    @search="search"
    @update="updatePage"
  >
  </PaginationBox>
  <EditDialog
    v-model:visible="editVisible"
    :data="currentData"
    @confirm="search"
  >
  </EditDialog>
  <MenuInterfaceDrawer
    v-model:visible="interfaceVisible"
    :data="currentData"
    @go-next="goNext"
  />
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onUnmounted, onMounted, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import dayjs from 'dayjs'
import PaginationBox from '@/components/PaginationBox.vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { enum2Options, findValueInEnum, formatterEmptyData, handleTableSort } from '@/utils/utils'
import { MenuItem, MenuTypeEnum, } from '@/type/authorization'
import { authorizationModel } from '@/api/authorization'
import { tableHeaderStyle } from '@/assets/js/constant'
import routeMap from '@/router/asyncRoute/route-map'
import to from 'await-to-js'
import Confirm from '@/components/message-box'
import EditDialog from './EditDialog.vue'
import MenuInterfaceDrawer from './MenuInterfaceDrawer.vue'

const loading = ref(false)

const currentPage = ref(1)
const total = ref(0)
const pageSizeList = [20, 50, 100]
const pageSize = ref(pageSizeList[0])
const tableData = ref<MenuItem[] | null>([])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}

const orderCol = ref('createTime')
const orderType = ref('desc')
const handleSortChange = (params: { prop: string, order: string }) => {
  orderCol.value = params.prop
  orderType.value = params.order
}
const tableTempData = computed(() => {
  const data = handleTableSort(tableData.value || [], orderCol.value, orderType.value)
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

const searchForm = reactive<Partial<MenuItem>>({
  menuType: undefined,
  menuCode: '',
  menuName: '',
})

const search = async () => {
  loading.value = true
  const res = await to(authorizationModel.findMenuList())
  tableData.value = (res[1] || []).filter(item => 
    (!searchForm.menuType || item.menuType === searchForm.menuType)
    && (!searchForm.menuCode || item.menuCode === searchForm.menuCode)
    && (!searchForm.menuName || item.menuName.includes(searchForm.menuName))
  )
  total.value = tableData.value?.length || 0
  loading.value = false
}


const currentData = ref<MenuItem | null>(null)
const editVisible = ref(false)
// 编辑菜单
const editMenu = (data?: MenuItem) => {
  currentData.value = data || null
  editVisible.value = true
}
// 编辑菜单的接口
const interfaceVisible = ref(false)
const editInterface = async (data: MenuItem) => {
  currentData.value = data || null
  interfaceVisible.value = true
}

const goNext = (flag: -1 | 1) => {
  if (!tableData.value?.length) return
  const index = tableData.value.findIndex(v => v.id === currentData.value?.id)
  if (index === -1 || !tableData.value[index + flag]) return
  currentData.value = tableData.value[index + flag]
}


const deleteMenu = (row: MenuItem) => {
  if (!row.id) {
    return ElMessage.warning('菜单ID获取失败')
  }
  Confirm({
    text: `您确定要删除【${row?.menuName || ''}】?`,
    type: 'danger',
    title: `删除确认`,
  }).then(async () => {
    const [err] = await to(authorizationModel.deleteMenu({
      id: row.id!,
    }))
    !err && ElMessage.success('删除成功')
    search()
  }).catch(() => {
  })
}

// 将前端route-map菜单初始化，已有的菜单不再创建
const initMenu = async () => {
  const data: Partial<MenuItem>[] = []
  loading.value = true
  Object.keys(routeMap).forEach(item => {
    if (!tableData.value?.find(vv => vv.menuCode === routeMap[item]?.id) && !data.find(vv => vv.menuCode === routeMap[item]?.id)) {
      data.push({
        menuCode: routeMap[item]?.id,
        menuName: item,
        menuType: routeMap[item]?.isMerchant ? MenuTypeEnum['商户端'] : MenuTypeEnum['运营端'],
        description: item,
      })
    }
    if (routeMap[item].permissions && Object.keys(routeMap[item].permissions).length) {
      data.push(...Object.entries(routeMap[item].permissions).flatMap(v => {
        if (tableData.value?.find(vv => vv.menuCode === v[1]) || data?.find(vv => vv.menuCode === v[1])) return []
        return [
          {
            menuCode: v[1],
            menuName: v[0],
            menuType: routeMap[item]?.isMerchant ? MenuTypeEnum['商户端'] : MenuTypeEnum['运营端'],
            description: item + '-' + v[0],
          }
        ]
      }))
    }
  })

  const newInfo = data.map(item => item.menuName + '【' + item.menuCode + '】').join(',')
  Confirm({
    text: `您即将同步${data.length}个新菜单：${newInfo}?`,
    type: 'warning',
    title: `同步确认`,
  }).then(async () => {
    for(let i = 0; i < data.length; i++) {
      await to(authorizationModel.saveMenu(data[i]))
    }
    ElMessage.success(`同步成功， 成功添加【${data.length}】个菜单：${newInfo}`)
    search()
  }).catch(() => {}).finally(() => {
    loading.value = false
  })
  
}

const deleteAll = async () => {
  loading.value = true
  await Promise.allSettled(tableData.value?.map(async item => {
    await to(authorizationModel.deleteMenu({
      id: item.id!
    }))
  }) || [])
  search()
  loading.value = false
}

onMounted(() => {
  search()
})
onUnmounted(() => {
  tableData.value = null
})

</script>

<style scoped lang="postcss" type="text/postcss">
:deep(.el-table .caret-wrapper) {
  display: none;
}
.el-table {
  font-size: 13px;
  :deep(.cell) {
    padding: 0 8px;
  }
}
</style>
