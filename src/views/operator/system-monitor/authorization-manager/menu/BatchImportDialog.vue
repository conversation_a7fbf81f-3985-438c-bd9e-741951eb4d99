<template>
  <el-dialog
    :model-value="visible"
    width="640px"
    class="dialog-form"
    :close-on-click-modal="false"
    align-center
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">批量导入接口</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="90px"
        ref="editRef"
      >
        <el-form-item label="菜单名称：" prop="name">
          <span class="info-title">{{ props.data?.menuName || '' }}</span>
        </el-form-item>
        <el-form-item label="菜单编号：" prop="name">
          <span class="info-title">{{ props.data?.menuCode || '' }}</span>
        </el-form-item>
        <el-form-item label="导入类型：" prop="type">
          <el-radio-group v-model="editData.type" class="tw-ml-[6px]">
            <el-radio :label="0">增加</el-radio>
            <el-radio :label="1">覆盖</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="导入接口：" prop="apiStr">
          <el-input v-model.trim="editData.apiStr"  :autosize="{ minRows: 4 }"  type="textarea" auto clearable placeholder="请输入接口，多个接口用英文逗号分隔"></el-input>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <div>
          <el-button @click="cancel" :icon="CloseBold">取消</el-button>
          <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
        </div>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, } from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import type { FormInstance, } from 'element-plus'
import { MenuItem } from '@/type/authorization'

const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean,
  data: MenuItem | null
}>();
const loading = ref(false)
const visible = ref(props.visible)
const editData = reactive<{
  type: number,
  apiStr?: string
}>({
  type: 0,
  apiStr: '',
})
const editRef = ref<FormInstance  | null>(null)
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  emits('update:visible', false)
}

const rules = {
  type: [{ required: true, message: '请选择导入类型', trigger: 'change' }],
  apiStr: [{ required: true, message: '请输入导入接口', trigger: 'blur' }],
}

const confirm = () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      cancel()
      emits('confirm', editData)
    }
  })
}

watch(() => props.visible, n => {
  visible.value = n
  if (n) {
    editData.type = 0
    editData.apiStr = ''
    editRef.value?.clearValidate()
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>