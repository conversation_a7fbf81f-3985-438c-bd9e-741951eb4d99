<template>
  <HeaderBox title="权限管理" class="tw-grow-0 tw-shrink-0"/>
  <div class="module-container">
    <TabsBox v-model:active="activeTab" :tabList="tabList"></TabsBox>
    <MenuList v-if="activeTab==tabList[0]"/>
    <InterfaceList v-else-if="activeTab==tabList[1]"/>
    <InterfaceLogList v-else-if="activeTab==tabList[2]"/>
  </div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, computed, ref, watch, } from 'vue'
import { useRoute } from 'vue-router'
import HeaderBox from '@/components/HeaderBox.vue'
import TabsBox from '@/components/TabsBox.vue'
const MenuList = defineAsyncComponent({loader: () => import('./menu/Index.vue')})
const InterfaceList = defineAsyncComponent({loader: () => import('./interface/Index.vue')})
const InterfaceLogList = defineAsyncComponent({loader: () => import('./log/Index.vue')})
// 路由
const tabList: string[] = ['菜单', '接口', '日志']

const activeTab = ref(tabList[0] || '')
</script>

<style scoped lang="postcss" type="text/postcss">
.module-container {
  width: 100%;
  min-width: 1080px;
}
</style>
