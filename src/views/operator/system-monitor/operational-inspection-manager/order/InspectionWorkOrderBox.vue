<template>
  <div class="tw-flex tw-flex-col tw-grow dialog-form tw-h-full">
    <el-scrollbar class="tw-grow tw-shrink" wrap-class="tw-pb-1">
      <el-form
        :model="editData"
        :rules="rules"
        :disabled="props.readonly"
        label-width="80px"
        ref="editRef"
      >
        <el-form-item label="工单ID：" prop="id">
          <span class="info-title">{{ editData.id || '-' }}</span>
        </el-form-item>
        <el-form-item label="问题类型：" prop="type">
          <span class="info-title">{{ findValueInEnum( editData.type, InspecteTypeEnum) || '-' }}</span>
        </el-form-item>
        <el-form-item label="问题描述：" prop="description">
          <span class="info-title">{{ editData.description || '-' }}</span>
        </el-form-item>
        <el-form-item label="提交人：" prop="commitUser">
          <span class="info-title">{{ editData.commitUser || '-' }}</span>
        </el-form-item>
        <el-form-item label="提交时间：" prop="createTime">
          <span class="info-title">{{ editData.createTime || '-' }}</span>
        </el-form-item>
        <el-form-item label="负责人：" prop="principal">
          <span class="info-title">{{ editData.principal || '-' }}</span>
        </el-form-item>
        <el-form-item label="处理人：" prop="handleUser">
          <span class="info-title">{{ editData.handleUser || '-' }}</span>
        </el-form-item>
        <el-form-item label="处理时间：" prop="handleTime">
          <span class="info-title">{{ editData.handleTime || '-' }}</span>
        </el-form-item>
        <el-form-item label="处理状态：" prop="status">
          <el-select
            v-model="editData.status"
            :disabled="props.readonly"
            filterable
            placeholder="请选择处理状态"
            style="width: 100%;"
          >
            <el-option
              v-for="item in enum2Options(InspecteStatusEnum)"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处理备注：" prop="comment">
          <el-input
            :disabled="props.readonly"
            v-model="editData.comment"
            :autosize="{minRows: 5}"
            type="textarea"
            show-word-limit
            clearable
            placeholder="请输入处理备注"
            :maxlength="200">
          </el-input>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div v-if="!props.readonly" class="tw-flex tw-w-full tw-flex-col tw-shrink-0 tw-grow-0 tw-pt-[8px] tw-border-t-[1px]">
      <el-button @click="submit(true)" class="tw-mb-[8px]" :loading="loading" type="primary">提交并下一个</el-button>
      <el-button @click="submit()" class="tw-mb-[8px]" :loading="loading" type="primary">提交</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, watch, defineAsyncComponent, onUnmounted } from 'vue'
import { pickAttrFromObj, enum2Options, findValueInEnum } from '@/utils/utils'
import { useUserStore } from '@/store/user'
import { ElMessage } from 'element-plus'
import { aiTeamModel, } from '@/api/user'
import { onBeforeRouteLeave } from 'vue-router'
import {  InspecteTypeEnum, InspecteStatusEnum, InspecteOrderItem, InspecteOrderItemOrigin } from '@/type/Inspection'
import { AccountItem } from '@/type/user'
import type { FormInstance } from 'element-plus'
import { inspectionModel } from '@/api/Inspection'
import to from 'await-to-js'

// 组件入参props
const props = defineProps<{
  data?: InspecteOrderItem | null,
  readonly?: boolean,
}>()
const emits = defineEmits(['close', 'confirm',])


const loading = ref(false)

const editData = reactive<Partial<InspecteOrderItem>>(new InspecteOrderItemOrigin())
const editRef = ref<FormInstance | null>(null)
const rules = {
  status: [
    { required: true, message: '请选择工单处理状态', trigger: 'change' },
  ],
  comment: [
    { required: true, message: '请输入处理备注', trigger: 'blur' },
  ],
}

const submit = (goNext: boolean = false) => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const userInfo = useUserStore()
      editData.handleUser = userInfo.account
      const [err, res] = await to(inspectionModel.handleInspectionWorkOrder(editData))
      loading.value = false
      if (!err) {
        ElMessage.success('操作成功')
        emits('confirm', res, goNext)
        emits('close')
      }
    }
  })
}

const cancel = () => {
  Object.assign(editData, JSON.parse(JSON.stringify(props.data)))
  emits('close')
}

const accountList = ref<AccountItem[] | null>(null)
const init = async () => {
  loading.value = true
  accountList.value = await aiTeamModel.getMerchantAccountList({}) as AccountItem[] || []
  loading.value = false
}
init()

/** watch开始 */
// 监听入参，更新选中数据和选项变化
watch(() => props.data?.id, () => {
  if (props.data?.id) {
    Object.assign(editData, JSON.parse(JSON.stringify(props.data)))
  }
}, { immediate: true })

const clearAllData = () => {
  accountList.value = null
}

onUnmounted(() => {
  clearAllData()
})

onBeforeRouteLeave(() => {
  clearAllData()
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.dialog-form {
  .el-form-item {
    margin-bottom: 12px;
    &:first-child {
      margin-top: 8px;
    }
    .el-input__wrapper {
      width: 100%
    }
  }
}
.el-button+.el-button {
  margin-left: 0;
}
</style>
