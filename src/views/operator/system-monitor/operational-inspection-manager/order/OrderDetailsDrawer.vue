<template>
  <el-drawer
    v-model="visible"
    :before-close="closeDetails"
    :size="drawerWidth()"
    :with-header="false"
  >
    <div class="tw-bg-white tw-h-[50px] tw-px-[12px] tw-border-b-[1px] tw-text-[16px] tw-font-semibold tw-text-left tw-text-[#313233] tw-flex tw-items-center tw-justify-between">
      <span>巡检记录详情</span>
      <el-button link @click="closeDetails">
        <el-icon :size="20" color="var(--primary-black-color-400)"><CloseBold /></el-icon>
      </el-button>
    </div>
    <div class="record-detail-container" v-loading="loading">
      <div class="left">
        <InspectionWorkOrderBox
          class="tw-grow tw-shrink"
          :data="props.tableData[currentIndex]"
          :readonly="inspectionWorkOrderStatus==0 || !!props.readonly"
          @confirm="confirmInspectionWorkOrder"
          @close="inspectionWorkOrderStatus = 0"
        />
        <div v-if="inspectionWorkOrderStatus == 0" class="tw-grow-0 tw-shrink-0 tw-w-full tw-flex tw-items-center tw-justify-end tw-px-[8px] tw-relative">
          <div  v-if="!props.readonly" class="tw-grow tw-flex">
            <el-button type="primary" size="small" @click="inspectionWorkOrderStatus = 1">处理工单</el-button>
          </div>
          <div>
            <el-button type="primary" link @click="handleAudioChange(-1)" :disabled="(searchForm.page || 1) * (searchForm.limit || 0) + currentIndex <= 0">上一条</el-button>
            <span class="tw-mx-1">{{ `${((searchForm.page || 1) - 1) * (searchForm.limit || 0) + currentIndex + 1} / ${total === 10000 ? '总数获取中…': total}` }}</span>
            <el-button type="primary" link @click="handleAudioChange(1)" :disabled="((searchForm.page || 1) - 1) * (searchForm.limit || 0) + currentIndex + 1 >= total">下一条</el-button>
          </div>
        </div>
      </div>
      <div class="left">
        <CallRecordInfoBox
          :record-data="recordData!"
          :recordType="props.tableData[currentIndex]?.phoneType"
        />
      </div>
      <div class="right" v-loading="dialogLoading">
        <div class="tw-flex tw-items-center tw-p-[16px] tw-pt-0">
          <span class="tw-mr-1">通话录音</span>
          <AudioMode
            v-if="recordData && recordData.callId && recordData.wholeAudioFileUrl"
            :audioUrl="recordData.wholeAudioFileUrl || ''"
            :audioName="recordData.taskName || '未知任务'"
            v-model:audioStatus="audioStatus"
            v-model:audioVolume="audioVolume"
          >
          </AudioMode>
        </div>
        <CallRecordDialogBoxNew
          v-model:clearAudio="clearAudio"
          v-model:needUpdate="needUpdate"
          :isTransfer="recordData?.isTransToCallSeat || false"
          :client-name="recordData?.name||''"
          :startEndInfo="startEndInfo"
          :info-query-map="infoQueryMap"
          :dataList="rowDialogData || []"
        />
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, defineAsyncComponent, onUnmounted } from 'vue'
import { TaskCallRecordItem, HangupEnum, RecordTypeEnum, CallStatusEnum } from '@/type/task'
import { InspecteOrderItem, InspecteOrderSearchParams } from '@/type/Inspection' 
import { aiOutboundTaskModel, } from '@/api/ai-report'
import { RecordDialogueData, TaskCallSearchModal } from '@/type/task'
import { CloseBold, CaretLeft } from '@element-plus/icons-vue'
import { findValueInEnum, } from '@/utils/utils'
import { InfoQueryItem, } from '@/type/speech-craft'
import { ElMessage } from 'element-plus'
import { scriptInfoModel } from '@/api/speech-craft'
import to from 'await-to-js';
import { inspectionModel, } from '@/api/Inspection'

const CallRecordDialogBoxNew = defineAsyncComponent({loader: () => import('@/components/record/CallRecordDialogBoxNew.vue')})
const CallRecordInfoBox = defineAsyncComponent({loader: () => import('@/components/record/CallRecordInfoBox.vue')})
const AudioMode = defineAsyncComponent({loader: () => import('@/components/AudioMode.vue')})
const InspectionWorkOrderBox = defineAsyncComponent({loader: () => import('./InspectionWorkOrderBox.vue')})

const props = withDefaults(defineProps<{
  visible: boolean; // 抽屉的可见性
  tableData: InspecteOrderItem[]; // 当前页的通话记录
  currentIndex: number; // 当前通话记录在当前页的通话记录的index
  total: number; // 总通话记录数（不是当页）
  searchForm: Partial<InspecteOrderSearchParams>; // 搜索条件，用于翻页
  readonly?: boolean;
}>(), {
  readonly: true
})

const emits = defineEmits(['close', 'update:visible', 'update:currentIndex', 'update:record'])

// 读取props数据，并用监听器监听变化
const visible = ref(props.visible || false)

// 外部巡检工单信息
const searchForm = reactive<Partial<InspecteOrderSearchParams>>(JSON.parse(JSON.stringify(props.searchForm)))
const currentIndex = ref<number>(props.currentIndex || 0)
const total = ref(props.total || 0)

const loading = ref(false)

const clearAudio = ref(false) // 对于一些操作，需要是的对话组件的音频清空
/** 左侧 */
const recordData = ref<TaskCallRecordItem | null>(null) // 左侧通话记录数据
const audioStatus = ref<'pause' | 'play' | 'none'>('pause') // 左侧通话记录音频播放状态
const audioVolume = ref<number>(70) // 左侧通话记录音频声音
const needUpdate = ref(false) // 通话记录对话详情是否需要更新

/** 刷新工单列表 */
const updateOrderListData = async () => {
  loading.value = true
  const [_, res] = await to(inspectionModel.findInspectionWorkOrder(searchForm))
  total.value = res?.total || 0
  emits('update:record', searchForm, currentIndex.value, total.value, res?.data as InspecteOrderItem[] || [])
  loading.value = false
}

const getRecordData = async () => {
  if (!props.tableData[currentIndex.value] || !props.tableData[currentIndex.value].callRecordId) return ElMessage.warning('无法获取到recordId！')
  // 获取通话记录
  const [_, res] = await to(inspectionModel.findCallRecordByRecordId({recordId: props.tableData[currentIndex.value].callRecordId || ''}))
  const recordObj = {
    [RecordTypeEnum['AI外呼']]: 'callRecord',
    [RecordTypeEnum['人工直呼']]: 'callRecordForManualDirect',
    [RecordTypeEnum['人机协同']]: 'singleHumanMachineRecord',
  }
  recordData.value = res ? res[recordObj[props.tableData[currentIndex.value].phoneType]] || null : null

  // 获取通话记录-对话详情
  const callId = recordData.value?.callId || ''
  if (!callId) {
    rowDialogData.value = []
    needUpdate.value = true
    return ElMessage.warning('无法获取到callId！')
  }
  dialogLoading.value = true
  switch(props.tableData[currentIndex.value].phoneType) {
    case RecordTypeEnum['AI外呼']: {
      const [_, data] = await to(aiOutboundTaskModel.getAiDialogueDataList({
        callId: callId,
        recordId: recordData.value?.recordId || '',
      }))
      rowDialogData.value = data || []
      break;
    }
    case RecordTypeEnum['人工直呼']: {
      const [_, data] = await to(aiOutboundTaskModel.getManualDialogueDataList({
        callId: callId,
        recordId: recordData.value?.recordId || '',
      }))
      rowDialogData.value = data || []
      break;
    }
    case RecordTypeEnum['人机协同']: {
      const [_, data] = await to(aiOutboundTaskModel.getMixDialogueDataList({
        callId: callId,
        recordId: recordData.value?.recordId || '',
      }))
      rowDialogData.value = data || []
      break;
    }
  }
  if (props.tableData[currentIndex.value].phoneType && [RecordTypeEnum['AI外呼'], RecordTypeEnum['人机协同']].includes(props.tableData[currentIndex.value].phoneType)) {
    const [err, res] = await to(scriptInfoModel.findInfoQueryList({id: recordData.value?.speechCraftId!})) as [any, InfoQueryItem[]]
    infoQueryMap.value = new Map([])
    res?.map(item => {
      infoQueryMap.value.set(item.infoFieldName, item)
    })
  }
  dialogLoading.value = false
  needUpdate.value = true
}


// 左侧通话记录切换
const handleAudioChange = async (flag: -1 | 1) => {
  if (!props.tableData) return
  audioStatus.value = 'pause'
  clearAudio.value = true
  const index2 = currentIndex.value + flag
  if (props.tableData[index2]) {
    currentIndex.value = index2
    getRecordData()
    emits('update:currentIndex', currentIndex.value)
  } else {
    if ((flag > 0 && searchForm.limit! > props.tableData?.length) || (flag < 0 && searchForm.page! <= 1)) {
      currentIndex.value = index2 as number
      ElMessage.warning('无新的巡检工单')
      if (currentIndex.value <= 0 && flag === -1) {
        handleAudioChange(1)
      }
      if (currentIndex.value >= props.tableData?.length && flag === 1) {
        handleAudioChange(-1)
      }
    } else {
      loading.value = true
      currentIndex.value = (flag > 0) ? 0 : ((searchForm.limit || 20) - 1)
      searchForm.page = (searchForm.page || 1) + flag
       await updateOrderListData()
      if (props.tableData?.length <= 0) {
        (flag > 0) ? handleAudioChange(-1) : handleAudioChange(1)
        ElMessage.warning('无新的巡检工单')
      }
      loading.value = false
    }
  }
}
// 快捷键触发通话记录切换
const handleKeyup = (e: {code: string, preventDefault: Function}) => {
  if (['ArrowRight',].includes(e.code)) {
    handleAudioChange(1)
    e.preventDefault();
  }
  if (['ArrowLeft',].includes(e.code)) {
    handleAudioChange(-1)
    e.preventDefault();
  }
  if (['ArrowDown'].includes(e.code)) {
    audioVolume.value = audioVolume.value - 10 < 0 ? 0 : audioVolume.value - 10
    e.preventDefault();
  }
  if (['ArrowUp'].includes(e.code)) {
    audioVolume.value = audioVolume.value + 10 > 100 ? 100 : audioVolume.value + 10
    e.preventDefault();
  }
  if (['Space',].includes(e.code)) {
    audioStatus.value =  audioStatus.value === 'play' ? 'pause' : 'play'
    e.preventDefault();
  }
}

// 右侧机器人用户对话
const rowDialogData = ref<RecordDialogueData[] | null>([]) // 数据列表
const dialogLoading = ref(false) // 针对右侧对话部分的的loading
const infoQueryMap = ref<Map<string, InfoQueryItem>>(new Map([]))
const startEndInfo = computed(() => {
  const res = [] as {dialogTime: string, content: string}[]
  if (recordData.value?.talkTimeStart) {
    res.push({ dialogTime: recordData.value?.talkTimeStart, content: '电话接通'})
  }
  if (recordData.value?.startPopWinTime) {
    res.push({ dialogTime: recordData.value?.startPopWinTime, content: '触发转人工'})
  }
  if (recordData.value?.startMonitorTime) {
    res.push({ dialogTime: recordData.value?.startMonitorTime, content: '坐席开始监听'})
  }
  if (recordData.value?.endMonitorTime) {
    res.push({ dialogTime: recordData.value?.endMonitorTime, content: '坐席结束监听'})
  }
  if (recordData.value?.startAnswerTime) {
    res.push({ dialogTime: recordData.value?.startAnswerTime, content: '坐席开始接听'})
  }
  if (recordData.value?.endAnswerTime) {
    res.push({ dialogTime: recordData.value?.endAnswerTime, content: '坐席结束接听'})
  }
  if (recordData.value?.talkTimeEnd) {
    res.push({ dialogTime: recordData.value?.talkTimeEnd, content: (findValueInEnum(recordData.value?.whoHangup, HangupEnum) || '')+ '挂断通话'})
  }
  return res
})

/** 查看巡检工单 */
const inspectionWorkOrderStatus = ref(0) // 0：查看，1：编辑
// 处理完成工单
const confirmInspectionWorkOrder = async (data: InspecteOrderItem, goNext: boolean = false) => {
  props.tableData[currentIndex.value] = data
  emits('update:record', searchForm, currentIndex.value, total.value, props.tableData)
  if (goNext) {
    handleAudioChange(1)
  }
}

// 关闭抽屉
const closeDetails = () => {
  emits('update:visible', false)
}

// 详情抽屉宽度
const drawerWidth = () => {
  return window.innerWidth > 1400 ? '80%' : '900px'
}

// watch
watch(() => props.visible, async n => {
  visible.value = n
  if (n) {
    currentIndex.value = props.currentIndex
    total.value = props.total
    Object.assign(searchForm, props.searchForm)
    await getRecordData()
    inspectionWorkOrderStatus.value = 0
    document.addEventListener('keyup', handleKeyup)
  } else {
    clearAllData()
  }
})

const clearAllData = () => {
  document.removeEventListener('keyup', handleKeyup);
  recordData.value = null
  rowDialogData.value = null
}

onUnmounted(() => {
  clearAllData()
})
</script>

<style lang="postcss" scoped type="text/postcss">
.record-detail-container {
  height: calc(100% - 50px);
  background-color: #fff;
  box-sizing: border-box;
  font-size: 13px;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: row;
  .order-btn {
    position: absolute;
    left: 8px;
    top: 2px;
    width: 60px;
    height: 20px;
    font-size: 12px;
    margin-right: 12px;
  }
  .label {
    color: #646566;
    text-align: right;
    min-width: 75px;
    display: inline-block;
    margin-bottom: 2px;
    flex-grow: 0;
    flex-shrink: 0;
  }
  .left {
    padding: 0 8px 12px;
    width: 284px;
    overflow: hidden;
    height: 100%;
    flex-shrink: 0;
    flex-grow: 1;
    box-sizing: border-box;
    border-right: 1px solid #ebeef5;
    display: flex;
    flex-direction: column;
  }
  .right {
    flex-grow: 4;
    flex-shrink: 1;
    width: 900px;
    text-align: center;
    display: flex;
    flex-direction: column;

  }
}

</style>
