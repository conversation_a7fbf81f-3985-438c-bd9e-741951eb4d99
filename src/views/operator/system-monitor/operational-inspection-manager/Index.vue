<template>
  <HeaderBox title="业务巡检" class="tw-grow-0 tw-shrink-0"/>
  <div class="line-container">
    <TabsBox v-model:active="activeTab" :tabList="tabList"></TabsBox>
    <keep-alive>
      <Record v-if="activeTab == '巡检记录'"/>
      <Order v-else-if="activeTab == '巡检工单'"/>
      <el-empty v-else/>
    </keep-alive>
   
  </div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, computed, ref, watch, onDeactivated } from 'vue'
import { useRoute } from 'vue-router'
import HeaderBox from '@/components/HeaderBox.vue'
import TabsBox from '@/components/TabsBox.vue'
import { useUserStore } from "@/store/user";
import routeMap from '@/router/asyncRoute/route-map'
import { onBeforeRouteLeave } from 'vue-router'

const Record = defineAsyncComponent({loader: () => import('./record/Index.vue')})
const Order = defineAsyncComponent({loader: () => import('./order/Index.vue')})

// 用户权限获取
// const userStore = useUserStore();
// const permissions = userStore.permissions[routeMap['业务巡检'].id]

// 路由
const route = useRoute();
const tabMap = {
  1: '巡检记录',
  2: '巡检工单',
}
const tabList = computed(() => {
  const res: string[] = ['巡检记录', '巡检工单']
  // permissions.includes(routeMap['业务巡检'].permissions['巡检记录']) && res.push('巡检记录')
  // permissions.includes(routeMap['业务巡检'].permissions['巡检工单']) && res.push('巡检工单')
  return res
})

const activeTab = ref(tabList.value[0] || '')
watch(() => route.query.activeTab, () => {
  const t = route.query.activeTab as unknown as keyof typeof tabMap
  if (route.query.activeTab) {
    activeTab.value = tabMap[t]
  }
}, {
  deep: true, immediate: true
})
</script>

<style scoped lang="postcss" type="text/postcss">
.line-container {
  width: 100%;
  box-sizing: border-box;
  min-width: 1080px;
  padding: 16px;
  height: calc(100vh - 55px);
  position: relative;
  font-size: 13px;
}
</style>