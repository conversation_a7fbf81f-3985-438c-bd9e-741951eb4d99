<template>
  <div class="card-box tw-flex-col tw-h-[calc(100%-32px)]">
    <div class="title-normal tw-self-start tw-mb-[16px]">
      <span>FS数据统计</span>
    </div>
    <div class="search-box tw-items-center tw-w-full">
      <div class="tw-grid tw-grid-cols-5 tw-gap-[8px] tw-w-full sm:tw-grow">
        <div class="item">
          <el-input
            v-model="searchForm.fsIp"
            placeholder="FS地址"
            @keyup.enter="search()"
            clearable
          >
          </el-input>
        </div>
        <div class="item">
          <el-select v-model="searchForm.ifRecall" clearable placeholder="请选择是否补呼" filterable @change="search()">
            <el-option v-for="item in ifRecallList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </div>
        <div class="item tw-col-span-2">
          <TimePickerBox
            v-model:start="searchForm.startTime"
            v-model:end="searchForm.endTime"
            :clearable="false"
            :maxRange="60*60*1000*24"
          />
        </div>
        <div class="item">
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
    </div>
    <div class="tw-flex tw-justify-between tw-w-full tw-mb-[6px]">
      <div class="tw-flex tw-items-center">
        <ColumnSetting
          :totalList="totalCols"
          :defaultList="defaultCols"
          :disabledList="disabledCols"
          :name="'fs'"
        />
        <div class="tw-text-[13px] tw-font-[600] tw-text-black">
          FS总数：{{total}}
        </div>
      </div>
      
      <el-button type="default" @click="search(true)">刷新数据</el-button>
    </div>
    <el-table
      :data="tableTempData"
      v-loading="loading3"
      :header-cell-style="tableHeaderStyle"
      :row-style="getRowStyle"
      @sort-change="handleSortChange"
      stripe
      class="tw-grow"
      row-key="id"
    >
      <el-table-column property="num" label="编号" align="left" fixed="left" :min-width="isMobile ? 40 : 60" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="fsIp" label="FS地址" align="left" fixed="left" :min-width="isMobile ? 120 : 160" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="calledCount" label="外呼量" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.calledCount) }}
        </template>
      </el-table-column>
      <el-table-column property="calledThroughCount" label="呼通量" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.calledThroughCount) }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('通话时长')" property="callDurationSec" label="通话时长" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.callDurationSec) + 's' }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('5分30秒')" property="time530" label="5分30秒" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.time530) }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('交互轮次')" property="cycleCount" label="交互轮次" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.cycleCount) }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('ABCD量级')" property="abcdCount" label="ABCD量级" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.abcdCount) }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('客户挂机量')" property="manHuangUpCount" label="客户挂机量" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.manHuangUpCount) }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('Ai挂机量')" property="robotHuangUpCount" label="Ai挂机量" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.robotHuangUpCount) }}
        </template>
      </el-table-column>
      <el-table-column property="calledRatio" label="呼通率" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <div :class="{'tw-text-[--primary-red-color]': outOfThreshold(row.calledRatio, 'calledRatio')}">
            {{ formatNumberPercent(row.calledRatio, 2) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('平均通话时长')" property="averageDuration" label="平均通话时长" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <div :class="{'tw-text-[--primary-red-color]': outOfThreshold(row.averageDuration, 'averageDuration')}">
            {{ formatNumber(row.averageDuration, 2)  + 's' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('5分30s占比')" property="time530Percent" label="5分30s占比" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumberPercent(row.time530Percent, 2) }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('平均交互次数')" property="averageCycleCount" label="平均交互次数" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <div :class="{'tw-text-[--primary-red-color]': outOfThreshold(row.averageCycleCount, 'averageCycleCount')}">
            {{ formatNumber(row.averageCycleCount, 2) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('ABCD占比')" property="abcdPercent" label="ABCD占比" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <div :class="{'tw-text-[--primary-red-color]': outOfThreshold(row.abcdPercent, 'abcdPercent')}">
            {{ formatNumberPercent(row.abcdPercent, 2)}}
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('AI挂机率')" property="robotHuangUpPercent" label="AI挂机率" align="left" sortable="custom" :min-width="isMobile ? 120 : 160" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <div :class="{'tw-text-[--primary-red-color]': outOfThreshold(row.robotHuangUpPercent, 'robotHuangUpPercent')}">
            {{ formatNumberPercent(row.robotHuangUpPercent, 2)}}
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      class="tw-flex-grow-0"
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search()"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
</template>

<script lang="ts" setup>
import { watch, computed, ref, reactive, defineAsyncComponent, onUnmounted } from 'vue'
import { CloseBold, CaretTop, CaretBottom, } from '@element-plus/icons-vue'
import { formatNumber, formatNumberPercent, formatDuration, formatterEmptyData } from '@/utils/utils'
import PaginationBox from '@/components/PaginationBox.vue'
import to from 'await-to-js'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import { FsMonitorItem } from '@/type/monitor-statistic'
import { tableHeaderStyle } from '@/assets/js/constant'
import TimePickerBox from '@/components/TimePickerBox.vue'
import dayjs from 'dayjs'
import { useGlobalStore } from '@/store/globalInfo'
import { onBeforeRouteLeave } from 'vue-router'
import ColumnSetting from '@/components/ColumnSetting.vue'
import { totalCols, defaultCols, disabledCols } from './constants'
import { useUserStore } from '@/store/user'

const props = defineProps<{
  needUpdate: boolean
}>();
const emits = defineEmits(['update:needUpdate',])

const userInfo = useUserStore()
const selectCols = computed(() => userInfo.colInfo['fs'] || [])

const globalInfo = useGlobalStore()
const isMobile = globalInfo.isMobile

const ifRecallList = [
  {name: '全部', value: '2'},
  {name: '首呼', value: '0'},
  {name: '补呼', value: '1'},
]

/** 底部账号维度列表 开始 */
const loading3 = ref(false)
const tableData = ref<FsMonitorItem[] | null>([]) // 全部数据
// 实际显示数据
const tableTempData = computed(() => {
  if (!tableData.value || tableData.value.length === 0) return []
  const averageFs:FsMonitorItem | undefined = thresholdInfo.value ? {
    fsIp: '均值',
    calledRatio: (thresholdInfo.value['calledRatio']?.minScope + thresholdInfo.value['calledRatio']?.maxScope) / 2,
    averageDuration: (thresholdInfo.value['averageDuration']?.minScope + thresholdInfo.value['averageDuration']?.maxScope) / 2,
    abcdPercent: (thresholdInfo.value['abcdPercent']?.minScope + thresholdInfo.value['abcdPercent']?.maxScope) / 2,
    robotHuangUpPercent: (thresholdInfo.value['robotHuangUpPercent']?.minScope + thresholdInfo.value['robotHuangUpPercent']?.maxScope) / 2,
    averageCycleCount: (thresholdInfo.value['averageCycleCount']?.minScope + thresholdInfo.value['averageCycleCount']?.maxScope) / 2,
  } : undefined
  let data: FsMonitorItem[] = tableData.value || []
  if (prop.value && order.value) {
    data = (averageFs ? [averageFs, ...data] : data).sort((a, b) => {
      if (prop.value.includes('Time')) {
        // @ts-ignore
        if (order.value === 'descending') {
          // @ts-ignore
          return dayjs(b[prop.value]).isBefore(dayjs(a[prop.value])) ? -1 : 1
        } else {
          // @ts-ignore
          return dayjs(b[prop.value]).isBefore(dayjs(a[prop.value])) ? 1 : -1
        }
      } else {
        // @ts-ignore
        return order.value === 'descending' ? b[prop.value] - a[prop.value] : a[prop.value] - b[prop.value]
      }
    })
  } else {
    const outOfThresholdList:FsMonitorItem[] = []
    const normalList: FsMonitorItem[] = []
    tableData.value ?.map(item => {
      if (outOfThreshold(item.calledRatio, 'calledRatio') ||
      outOfThreshold(item.averageDuration, 'averageDuration') || outOfThreshold(item.averageCycleCount, 'averageCycleCount') ||
      outOfThreshold(item.abcdPercent, 'abcdPercent') || outOfThreshold(item.robotHuangUpPercent, 'robotHuangUpPercent')) {
        outOfThresholdList.push(item)
      } else {
        normalList.push(item)
      }
    }) || []
    data = averageFs ? [averageFs, ...outOfThresholdList, ... normalList] : [...outOfThresholdList, ... normalList]
  }
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})
const getRowStyle = ({row}: {row: FsMonitorItem}) => {
  if (row.fsIp === '均值') {
    return {
      fontWeight: 700,
      color: '#000'
    }
  } else {
    return null
  }
}


const thresholdInfo = ref<Record<string, { minScope: number, maxScope: number,}>>({})
const outOfThreshold = (num: number | undefined, name: string) => {
  if (typeof num !== 'number') return true
  if (num >= thresholdInfo.value[name]?.minScope && num <= thresholdInfo.value[name]?.maxScope) {
    return false
  }
  return true
}



/** 搜索和接口数据获取 */
class SearchFormOrigin {
  startTime =  dayjs().add(-5, 'm').format('YYYY-MM-DD HH:mm:ss')
  endTime =  dayjs().format('YYYY-MM-DD HH:mm:ss')
  fsIp =  ''
  ifRecall = '2'
}
const searchForm = reactive<{
  startTime: string,
  endTime: string,
  fsIp: string,
  ifRecall: string,
}>(new SearchFormOrigin())

const search = async (updateTime:boolean = false) => {
  loading3.value = true
  if (updateTime) {
    searchForm.startTime =  dayjs().add(-5, 'm').format('YYYY-MM-DD HH:mm:ss')
    searchForm.endTime =  dayjs().format('YYYY-MM-DD HH:mm:ss')
  }
  try {
    const params = {
      ...searchForm,
      ifRecall: searchForm.ifRecall === '2' ? '' : searchForm.ifRecall
    }
    const [_, data] = await to(monitorStatisticModel.getFsList(params))
    thresholdInfo.value = data?.averageValue || {}
    tableData.value = [
      ...(data?.dataList || [])
    ]
    total.value = tableData.value?.length || 0
  } catch(err){} finally {
    loading3.value = false
  }
  
}

/** 分页 */ 
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(500)
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  search()
}

/** 排序 */
const prop = ref('')
const order = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  prop.value = params.prop
  order.value = params.order
}

/** 底部账号维度列表 结束 */

const init = () => {
  search()
  emits('update:needUpdate', false)
}
watch(() => props.needUpdate, n => {
  n && init()
})
init()

onUnmounted(() => {
  tableData.value = null
})
onBeforeRouteLeave(() => {
  tableData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.search-box {
  padding: 0 0 12px;
}
.el-table {
  font-size: var(--el-font-size-base);
  @media screen and (max-width: 600px) {
    font-size: 10px;
    .el-button {
      font-size: 10px
    }
  }

  :deep(.cell) {
    padding: 0 8px;
    @media screen and (max-width: 600px) {
      padding: 0 2px;
    }
  }
  :deep(.caret-wrapper) {
    display: none;
  }
}
.card-box {
  @media screen and (max-width: 600px) {
    padding-left: 4px;
    padding-right: 4px;
  }
}
.title-normal {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-black-color-600);
  line-height: 20px;
  display: flex;
  align-items: center;
}
</style>