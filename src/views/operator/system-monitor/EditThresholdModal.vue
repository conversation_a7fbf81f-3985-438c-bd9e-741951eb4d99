<template>
   <el-dialog
    v-model="showModal"
    @close="close()"
    width="540px"
    align-center
    class="dialog-form"
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">编辑阈值</div>
    </template>
    <el-scrollbar class="tw-max-h-[calc(100vh-200px)] tw-px-3">
      <el-form ref="formRef" :model="addData" :rules="rules" label-width="90px" label-placement="left">
        <el-form-item label="Engine：">
          {{ props.data?.join('、') }}
        </el-form-item>
        <el-form-item prop="threshold" label="阈值：">
          <el-input-number
            v-model="addData.threshold"
            :precision="0"
            :controls="false"
            style="width: 100%"
            placeholder="请输入阈值"
            clearable
            :min="0" :max="1000"
          >
          </el-input-number>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <el-button type="primary"  @click="confirm" :loading="loading">确认</el-button>
      <el-button class="tw-ml-3" @click="close" >关闭</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from 'vue'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import to from 'await-to-js'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, } from 'element-plus'
type EditThresholdItem = {
  engineFlag?: string,
  threshold?: number,
}
const rules = {
  threshold: [
    { required: true,  message: '请输入阈值，0-1000', type: 'number', trigger: ['input', 'blur'] },
  ],
}
const props = defineProps<{
  visible: boolean,
  data: string[]
}>();
const emits = defineEmits(['update:visible', 'confirm'])

const showModal = ref(false)

const addData = reactive<EditThresholdItem>({
  threshold: undefined
})

const close = () => {
  emits('update:visible', false)
}

const formRef = ref<FormInstance  | null>(null)
const loading = ref(false)
const confirm = () => {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      loading.value = true
      let hasError = false
      await Promise.all(props.data?.map(async (item: string) => {
        const [err, _] = await to(monitorStatisticModel.editEngineThreshold({
          engineFlag: item,
          threshold: addData.threshold,
        }))
        if (err) {
          hasError = true
          ElMessage.error(item + '操作失败')
        }
      }))
      if (!hasError) {
        showModal.value = false
        close()
        ElMessage.success('操作成功')
        emits('confirm')
      }
      loading.value = false
    }
  })
}

watch(() => props.visible, n => {
  showModal.value = n
  if (n) {
    Object.assign(addData, props.data || {
      engineFlag: undefined,
      threshold: undefined
    })
  }
})
</script>

<style scoped lang="postcss" type="text/postcss">

</style>