<template>
  <!--标签卡容器-->
  <div class="tw-bg-white">
    <!--搜索条-->
    <div class="tw-grid tw-grid-cols-5 tw-gap-[12px] tw-p-[16px] tw-text-left">
      <div class="tw-col-span-1">
        <div class="tw-mb-[8px] tw-text-[13px] tw-text-[#969799] tw-text-left">类型：</div>
        <el-select
          v-model="searchForm.type"
          placeholder="全部"
          style="width: 100%;"
          clearable
        >
          <el-option value="" label="全部" />
          <el-option
            v-for="typeItem in Object.entries(SeatLogTypeEnum)"
            :key="typeItem.at(1)"
            :value="typeItem.at(1)"
            :label="typeItem.at(0)"
          />
        </el-select>
      </div>

      <div class="tw-col-span-1">
        <div class="tw-mb-[8px] tw-text-[13px] tw-text-[#969799] tw-text-left">账号：</div>
        <el-select
          v-model.trim="searchForm.account"
          placeholder="全部"
          clearable
          filterable
          style="width: 100%;"
        >
          <el-option
            v-for="seatItem in allSeatList"
            :key="seatItem.id"
            :value="seatItem.account"
            :label="`${seatItem.account||''}（${seatItem.name||''}）`"
          />
        </el-select>
      </div>

      <div class="tw-col-span-1">
        <div class="tw-mb-[8px] tw-text-[13px] tw-text-[#969799] tw-text-left">标题：</div>
        <el-select
          v-model.trim="searchForm.title"
          placeholder="全部"
          clearable
          filterable
          style="width: 100%;"
        >
          <el-option
            v-for="titleItem in Object.values(SeatLogActionEnum)"
            :key="titleItem"
            :value="titleItem"
            :label="titleItem"
          />
        </el-select>
      </div>

      <div class="tw-col-span-2">
        <div class="tw-mb-[8px] tw-text-[13px] tw-text-[#969799]">日期：</div>
        <el-date-picker
          v-model="datetimeRangeVal"
          type="datetimerange"
          editable
          clearable
          placeholder="全部时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          :shortcuts="dateShortcuts"
          :disabled-date="checkDisableDate"
          style="width: 100%;"
          :default-time="defaultTime"
          @change="onDatetimeRangeChange"
          @visible-change="onDatetimeRangeVisibleChange"
        />
      </div>

      <!--搜索按钮工具条-->
      <div class="tw-col-span-5 tw-mb-[8px] tw-ml-auto">
        <el-button type="primary" link @click="onClickReset">
          <el-icon size="--el-font-size-base">
            <SvgIcon name="filter" color="var(--el-color-primary)" />
          </el-icon>
          <span>重置</span>
        </el-button>

        <el-button type="primary" link @click="onClickSearch">
          <el-icon size="--el-font-size-base">
            <SvgIcon name="filter" color="var(--el-color-primary)" />
          </el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>

    <!--按钮组-->
    <div class="tw-flex tw-justify-end tw-px-[16px] tw-pb-[16px]">
      <el-button plain type="primary" @click="onClickRefresh">
        刷新数据
      </el-button>
      <el-button type="primary" @click="onClickExport">
        导出数据
      </el-button>
    </div>

    <!--表格上下各放一个分页条-->
    <!--分页条-->
    <PaginationBox
      :currentPage="pageNum"
      :pageSize="pageSize"
      :pageSizeList="pageSizeList"
      :total="total"
      @search="updateList"
      @update="updateList"
    />

    <!--表格-->
    <el-table
      v-loading="loadingAllList"
      :data="currentList"
      stripe
      border
      :header-cell-style="tableHeaderStyle"
    >
      <template #empty>
        <span v-show="hasSearched">暂无数据</span>
        <span v-show="!hasSearched">请选择筛选条件并点击查询按钮</span>
      </template>

      <el-table-column prop="reportTime" label="日期时间" align="left" width="170" />
      <el-table-column prop="type" label="类型" align="left" width="80">
        <template #default="{row}:{row:SeatLogParam}">
          <span class="status-box-mini" :class="getTypeClassName(row)">
            {{ formatTypeText(row) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="account" label="账号" align="left" width="150" />
      <el-table-column prop="title" label="标题" align="left" width="150" />
      <el-table-column prop="content" label="内容" align="left" />
    </el-table>

    <!--分页条-->
    <PaginationBox
      :currentPage="pageNum"
      :pageSize="pageSize"
      :pageSizeList="pageSizeList"
      :total="total"
      @search="updateList"
      @update="updateList"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, reactive, ref, toRaw } from 'vue'
import { SeatLogActionEnum, SeatLogParam, SeatLogTypeEnum, SeatMember } from '@/type/seat'
import { SeatLogSearchClass, SeatLogSearchParam } from '@/type/monitor-statistic'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import { tableHeaderStyle } from '@/assets/js/constant'
import { findValueInEnum, Throttle, updateCurrentPageList } from '@/utils/utils'
import { generateExcelByAoa } from '@/utils/export'
import { ElMessage } from 'element-plus'
import { ResponseData } from '@/axios/request/types'
import dayjs from 'dayjs'
import PaginationBox from '@/components/PaginationBox.vue'
import { getCallTeamAndSeatOptions } from '@/views/merchant/call-record/constants'

// ---------------------------------------- 搜索 开始 ----------------------------------------

// 是否执行过搜索
const hasSearched = ref(false)

// 搜索表单
const searchForm = reactive<SeatLogSearchParam>(new SeatLogSearchClass())
// 全部坐席列表
const allSeatList = ref<SeatMember[]>([])
// 日期时间范围
const datetimeRangeVal = ref<[string, string] | null>(null)
// 日期预设值，计算属性是为了每次选择时都按实时的系统时间重新计算
const dateShortcuts = ref<{
  text: string,
  value: [string, string],
}[]>([])
// 默认开始时间和默认结束时间
const defaultTime = ref([
  new Date(new Date().setHours(0, 0, 0, 0)),
  new Date(new Date().setHours(23, 59, 59, 0))
])

/**
 * 更新日期快捷选项
 */
const updateDateShortcuts = () => {
  dateShortcuts.value = []
  dateShortcuts.value = [
    {
      text: '最近1小时',
      value: [
        dayjs().subtract(1, 'h').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
    {
      text: '最近2小时',
      value: [
        dayjs().subtract(2, 'h').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
    {
      text: '最近3小时',
      value: [
        dayjs().subtract(3, 'h').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
    {
      text: '最近6小时',
      value: [
        dayjs().subtract(6, 'h').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
    {
      text: '最近半天',
      value: [
        dayjs().subtract(12, 'h').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
    {
      text: '最近一天',
      value: [
        dayjs().subtract(24, 'h').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
    {
      text: '今天',
      value: [
        dayjs().startOf('d').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().endOf('d').format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
    {
      text: '昨天',
      value: [
        dayjs().subtract(1, 'd').startOf('d').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().subtract(1, 'd').endOf('d').format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
  ]
}
/**
 * 判断日期是否禁用（不可选）
 * @param {Date} date 日期对象
 * @returns {boolean} 是否禁用 true 禁用 false 启用
 */
const checkDisableDate = (date: Date): boolean => {
  const day = dayjs(date)
  // 未来日期禁用
  if (day.isAfter(dayjs())) {
    return true
  }
  // // 15天以前禁用
  // const diff = day.diff(dayjs(), 'day')
  // return diff < -15 || diff > 0
  return false
}
/**
 * 重置搜索表单
 */
const resetForm = () => {
  Object.assign(searchForm, new SeatLogSearchClass())
  updateDatetimeRange()
}
/**
 * 点击重置按钮
 */
const onClickReset = () => {
  resetForm()
}
/**
 * 点击查询按钮
 */
const onClickSearch = () => {
  updateAllList()
}
const updateDatetimeRange = async (val: [string, string] | null = null) => {
  await nextTick()
  if (!val?.length) {
    // 没有数据，默认今天
    datetimeRangeVal.value = [
      dayjs().startOf('d').format('YYYY-MM-DD HH:mm:ss'),
      dayjs().endOf('d').format('YYYY-MM-DD HH:mm:ss'),
    ]
    searchForm.reportTimeStart = datetimeRangeVal.value.at(0) ?? ''
    searchForm.reportTimeEnd = datetimeRangeVal.value.at(1) ?? ''
    // ElMessage.success('已将查询日期设置为今天')
  } else {
    datetimeRangeVal.value = val
    searchForm.reportTimeStart = val?.at(0) ?? ''
    searchForm.reportTimeEnd = val?.at(1) ?? ''
  }
}
/**
 * 日期选择范围改变
 * @param {[any, any]} val 新值
 */
const onDatetimeRangeChange = (val: [string, string] | null) => {
  updateDatetimeRange(val)
}
/**
 * 日期选择范围显示隐藏
 * @param val 是否显示
 */
const onDatetimeRangeVisibleChange = (val: boolean) => {
  val && updateDateShortcuts()
}
/**
 * 更新全部坐席列表
 */
const updateAllSeatList = async () => {
  getCallTeamAndSeatOptions(false).then(({ callSeatList }) => {
    // 将坐席按账号进行字母排序
    allSeatList.value = <SeatMember[]>callSeatList.sort((a: SeatMember, b: SeatMember) => {
      return (a.account || '')?.localeCompare(b.account || '')
    })
  }).catch((e) => {
    allSeatList.value = []
    console.error('获取全部坐席列表时出错', e)
    ElMessage.error('获取全部坐席列表时出错' + e.toString())
  })
}

// ---------------------------------------- 搜索 结束 ----------------------------------------

// ---------------------------------------- 表格 开始 ----------------------------------------

// 全部列表，接口数据
const allList = ref<SeatLogParam[]>([])
// 全部列表，正在加载
const loadingAllList = ref<boolean>(false)
// 全部列表，加载节流锁
const throttleAllList = new Throttle(loadingAllList)

// 当前页列表，页面展示，搜索结果的子集
const currentList = ref<SeatLogParam[]>([])
// 当前页码
const pageNum = ref(1)
// 每页大小
const pageSize = ref(100)
// 可选页码大小
const pageSizeList = [10, 20, 50, 100, 200]
// 总数
const total = computed(() => {
  return allList.value.length ?? 0
})

/**
 * 更新列表 全部
 */
const updateAllList = async () => {
  // 节流锁上锁
  if (throttleAllList.check()) {
    return
  }
  throttleAllList.lock()

  // 清空列表
  allList.value = []
  updateList(pageNum.value, pageSize.value)
  await nextTick()

  try {
    // 请求参数
    const params = toRaw(searchForm)
    // 请求接口
    const res = <ResponseData>await monitorStatisticModel.getSeatLogList(params)
    let list: SeatLogParam[] = (<SeatLogParam[]>res?.data)?.length ? <SeatLogParam[]>res?.data : []
    // 更新列表
    allList.value = list.sort((a: SeatLogParam, b: SeatLogParam) => {
      return Number(b.id) - Number(a.id)
    })
    updateList(pageNum.value, pageSize.value)
  } catch (e) {
    ElMessage.error('更新数据出错，请重试')
    // 发生错误，重置列表
    allList.value = []
    updateList(pageNum.value, pageSize.value)
  } finally {
    // 节流锁解锁
    throttleAllList.unlock()
    // 标记为已执行过搜索
    hasSearched.value = true
  }
}
/**
 * 更新列表 当前页码
 */
const updateList = (p?: number, s?: number) => {
  if (p || s) {
    // 如果参数指定了页码或/和每页大小，则按参数更新列表
    p && (pageNum.value = p)
    s && (pageSize.value = s)
    currentList.value = updateCurrentPageList(allList.value, pageNum.value, pageSize.value)
  } else {
    // 如果参数都没有指定，则页码和页面大小保持不变，更新全部列表
    updateAllList()
  }
}
/**
 * 点击刷新按钮
 */
const onClickRefresh = () => {
  updateAllList()
}
/**
 * 点击导出数据
 */
const onClickExport = () => {
  const data = [
    ['日期时间', '类型', '账号', '标题', '内容'],
    ...allList.value?.map(item => ([item.reportTime || '', formatTypeText(item), item.account || '', item.title || '', item.content || '']))
  ]
  generateExcelByAoa(data as string[][], `商户坐席日志.xlsx`)
}
/**
 * 格式化日志类型文本
 * @param row 行数据
 * @return {string} 展示文本
 */
const formatTypeText = (row: SeatLogParam): string => {
  return findValueInEnum(row.type ?? '', SeatLogTypeEnum) ?? '-'
}
const getTypeClassName = (row: SeatLogParam): string => {
  if (row?.type === SeatLogTypeEnum['信息']) {
    return 'blue-status'
  } else if (row?.type === SeatLogTypeEnum['警告']) {
    return 'orange-status'
  } else if (row?.type === SeatLogTypeEnum['错误']) {
    return 'red-status'
  } else {
    return 'gray-status'
  }
}

// ---------------------------------------- 表格 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// updateAllList()
updateDateShortcuts()
updateDatetimeRange()
updateAllSeatList()

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
