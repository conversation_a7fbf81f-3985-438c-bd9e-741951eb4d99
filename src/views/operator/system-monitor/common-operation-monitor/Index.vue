<template>
  <HeaderBox title="运营监控" :can-refresh="true" @refresh="init"/>
  <div class="tw-min-w-[1080px] sm:tw-min-w-full tw-p-[16px] sm:tw-p-0 sm:tw-overflow-x-hidden tw-flex tw-flex-col tw-h-[calc(100%-56px)]">
    <Statistic v-model:needUpdate="needUpdateTop"/>
    <!-- tab：账号分布 | 账号统计 -->
    <div class="tw-flex tw-w-full tw-mb-[8px] tw-mt-[16px] sm:tw-mb-[4px] sm:tw-mt-[8px] sm:tw-px-[6px]">
      <RadioButtonBox v-model:active="viewType" :list="viewTypeList" @update:active="needUpdateTab=true"/>
    </div>
    <el-scrollbar class="tw-grow tw-overflow-y-auto tw-mr-[-16px]" wrap-class="tw-pr-[16px]">
       <!-- 账号分布 -->
      <keep-alive>
        <AccountTable v-if="viewType===viewTypeList[0]" v-model:needUpdate="needUpdateTab" />
        <MerchantLineStatistic v-else-if="viewType===viewTypeList[1]" v-model:needUpdate="needUpdateTab" />
        <RealTimeStatusList v-else-if="viewType===viewTypeList[2]" />
        <MerchantSeatLog v-else-if="viewType===viewTypeList[3]"/>
      </keep-alive>
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, defineAsyncComponent, } from 'vue'
import HeaderBox from '@/components/HeaderBox.vue'
import RadioButtonBox from '@/components/RadioButtonBox.vue'
import RealTimeStatusList from '@/views/merchant/manual-call/SeatMonitor/RealTimeStatusList.vue'
import Statistic from '@/views/operator/merchant-data-manager/OperationMonitor/Statistic.vue'
const MerchantLineStatistic = defineAsyncComponent({loader: () => {return import('./MerchantLineStatistic.vue')}})
const AccountTable = defineAsyncComponent({loader: () => {return import('@/views/operator/merchant-data-manager/OperationMonitor/AccountTable.vue')}})
const MerchantSeatLog = defineAsyncComponent({loader: () => {return import('./MerchantSeatLog.vue')}})

// tab变量
const viewTypeList = ['账号监控', '商户线路监控', '商户坐席监控', '商户坐席日志']
const viewType = ref(viewTypeList[0])

const needUpdateTop = ref(false) // 是否需要刷新头部数据
const needUpdateTab = ref(false) // 是否需要刷新tab下页面数据


const mainAccountList = ref<{
  account: string,
  groupId: string,
}[]>([])
const getMainAccountList = async () => {
  // mainAccountList.value = await monitorStatisticModel.getAllMainAccount() || []
}
// 初始化数据
const init = async () => {
  needUpdateTop.value = true
  needUpdateTab.value = true
  getMainAccountList()
}
init()
</script>

<style scoped lang="postcss" type="text/postcss">
</style>
