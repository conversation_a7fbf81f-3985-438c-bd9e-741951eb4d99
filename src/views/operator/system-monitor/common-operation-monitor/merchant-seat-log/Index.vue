<template>
  <!--标签卡容器-->
  <div class="tw-bg-white">
    <!--搜索条-->
    <div class="tw-grid tw-grid-cols-5 tw-gap-[12px] tw-p-[16px] tw-text-left">
      <div class="tw-col-span-1">
        <div class="tw-mb-[8px] tw-text-[13px] tw-text-[#969799] tw-text-left">事件等级：</div>
        <el-select
          v-model="searchForm.type"
          placeholder="全部"
          style="width: 100%;"
          clearable
        >
          <el-option value="" label="全部" />
          <el-option
            v-for="typeItem in Object.entries(SeatLogTypeEnum)"
            :key="typeItem.at(1)"
            :value="typeItem.at(1)"
            :label="typeItem.at(0)"
          />
        </el-select>
      </div>

      <div class="tw-col-span-1">
        <div class="tw-mb-[8px] tw-text-[13px] tw-text-[#969799] tw-text-left">坐席账号：</div>
        <el-select
          v-model.trim="searchForm.account"
          placeholder="全部"
          clearable
          filterable
          style="width: 100%;"
        >
          <el-option
            v-for="seatItem in allSeatList"
            :key="seatItem.id"
            :value="seatItem.account"
            :label="`${seatItem.account||''}（${seatItem.name||''}）`"
          />
        </el-select>
      </div>

      <div class="tw-col-span-1">
        <div class="tw-mb-[8px] tw-text-[13px] tw-text-[#969799] tw-text-left">事件名称：</div>
        <el-select
          v-model.trim="searchForm.title"
          placeholder="全部"
          clearable
          filterable
          style="width: 100%;"
        >
          <el-option
            v-for="titleItem in Object.values(SeatLogActionEnum)"
            :key="titleItem"
            :value="titleItem"
            :label="titleItem"
          />
        </el-select>
      </div>

      <div class="tw-col-span-2">
        <div class="tw-mb-[8px] tw-text-[13px] tw-text-[#969799]">日期时间：</div>
        <el-date-picker
          v-model="datetimeRangeVal"
          type="datetimerange"
          editable
          clearable
          placeholder="全部时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          :shortcuts="dateShortcuts"
          :disabled-date="checkDisableDate"
          style="width: 100%;"
          :default-time="defaultTime"
          @change="onDatetimeRangeChange"
          @visible-change="onDatetimeRangeVisibleChange"
        />
      </div>

      <!--搜索按钮工具条-->
      <div class="tw-col-span-5 tw-mb-[8px] tw-ml-auto">
        <el-button type="primary" link @click="onClickReset">
          <el-icon size="--el-font-size-base">
            <SvgIcon name="filter" color="var(--el-color-primary)" />
          </el-icon>
          <span>重置</span>
        </el-button>

        <el-button type="primary" link @click="onClickSearch">
          <el-icon size="--el-font-size-base">
            <SvgIcon name="filter" color="var(--el-color-primary)" />
          </el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>

    <!--按钮组-->
    <div class="tw-flex tw-justify-end tw-px-[16px] tw-pb-[16px]">
      <el-button plain type="primary" @click="onClickRefresh">
        刷新数据
      </el-button>
      <el-button type="primary" @click="onClickExport">
        导出数据
      </el-button>
    </div>

    <!--表格上下各放一个分页条-->
    <!--分页条-->
    <PaginationBox
      :currentPage="pageNum"
      :pageSize="pageSize"
      :pageSizeList="pageSizeList"
      :total="total"
      @search="updateList"
      @update="updateList"
    />

    <!--表格-->
    <el-table
      v-loading="loadingAllList"
      :data="currentList"
      stripe
      border
      :header-cell-style="tableHeaderStyle"
    >
      <template #empty>
        <span v-show="hasSearched">暂无数据</span>
        <span v-show="!hasSearched">请选择筛选条件并点击查询按钮</span>
      </template>

      <el-table-column prop="reportTime" label="日期时间" align="center" width="170" />
      <el-table-column prop="type" label="事件等级" align="center" width="90">
        <template #default="{row}:{row:SeatLogParam}">
          <div class="tw-w-full tw-text-center">
          <span class="status-box-mini" :class="getTypeClassName(row)">
            {{ formatTypeText(row) }}
          </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="account" label="坐席账号" align="center" width="120" />
      <el-table-column prop="title" label="事件名称" align="center" width="150" />

      <!-- 从content JSON中提取的字段 -->
      <el-table-column label="坐席状态" align="center" width="150">
        <template #default="{row}:{row:SeatLogParam}">
          <div class="tw-text-center">
            <span>{{ getContentField(row, 'seatStatus') || '-' }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="通话模式" align="center" width="120">
        <template #default="{row}:{row:SeatLogParam}">
          <span>{{ SeatCallTypeEnum[(getContentField(row, 'callType') || '-')] }}</span>
        </template>
      </el-table-column>

      <el-table-column label="任务数量" align="center" width="90">
        <template #default="{row}:{row:SeatLogParam}">
          <span>{{ getContentField(row, 'taskCount') || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="内部号码 phone" align="center" width="250">
        <template #default="{row}:{row:SeatLogParam}">
          <span>{{ getContentField(row, 'phone') || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="记录编号 recordId" align="center" width="200">
        <template #default="{row}:{row:SeatLogParam}">
          <span>{{ getContentField(row, 'recordId') || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="通话编号 callId" align="center" width="200">
        <template #default="{row}:{row:SeatLogParam}">
          <span>{{ getContentField(row, 'callId') || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="FS账号" align="center" width="110">
        <template #default="{row}:{row:SeatLogParam}">
          <span>{{ getContentField(row, 'fsUser') || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="FS服务器IP" align="center" width="130">
        <template #default="{row}:{row:SeatLogParam}">
          <span>{{ getContentField(row, 'fsIp') || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="请求参数" align="center" width="130">
        <template #default="{row}:{row:SeatLogParam}">
          <span>{{ getContentField(row, 'params') || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="响应内容" align="center" width="130">
        <template #default="{row}:{row:SeatLogParam}">
          <span>{{ getContentField(row, 'response') || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作描述" align="center" width="200">
        <template #default="{row}:{row:SeatLogParam}">
          <el-tooltip
            v-if="getContentField(row, 'desc')"
            :content="formatDescription(getContentField(row, 'desc'))"
            placement="top"
            :show-after="500"
          >
            <span class="tw-cursor-help tw-text-blue-600">
              {{ truncateText(formatDescription(getContentField(row, 'desc')), 30) }}
            </span>
          </el-tooltip>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="备注" align="center">
        <template #default="{row}:{row:SeatLogParam}">
          <span>{{ getContentField(row, 'remark') || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="详细信息" align="center" width="100" fixed="right">
        <template #default="{row}:{row:SeatLogParam}">
          <el-button
            type="primary"
            link
            size="small"
            @click="showContentDetail(row)"
          >
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--分页条-->
    <PaginationBox
      :currentPage="pageNum"
      :pageSize="pageSize"
      :pageSizeList="pageSizeList"
      :total="total"
      @search="updateList"
      @update="updateList"
    />
  </div>

  <!-- 详情弹窗 -->
  <el-dialog
    v-model="contentDetailVisible"
    title="坐席日志详情"
    width="80%"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="detail-content">
      <!-- 基础信息 -->
      <div class="detail-section">
        <h4>基础信息</h4>
        <div class="detail-grid">
          <div class="detail-item">
            <span class="label">日期时间:</span>
            <span>{{ currentDetailRow.reportTime || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">日志类型:</span>
            <span :class="getTypeClassName(currentDetailRow)">
              {{ formatTypeText(currentDetailRow) }}
            </span>
          </div>
          <div class="detail-item">
            <span class="label">坐席账号:</span>
            <span>{{ currentDetailRow.account || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">事件名称:</span>
            <span>{{ currentDetailRow.title || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="detail-section">
        <h4>详细信息</h4>
        <div class="detail-grid">
          <div class="detail-item">
            <span class="label">坐席状态:</span>
            <span>{{ getContentField(currentDetailRow, 'seatStatus') || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">通话模式:</span>
            <span>{{ getContentField(currentDetailRow, 'callType') || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">客户电话:</span>
            <span>{{ getContentField(currentDetailRow, 'phone') || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">通话记录ID:</span>
            <span>{{ getContentField(currentDetailRow, 'recordId') || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">通话ID:</span>
            <span>{{ getContentField(currentDetailRow, 'callId') || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">FS账号:</span>
            <span>{{ getContentField(currentDetailRow, 'fsUser') || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">FS服务器IP:</span>
            <span>{{ getContentField(currentDetailRow, 'fsIp') || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">任务数量:</span>
            <span>{{ getContentField(currentDetailRow, 'taskCount') || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 操作描述 -->
      <div class="detail-section" v-if="getContentField(currentDetailRow, 'desc')">
        <h4>操作描述</h4>
        <div class="detail-text-area">
          <pre>{{ formatDescription(getContentField(currentDetailRow, 'desc')) }}</pre>
        </div>
      </div>

      <!-- 原始Content -->
      <div class="detail-section">
        <h4>原始Content</h4>
        <div class="detail-text-area">
          <pre>{{ currentDetailRow.content || '-' }}</pre>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="contentDetailVisible = false">关闭</el-button>
        <el-button
          type="primary"
          @click="copyToClipboard(currentDetailRow.content || '')"
        >
          复制Content
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, nextTick, reactive, ref, toRaw } from 'vue'
import { SeatCallTypeEnum, SeatLogActionEnum, SeatLogParam, SeatLogTypeEnum, SeatMember } from '@/type/seat'
import { SeatLogSearchClass, SeatLogSearchParam } from '@/type/monitor-statistic'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import { tableHeaderStyle } from '@/assets/js/constant'
import { findValueInEnum, Throttle, updateCurrentPageList } from '@/utils/utils'
import { generateExcelByAoa } from '@/utils/export'
import { ElMessage } from 'element-plus'
import { ResponseData } from '@/axios/request/types'
import dayjs from 'dayjs'
import PaginationBox from '@/components/PaginationBox.vue'
import { getCallTeamAndSeatOptions } from '@/views/merchant/call-record/constants'

// ---------------------------------------- 搜索 开始 ----------------------------------------

// 是否执行过搜索
const hasSearched = ref(false)

// 搜索表单
const searchForm = reactive<SeatLogSearchParam>(new SeatLogSearchClass())
// 全部坐席列表
const allSeatList = ref<SeatMember[]>([])
// 日期时间范围
const datetimeRangeVal = ref<[string, string] | null>(null)
// 日期预设值，计算属性是为了每次选择时都按实时的系统时间重新计算
const dateShortcuts = ref<{
  text: string,
  value: [string, string],
}[]>([])
// 默认开始时间和默认结束时间
const defaultTime = ref([
  new Date(new Date().setHours(0, 0, 0, 0)),
  new Date(new Date().setHours(23, 59, 59, 0))
])

/**
 * 更新日期快捷选项
 */
const updateDateShortcuts = () => {
  dateShortcuts.value = []
  dateShortcuts.value = [
    {
      text: '最近1小时',
      value: [
        dayjs().subtract(1, 'h').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
    {
      text: '最近2小时',
      value: [
        dayjs().subtract(2, 'h').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
    {
      text: '最近3小时',
      value: [
        dayjs().subtract(3, 'h').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
    {
      text: '最近6小时',
      value: [
        dayjs().subtract(6, 'h').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
    {
      text: '最近半天',
      value: [
        dayjs().subtract(12, 'h').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
    {
      text: '最近一天',
      value: [
        dayjs().subtract(24, 'h').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
    {
      text: '今天',
      value: [
        dayjs().startOf('d').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().endOf('d').format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
    {
      text: '昨天',
      value: [
        dayjs().subtract(1, 'd').startOf('d').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().subtract(1, 'd').endOf('d').format('YYYY-MM-DD HH:mm:ss'),
      ],
    },
  ]
}
/**
 * 判断日期是否禁用（不可选）
 * @param {Date} date 日期对象
 * @returns {boolean} 是否禁用 true 禁用 false 启用
 */
const checkDisableDate = (date: Date): boolean => {
  const day = dayjs(date)
  // 未来日期禁用
  if (day.isAfter(dayjs())) {
    return true
  }
  // // 15天以前禁用
  // const diff = day.diff(dayjs(), 'day')
  // return diff < -15 || diff > 0
  return false
}
/**
 * 重置搜索表单
 */
const resetForm = () => {
  Object.assign(searchForm, new SeatLogSearchClass())
  updateDatetimeRange()
}
/**
 * 点击重置按钮
 */
const onClickReset = () => {
  resetForm()
}
/**
 * 点击查询按钮
 */
const onClickSearch = () => {
  updateAllList()
}
const updateDatetimeRange = async (val: [string, string] | null = null) => {
  await nextTick()
  if (!val?.length) {
    // 没有数据，默认今天
    datetimeRangeVal.value = [
      dayjs().startOf('d').format('YYYY-MM-DD HH:mm:ss'),
      dayjs().endOf('d').format('YYYY-MM-DD HH:mm:ss'),
    ]
    searchForm.reportTimeStart = datetimeRangeVal.value.at(0) ?? ''
    searchForm.reportTimeEnd = datetimeRangeVal.value.at(1) ?? ''
    // ElMessage.success('已将查询日期设置为今天')
  } else {
    datetimeRangeVal.value = val
    searchForm.reportTimeStart = val?.at(0) ?? ''
    searchForm.reportTimeEnd = val?.at(1) ?? ''
  }
}
/**
 * 日期选择范围改变
 * @param {[any, any]} val 新值
 */
const onDatetimeRangeChange = (val: [string, string] | null) => {
  updateDatetimeRange(val)
}
/**
 * 日期选择范围显示隐藏
 * @param val 是否显示
 */
const onDatetimeRangeVisibleChange = (val: boolean) => {
  val && updateDateShortcuts()
}
/**
 * 更新全部坐席列表
 */
const updateAllSeatList = async () => {
  getCallTeamAndSeatOptions(false).then(({ callSeatList }) => {
    // 将坐席按账号进行字母排序
    allSeatList.value = <SeatMember[]>callSeatList.sort((a: SeatMember, b: SeatMember) => {
      return (a.account || '')?.localeCompare(b.account || '')
    })
  }).catch((e) => {
    allSeatList.value = []
    console.error('获取全部坐席列表时出错', e)
    ElMessage.error('获取全部坐席列表时出错' + e.toString())
  })
}

// ---------------------------------------- 搜索 结束 ----------------------------------------

// ---------------------------------------- 表格 开始 ----------------------------------------

// 全部列表，接口数据
const allList = ref<SeatLogParam[]>([])
// 全部列表，正在加载
const loadingAllList = ref<boolean>(false)
// 全部列表，加载节流锁
const throttleAllList = new Throttle(loadingAllList)

// 当前页列表，页面展示，搜索结果的子集
const currentList = ref<SeatLogParam[]>([])
// 当前页码
const pageNum = ref(1)
// 每页大小
const pageSize = ref(100)
// 可选页码大小
const pageSizeList = [10, 20, 50, 100, 200]
// 总数
const total = computed(() => {
  return allList.value.length ?? 0
})

/**
 * 更新列表 全部
 */
const updateAllList = async () => {
  // 节流锁上锁
  if (throttleAllList.check()) {
    return
  }
  throttleAllList.lock()

  // 清空列表
  allList.value = []
  updateList(pageNum.value, pageSize.value)
  await nextTick()

  try {
    // 请求参数
    const params = toRaw(searchForm)
    // 请求接口
    const res = <ResponseData>await monitorStatisticModel.getSeatLogList(params)
    let list: SeatLogParam[] = (<SeatLogParam[]>res?.data)?.length ? <SeatLogParam[]>res?.data : []
    // 更新列表
    allList.value = list.sort((a: SeatLogParam, b: SeatLogParam) => {
      return Number(b.id) - Number(a.id)
    })
    updateList(pageNum.value, pageSize.value)
  } catch (e) {
    ElMessage.error('更新数据出错，请重试')
    // 发生错误，重置列表
    allList.value = []
    updateList(pageNum.value, pageSize.value)
  } finally {
    // 节流锁解锁
    throttleAllList.unlock()
    // 标记为已执行过搜索
    hasSearched.value = true
  }
}
/**
 * 更新列表 当前页码
 */
const updateList = (p?: number, s?: number) => {
  if (p || s) {
    // 如果参数指定了页码或/和每页大小，则按参数更新列表
    p && (pageNum.value = p)
    s && (pageSize.value = s)
    currentList.value = updateCurrentPageList(allList.value, pageNum.value, pageSize.value)
  } else {
    // 如果参数都没有指定，则页码和页面大小保持不变，更新全部列表
    updateAllList()
  }
}
/**
 * 点击刷新按钮
 */
const onClickRefresh = () => {
  updateAllList()
}
/**
 * 点击导出数据
 */
const onClickExport = () => {
  const data = [
    ['日期时间', '类型', '账号', '标题', '内容'],
    ...allList.value?.map(item => ([item.reportTime || '', formatTypeText(item), item.account || '', item.title || '', item.content || '']))
  ]
  generateExcelByAoa(data as string[][], `商户坐席日志.xlsx`)
}
/**
 * 格式化日志类型文本
 * @param row 行数据
 * @return {string} 展示文本
 */
const formatTypeText = (row: SeatLogParam): string => {
  return findValueInEnum(row.type ?? '', SeatLogTypeEnum) ?? '-'
}
const getTypeClassName = (row: SeatLogParam): string => {
  if (row?.type === SeatLogTypeEnum['信息']) {
    return 'blue-status'
  } else if (row?.type === SeatLogTypeEnum['警告']) {
    return 'orange-status'
  } else if (row?.type === SeatLogTypeEnum['错误']) {
    return 'red-status'
  } else {
    return 'gray-status'
  }
}

/**
 * 从content JSON字符串中提取指定字段
 * @param row 行数据
 * @param field 字段名
 * @return {any} 字段值
 */
const getContentField = (row: SeatLogParam, field: string): any => {
  try {
    if (!row.content) return null
    const content = JSON.parse(row.content)
    return content[field] ?? null
  } catch (error) {
    console.warn('解析content JSON失败:', error)
    return null
  }
}

/**
 * 格式化描述信息
 * @param desc 描述内容
 * @return {string} 格式化后的描述
 */
const formatDescription = (desc: any): string => {
  if (!desc) return ''

  if (typeof desc === 'string') {
    return desc
  } else if (typeof desc === 'object') {
    try {
      return JSON.stringify(desc, null, 2)
    } catch (error) {
      return String(desc)
    }
  } else {
    return String(desc)
  }
}

/**
 * 截断文本
 * @param text 原文本
 * @param maxLength 最大长度
 * @return {string} 截断后的文本
 */
const truncateText = (text: string, maxLength: number): string => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// 详情弹窗相关
const contentDetailVisible = ref(false)
const currentDetailRow = ref<SeatLogParam>({})

/**
 * 显示内容详情
 * @param row 行数据
 */
const showContentDetail = (row: SeatLogParam) => {
  currentDetailRow.value = { ...row }
  contentDetailVisible.value = true
}

/**
 * 获取解析后的content对象
 * @param row 行数据
 * @return {any} 解析后的对象
 */
const getParsedContent = (row: SeatLogParam): any => {
  try {
    if (!row.content) return {}
    return JSON.parse(row.content)
  } catch (error) {
    console.warn('解析content JSON失败:', error)
    return {}
  }
}

/**
 * 格式化JSON显示
 * @param obj 对象
 * @return {string} 格式化后的JSON字符串
 */
const formatJSON = (obj: any): string => {
  try {
    return JSON.stringify(obj, null, 2)
  } catch (error) {
    return String(obj)
  }
}

/**
 * 复制到剪贴板
 * @param text 要复制的文本
 */
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('已复制到剪贴板')
    } catch (err) {
      ElMessage.error('复制失败')
    }
    document.body.removeChild(textArea)
  }
}

// ---------------------------------------- 表格 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// updateAllList()
updateDateShortcuts()
updateDatetimeRange()
updateAllSeatList()

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.blue-status {
  color: #409eff;
}
.orange-status {
  color: #e6a23c;
}
.red-status {
  color: #f56c6c;
}
.gray-status {
  color: #909399;
}
/* 详情弹窗样式 */
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}
.detail-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}
.detail-section:last-child {
  border-bottom: none;
}
.detail-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 8px;
}
.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}
.detail-item {
  display: flex;
  align-items: flex-start;
  padding: 8px;
  background-color: #fafafa;
  border-radius: 4px;
}
.detail-item .label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
  margin-right: 8px;
  flex-shrink: 0;
}
.detail-text-area {
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  max-height: 300px;
  overflow-y: auto;
}
.detail-text-area pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-all;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
/* 表格中的工具提示样式 */
.tw-cursor-help {
  cursor: help;
}
.tw-text-blue-600 {
  color: #2563eb;
}
.tw-text-blue-600:hover {
  color: #1d4ed8;
  text-decoration: underline;
}
</style>
