<template>
  <div class="tw-border-r-[1px] tw-border-[#dcdfe6] tw-overflow-x-hidden tw-h-[56px] tw-w-[184px] tw-flex-shrink-0 tw-flex-grow-0" :class="{'logo-min': isCollapse}" @click="changeCollapse()">
    <span  class="tw-flex tw-mt-[16px] tw-mb-[8px] tw-ml-[16px]">
      <template v-if="!isCollapse">
        <span class="tw-w-[139px] tw-h-[32px] tw-text-[#165DFF]">
          <SvgIcon name="baize" color="#165DFF" class="tw-w-full tw-h-full"/>
        </span>
        <!-- <span class="tw-self-end tw-text-[12px] tw-text-[#165DFF] tw-ml-[5px]">{{ accountType === 0 ? '运营端' : '商户端' }}</span> -->
      </template>
      <span v-else class="tw-w-[36px] tw-h-[32px] tw-text-[#165DFF]">
        <SvgIcon name="baize-mini" color="#165DFF" class="tw-w-full tw-h-full"/>
      </span>
    </span>
  </div>
  <el-scrollbar
    wrap-class="tw-h-full tw-pb-[12px] tw-border-r-[1px] tw-border-[#dcdfe6]"
    class="tw-pr-[0px] tw-flex tw-flex-grow aside-box"
    :class="isCollapse ? 'tw-w-[64px]': 'tw-w-[184px]'"
  >
    <el-menu
      :class="isCollapse ? 'tw-w-[64px]': 'tw-w-[184px]'"
      unique-opened
      :collapse="isCollapse"
      :collapse-transition="false"
      router
      :default-active="route.meta.type === 3 ? preRoute : route.path"
      active-text-color="#165DFF"
      @select="handleMenuSelect"
      @open="handleMenuOpen"
    >
    <template v-for="item in menuList">
      <el-sub-menu v-if="item.children && item.children.length>0" :index="item.path" :disabled="menuDisabled">
        <template #title>
          <template v-if="item.meta.icon">
            <el-icon v-if="item.path===currentRoute[0]" :size="16" @click="changeCollapse(false)" color="var(--primary-blue-color)" style="color: #165DFF">
              <SvgIcon :name="item.meta.icon+'-fill'" color="inherit"/>
            </el-icon>
            <el-icon v-else :size="16" @click="changeCollapse(false)" color="var(--primary-black-color-500)" style="color: var(--primary-black-color-500)">
              <SvgIcon :name="item.meta.icon" color="inherit"/>
            </el-icon>
          </template>

          <span class="tw-text-[14px] tw-text-[var(--primary-black-color-500)] tw-font-[400]" :class="{'active-sub-menu': item.path===currentRoute[0]}">{{ item.meta.title }}</span>
        </template>
        <template v-for="subItem in item.children" :class="{'active-sub-menu': item.path===currentRoute[0]}">
          <el-menu-item v-if="subItem.meta.type===2" :index="subItem.path" :route="{path: subItem.path}" :disabled="menuDisabled">{{ subItem.meta.title }}</el-menu-item>
        </template>
      </el-sub-menu>
      <el-menu-item
        :disabled="menuDisabled"
        v-if="!item.children && (item.meta.type??4) < 3 && item.meta.type !== 0"
        class="tw-font-[400]"
        :class="{'master-menu': item.meta.type == 0}"
        :index="item.path"
        :route="{path: item.path}"
      >
        <template v-if="item.meta.icon">
          <el-icon v-if="item.path===currentRoute[0]" :size="16" color="var(--primary-blue-color)" style="color: #165DFF" @click="changeCollapse(false)">
            <SvgIcon :name="item.meta.icon+'-fill'" color="inherit"/>
          </el-icon>
          <el-icon v-else :size="16" @click="changeCollapse(false)" color="var(--primary-black-color-500)" style="color: var(--primary-black-color-500)">
            <SvgIcon :name="item.meta.icon" color="inherit"/>
          </el-icon>
        </template>
        <span class="tw-text-[14px] tw-text-[var(--primary-black-color-500)]" :class="{'active-sub-menu': item.path===currentRoute[0]}">{{ item.meta.title }}</span>
      </el-menu-item>
    </template>
    </el-menu>
    <!-- <el-button
        class="tw-self-center tw-flex-grow-0 tw-w-[20px] tw-h-[20px] tw-translate-x-[-50%]"
        round
        :icon="isCollapse ? ArrowRightBold :ArrowLeftBold"
        @click="isCollapse=!isCollapse"
      /> -->
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { ref, computed, watch, } from 'vue'
import { useRoute } from 'vue-router'
import router from "@/router";
import { useRouteStore } from "@/store/routes";
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import { useSeatInfoStore } from '@/store/seat/seat-info'
const emits = defineEmits(['menu-change'])
const routeStore = useRouteStore();
const userStore = useUserStore();
const { accountType } = userStore
const seatInfoStore = useSeatInfoStore()
const { seatOnline } = storeToRefs(seatInfoStore)
const routesList = routeStore.asyncRouts[0].children || []
const menuList = computed(() => {
  const index1 = routesList.findIndex(item => item.meta.title === '商户端')??-1
  const index2 = routesList.findIndex(item => item.meta.title === '运营端')??-1
  if (userStore.roleName === '超级管理员' && index1 > -1 && index2 > -1) {
    if(index2 - index1 <= 1) {
      return routesList.filter(item => item.meta.title !== '商户端')
    }
    if (index2 === routesList.length - 1){
      return routesList.filter(item => item.meta.title !== '运营端')
    }
  }
  return routesList
})

const route = useRoute()
const isCollapse = ref(window.innerWidth <= 600 || window.innerHeight <= 500)
const currentRoute = ref<string[]>([])
const handleMenuSelect = (index: string, item: string[]) => {
  currentRoute.value = item
  emits('menu-change')
}
const handleMenuOpen = (index: string, item: string[]) => {
  if(currentRoute.value.length < 1) {
    currentRoute.value = item
  }
}
const changeCollapse = (val?: boolean) => {
  isCollapse.value = window.innerWidth <= 600 || window.innerHeight <= 500 ? true : (val ?? !isCollapse.value)
}
const preRoute = computed(() => {
  return route.meta.preRoute??window.history.state.back??route.path
})
// 菜单是否禁用
const menuDisabled = computed(() => {
  return router.currentRoute.value.name === 'Workbench' && seatOnline.value
})
</script>

<style scoped lang="postcss" type="text/postcss">
.aside-box {
  .el-menu {
    height: 100%;
    --el-menu-sub-item-height: 36px;
    --el-menu-item-height: 40px;
    --el-menu-text-color: unset;
    /* border-right: 0; */
  }
  .el-menu-item.is-active {
    background: #F7F8FA;
    color: #165DFF;
  }
  .el-sub-menu.is-opened {
    .el-menu-item {
      color: var(--primary-black-color-600);
      font-size: 13px;
    }
    .el-menu-item.is-active {
      background: #F7F8FA;
      color: #165DFF;
    }
  }
  .active-sub-menu {
    color: #165DFF;
    font-weight: 600;
  }
}
.logo-min {
  width: 64px;
}
</style>
