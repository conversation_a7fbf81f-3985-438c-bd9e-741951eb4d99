import dayjs from 'dayjs'
import Client from 'ssh2-sftp-client' // 用了连接服务器进行文件or文件夹相关操作
import ora from 'ora' // 用来实现loading加载效果
import config from'./sftp-path.js'

async function uploadLocalFolder() {
  const args = process.argv.slice(2)
  const spinner = ora('发布脚本开始执行--->').start(); // 开始加载...
  const sftp = new Client(); // 实例化用于调用其自带的方法
  const { sftp: sftpConfig, pathConfig } = config[args[0]];
  try {
    spinner.text = '连接服务器...'
    await sftp.connect(sftpConfig); // 使用上述配置连接远程服务器
    spinner.text = '连接服务器成功'

    //删除备份文件,如果有
    spinner.text = '删除远程文件...'
    if (await sftp.exists(pathConfig.remotePath)) {
      await sftp.rmdir(pathConfig.remotePath, true)
      spinner.text = '删除远程文件成功'
    }

    spinner.text = '新打包文件夹上传...'
    await sftp.uploadDir(pathConfig.localPath, pathConfig.remotePath); // 新的dist删除掉
    spinner.text = '新的打包文件夹上传成功'

  } catch (err) {
    console.error(err)
  } finally {
    await sftp.end();
    spinner.succeed('脚本执行完毕');
    console.log('完成时间：' + dayjs().format('YYYY-MM-DD HH:mm:ss'));
    console.log('上传地址：' + sftpConfig.host + pathConfig.remotePath)
  }
}

uploadLocalFolder();