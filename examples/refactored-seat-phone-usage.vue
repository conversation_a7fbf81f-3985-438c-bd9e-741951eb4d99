<template>
  <div class="seat-phone-workbench">
    <!-- 坐席状态栏 -->
    <div class="status-bar">
      <div class="seat-info">
        <span>坐席: {{ currentSeat.name || '未设置' }}</span>
        <el-tag :type="seatOnline ? 'success' : 'danger'">
          {{ seatOnline ? '在线' : '离线' }}
        </el-tag>
      </div>
      
      <div class="mode-info" v-if="currentMode">
        <span>当前模式: </span>
        <el-tag :type="getModeTagType(currentMode)">
          {{ getModeText(currentMode) }}
        </el-tag>
      </div>
      
      <div class="actions">
        <el-button 
          v-if="!seatOnline"
          type="primary" 
          :disabled="!canGoOnline"
          @click="handleGoOnline"
        >
          上线
        </el-button>
        
        <el-button 
          v-if="seatOnline"
          type="danger" 
          :disabled="!canGoOffline"
          @click="handleGoOffline"
        >
          下线
        </el-button>
      </div>
    </div>

    <!-- 功能区域 -->
    <el-tabs v-model="activeTab" class="function-tabs">
      <!-- 人工直呼 -->
      <el-tab-pane label="人工直呼" name="manual">
        <div class="manual-direct-panel">
          <div class="clue-selector">
            <el-select v-model="selectedClue" placeholder="选择线索">
              <el-option
                v-for="clue in mockClues"
                :key="clue.id"
                :label="`${clue.name} - ${clue.phone}`"
                :value="clue"
              />
            </el-select>
            
            <el-button 
              type="primary"
              :disabled="!canExecuteAction('launch-call') || !selectedClue"
              :loading="manualDirectState.isDialing"
              @click="handleLaunchCall"
            >
              {{ manualDirectState.isDialing ? '呼叫中...' : '发起呼叫' }}
            </el-button>
          </div>
          
          <div v-if="manualDirectState.isDialing" class="dialing-info">
            <p>正在呼叫: {{ manualDirectState.currentClue.phone }}</p>
            <p>已用时: {{ manualDirectState.dialingDuration }}秒</p>
          </div>
          
          <div v-if="currentMode === 'DIRECT' && manualDirectState.busy" class="call-controls">
            <p>通话中: {{ manualDirectState.currentClue.phone }}</p>
            <el-button type="danger" @click="handleHangup">挂断</el-button>
            <el-button @click="handleToggleMute">{{ isMuted ? '取消静音' : '静音' }}</el-button>
          </div>
        </div>
      </el-tab-pane>

      <!-- 人机协同监听 -->
      <el-tab-pane label="人机协同监听" name="monitor">
        <div class="monitor-panel">
          <div class="monitor-controls">
            <el-input 
              v-model="monitorRecordId" 
              placeholder="输入通话记录ID"
              style="width: 200px; margin-right: 10px;"
            />
            
            <el-button 
              type="primary"
              :disabled="!canExecuteAction('start-monitor') || !monitorRecordId"
              :loading="humanMachineMonitorState.isMonitoring && !humanMachineMonitorState.busy"
              @click="handleStartMonitor"
            >
              开始监听
            </el-button>
          </div>
          
          <div v-if="humanMachineMonitorState.isMonitoring" class="monitoring-info">
            <p>监听中: {{ humanMachineMonitorState.callRecord.recordId }}</p>
            <p>监听时长: {{ humanMachineMonitorState.monitorDuration }}秒</p>
            
            <div class="monitor-actions">
              <el-button 
                type="warning"
                :disabled="!humanMachineMonitorState.canExecuteIntervene"
                :loading="humanMachineMonitorState.isIntervening"
                @click="handleIntervene"
              >
                {{ getInterveneButtonText() }}
              </el-button>
              
              <el-button 
                type="danger"
                @click="handleExitMonitor"
              >
                退出监听
              </el-button>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 人机协同接管 -->
      <el-tab-pane label="直接接管" name="takeover">
        <div class="takeover-panel">
          <div class="takeover-controls">
            <el-input 
              v-model="takeoverRecordId" 
              placeholder="输入通话记录ID"
              style="width: 200px; margin-right: 10px;"
            />
            
            <el-button 
              type="warning"
              :disabled="!canExecuteAction('takeover') || !takeoverRecordId"
              :loading="humanMachineTakeoverState.isTakingOver"
              @click="handleTakeover"
            >
              {{ getTakeoverButtonText() }}
            </el-button>
          </div>
          
          <div v-if="humanMachineTakeoverState.isTakingOver" class="takeover-info">
            <p v-if="humanMachineTakeoverState.isWaitingForIce">
              正在建立音频连接...
            </p>
            <p v-else>
              正在接管: {{ humanMachineTakeoverState.callRecord.recordId }}
            </p>
          </div>
          
          <div v-if="currentMode === 'ANSWER' && humanMachineTakeoverState.busy" class="takeover-active">
            <p>接管成功，通话时长: {{ humanMachineTakeoverState.takeoverDuration }}秒</p>
            <el-button type="danger" @click="handleHangup">挂断</el-button>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 状态监控 -->
    <div class="status-monitor">
      <el-card header="状态监控" size="small">
        <div class="status-grid">
          <div class="status-item">
            <span>当前模式:</span>
            <span>{{ getModeText(currentMode) || '无' }}</span>
          </div>
          <div class="status-item">
            <span>通话状态:</span>
            <span>{{ isInCall ? '通话中' : '空闲' }}</span>
          </div>
          <div class="status-item">
            <span>坐席状态:</span>
            <span>{{ seatStatus }}</span>
          </div>
        </div>
        
        <div class="service-health">
          <h4>服务健康状态</h4>
          <div class="health-grid">
            <div 
              v-for="(service, name) in servicesHealth" 
              :key="name"
              class="health-item"
            >
              <span>{{ getServiceName(name) }}:</span>
              <el-tag 
                :type="service.available && service.registered ? 'success' : 'danger'"
                size="small"
              >
                {{ service.available && service.registered ? '正常' : '异常' }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useSeatPhoneStore } from '@/store/seat-phone-refactored'
import type { ClueItem } from '@/type/clue'

// ================================ Store ================================

const store = useSeatPhoneStore()

// ================================ 响应式数据 ================================

const activeTab = ref('manual')
const selectedClue = ref<ClueItem | null>(null)
const monitorRecordId = ref('')
const takeoverRecordId = ref('')
const isMuted = ref(false)

// 模拟线索数据
const mockClues = ref<ClueItem[]>([
  { id: 1, name: '张三', phone: '13800138001' },
  { id: 2, name: '李四', phone: '13800138002' },
  { id: 3, name: '王五', phone: '13800138003' },
])

// ================================ 计算属性 ================================

// 从store获取状态
const currentMode = computed(() => store.currentMode)
const seatStatus = computed(() => store.seatStatus)
const seatOnline = computed(() => store.seatOnline)
const isInCall = computed(() => store.isInCall)
const canGoOnline = computed(() => store.canGoOnline)
const canGoOffline = computed(() => store.canGoOffline)
const currentSeat = computed(() => store.currentSeat)

// 各模式状态
const manualDirectState = computed(() => store.manualDirectState)
const humanMachineMonitorState = computed(() => store.humanMachineMonitorState)
const humanMachineTakeoverState = computed(() => store.humanMachineTakeoverState)

// 服务健康状态
const servicesHealth = computed(() => store.getServicesHealth())

// ================================ 方法 ================================

/**
 * 坐席上线
 */
const handleGoOnline = async () => {
  // 设置模拟的坐席信息
  store.setSeatInfo(
    { id: 1, name: '测试坐席' },
    { account: 'test001', password: '123456', address: '*************' }
  )
  
  const success = await store.goOnline()
  if (success) {
    ElMessage.success('坐席上线成功')
  }
}

/**
 * 坐席下线
 */
const handleGoOffline = async () => {
  const success = await store.goOffline()
  if (success) {
    ElMessage.success('坐席下线成功')
  }
}

/**
 * 发起人工直呼
 */
const handleLaunchCall = async () => {
  if (!selectedClue.value) return
  
  const success = await store.launchManualCall(selectedClue.value)
  if (success) {
    ElMessage.success('呼叫发起成功')
  }
}

/**
 * 开始监听
 */
const handleStartMonitor = async () => {
  const callRecord = { recordId: monitorRecordId.value }
  const success = await store.startHumanMachineMonitor(callRecord)
  if (success) {
    ElMessage.success('开始监听')
  }
}

/**
 * 执行介入
 */
const handleIntervene = async () => {
  const success = await store.executeHumanMachineIntervene()
  if (success) {
    ElMessage.success('介入成功')
  }
}

/**
 * 退出监听
 */
const handleExitMonitor = async () => {
  const success = await store.exitHumanMachineMonitor('用户手动退出')
  if (success) {
    ElMessage.success('已退出监听')
  }
}

/**
 * 执行接管
 */
const handleTakeover = async () => {
  const callRecord = { recordId: takeoverRecordId.value }
  const success = await store.executeHumanMachineTakeover(callRecord)
  if (success) {
    ElMessage.success('接管成功')
  }
}

/**
 * 挂断电话
 */
const handleHangup = () => {
  store.hangupCurrent()
  ElMessage.warning('电话已挂断')
}

/**
 * 切换静音
 */
const handleToggleMute = () => {
  isMuted.value = store.toggleMute(!isMuted.value)
}

/**
 * 检查是否可以执行操作
 */
const canExecuteAction = (action: string): boolean => {
  return store.canExecuteAction(action)
}

/**
 * 获取模式标签类型
 */
const getModeTagType = (mode: string | null): string => {
  switch (mode) {
    case 'DIRECT': return 'primary'
    case 'LISTEN': return 'warning'
    case 'ANSWER': return 'danger'
    default: return 'info'
  }
}

/**
 * 获取模式文本
 */
const getModeText = (mode: string | null): string => {
  switch (mode) {
    case 'DIRECT': return '人工直呼'
    case 'LISTEN': return '人机协同监听'
    case 'ANSWER': return '人机协同接管'
    default: return '无'
  }
}

/**
 * 获取介入按钮文本
 */
const getInterveneButtonText = (): string => {
  const state = humanMachineMonitorState.value
  if (state.isIntervening) return '介入中...'
  if (!state.interveneButtonEnabled) return '准备中...'
  if (!state.canExecuteIntervene) return '无法介入'
  return '介入'
}

/**
 * 获取接管按钮文本
 */
const getTakeoverButtonText = (): string => {
  const state = humanMachineTakeoverState.value
  if (state.isTakingOver) {
    return state.isWaitingForIce ? '连接中...' : '接管中...'
  }
  return '直接接管'
}

/**
 * 获取服务名称
 */
const getServiceName = (name: string): string => {
  switch (name) {
    case 'manualDirect': return '人工直呼'
    case 'humanMachineMonitor': return '人机监听'
    case 'humanMachineTakeover': return '人机接管'
    default: return name
  }
}

// ================================ 生命周期 ================================

onMounted(() => {
  console.log('重构后的坐席电话工作台已加载')
})
</script>

<style scoped>
.seat-phone-workbench {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.seat-info, .mode-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.function-tabs {
  margin-bottom: 20px;
}

.manual-direct-panel,
.monitor-panel,
.takeover-panel {
  padding: 20px;
}

.clue-selector,
.monitor-controls,
.takeover-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.dialing-info,
.monitoring-info,
.takeover-info,
.call-controls,
.takeover-active {
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.monitor-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.status-monitor {
  margin-top: 20px;
}

.status-grid,
.health-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-bottom: 15px;
}

.status-item,
.health-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background-color: #fafafa;
  border-radius: 4px;
}

.service-health h4 {
  margin: 15px 0 10px 0;
  color: #409eff;
}
</style>
