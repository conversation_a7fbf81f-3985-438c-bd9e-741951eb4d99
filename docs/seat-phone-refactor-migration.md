# 坐席电话Store重构迁移指南

## 重构概述

原有的 `seat-phone.ts` 文件有3544行代码，功能耦合严重，难以维护。重构后采用模块化架构，按照三大通话模式进行拆分：

1. **人工直呼模式** (`ManualDirectService`)
2. **人机协同监听模式** (`HumanMachineMonitorService`) 
3. **人机协同直接接管模式** (`HumanMachineTakeoverService`)

## 新架构结构

```
src/store/seat/
├── core/
│   └── base-call-service.ts          # 基础通话服务类
├── modes/
│   ├── manual-direct-service.ts      # 人工直呼服务
│   ├── human-machine-monitor-service.ts    # 人机协同监听服务
│   └── human-machine-takeover-service.ts   # 人机协同接管服务
├── seat-manager.ts                   # 坐席管理器
└── seat-phone-refactored.ts          # 重构后的主Store
```

## 迁移步骤

### 1. 导入新Store

```typescript
// 旧方式
import { useSeatPhoneStore } from '@/store/seat-phone'

// 新方式
import { useSeatPhoneStore } from '@/store/seat-phone-refactored'
```

### 2. API变更对照表

#### 坐席生命周期

| 旧API | 新API | 说明 |
|-------|-------|------|
| `goOnline()` | `goOnline()` | 无变化 |
| `goOffline()` | `goOffline()` | 无变化 |
| `setSeatInfo()` | `setSeatInfo(seat, account)` | 参数合并 |

#### 人工直呼模式

| 旧API | 新API | 说明 |
|-------|-------|------|
| `launchCall(clue)` | `launchManualCall(clue)` | 更明确的命名 |
| `hangup()` | `hangupCurrent()` | 更明确的命名 |
| `busy` | `manualDirectState.busy` | 状态获取方式变更 |
| `dialingDuration` | `manualDirectState.dialingDuration` | 状态获取方式变更 |

#### 人机协同监听模式

| 旧API | 新API | 说明 |
|-------|-------|------|
| `startMonitor()` | `startHumanMachineMonitor(callRecord)` | 参数明确化 |
| `intervene()` | `executeHumanMachineIntervene()` | 更明确的命名 |
| `exitMonitor()` | `exitHumanMachineMonitor(reason)` | 参数明确化 |

#### 人机协同直接接管模式

| 旧API | 新API | 说明 |
|-------|-------|------|
| `handleDirectTakeover()` | `executeHumanMachineTakeover(callRecord)` | 更明确的命名和参数 |

### 3. 状态获取方式变更

#### 旧方式
```typescript
const store = useSeatPhoneStore()

// 获取各种状态
const busy = store.busy
const iceState = store.iceState
const dialingDuration = store.dialingDuration
```

#### 新方式
```typescript
const store = useSeatPhoneStore()

// 获取全局状态
const isInCall = store.isInCall
const currentMode = store.currentMode

// 获取特定模式状态
const manualState = store.manualDirectState
const monitorState = store.humanMachineMonitorState
const takeoverState = store.humanMachineTakeoverState

// 或者获取当前模式状态
const currentState = store.getCurrentModeState()
```

### 4. 组件迁移示例

#### 旧组件代码
```vue
<template>
  <div>
    <el-button 
      :disabled="!store.canMakeCall" 
      @click="store.launchCall(clue)"
    >
      发起呼叫
    </el-button>
    
    <div v-if="store.busy">
      通话中: {{ store.dialingDuration }}秒
    </div>
  </div>
</template>

<script setup>
import { useSeatPhoneStore } from '@/store/seat-phone'

const store = useSeatPhoneStore()
const clue = ref({})
</script>
```

#### 新组件代码
```vue
<template>
  <div>
    <el-button 
      :disabled="!store.canExecuteAction('launch-call')" 
      @click="store.launchManualCall(clue)"
    >
      发起呼叫
    </el-button>
    
    <div v-if="store.isInCall && store.currentMode === 'DIRECT'">
      通话中: {{ store.manualDirectState.dialingDuration }}秒
    </div>
  </div>
</template>

<script setup>
import { useSeatPhoneStore } from '@/store/seat-phone-refactored'

const store = useSeatPhoneStore()
const clue = ref({})
</script>
```

### 5. 兼容性处理

为了平滑迁移，新Store提供了一些兼容性方法：

```typescript
// 这些方法保持向后兼容
const store = useSeatPhoneStore()

store.launchCall(clue)  // 等同于 launchManualCall
store.intervene()       // 等同于 executeHumanMachineIntervene
store.hangup()          // 等同于 hangupCurrent
```

### 6. 错误处理改进

#### 旧方式
```typescript
try {
  await store.launchCall(clue)
} catch (error) {
  // 错误处理分散在各处
  ElMessage.error('呼叫失败')
}
```

#### 新方式
```typescript
// 新架构内置了统一的错误处理
const success = await store.launchManualCall(clue)
if (success) {
  // 成功处理
} else {
  // 失败已经在内部处理，包括用户提示
}
```

## 重构优势

### 1. 代码组织

- **模块化**: 按功能模块拆分，职责清晰
- **可维护性**: 单个文件代码量大幅减少
- **可测试性**: 每个模块可独立测试

### 2. 性能优化

- **按需加载**: 只初始化需要的服务
- **资源管理**: 更好的定时器和连接管理
- **内存优化**: 及时清理不需要的资源

### 3. 开发体验

- **类型安全**: 更好的TypeScript支持
- **调试友好**: 清晰的日志和状态管理
- **扩展性**: 易于添加新功能

### 4. 代码质量

- **低耦合**: 各模块独立，互不影响
- **高内聚**: 相关功能聚合在一起
- **复用性**: 公共功能可在多处使用

## 迁移检查清单

- [ ] 更新Store导入路径
- [ ] 修改API调用方式
- [ ] 更新状态获取逻辑
- [ ] 测试所有通话模式功能
- [ ] 验证错误处理是否正常
- [ ] 检查性能是否有改善
- [ ] 更新相关文档

## 注意事项

1. **渐进式迁移**: 建议逐个页面迁移，避免一次性大改
2. **充分测试**: 每个通话模式都要充分测试
3. **向后兼容**: 利用兼容性方法减少迁移工作量
4. **监控观察**: 迁移后密切观察系统稳定性

## 回滚方案

如果迁移过程中遇到问题，可以：

1. 快速回滚到旧Store
2. 保留新旧Store并存，逐步迁移
3. 使用适配器模式包装新Store以兼容旧API

## 后续优化

重构完成后，可以进一步优化：

1. **添加单元测试**: 为每个服务类添加测试
2. **性能监控**: 添加性能指标收集
3. **功能扩展**: 基于新架构添加新功能
4. **文档完善**: 补充API文档和使用示例

这次重构为坐席电话功能奠定了坚实的基础，后续的维护和扩展将变得更加容易。
