{
  "compilerOptions": {
    "target": "esnext",
    "typeRoots": [
      "node_modules/@types",
      "src/types"
    ],
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "lib": ["esnext", "dom"],
    "skipLibCheck": true,
    "baseUrl": "./",
    "paths":{
      "@": ["src"],
      "@/*": ["src/*"]
    }
  },
  "include": [
    "env.d.ts",
    "src/**/*",
    "src/**/*.vue",
    "auto-imports.d.ts",
  ],
  "references": [{ "path": "./tsconfig.config.json" }]
}
