export default {
  "test.85": {
    "sftp": {
      "host": "*************",
      "port": "22",
      "username": "root",
      "password": "root"
    },
    "pathConfig": {
      "remotePath": "/static/aispeech-ptest",
      "localPath": "./dist"
    }
  },
  "test.110": {
    "sftp": {
      "host": "***************",
      "port": "22",
      "username": "root",
      "password": "%k&ZGwUWfRUx"
    },
    "pathConfig": {
      "remotePath": "/static/aispeech",
      "localPath": "./dist"
    }
  },
  "test.113": {
    "sftp": {
      "host": "***************",
      "port": "22",
      "username": "root",
      "password": "TxrWYvGt8mN5EB#J"
    },
    "pathConfig": {
      "remotePath": "/home/<USER>/aiSpeech",
      "localPath": "./dist"
    }
  },
  "test.125": {
    "sftp": {
      "host": "**************",
      "port": "22",
      "username": "root",
      "password": "123456"
    },
    "pathConfig": {
      "remotePath": "/opt/aiSpeech",
      "localPath": "./dist"
    }
  },
  "test.111": {
    "sftp": {
      "host": "***************",
      "port": "22",
      "username": "root",
      "password": "%k&ZGwUWfRUx"
    },
    "pathConfig": {
      "remotePath": "/static/aispeech",
      "localPath": "./dist"
    }
  }
}
