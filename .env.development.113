NODE_ENV='development'
VUE_APP_TITLE='dev'

# 开发环境

# HTTP API
VITE_API_ORIGIN_HTTP='http://192.168.231.113:8860/market/'
# HTTPS API
VITE_API_ORIGIN_HTTPS='https://192.168.231.113:8861/market/'

# Janus HTTP 服务器
VITE_JANUS_HTTP='http://192.168.23.176:8188/janus'
# Janus HTTPS 服务器
VITE_JANUS_HTTPS='https://192.168.23.176:8189/janus'
# Janus WebSocket 服务器
VITE_JANUS_WEBSOCKET='ws://192.168.23.176:8988/janus'
# Janus WebSockets 服务器
VITE_JANUS_WEBSOCKETS='wss://192.168.23.176:8989/janus'

# STUN 服务器
VITE_STUN_SERVER='stun:192.168.23.176:3478'
# TURN 服务器 UDP 模式
VITE_TURN_SERVER_UDP='turn:192.168.23.176:3478?transport=udp'
# TURN 服务器 TCP 普通明文模式
VITE_TURN_SERVER_TCP_PLAIN='turn:192.168.23.176:3478?transport=tcp'
# TURN 服务器 TCP TLS加密模式
VITE_TURN_SERVER_TCP_TLS='turn:192.168.23.176:3478?transport=tcp'
# TURN 用户名
VITE_TURN_USERNAME='username'
# TURN 密码
VITE_TURN_PASSWORD='password'

# 人工外呼 WebSocket 服务器地址
#VITE_WEBSOCKET_URL='ws://192.168.23.85:8268/ws'
VITE_WEBSOCKET_URL='wss://wstest.system.bountech.com:443/ws'

# 监控平台 HTTP API
VITE_MONITOR_HTTP='http://192.168.23.176:8266'
# 监控平台 HTTPS API
VITE_MONITOR_HTTPS='https://192.168.23.176:8266'
